{"ast": null, "code": "/**\n * Converts 24-hour time format to 12-hour AM/PM format\n * @param time24 - Time in 24-hour format (e.g., \"06:00\", \"15:30\")\n * @returns FormattedTime object with original, formatted, and period\n */\nexport const convertTo12Hour = time24 => {\n  const timeParts = time24.split(':');\n  const hours = Number(timeParts[0]);\n  const minutes = Number(timeParts[1]);\n  if (isNaN(hours) || isNaN(minutes) || timeParts.length !== 2) {\n    return {\n      original: time24,\n      formatted: time24,\n      period: 'AM'\n    };\n  }\n  const period = hours >= 12 ? 'PM' : 'AM';\n  const hours12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;\n  const formattedMinutes = minutes.toString().padStart(2, '0');\n  return {\n    original: time24,\n    formatted: `${hours12}:${formattedMinutes} ${period}`,\n    period\n  };\n};\n\n/**\n * Formats a time range from 24-hour to 12-hour format\n * @param timeRange - Time range in format \"06:00 - 07:30\"\n * @returns Formatted time range string\n */\nexport const formatTimeRange = timeRange => {\n  const rangeParts = timeRange.split(' - ');\n  if (rangeParts.length !== 2 || !rangeParts[0] || !rangeParts[1]) {\n    return timeRange; // Return original if not a valid range\n  }\n  const startTime = convertTo12Hour(rangeParts[0].trim());\n  const endTime = convertTo12Hour(rangeParts[1].trim());\n  return `${startTime.formatted} - ${endTime.formatted}`;\n};\n\n/**\n * Extracts and formats time from a string that contains time information\n * @param text - Text containing time (e.g., \"06:00 - 07:30 | 💪 Pillar 1: Physical Fitness\")\n * @returns Formatted text with converted time\n */\nexport const formatTimeInText = text => {\n  // Regex to match time patterns like \"06:00 - 07:30\" or \"06:00\"\n  const timeRangeRegex = /(\\d{2}:\\d{2})\\s*-\\s*(\\d{2}:\\d{2})/g;\n  const singleTimeRegex = /(\\d{2}:\\d{2})/g;\n\n  // First, handle time ranges\n  let formattedText = text.replace(timeRangeRegex, (_match, start, end) => {\n    const startFormatted = convertTo12Hour(start);\n    const endFormatted = convertTo12Hour(end);\n    return `${startFormatted.formatted} - ${endFormatted.formatted}`;\n  });\n\n  // Then handle single times that weren't part of ranges\n  formattedText = formattedText.replace(singleTimeRegex, match => {\n    // Check if this time is already formatted (contains AM/PM)\n    if (formattedText.includes(`${match} AM`) || formattedText.includes(`${match} PM`)) {\n      return match; // Already formatted, don't change\n    }\n    const formatted = convertTo12Hour(match);\n    return formatted.formatted;\n  });\n  return formattedText;\n};\n\n/**\n * Gets the current time in 12-hour format\n * @returns Current time string in 12-hour format\n */\nexport const getCurrentTime12Hour = () => {\n  const now = new Date();\n  const hours = now.getHours();\n  const minutes = now.getMinutes();\n  const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;\n  return convertTo12Hour(timeString).formatted;\n};\n\n/**\n * Checks if a given time is in the morning (AM)\n * @param time24 - Time in 24-hour format\n * @returns True if the time is AM, false if PM\n */\nexport const isAM = time24 => {\n  const formatted = convertTo12Hour(time24);\n  return formatted.period === 'AM';\n};\n\n/**\n * Sorts an array of time strings in chronological order\n * @param times - Array of time strings in 24-hour format\n * @returns Sorted array of times\n */\nexport const sortTimes = times => {\n  return times.sort((a, b) => {\n    const partsA = a.split(':');\n    const partsB = b.split(':');\n    const hoursA = Number(partsA[0]) || 0;\n    const minutesA = Number(partsA[1]) || 0;\n    const hoursB = Number(partsB[0]) || 0;\n    const minutesB = Number(partsB[1]) || 0;\n    const totalMinutesA = hoursA * 60 + minutesA;\n    const totalMinutesB = hoursB * 60 + minutesB;\n    return totalMinutesA - totalMinutesB;\n  });\n};", "map": {"version": 3, "names": ["convertTo12Hour", "time24", "timeParts", "split", "hours", "Number", "minutes", "isNaN", "length", "original", "formatted", "period", "hours12", "formattedMinutes", "toString", "padStart", "formatTimeRange", "timeRange", "rangeParts", "startTime", "trim", "endTime", "formatTimeInText", "text", "timeRangeRegex", "singleTimeRegex", "formattedText", "replace", "_match", "start", "end", "startFormatted", "endFormatted", "match", "includes", "getCurrentTime12Hour", "now", "Date", "getHours", "getMinutes", "timeString", "isAM", "sortTimes", "times", "sort", "a", "b", "partsA", "partsB", "hoursA", "minutesA", "hoursB", "minutesB", "totalMinutesA", "totalMinutesB"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/utils/timeFormat.ts"], "sourcesContent": ["import { FormattedTime } from '../types';\n\n/**\n * Converts 24-hour time format to 12-hour AM/PM format\n * @param time24 - Time in 24-hour format (e.g., \"06:00\", \"15:30\")\n * @returns FormattedTime object with original, formatted, and period\n */\nexport const convertTo12Hour = (time24: string): FormattedTime => {\n  const timeParts = time24.split(':');\n  const hours = Number(timeParts[0]);\n  const minutes = Number(timeParts[1]);\n\n  if (isNaN(hours) || isNaN(minutes) || timeParts.length !== 2) {\n    return {\n      original: time24,\n      formatted: time24,\n      period: 'AM'\n    };\n  }\n\n  const period: 'AM' | 'PM' = hours >= 12 ? 'PM' : 'AM';\n  const hours12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;\n  const formattedMinutes = minutes.toString().padStart(2, '0');\n  \n  return {\n    original: time24,\n    formatted: `${hours12}:${formattedMinutes} ${period}`,\n    period\n  };\n};\n\n/**\n * Formats a time range from 24-hour to 12-hour format\n * @param timeRange - Time range in format \"06:00 - 07:30\"\n * @returns Formatted time range string\n */\nexport const formatTimeRange = (timeRange: string): string => {\n  const rangeParts = timeRange.split(' - ');\n  if (rangeParts.length !== 2 || !rangeParts[0] || !rangeParts[1]) {\n    return timeRange; // Return original if not a valid range\n  }\n\n  const startTime = convertTo12Hour(rangeParts[0].trim());\n  const endTime = convertTo12Hour(rangeParts[1].trim());\n  \n  return `${startTime.formatted} - ${endTime.formatted}`;\n};\n\n/**\n * Extracts and formats time from a string that contains time information\n * @param text - Text containing time (e.g., \"06:00 - 07:30 | 💪 Pillar 1: Physical Fitness\")\n * @returns Formatted text with converted time\n */\nexport const formatTimeInText = (text: string): string => {\n  // Regex to match time patterns like \"06:00 - 07:30\" or \"06:00\"\n  const timeRangeRegex = /(\\d{2}:\\d{2})\\s*-\\s*(\\d{2}:\\d{2})/g;\n  const singleTimeRegex = /(\\d{2}:\\d{2})/g;\n  \n  // First, handle time ranges\n  let formattedText = text.replace(timeRangeRegex, (_match, start, end) => {\n    const startFormatted = convertTo12Hour(start);\n    const endFormatted = convertTo12Hour(end);\n    return `${startFormatted.formatted} - ${endFormatted.formatted}`;\n  });\n  \n  // Then handle single times that weren't part of ranges\n  formattedText = formattedText.replace(singleTimeRegex, (match) => {\n    // Check if this time is already formatted (contains AM/PM)\n    if (formattedText.includes(`${match} AM`) || formattedText.includes(`${match} PM`)) {\n      return match; // Already formatted, don't change\n    }\n    const formatted = convertTo12Hour(match);\n    return formatted.formatted;\n  });\n  \n  return formattedText;\n};\n\n/**\n * Gets the current time in 12-hour format\n * @returns Current time string in 12-hour format\n */\nexport const getCurrentTime12Hour = (): string => {\n  const now = new Date();\n  const hours = now.getHours();\n  const minutes = now.getMinutes();\n  const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;\n  return convertTo12Hour(timeString).formatted;\n};\n\n/**\n * Checks if a given time is in the morning (AM)\n * @param time24 - Time in 24-hour format\n * @returns True if the time is AM, false if PM\n */\nexport const isAM = (time24: string): boolean => {\n  const formatted = convertTo12Hour(time24);\n  return formatted.period === 'AM';\n};\n\n/**\n * Sorts an array of time strings in chronological order\n * @param times - Array of time strings in 24-hour format\n * @returns Sorted array of times\n */\nexport const sortTimes = (times: string[]): string[] => {\n  return times.sort((a, b) => {\n    const partsA = a.split(':');\n    const partsB = b.split(':');\n    const hoursA = Number(partsA[0]) || 0;\n    const minutesA = Number(partsA[1]) || 0;\n    const hoursB = Number(partsB[0]) || 0;\n    const minutesB = Number(partsB[1]) || 0;\n\n    const totalMinutesA = hoursA * 60 + minutesA;\n    const totalMinutesB = hoursB * 60 + minutesB;\n\n    return totalMinutesA - totalMinutesB;\n  });\n};\n"], "mappings": "AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,eAAe,GAAIC,MAAc,IAAoB;EAChE,MAAMC,SAAS,GAAGD,MAAM,CAACE,KAAK,CAAC,GAAG,CAAC;EACnC,MAAMC,KAAK,GAAGC,MAAM,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;EAClC,MAAMI,OAAO,GAAGD,MAAM,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;EAEpC,IAAIK,KAAK,CAACH,KAAK,CAAC,IAAIG,KAAK,CAACD,OAAO,CAAC,IAAIJ,SAAS,CAACM,MAAM,KAAK,CAAC,EAAE;IAC5D,OAAO;MACLC,QAAQ,EAAER,MAAM;MAChBS,SAAS,EAAET,MAAM;MACjBU,MAAM,EAAE;IACV,CAAC;EACH;EAEA,MAAMA,MAAmB,GAAGP,KAAK,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI;EACrD,MAAMQ,OAAO,GAAGR,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,KAAK,GAAG,EAAE,GAAGA,KAAK,GAAG,EAAE,GAAGA,KAAK;EAClE,MAAMS,gBAAgB,GAAGP,OAAO,CAACQ,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAE5D,OAAO;IACLN,QAAQ,EAAER,MAAM;IAChBS,SAAS,EAAE,GAAGE,OAAO,IAAIC,gBAAgB,IAAIF,MAAM,EAAE;IACrDA;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,eAAe,GAAIC,SAAiB,IAAa;EAC5D,MAAMC,UAAU,GAAGD,SAAS,CAACd,KAAK,CAAC,KAAK,CAAC;EACzC,IAAIe,UAAU,CAACV,MAAM,KAAK,CAAC,IAAI,CAACU,UAAU,CAAC,CAAC,CAAC,IAAI,CAACA,UAAU,CAAC,CAAC,CAAC,EAAE;IAC/D,OAAOD,SAAS,CAAC,CAAC;EACpB;EAEA,MAAME,SAAS,GAAGnB,eAAe,CAACkB,UAAU,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;EACvD,MAAMC,OAAO,GAAGrB,eAAe,CAACkB,UAAU,CAAC,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;EAErD,OAAO,GAAGD,SAAS,CAACT,SAAS,MAAMW,OAAO,CAACX,SAAS,EAAE;AACxD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMY,gBAAgB,GAAIC,IAAY,IAAa;EACxD;EACA,MAAMC,cAAc,GAAG,oCAAoC;EAC3D,MAAMC,eAAe,GAAG,gBAAgB;;EAExC;EACA,IAAIC,aAAa,GAAGH,IAAI,CAACI,OAAO,CAACH,cAAc,EAAE,CAACI,MAAM,EAAEC,KAAK,EAAEC,GAAG,KAAK;IACvE,MAAMC,cAAc,GAAG/B,eAAe,CAAC6B,KAAK,CAAC;IAC7C,MAAMG,YAAY,GAAGhC,eAAe,CAAC8B,GAAG,CAAC;IACzC,OAAO,GAAGC,cAAc,CAACrB,SAAS,MAAMsB,YAAY,CAACtB,SAAS,EAAE;EAClE,CAAC,CAAC;;EAEF;EACAgB,aAAa,GAAGA,aAAa,CAACC,OAAO,CAACF,eAAe,EAAGQ,KAAK,IAAK;IAChE;IACA,IAAIP,aAAa,CAACQ,QAAQ,CAAC,GAAGD,KAAK,KAAK,CAAC,IAAIP,aAAa,CAACQ,QAAQ,CAAC,GAAGD,KAAK,KAAK,CAAC,EAAE;MAClF,OAAOA,KAAK,CAAC,CAAC;IAChB;IACA,MAAMvB,SAAS,GAAGV,eAAe,CAACiC,KAAK,CAAC;IACxC,OAAOvB,SAAS,CAACA,SAAS;EAC5B,CAAC,CAAC;EAEF,OAAOgB,aAAa;AACtB,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMS,oBAAoB,GAAGA,CAAA,KAAc;EAChD,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;EACtB,MAAMjC,KAAK,GAAGgC,GAAG,CAACE,QAAQ,CAAC,CAAC;EAC5B,MAAMhC,OAAO,GAAG8B,GAAG,CAACG,UAAU,CAAC,CAAC;EAChC,MAAMC,UAAU,GAAG,GAAGpC,KAAK,CAACU,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIT,OAAO,CAACQ,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAChG,OAAOf,eAAe,CAACwC,UAAU,CAAC,CAAC9B,SAAS;AAC9C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM+B,IAAI,GAAIxC,MAAc,IAAc;EAC/C,MAAMS,SAAS,GAAGV,eAAe,CAACC,MAAM,CAAC;EACzC,OAAOS,SAAS,CAACC,MAAM,KAAK,IAAI;AAClC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM+B,SAAS,GAAIC,KAAe,IAAe;EACtD,OAAOA,KAAK,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAC1B,MAAMC,MAAM,GAAGF,CAAC,CAAC1C,KAAK,CAAC,GAAG,CAAC;IAC3B,MAAM6C,MAAM,GAAGF,CAAC,CAAC3C,KAAK,CAAC,GAAG,CAAC;IAC3B,MAAM8C,MAAM,GAAG5C,MAAM,CAAC0C,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACrC,MAAMG,QAAQ,GAAG7C,MAAM,CAAC0C,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACvC,MAAMI,MAAM,GAAG9C,MAAM,CAAC2C,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACrC,MAAMI,QAAQ,GAAG/C,MAAM,CAAC2C,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAEvC,MAAMK,aAAa,GAAGJ,MAAM,GAAG,EAAE,GAAGC,QAAQ;IAC5C,MAAMI,aAAa,GAAGH,MAAM,GAAG,EAAE,GAAGC,QAAQ;IAE5C,OAAOC,aAAa,GAAGC,aAAa;EACtC,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}