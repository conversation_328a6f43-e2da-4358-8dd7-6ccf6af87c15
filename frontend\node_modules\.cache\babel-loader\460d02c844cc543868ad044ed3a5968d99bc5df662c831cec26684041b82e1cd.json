{"ast": null, "code": "/**\n * Converts 24-hour time format to 12-hour AM/PM format\n * @param time24 - Time in 24-hour format (e.g., \"06:00\", \"15:30\")\n * @returns FormattedTime object with original, formatted, and period\n */export const convertTo12Hour=time24=>{const[hours,minutes]=time24.split(':').map(Number);if(isNaN(hours)||isNaN(minutes)){return{original:time24,formatted:time24,period:'AM'};}const period=hours>=12?'PM':'AM';const hours12=hours===0?12:hours>12?hours-12:hours;const formattedMinutes=minutes.toString().padStart(2,'0');return{original:time24,formatted:\"\".concat(hours12,\":\").concat(formattedMinutes,\" \").concat(period),period};};/**\n * Formats a time range from 24-hour to 12-hour format\n * @param timeRange - Time range in format \"06:00 - 07:30\"\n * @returns Formatted time range string\n */export const formatTimeRange=timeRange=>{const rangeParts=timeRange.split(' - ');if(rangeParts.length!==2){return timeRange;// Return original if not a valid range\n}const startTime=convertTo12Hour(rangeParts[0].trim());const endTime=convertTo12Hour(rangeParts[1].trim());return\"\".concat(startTime.formatted,\" - \").concat(endTime.formatted);};/**\n * Extracts and formats time from a string that contains time information\n * @param text - Text containing time (e.g., \"06:00 - 07:30 | 💪 Pillar 1: Physical Fitness\")\n * @returns Formatted text with converted time\n */export const formatTimeInText=text=>{// Regex to match time patterns like \"06:00 - 07:30\" or \"06:00\"\nconst timeRangeRegex=/(\\d{2}:\\d{2})\\s*-\\s*(\\d{2}:\\d{2})/g;const singleTimeRegex=/(\\d{2}:\\d{2})/g;// First, handle time ranges\nlet formattedText=text.replace(timeRangeRegex,(match,start,end)=>{const startFormatted=convertTo12Hour(start);const endFormatted=convertTo12Hour(end);return\"\".concat(startFormatted.formatted,\" - \").concat(endFormatted.formatted);});// Then handle single times that weren't part of ranges\nformattedText=formattedText.replace(singleTimeRegex,match=>{// Check if this time is already formatted (contains AM/PM)\nif(formattedText.includes(\"\".concat(match,\" AM\"))||formattedText.includes(\"\".concat(match,\" PM\"))){return match;// Already formatted, don't change\n}const formatted=convertTo12Hour(match);return formatted.formatted;});return formattedText;};/**\n * Gets the current time in 12-hour format\n * @returns Current time string in 12-hour format\n */export const getCurrentTime12Hour=()=>{const now=new Date();const hours=now.getHours();const minutes=now.getMinutes();const timeString=\"\".concat(hours.toString().padStart(2,'0'),\":\").concat(minutes.toString().padStart(2,'0'));return convertTo12Hour(timeString).formatted;};/**\n * Checks if a given time is in the morning (AM)\n * @param time24 - Time in 24-hour format\n * @returns True if the time is AM, false if PM\n */export const isAM=time24=>{const formatted=convertTo12Hour(time24);return formatted.period==='AM';};/**\n * Sorts an array of time strings in chronological order\n * @param times - Array of time strings in 24-hour format\n * @returns Sorted array of times\n */export const sortTimes=times=>{return times.sort((a,b)=>{const[hoursA,minutesA]=a.split(':').map(Number);const[hoursB,minutesB]=b.split(':').map(Number);const totalMinutesA=hoursA*60+minutesA;const totalMinutesB=hoursB*60+minutesB;return totalMinutesA-totalMinutesB;});};", "map": {"version": 3, "names": ["convertTo12Hour", "time24", "hours", "minutes", "split", "map", "Number", "isNaN", "original", "formatted", "period", "hours12", "formattedMinutes", "toString", "padStart", "concat", "formatTimeRange", "timeRange", "rangeParts", "length", "startTime", "trim", "endTime", "formatTimeInText", "text", "timeRangeRegex", "singleTimeRegex", "formattedText", "replace", "match", "start", "end", "startFormatted", "endFormatted", "includes", "getCurrentTime12Hour", "now", "Date", "getHours", "getMinutes", "timeString", "isAM", "sortTimes", "times", "sort", "a", "b", "hoursA", "minutesA", "hoursB", "minutesB", "totalMinutesA", "totalMinutesB"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/utils/timeFormat.ts"], "sourcesContent": ["import { FormattedTime } from '../types';\n\n/**\n * Converts 24-hour time format to 12-hour AM/PM format\n * @param time24 - Time in 24-hour format (e.g., \"06:00\", \"15:30\")\n * @returns FormattedTime object with original, formatted, and period\n */\nexport const convertTo12Hour = (time24: string): FormattedTime => {\n  const [hours, minutes] = time24.split(':').map(Number);\n  \n  if (isNaN(hours) || isNaN(minutes)) {\n    return {\n      original: time24,\n      formatted: time24,\n      period: 'AM'\n    };\n  }\n  \n  const period: 'AM' | 'PM' = hours >= 12 ? 'PM' : 'AM';\n  const hours12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;\n  const formattedMinutes = minutes.toString().padStart(2, '0');\n  \n  return {\n    original: time24,\n    formatted: `${hours12}:${formattedMinutes} ${period}`,\n    period\n  };\n};\n\n/**\n * Formats a time range from 24-hour to 12-hour format\n * @param timeRange - Time range in format \"06:00 - 07:30\"\n * @returns Formatted time range string\n */\nexport const formatTimeRange = (timeRange: string): string => {\n  const rangeParts = timeRange.split(' - ');\n  if (rangeParts.length !== 2) {\n    return timeRange; // Return original if not a valid range\n  }\n  \n  const startTime = convertTo12Hour(rangeParts[0].trim());\n  const endTime = convertTo12Hour(rangeParts[1].trim());\n  \n  return `${startTime.formatted} - ${endTime.formatted}`;\n};\n\n/**\n * Extracts and formats time from a string that contains time information\n * @param text - Text containing time (e.g., \"06:00 - 07:30 | 💪 Pillar 1: Physical Fitness\")\n * @returns Formatted text with converted time\n */\nexport const formatTimeInText = (text: string): string => {\n  // Regex to match time patterns like \"06:00 - 07:30\" or \"06:00\"\n  const timeRangeRegex = /(\\d{2}:\\d{2})\\s*-\\s*(\\d{2}:\\d{2})/g;\n  const singleTimeRegex = /(\\d{2}:\\d{2})/g;\n  \n  // First, handle time ranges\n  let formattedText = text.replace(timeRangeRegex, (match, start, end) => {\n    const startFormatted = convertTo12Hour(start);\n    const endFormatted = convertTo12Hour(end);\n    return `${startFormatted.formatted} - ${endFormatted.formatted}`;\n  });\n  \n  // Then handle single times that weren't part of ranges\n  formattedText = formattedText.replace(singleTimeRegex, (match) => {\n    // Check if this time is already formatted (contains AM/PM)\n    if (formattedText.includes(`${match} AM`) || formattedText.includes(`${match} PM`)) {\n      return match; // Already formatted, don't change\n    }\n    const formatted = convertTo12Hour(match);\n    return formatted.formatted;\n  });\n  \n  return formattedText;\n};\n\n/**\n * Gets the current time in 12-hour format\n * @returns Current time string in 12-hour format\n */\nexport const getCurrentTime12Hour = (): string => {\n  const now = new Date();\n  const hours = now.getHours();\n  const minutes = now.getMinutes();\n  const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;\n  return convertTo12Hour(timeString).formatted;\n};\n\n/**\n * Checks if a given time is in the morning (AM)\n * @param time24 - Time in 24-hour format\n * @returns True if the time is AM, false if PM\n */\nexport const isAM = (time24: string): boolean => {\n  const formatted = convertTo12Hour(time24);\n  return formatted.period === 'AM';\n};\n\n/**\n * Sorts an array of time strings in chronological order\n * @param times - Array of time strings in 24-hour format\n * @returns Sorted array of times\n */\nexport const sortTimes = (times: string[]): string[] => {\n  return times.sort((a, b) => {\n    const [hoursA, minutesA] = a.split(':').map(Number);\n    const [hoursB, minutesB] = b.split(':').map(Number);\n    \n    const totalMinutesA = hoursA * 60 + minutesA;\n    const totalMinutesB = hoursB * 60 + minutesB;\n    \n    return totalMinutesA - totalMinutesB;\n  });\n};\n"], "mappings": "AAEA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAA,eAAe,CAAIC,MAAc,EAAoB,CAChE,KAAM,CAACC,KAAK,CAAEC,OAAO,CAAC,CAAGF,MAAM,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC,CAEtD,GAAIC,KAAK,CAACL,KAAK,CAAC,EAAIK,KAAK,CAACJ,OAAO,CAAC,CAAE,CAClC,MAAO,CACLK,QAAQ,CAAEP,MAAM,CAChBQ,SAAS,CAAER,MAAM,CACjBS,MAAM,CAAE,IACV,CAAC,CACH,CAEA,KAAM,CAAAA,MAAmB,CAAGR,KAAK,EAAI,EAAE,CAAG,IAAI,CAAG,IAAI,CACrD,KAAM,CAAAS,OAAO,CAAGT,KAAK,GAAK,CAAC,CAAG,EAAE,CAAGA,KAAK,CAAG,EAAE,CAAGA,KAAK,CAAG,EAAE,CAAGA,KAAK,CAClE,KAAM,CAAAU,gBAAgB,CAAGT,OAAO,CAACU,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAE5D,MAAO,CACLN,QAAQ,CAAEP,MAAM,CAChBQ,SAAS,IAAAM,MAAA,CAAKJ,OAAO,MAAAI,MAAA,CAAIH,gBAAgB,MAAAG,MAAA,CAAIL,MAAM,CAAE,CACrDA,MACF,CAAC,CACH,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAM,eAAe,CAAIC,SAAiB,EAAa,CAC5D,KAAM,CAAAC,UAAU,CAAGD,SAAS,CAACb,KAAK,CAAC,KAAK,CAAC,CACzC,GAAIc,UAAU,CAACC,MAAM,GAAK,CAAC,CAAE,CAC3B,MAAO,CAAAF,SAAS,CAAE;AACpB,CAEA,KAAM,CAAAG,SAAS,CAAGpB,eAAe,CAACkB,UAAU,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC,CACvD,KAAM,CAAAC,OAAO,CAAGtB,eAAe,CAACkB,UAAU,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC,CAErD,SAAAN,MAAA,CAAUK,SAAS,CAACX,SAAS,QAAAM,MAAA,CAAMO,OAAO,CAACb,SAAS,EACtD,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAc,gBAAgB,CAAIC,IAAY,EAAa,CACxD;AACA,KAAM,CAAAC,cAAc,CAAG,oCAAoC,CAC3D,KAAM,CAAAC,eAAe,CAAG,gBAAgB,CAExC;AACA,GAAI,CAAAC,aAAa,CAAGH,IAAI,CAACI,OAAO,CAACH,cAAc,CAAE,CAACI,KAAK,CAAEC,KAAK,CAAEC,GAAG,GAAK,CACtE,KAAM,CAAAC,cAAc,CAAGhC,eAAe,CAAC8B,KAAK,CAAC,CAC7C,KAAM,CAAAG,YAAY,CAAGjC,eAAe,CAAC+B,GAAG,CAAC,CACzC,SAAAhB,MAAA,CAAUiB,cAAc,CAACvB,SAAS,QAAAM,MAAA,CAAMkB,YAAY,CAACxB,SAAS,EAChE,CAAC,CAAC,CAEF;AACAkB,aAAa,CAAGA,aAAa,CAACC,OAAO,CAACF,eAAe,CAAGG,KAAK,EAAK,CAChE;AACA,GAAIF,aAAa,CAACO,QAAQ,IAAAnB,MAAA,CAAIc,KAAK,OAAK,CAAC,EAAIF,aAAa,CAACO,QAAQ,IAAAnB,MAAA,CAAIc,KAAK,OAAK,CAAC,CAAE,CAClF,MAAO,CAAAA,KAAK,CAAE;AAChB,CACA,KAAM,CAAApB,SAAS,CAAGT,eAAe,CAAC6B,KAAK,CAAC,CACxC,MAAO,CAAApB,SAAS,CAACA,SAAS,CAC5B,CAAC,CAAC,CAEF,MAAO,CAAAkB,aAAa,CACtB,CAAC,CAED;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAQ,oBAAoB,CAAGA,CAAA,GAAc,CAChD,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAnC,KAAK,CAAGkC,GAAG,CAACE,QAAQ,CAAC,CAAC,CAC5B,KAAM,CAAAnC,OAAO,CAAGiC,GAAG,CAACG,UAAU,CAAC,CAAC,CAChC,KAAM,CAAAC,UAAU,IAAAzB,MAAA,CAAMb,KAAK,CAACW,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,MAAAC,MAAA,CAAIZ,OAAO,CAACU,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,CAAE,CAChG,MAAO,CAAAd,eAAe,CAACwC,UAAU,CAAC,CAAC/B,SAAS,CAC9C,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAgC,IAAI,CAAIxC,MAAc,EAAc,CAC/C,KAAM,CAAAQ,SAAS,CAAGT,eAAe,CAACC,MAAM,CAAC,CACzC,MAAO,CAAAQ,SAAS,CAACC,MAAM,GAAK,IAAI,CAClC,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAgC,SAAS,CAAIC,KAAe,EAAe,CACtD,MAAO,CAAAA,KAAK,CAACC,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK,CAC1B,KAAM,CAACC,MAAM,CAAEC,QAAQ,CAAC,CAAGH,CAAC,CAACzC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC,CACnD,KAAM,CAAC2C,MAAM,CAAEC,QAAQ,CAAC,CAAGJ,CAAC,CAAC1C,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC,CAEnD,KAAM,CAAA6C,aAAa,CAAGJ,MAAM,CAAG,EAAE,CAAGC,QAAQ,CAC5C,KAAM,CAAAI,aAAa,CAAGH,MAAM,CAAG,EAAE,CAAGC,QAAQ,CAE5C,MAAO,CAAAC,aAAa,CAAGC,aAAa,CACtC,CAAC,CAAC,CACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}