import React from 'react';
import { Link } from 'react-router-dom';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { useChartData } from '../hooks/useChartData';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const Pillar1: React.FC = () => {
  const { data: keyLiftsData, options: keyLiftsOptions, loading } = useChartData('keyLifts');

  return (
    <div className="bg-gray-100 text-gray-800 min-h-screen">
      <div className="container mx-auto p-4 md:p-8">
        <header className="text-center mb-10">
          <div className="inline-block bg-red-500 text-white rounded-full px-5 py-2 mb-4 text-sm font-bold">
            PILLAR 1: THE BODY
          </div>
          <h1 className="text-4xl md:text-5xl font-black text-reedsoft-primary tracking-tight">
            Forging a Capable Machine
          </h1>
          <p className="text-lg text-gray-600 mt-2">
            Building functional strength and endurance for real-world work.
          </p>
        </header>

        {/* Weekly Workout Split */}
        <section id="workout-split" className="mb-12 bg-white p-6 rounded-lg shadow-lg">
          <h2 className="text-2xl font-bold text-center mb-6 text-reedsoft-primary">The Weekly Workout Split</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-center">
            <div className="bg-gray-50 p-6 rounded-lg border-l-4 border-red-500">
              <h3 className="font-bold text-lg">Strength Focus</h3>
              <p className="font-semibold text-red-500">Mon / Wed / Fri</p>
              <p className="mt-2 text-sm">
                Full-body compound exercises to build a powerful foundation. The core of your physical transformation.
              </p>
            </div>
            <div className="bg-gray-50 p-6 rounded-lg border-l-4 border-blue-500">
              <h3 className="font-bold text-lg">Cardio & Core</h3>
              <p className="font-semibold text-blue-500">Tue / Thu</p>
              <p className="mt-2 text-sm">
                High-Intensity Interval Training and core work to build work capacity and a resilient midsection.
              </p>
            </div>
          </div>
        </section>

        {/* Key Lifts Chart */}
        <section id="key-lifts" className="mb-12 bg-white p-6 rounded-lg shadow-lg">
          <h2 className="text-2xl font-bold text-center mb-6 text-reedsoft-primary">The Five Foundational Lifts</h2>
          <p className="text-sm text-gray-600 mb-6 text-center max-w-2xl mx-auto">
            Mastering these five compound movements is the fastest path to functional strength.
            They work multiple muscle groups simultaneously, mimicking real-world physical tasks.
          </p>
          <div className="relative w-full max-w-lg mx-auto h-96">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-reedsoft-primary"></div>
              </div>
            ) : keyLiftsData && keyLiftsOptions ? (
              <Bar data={keyLiftsData} options={keyLiftsOptions as any} />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500">
                Chart loading...
              </div>
            )}
          </div>
        </section>

        {/* Nutrition Pyramid */}
        <section id="nutrition" className="mb-12 bg-white p-6 rounded-lg shadow-lg">
          <h2 className="text-2xl font-bold text-center mb-6 text-reedsoft-primary">The Fuel Pyramid</h2>
          <p className="text-sm text-gray-600 mb-6 text-center">
            Your body is a machine; give it the right fuel. Follow this simple hierarchy for optimal performance and recovery. No complex diet needed.
          </p>
          <div className="relative max-w-sm mx-auto text-center font-bold text-white">
            <div className="w-full h-24 bg-red-500 flex items-center justify-center rounded-t-lg" style={{clipPath: 'polygon(15% 0, 85% 0, 100% 100%, 0% 100%)'}}>
              PROTEIN (Every Meal)
            </div>
            <div className="w-full h-20 bg-yellow-500 flex items-center justify-center" style={{clipPath: 'polygon(0 0, 100% 0, 85% 100%, 15% 100%)'}}>
              COMPLEX CARBS
            </div>
            <div className="w-full h-16 bg-green-500 flex items-center justify-center" style={{clipPath: 'polygon(15% 0, 85% 0, 70% 100%, 30% 100%)'}}>
              VEGETABLES
            </div>
            <div className="w-full h-12 bg-blue-500 flex items-center justify-center rounded-b-lg" style={{clipPath: 'polygon(30% 0, 70% 0, 55% 100%, 45% 100%)'}}>
              3L+ WATER DAILY
            </div>
          </div>
        </section>

        {/* Navigation */}
        <div className="flex justify-between items-center">
          <Link
            to="/japan"
            className="bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors"
          >
            ← Back to Dashboard
          </Link>
          <Link
            to="/japan/pillar-2"
            className="bg-reedsoft-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors"
          >
            Next: Japanese Language →
          </Link>
        </div>

        <footer className="text-center mt-10 pt-6 border-t border-gray-300">
          <p className="text-gray-700 font-semibold">Your body is your primary tool. Keep it sharp.</p>
        </footer>
      </div>
    </div>
  );
};

export default Pillar1;
