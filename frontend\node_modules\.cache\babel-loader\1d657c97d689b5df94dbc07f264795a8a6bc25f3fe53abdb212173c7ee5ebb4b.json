{"ast": null, "code": "import React from'react';import{<PERSON>}from'react-router-dom';import{<PERSON>hn<PERSON>}from'react-chartjs-2';import{Chart as ChartJS,ArcElement,Tooltip,Legend,Title}from'chart.js';import{useChartData}from'../hooks/useChartData';// Register Chart.js components\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";ChartJS.register(ArcElement,Tooltip,Legend,Title);const Pillar2=()=>{const{data:ankiData,options:ankiOptions,loading}=useChartData('anki');return/*#__PURE__*/_jsx(\"div\",{className:\"bg-cyan-50 text-gray-800 min-h-screen\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto p-4 md:p-8\",children:[/*#__PURE__*/_jsxs(\"header\",{className:\"text-center mb-10\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"inline-block bg-slate-800 text-white rounded-full px-5 py-2 mb-4 text-sm font-bold\",children:\"PILLAR 2: THE VOICE\"}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-4xl md:text-5xl font-black text-slate-800 tracking-tight\",children:\"Building a Bridge to Japan\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg text-teal-700 mt-2\",children:\"Your structured path to JLPT N4 conversational fluency.\"})]}),/*#__PURE__*/_jsxs(\"section\",{id:\"method\",className:\"mb-12 bg-white p-6 rounded-lg shadow-lg\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-center mb-6 text-slate-800\",children:\"The Four-Step Language Method\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-4 text-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 rounded-lg bg-gray-100 w-full md:w-auto\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-4xl\",children:\"\\u2460\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"font-bold mt-2\",children:\"Kana Mastery\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs\",children:\"The Alphabet\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-4xl text-cyan-600\",children:\"\\u2192\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 rounded-lg bg-gray-100 w-full md:w-auto\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-4xl\",children:\"\\u2461\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"font-bold mt-2\",children:\"Grammar Core\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs\",children:\"The Skeleton\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-4xl text-cyan-600\",children:\"\\u2192\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 rounded-lg bg-gray-100 w-full md:w-auto\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-4xl\",children:\"\\u2462\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"font-bold mt-2\",children:\"Anki Vocab\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs\",children:\"The Muscle\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-4xl text-cyan-600\",children:\"\\u2192\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4 rounded-lg bg-gray-100 w-full md:w-auto\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-4xl\",children:\"\\u2463\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"font-bold mt-2\",children:\"Immersion\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs\",children:\"The Real World\"})]})]})]}),/*#__PURE__*/_jsxs(\"section\",{className:\"mb-12 grid grid-cols-1 md:grid-cols-2 gap-8 items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white p-6 rounded-lg shadow-lg\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-center mb-4 text-slate-800\",children:\"The Anki Engine\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-center text-gray-600 mb-4\",children:\"Anki is your non-negotiable daily habit for vocabulary. This is how you burn thousands of words into your long-term memory.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"relative w-full max-w-sm mx-auto h-80\",children:loading?/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-full\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-b-2 border-slate-800\"})}):ankiData&&ankiOptions?/*#__PURE__*/_jsx(Doughnut,{data:ankiData,options:ankiOptions}):/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-full text-gray-500\",children:\"Chart loading...\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white p-6 rounded-lg shadow-lg\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-center mb-4 text-slate-800\",children:\"Monthly Mission Focus\"}),/*#__PURE__*/_jsxs(\"ul\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"li\",{className:\"p-3 bg-cyan-50 rounded-md border-l-4 border-teal-700\",children:[/*#__PURE__*/_jsx(\"strong\",{className:\"text-teal-700\",children:\"Month 1: Foundation.\"}),\" Master Hiragana & Katakana. Complete first half of Genki I. Start daily Anki habit.\"]}),/*#__PURE__*/_jsxs(\"li\",{className:\"p-3 bg-cyan-50 rounded-md border-l-4 border-teal-700\",children:[/*#__PURE__*/_jsx(\"strong\",{className:\"text-teal-700\",children:\"Month 2: Acceleration.\"}),\" Finish Genki I. Start Genki II. Aggressively build vocabulary with Anki (aim for 1000+ words).\"]}),/*#__PURE__*/_jsxs(\"li\",{className:\"p-3 bg-cyan-50 rounded-md border-l-4 border-teal-700\",children:[/*#__PURE__*/_jsx(\"strong\",{className:\"text-teal-700\",children:\"Month 3: Application.\"}),\" Continue Genki II. Begin daily active listening (podcasts) and speaking practice (HelloTalk).\"]}),/*#__PURE__*/_jsxs(\"li\",{className:\"p-3 bg-cyan-50 rounded-md border-l-4 border-teal-700\",children:[/*#__PURE__*/_jsx(\"strong\",{className:\"text-teal-700\",children:\"Month 4: Refinement.\"}),\" Finish Genki II. Focus heavily on conversational practice and reading simple news (NHK Easy).\"]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center\",children:[/*#__PURE__*/_jsx(Link,{to:\"/japan/pillar-1\",className:\"bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors\",children:\"\\u2190 Previous: Physical Fitness\"}),/*#__PURE__*/_jsx(Link,{to:\"/japan/pillar-3\",className:\"bg-reedsoft-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors\",children:\"Next: Practical Skills \\u2192\"})]}),/*#__PURE__*/_jsx(\"footer\",{className:\"text-center mt-10 pt-6 border-t border-cyan-300\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-teal-700 font-semibold\",children:\"Language is a skill built daily, not in a day.\"})})]})});};export default Pillar2;", "map": {"version": 3, "names": ["React", "Link", "Doughnut", "Chart", "ChartJS", "ArcElement", "<PERSON><PERSON><PERSON>", "Legend", "Title", "useChartData", "jsx", "_jsx", "jsxs", "_jsxs", "register", "Pillar2", "data", "ankiData", "options", "ankiOptions", "loading", "className", "children", "id", "to"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/pages/Pillar2.tsx"], "sourcesContent": ["import React from 'react';\nimport { <PERSON> } from 'react-router-dom';\nimport { Doughn<PERSON> } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  ArcElement,\n  Tooltip,\n  Legend,\n  Title\n} from 'chart.js';\nimport { useChartData } from '../hooks/useChartData';\n\n// Register Chart.js components\nChartJS.register(Arc<PERSON><PERSON>, Tooltip, Legend, Title);\n\nconst Pillar2: React.FC = () => {\n  const { data: ankiData, options: ankiOptions, loading } = useChartData('anki');\n\n  return (\n    <div className=\"bg-cyan-50 text-gray-800 min-h-screen\">\n      <div className=\"container mx-auto p-4 md:p-8\">\n        <header className=\"text-center mb-10\">\n          <div className=\"inline-block bg-slate-800 text-white rounded-full px-5 py-2 mb-4 text-sm font-bold\">\n            PILLAR 2: THE VOICE\n          </div>\n          <h1 className=\"text-4xl md:text-5xl font-black text-slate-800 tracking-tight\">\n            Building a Bridge to Japan\n          </h1>\n          <p className=\"text-lg text-teal-700 mt-2\">\n            Your structured path to JLPT N4 conversational fluency.\n          </p>\n        </header>\n\n        {/* Four-Step Method Section */}\n        <section id=\"method\" className=\"mb-12 bg-white p-6 rounded-lg shadow-lg\">\n          <h2 className=\"text-2xl font-bold text-center mb-6 text-slate-800\">The Four-Step Language Method</h2>\n          <div className=\"flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-4 text-center\">\n            <div className=\"p-4 rounded-lg bg-gray-100 w-full md:w-auto\">\n              <div className=\"text-4xl\">①</div>\n              <h3 className=\"font-bold mt-2\">Kana Mastery</h3>\n              <p className=\"text-xs\">The Alphabet</p>\n            </div>\n            <div className=\"text-4xl text-cyan-600\">→</div>\n            <div className=\"p-4 rounded-lg bg-gray-100 w-full md:w-auto\">\n              <div className=\"text-4xl\">②</div>\n              <h3 className=\"font-bold mt-2\">Grammar Core</h3>\n              <p className=\"text-xs\">The Skeleton</p>\n            </div>\n            <div className=\"text-4xl text-cyan-600\">→</div>\n            <div className=\"p-4 rounded-lg bg-gray-100 w-full md:w-auto\">\n              <div className=\"text-4xl\">③</div>\n              <h3 className=\"font-bold mt-2\">Anki Vocab</h3>\n              <p className=\"text-xs\">The Muscle</p>\n            </div>\n            <div className=\"text-4xl text-cyan-600\">→</div>\n            <div className=\"p-4 rounded-lg bg-gray-100 w-full md:w-auto\">\n              <div className=\"text-4xl\">④</div>\n              <h3 className=\"font-bold mt-2\">Immersion</h3>\n              <p className=\"text-xs\">The Real World</p>\n            </div>\n          </div>\n        </section>\n\n        {/* Anki Engine and Monthly Missions */}\n        <section className=\"mb-12 grid grid-cols-1 md:grid-cols-2 gap-8 items-center\">\n          <div className=\"bg-white p-6 rounded-lg shadow-lg\">\n            <h2 className=\"text-2xl font-bold text-center mb-4 text-slate-800\">The Anki Engine</h2>\n            <p className=\"text-sm text-center text-gray-600 mb-4\">\n              Anki is your non-negotiable daily habit for vocabulary. This is how you burn thousands of words into your long-term memory.\n            </p>\n            <div className=\"relative w-full max-w-sm mx-auto h-80\">\n              {loading ? (\n                <div className=\"flex items-center justify-center h-full\">\n                  <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-slate-800\"></div>\n                </div>\n              ) : ankiData && ankiOptions ? (\n                <Doughnut data={ankiData} options={ankiOptions as any} />\n              ) : (\n                <div className=\"flex items-center justify-center h-full text-gray-500\">\n                  Chart loading...\n                </div>\n              )}\n            </div>\n          </div>\n\n          <div className=\"bg-white p-6 rounded-lg shadow-lg\">\n            <h2 className=\"text-2xl font-bold text-center mb-4 text-slate-800\">Monthly Mission Focus</h2>\n            <ul className=\"space-y-4\">\n              <li className=\"p-3 bg-cyan-50 rounded-md border-l-4 border-teal-700\">\n                <strong className=\"text-teal-700\">Month 1: Foundation.</strong> Master Hiragana & Katakana. Complete first half of Genki I. Start daily Anki habit.\n              </li>\n              <li className=\"p-3 bg-cyan-50 rounded-md border-l-4 border-teal-700\">\n                <strong className=\"text-teal-700\">Month 2: Acceleration.</strong> Finish Genki I. Start Genki II. Aggressively build vocabulary with Anki (aim for 1000+ words).\n              </li>\n              <li className=\"p-3 bg-cyan-50 rounded-md border-l-4 border-teal-700\">\n                <strong className=\"text-teal-700\">Month 3: Application.</strong> Continue Genki II. Begin daily active listening (podcasts) and speaking practice (HelloTalk).\n              </li>\n              <li className=\"p-3 bg-cyan-50 rounded-md border-l-4 border-teal-700\">\n                <strong className=\"text-teal-700\">Month 4: Refinement.</strong> Finish Genki II. Focus heavily on conversational practice and reading simple news (NHK Easy).\n              </li>\n            </ul>\n          </div>\n        </section>\n\n        {/* Navigation */}\n        <div className=\"flex justify-between items-center\">\n          <Link\n            to=\"/japan/pillar-1\"\n            className=\"bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors\"\n          >\n            ← Previous: Physical Fitness\n          </Link>\n          <Link\n            to=\"/japan/pillar-3\"\n            className=\"bg-reedsoft-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors\"\n          >\n            Next: Practical Skills →\n          </Link>\n        </div>\n\n        <footer className=\"text-center mt-10 pt-6 border-t border-cyan-300\">\n          <p className=\"text-teal-700 font-semibold\">\n            Language is a skill built daily, not in a day.\n          </p>\n        </footer>\n      </div>\n    </div>\n  );\n};\n\nexport default Pillar2;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,QAAQ,KAAQ,iBAAiB,CAC1C,OACEC,KAAK,GAAI,CAAAC,OAAO,CAChBC,UAAU,CACVC,OAAO,CACPC,MAAM,CACNC,KAAK,KACA,UAAU,CACjB,OAASC,YAAY,KAAQ,uBAAuB,CAEpD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACAT,OAAO,CAACU,QAAQ,CAACT,UAAU,CAAEC,OAAO,CAAEC,MAAM,CAAEC,KAAK,CAAC,CAEpD,KAAM,CAAAO,OAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAEC,IAAI,CAAEC,QAAQ,CAAEC,OAAO,CAAEC,WAAW,CAAEC,OAAQ,CAAC,CAAGX,YAAY,CAAC,MAAM,CAAC,CAE9E,mBACEE,IAAA,QAAKU,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpDT,KAAA,QAAKQ,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3CT,KAAA,WAAQQ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACnCX,IAAA,QAAKU,SAAS,CAAC,oFAAoF,CAAAC,QAAA,CAAC,qBAEpG,CAAK,CAAC,cACNX,IAAA,OAAIU,SAAS,CAAC,+DAA+D,CAAAC,QAAA,CAAC,4BAE9E,CAAI,CAAC,cACLX,IAAA,MAAGU,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,yDAE1C,CAAG,CAAC,EACE,CAAC,cAGTT,KAAA,YAASU,EAAE,CAAC,QAAQ,CAACF,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eACtEX,IAAA,OAAIU,SAAS,CAAC,oDAAoD,CAAAC,QAAA,CAAC,+BAA6B,CAAI,CAAC,cACrGT,KAAA,QAAKQ,SAAS,CAAC,uGAAuG,CAAAC,QAAA,eACpHT,KAAA,QAAKQ,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DX,IAAA,QAAKU,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,QAAC,CAAK,CAAC,cACjCX,IAAA,OAAIU,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cAChDX,IAAA,MAAGU,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,cAAY,CAAG,CAAC,EACpC,CAAC,cACNX,IAAA,QAAKU,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,QAAC,CAAK,CAAC,cAC/CT,KAAA,QAAKQ,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DX,IAAA,QAAKU,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,QAAC,CAAK,CAAC,cACjCX,IAAA,OAAIU,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cAChDX,IAAA,MAAGU,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,cAAY,CAAG,CAAC,EACpC,CAAC,cACNX,IAAA,QAAKU,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,QAAC,CAAK,CAAC,cAC/CT,KAAA,QAAKQ,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DX,IAAA,QAAKU,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,QAAC,CAAK,CAAC,cACjCX,IAAA,OAAIU,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,YAAU,CAAI,CAAC,cAC9CX,IAAA,MAAGU,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,YAAU,CAAG,CAAC,EAClC,CAAC,cACNX,IAAA,QAAKU,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CAAC,QAAC,CAAK,CAAC,cAC/CT,KAAA,QAAKQ,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DX,IAAA,QAAKU,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,QAAC,CAAK,CAAC,cACjCX,IAAA,OAAIU,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,WAAS,CAAI,CAAC,cAC7CX,IAAA,MAAGU,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,gBAAc,CAAG,CAAC,EACtC,CAAC,EACH,CAAC,EACC,CAAC,cAGVT,KAAA,YAASQ,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eAC3ET,KAAA,QAAKQ,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDX,IAAA,OAAIU,SAAS,CAAC,oDAAoD,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cACvFX,IAAA,MAAGU,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,6HAEtD,CAAG,CAAC,cACJX,IAAA,QAAKU,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CACnDF,OAAO,cACNT,IAAA,QAAKU,SAAS,CAAC,yCAAyC,CAAAC,QAAA,cACtDX,IAAA,QAAKU,SAAS,CAAC,iEAAiE,CAAM,CAAC,CACpF,CAAC,CACJJ,QAAQ,EAAIE,WAAW,cACzBR,IAAA,CAACT,QAAQ,EAACc,IAAI,CAAEC,QAAS,CAACC,OAAO,CAAEC,WAAmB,CAAE,CAAC,cAEzDR,IAAA,QAAKU,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,kBAEvE,CAAK,CACN,CACE,CAAC,EACH,CAAC,cAENT,KAAA,QAAKQ,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDX,IAAA,OAAIU,SAAS,CAAC,oDAAoD,CAAAC,QAAA,CAAC,uBAAqB,CAAI,CAAC,cAC7FT,KAAA,OAAIQ,SAAS,CAAC,WAAW,CAAAC,QAAA,eACvBT,KAAA,OAAIQ,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eAClEX,IAAA,WAAQU,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,sBAAoB,CAAQ,CAAC,uFACjE,EAAI,CAAC,cACLT,KAAA,OAAIQ,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eAClEX,IAAA,WAAQU,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,wBAAsB,CAAQ,CAAC,kGACnE,EAAI,CAAC,cACLT,KAAA,OAAIQ,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eAClEX,IAAA,WAAQU,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,uBAAqB,CAAQ,CAAC,iGAClE,EAAI,CAAC,cACLT,KAAA,OAAIQ,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eAClEX,IAAA,WAAQU,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,sBAAoB,CAAQ,CAAC,iGACjE,EAAI,CAAC,EACH,CAAC,EACF,CAAC,EACC,CAAC,cAGVT,KAAA,QAAKQ,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDX,IAAA,CAACV,IAAI,EACHuB,EAAE,CAAC,iBAAiB,CACpBH,SAAS,CAAC,kGAAkG,CAAAC,QAAA,CAC7G,mCAED,CAAM,CAAC,cACPX,IAAA,CAACV,IAAI,EACHuB,EAAE,CAAC,iBAAiB,CACpBH,SAAS,CAAC,iHAAiH,CAAAC,QAAA,CAC5H,+BAED,CAAM,CAAC,EACJ,CAAC,cAENX,IAAA,WAAQU,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cACjEX,IAAA,MAAGU,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,gDAE3C,CAAG,CAAC,CACE,CAAC,EACN,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAP,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}