// Chart.js related types
export interface ChartData {
  labels: (string | string[])[];
  datasets: ChartDataset[];
}

export interface ChartDataset {
  label?: string;
  data: number[];
  backgroundColor: string | string[];
  borderColor?: string | string[];
  borderWidth?: number;
  borderRadius?: number;
  pointBackgroundColor?: string;
  pointBorderColor?: string;
  pointHoverBackgroundColor?: string;
  pointHoverBorderColor?: string;
}

export interface ChartOptions {
  responsive: boolean;
  maintainAspectRatio: boolean;
  plugins?: {
    legend?: {
      position?: 'top' | 'bottom' | 'left' | 'right';
      display?: boolean;
      labels?: {
        font?: {
          size?: number;
          weight?: string;
        };
      };
    };
    title?: {
      display: boolean;
      text: string;
      font?: {
        size?: number;
        weight?: string;
      };
      padding?: {
        top?: number;
        bottom?: number;
      };
    };
    tooltip?: {
      callbacks?: {
        title?: (tooltipItems: any[]) => string;
        label?: (context: any) => string;
      };
    };
  };
  scales?: {
    [key: string]: {
      display?: boolean;
      ticks?: {
        display?: boolean;
        font?: {
          size?: number;
          weight?: string;
        };
      };
      angleLines?: {
        color?: string;
      };
      grid?: {
        color?: string;
      };
      pointLabels?: {
        font?: {
          size?: number;
          weight?: string;
        };
      };
    };
  };
  indexAxis?: 'x' | 'y';
}

// Time format types
export interface TimeBlock {
  time: string;
  title: string;
  description: string;
  pillar: number;
}

export interface FormattedTime {
  original: string;
  formatted: string;
  period: 'AM' | 'PM';
}

// Navigation types
export interface NavItem {
  path: string;
  label: string;
  icon?: string;
}

export interface BreadcrumbItem {
  path: string;
  label: string;
}

// Pillar types
export interface PillarData {
  id: number;
  title: string;
  subtitle: string;
  description: string;
  color: string;
  icon: string;
}

// Monthly mission types
export interface MonthlyMission {
  month: number;
  title: string;
  subtitle: string;
  description: string;
}

// Component props types
export interface ChartComponentProps {
  data: ChartData;
  options?: ChartOptions;
  className?: string;
}

export interface PillarComponentProps {
  pillarData: PillarData;
  className?: string;
}

export interface NavigationProps {
  currentPath: string;
  breadcrumbs?: BreadcrumbItem[];
}

// Utility types
export type ChartType = 'doughnut' | 'bar' | 'radar' | 'pie' | 'line';

export interface ColorPalette {
  primary: string;
  secondary: string;
  accent: string;
  light: string;
  [key: string]: string;
}
