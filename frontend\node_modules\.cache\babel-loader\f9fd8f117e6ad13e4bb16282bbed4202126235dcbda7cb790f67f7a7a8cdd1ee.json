{"ast": null, "code": "// Reedsoft color palette\nexport const COLORS = {\n  primary: '#003B46',\n  secondary: '#07575B',\n  accent: '#66A5AD',\n  light: '#C4DFE6',\n  // Additional colors for charts\n  red: '#FF5733',\n  orange: '#F0AD4E',\n  blue: '#4A90E2',\n  green: '#50E3C2',\n  yellow: '#F5A623',\n  purple: '#9013FE',\n  teal: '#00BCD4',\n  pink: '#E91E63'\n};\n\n/**\n * Wraps long labels for better chart display\n * @param label - The label to wrap\n * @param maxLength - Maximum length per line\n * @returns Array of wrapped lines or original label\n */\nexport const wrapLabel = (label, maxLength = 16) => {\n  if (label.length <= maxLength) return [label];\n  const words = label.split(' ');\n  const lines = [];\n  let currentLine = '';\n  for (const word of words) {\n    if ((currentLine + ' ' + word).trim().length > maxLength) {\n      if (currentLine) {\n        lines.push(currentLine);\n        currentLine = word;\n      } else {\n        lines.push(word);\n      }\n    } else {\n      currentLine = (currentLine + ' ' + word).trim();\n    }\n  }\n  if (currentLine) {\n    lines.push(currentLine);\n  }\n  return lines;\n};\n\n/**\n * Tooltip title callback for wrapped labels\n * @param tooltipItems - Chart.js tooltip items\n * @returns Formatted title string\n */\nexport const tooltipTitleCallback = tooltipItems => {\n  const item = tooltipItems[0];\n  let label = item.chart.data.labels[item.dataIndex];\n  if (Array.isArray(label)) {\n    return label.join(' ');\n  }\n  return label;\n};\n\n/**\n * Creates default chart options with Reedsoft styling\n * @param type - Chart type\n * @param title - Chart title\n * @returns Chart options object\n */\nexport const createChartOptions = (type, title) => {\n  const baseOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'bottom',\n        labels: {\n          font: {\n            size: 12\n          }\n        }\n      },\n      tooltip: {\n        callbacks: {\n          title: tooltipTitleCallback\n        }\n      }\n    }\n  };\n  if (title) {\n    baseOptions.plugins.title = {\n      display: true,\n      text: title,\n      font: {\n        size: 16,\n        weight: 'bold'\n      },\n      padding: {\n        top: 10,\n        bottom: 20\n      }\n    };\n  }\n\n  // Type-specific configurations\n  switch (type) {\n    case 'bar':\n      return {\n        ...baseOptions,\n        scales: {\n          x: {\n            display: false\n          },\n          y: {\n            ticks: {\n              font: {\n                size: 14,\n                weight: 'bold'\n              }\n            }\n          }\n        },\n        plugins: {\n          ...baseOptions.plugins,\n          legend: {\n            display: false\n          }\n        },\n        indexAxis: 'y'\n      };\n    case 'radar':\n      return {\n        ...baseOptions,\n        scales: {\n          r: {\n            angleLines: {\n              color: '#ddd'\n            },\n            grid: {\n              color: '#ddd'\n            },\n            pointLabels: {\n              font: {\n                size: 12,\n                weight: 'bold'\n              }\n            },\n            ticks: {\n              display: false\n            }\n          }\n        },\n        plugins: {\n          ...baseOptions.plugins,\n          legend: {\n            display: false\n          }\n        }\n      };\n    default:\n      return baseOptions;\n  }\n};\n\n/**\n * Creates time allocation chart data\n * @returns Chart data for time allocation doughnut chart\n */\nexport const createTimeAllocationData = () => ({\n  labels: ['Pillar 2: Japanese Study (Voice)', 'Pillar 1: Physical Fitness (Body)', 'Pillar 3: Practical Skills (Hands)', 'Pillar 4: Cognitive Fitness (Mind)'],\n  datasets: [{\n    label: 'Weekly Hours',\n    data: [20, 7.5, 10, 7.5],\n    backgroundColor: [COLORS.primary, COLORS.secondary, COLORS.accent, COLORS.light],\n    borderColor: '#FFFFFF',\n    borderWidth: 3\n  }]\n});\n\n/**\n * Creates key lifts chart data\n * @returns Chart data for key lifts bar chart\n */\nexport const createKeyLiftsData = () => ({\n  labels: ['Squat', 'Deadlift', 'Bench Press', 'Overhead Press', 'Barbell Row'],\n  datasets: [{\n    label: 'Functional Strength Rating',\n    data: [95, 100, 85, 80, 90],\n    backgroundColor: [COLORS.red || '#FF5733', '#C70039', '#900C3F', '#581845', COLORS.blue || '#4A90E2'],\n    borderColor: '#fff',\n    borderWidth: 2,\n    borderRadius: 5\n  }]\n});\n\n/**\n * Creates Anki chart data\n * @returns Chart data for Anki doughnut chart\n */\nexport const createAnkiData = () => ({\n  labels: ['Daily Reviews', 'Learn New Words (20/day)', 'Long-Term Memory'],\n  datasets: [{\n    data: [50, 25, 25],\n    backgroundColor: [COLORS.primary, COLORS.secondary, COLORS.accent],\n    borderColor: '#fff',\n    borderWidth: 4\n  }]\n});\n\n/**\n * Creates knowledge areas radar chart data\n * @returns Chart data for knowledge areas radar chart\n */\nexport const createKnowledgeData = () => ({\n  labels: ['Hand Tools', 'Power Tools', 'Site Safety', 'Materials ID', 'Essential Skills'],\n  datasets: [{\n    label: 'Study Focus',\n    data: [8, 7, 10, 6, 9],\n    backgroundColor: 'rgba(217, 83, 79, 0.2)',\n    borderColor: '#D9534F',\n    pointBackgroundColor: '#D9534F',\n    pointBorderColor: '#fff',\n    pointHoverBackgroundColor: '#fff',\n    pointHoverBorderColor: '#D9534F',\n    borderWidth: 2\n  }]\n});\n\n/**\n * Creates mind workout pie chart data\n * @returns Chart data for mind workout pie chart\n */\nexport const createMindWorkoutData = () => ({\n  labels: ['Active Reading (45 min)', 'Problem Solving (30 min)', 'Meditation (15 min)'],\n  datasets: [{\n    data: [50, 33, 17],\n    backgroundColor: [COLORS.green || '#50E3C2', COLORS.yellow || '#F5A623', COLORS.blue || '#4A90E2'],\n    borderColor: '#fff',\n    borderWidth: 4\n  }]\n});", "map": {"version": 3, "names": ["COLORS", "primary", "secondary", "accent", "light", "red", "orange", "blue", "green", "yellow", "purple", "teal", "pink", "wrapLabel", "label", "max<PERSON><PERSON><PERSON>", "length", "words", "split", "lines", "currentLine", "word", "trim", "push", "tooltipTitleCallback", "tooltipItems", "item", "chart", "data", "labels", "dataIndex", "Array", "isArray", "join", "createChartOptions", "type", "title", "baseOptions", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "font", "size", "tooltip", "callbacks", "display", "text", "weight", "padding", "top", "bottom", "scales", "x", "y", "ticks", "indexAxis", "r", "angleLines", "color", "grid", "point<PERSON><PERSON><PERSON>", "createTimeAllocationData", "datasets", "backgroundColor", "borderColor", "borderWidth", "createKeyLiftsData", "borderRadius", "createAnkiData", "createKnowledgeData", "pointBackgroundColor", "pointBorderColor", "pointHoverBackgroundColor", "pointHoverBorderColor", "createMindWorkoutData"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/utils/chartConfig.ts"], "sourcesContent": ["import { ChartData, ChartOptions, ColorPalette } from '../types';\n\n// Reedsoft color palette\nexport const COLORS: ColorPalette = {\n  primary: '#003B46',\n  secondary: '#07575B',\n  accent: '#66A5AD',\n  light: '#C4DFE6',\n  // Additional colors for charts\n  red: '#FF5733',\n  orange: '#F0AD4E',\n  blue: '#4A90E2',\n  green: '#50E3C2',\n  yellow: '#F5A623',\n  purple: '#9013FE',\n  teal: '#00BCD4',\n  pink: '#E91E63'\n};\n\n/**\n * Wraps long labels for better chart display\n * @param label - The label to wrap\n * @param maxLength - Maximum length per line\n * @returns Array of wrapped lines or original label\n */\nexport const wrapLabel = (label: string, maxLength: number = 16): string[] => {\n  if (label.length <= maxLength) return [label];\n  \n  const words = label.split(' ');\n  const lines: string[] = [];\n  let currentLine = '';\n  \n  for (const word of words) {\n    if ((currentLine + ' ' + word).trim().length > maxLength) {\n      if (currentLine) {\n        lines.push(currentLine);\n        currentLine = word;\n      } else {\n        lines.push(word);\n      }\n    } else {\n      currentLine = (currentLine + ' ' + word).trim();\n    }\n  }\n  \n  if (currentLine) {\n    lines.push(currentLine);\n  }\n  \n  return lines;\n};\n\n/**\n * Tooltip title callback for wrapped labels\n * @param tooltipItems - Chart.js tooltip items\n * @returns Formatted title string\n */\nexport const tooltipTitleCallback = (tooltipItems: any[]): string => {\n  const item = tooltipItems[0];\n  let label = item.chart.data.labels[item.dataIndex];\n  \n  if (Array.isArray(label)) {\n    return label.join(' ');\n  }\n  \n  return label;\n};\n\n/**\n * Creates default chart options with Reedsoft styling\n * @param type - Chart type\n * @param title - Chart title\n * @returns Chart options object\n */\nexport const createChartOptions = (\n  type: 'doughnut' | 'bar' | 'radar' | 'pie' | 'line',\n  title?: string\n): ChartOptions => {\n  const baseOptions: ChartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'bottom',\n        labels: {\n          font: { size: 12 }\n        }\n      },\n      tooltip: {\n        callbacks: {\n          title: tooltipTitleCallback\n        }\n      }\n    }\n  };\n\n  if (title) {\n    baseOptions.plugins!.title = {\n      display: true,\n      text: title,\n      font: { size: 16, weight: 'bold' },\n      padding: { top: 10, bottom: 20 }\n    };\n  }\n\n  // Type-specific configurations\n  switch (type) {\n    case 'bar':\n      return {\n        ...baseOptions,\n        scales: {\n          x: { display: false },\n          y: { \n            ticks: { \n              font: { size: 14, weight: 'bold' } \n            } \n          }\n        },\n        plugins: {\n          ...baseOptions.plugins,\n          legend: { display: false }\n        },\n        indexAxis: 'y'\n      };\n\n    case 'radar':\n      return {\n        ...baseOptions,\n        scales: {\n          r: {\n            angleLines: { color: '#ddd' },\n            grid: { color: '#ddd' },\n            pointLabels: { \n              font: { size: 12, weight: 'bold' } \n            },\n            ticks: { display: false }\n          }\n        },\n        plugins: {\n          ...baseOptions.plugins,\n          legend: { display: false }\n        }\n      };\n\n    default:\n      return baseOptions;\n  }\n};\n\n/**\n * Creates time allocation chart data\n * @returns Chart data for time allocation doughnut chart\n */\nexport const createTimeAllocationData = (): ChartData => ({\n  labels: [\n    'Pillar 2: Japanese Study (Voice)',\n    'Pillar 1: Physical Fitness (Body)',\n    'Pillar 3: Practical Skills (Hands)',\n    'Pillar 4: Cognitive Fitness (Mind)'\n  ],\n  datasets: [{\n    label: 'Weekly Hours',\n    data: [20, 7.5, 10, 7.5],\n    backgroundColor: [\n      COLORS.primary,\n      COLORS.secondary,\n      COLORS.accent,\n      COLORS.light\n    ],\n    borderColor: '#FFFFFF',\n    borderWidth: 3\n  }]\n});\n\n/**\n * Creates key lifts chart data\n * @returns Chart data for key lifts bar chart\n */\nexport const createKeyLiftsData = (): ChartData => ({\n  labels: ['Squat', 'Deadlift', 'Bench Press', 'Overhead Press', 'Barbell Row'],\n  datasets: [{\n    label: 'Functional Strength Rating',\n    data: [95, 100, 85, 80, 90],\n    backgroundColor: [COLORS.red || '#FF5733', '#C70039', '#900C3F', '#581845', COLORS.blue || '#4A90E2'],\n    borderColor: '#fff',\n    borderWidth: 2,\n    borderRadius: 5\n  }]\n});\n\n/**\n * Creates Anki chart data\n * @returns Chart data for Anki doughnut chart\n */\nexport const createAnkiData = (): ChartData => ({\n  labels: ['Daily Reviews', 'Learn New Words (20/day)', 'Long-Term Memory'],\n  datasets: [{\n    data: [50, 25, 25],\n    backgroundColor: [COLORS.primary, COLORS.secondary, COLORS.accent],\n    borderColor: '#fff',\n    borderWidth: 4\n  }]\n});\n\n/**\n * Creates knowledge areas radar chart data\n * @returns Chart data for knowledge areas radar chart\n */\nexport const createKnowledgeData = (): ChartData => ({\n  labels: ['Hand Tools', 'Power Tools', 'Site Safety', 'Materials ID', 'Essential Skills'],\n  datasets: [{\n    label: 'Study Focus',\n    data: [8, 7, 10, 6, 9],\n    backgroundColor: 'rgba(217, 83, 79, 0.2)',\n    borderColor: '#D9534F',\n    pointBackgroundColor: '#D9534F',\n    pointBorderColor: '#fff',\n    pointHoverBackgroundColor: '#fff',\n    pointHoverBorderColor: '#D9534F',\n    borderWidth: 2\n  }]\n});\n\n/**\n * Creates mind workout pie chart data\n * @returns Chart data for mind workout pie chart\n */\nexport const createMindWorkoutData = (): ChartData => ({\n  labels: ['Active Reading (45 min)', 'Problem Solving (30 min)', 'Meditation (15 min)'],\n  datasets: [{\n    data: [50, 33, 17],\n    backgroundColor: [COLORS.green || '#50E3C2', COLORS.yellow || '#F5A623', COLORS.blue || '#4A90E2'],\n    borderColor: '#fff',\n    borderWidth: 4\n  }]\n});\n"], "mappings": "AAEA;AACA,OAAO,MAAMA,MAAoB,GAAG;EAClCC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,MAAM,EAAE,SAAS;EACjBC,KAAK,EAAE,SAAS;EAChB;EACAC,GAAG,EAAE,SAAS;EACdC,MAAM,EAAE,SAAS;EACjBC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,SAAS;EAChBC,MAAM,EAAE,SAAS;EACjBC,MAAM,EAAE,SAAS;EACjBC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE;AACR,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,SAAS,GAAGA,CAACC,KAAa,EAAEC,SAAiB,GAAG,EAAE,KAAe;EAC5E,IAAID,KAAK,CAACE,MAAM,IAAID,SAAS,EAAE,OAAO,CAACD,KAAK,CAAC;EAE7C,MAAMG,KAAK,GAAGH,KAAK,CAACI,KAAK,CAAC,GAAG,CAAC;EAC9B,MAAMC,KAAe,GAAG,EAAE;EAC1B,IAAIC,WAAW,GAAG,EAAE;EAEpB,KAAK,MAAMC,IAAI,IAAIJ,KAAK,EAAE;IACxB,IAAI,CAACG,WAAW,GAAG,GAAG,GAAGC,IAAI,EAAEC,IAAI,CAAC,CAAC,CAACN,MAAM,GAAGD,SAAS,EAAE;MACxD,IAAIK,WAAW,EAAE;QACfD,KAAK,CAACI,IAAI,CAACH,WAAW,CAAC;QACvBA,WAAW,GAAGC,IAAI;MACpB,CAAC,MAAM;QACLF,KAAK,CAACI,IAAI,CAACF,IAAI,CAAC;MAClB;IACF,CAAC,MAAM;MACLD,WAAW,GAAG,CAACA,WAAW,GAAG,GAAG,GAAGC,IAAI,EAAEC,IAAI,CAAC,CAAC;IACjD;EACF;EAEA,IAAIF,WAAW,EAAE;IACfD,KAAK,CAACI,IAAI,CAACH,WAAW,CAAC;EACzB;EAEA,OAAOD,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,oBAAoB,GAAIC,YAAmB,IAAa;EACnE,MAAMC,IAAI,GAAGD,YAAY,CAAC,CAAC,CAAC;EAC5B,IAAIX,KAAK,GAAGY,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,MAAM,CAACH,IAAI,CAACI,SAAS,CAAC;EAElD,IAAIC,KAAK,CAACC,OAAO,CAAClB,KAAK,CAAC,EAAE;IACxB,OAAOA,KAAK,CAACmB,IAAI,CAAC,GAAG,CAAC;EACxB;EAEA,OAAOnB,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMoB,kBAAkB,GAAGA,CAChCC,IAAmD,EACnDC,KAAc,KACG;EACjB,MAAMC,WAAyB,GAAG;IAChCC,UAAU,EAAE,IAAI;IAChBC,mBAAmB,EAAE,KAAK;IAC1BC,OAAO,EAAE;MACPC,MAAM,EAAE;QACNC,QAAQ,EAAE,QAAQ;QAClBb,MAAM,EAAE;UACNc,IAAI,EAAE;YAAEC,IAAI,EAAE;UAAG;QACnB;MACF,CAAC;MACDC,OAAO,EAAE;QACPC,SAAS,EAAE;UACTV,KAAK,EAAEZ;QACT;MACF;IACF;EACF,CAAC;EAED,IAAIY,KAAK,EAAE;IACTC,WAAW,CAACG,OAAO,CAAEJ,KAAK,GAAG;MAC3BW,OAAO,EAAE,IAAI;MACbC,IAAI,EAAEZ,KAAK;MACXO,IAAI,EAAE;QAAEC,IAAI,EAAE,EAAE;QAAEK,MAAM,EAAE;MAAO,CAAC;MAClCC,OAAO,EAAE;QAAEC,GAAG,EAAE,EAAE;QAAEC,MAAM,EAAE;MAAG;IACjC,CAAC;EACH;;EAEA;EACA,QAAQjB,IAAI;IACV,KAAK,KAAK;MACR,OAAO;QACL,GAAGE,WAAW;QACdgB,MAAM,EAAE;UACNC,CAAC,EAAE;YAAEP,OAAO,EAAE;UAAM,CAAC;UACrBQ,CAAC,EAAE;YACDC,KAAK,EAAE;cACLb,IAAI,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEK,MAAM,EAAE;cAAO;YACnC;UACF;QACF,CAAC;QACDT,OAAO,EAAE;UACP,GAAGH,WAAW,CAACG,OAAO;UACtBC,MAAM,EAAE;YAAEM,OAAO,EAAE;UAAM;QAC3B,CAAC;QACDU,SAAS,EAAE;MACb,CAAC;IAEH,KAAK,OAAO;MACV,OAAO;QACL,GAAGpB,WAAW;QACdgB,MAAM,EAAE;UACNK,CAAC,EAAE;YACDC,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAO,CAAC;YAC7BC,IAAI,EAAE;cAAED,KAAK,EAAE;YAAO,CAAC;YACvBE,WAAW,EAAE;cACXnB,IAAI,EAAE;gBAAEC,IAAI,EAAE,EAAE;gBAAEK,MAAM,EAAE;cAAO;YACnC,CAAC;YACDO,KAAK,EAAE;cAAET,OAAO,EAAE;YAAM;UAC1B;QACF,CAAC;QACDP,OAAO,EAAE;UACP,GAAGH,WAAW,CAACG,OAAO;UACtBC,MAAM,EAAE;YAAEM,OAAO,EAAE;UAAM;QAC3B;MACF,CAAC;IAEH;MACE,OAAOV,WAAW;EACtB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAM0B,wBAAwB,GAAGA,CAAA,MAAkB;EACxDlC,MAAM,EAAE,CACN,kCAAkC,EAClC,mCAAmC,EACnC,oCAAoC,EACpC,oCAAoC,CACrC;EACDmC,QAAQ,EAAE,CAAC;IACTlD,KAAK,EAAE,cAAc;IACrBc,IAAI,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC;IACxBqC,eAAe,EAAE,CACfjE,MAAM,CAACC,OAAO,EACdD,MAAM,CAACE,SAAS,EAChBF,MAAM,CAACG,MAAM,EACbH,MAAM,CAACI,KAAK,CACb;IACD8D,WAAW,EAAE,SAAS;IACtBC,WAAW,EAAE;EACf,CAAC;AACH,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA,OAAO,MAAMC,kBAAkB,GAAGA,CAAA,MAAkB;EAClDvC,MAAM,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,gBAAgB,EAAE,aAAa,CAAC;EAC7EmC,QAAQ,EAAE,CAAC;IACTlD,KAAK,EAAE,4BAA4B;IACnCc,IAAI,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC3BqC,eAAe,EAAE,CAACjE,MAAM,CAACK,GAAG,IAAI,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAEL,MAAM,CAACO,IAAI,IAAI,SAAS,CAAC;IACrG2D,WAAW,EAAE,MAAM;IACnBC,WAAW,EAAE,CAAC;IACdE,YAAY,EAAE;EAChB,CAAC;AACH,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA,OAAO,MAAMC,cAAc,GAAGA,CAAA,MAAkB;EAC9CzC,MAAM,EAAE,CAAC,eAAe,EAAE,0BAA0B,EAAE,kBAAkB,CAAC;EACzEmC,QAAQ,EAAE,CAAC;IACTpC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAClBqC,eAAe,EAAE,CAACjE,MAAM,CAACC,OAAO,EAAED,MAAM,CAACE,SAAS,EAAEF,MAAM,CAACG,MAAM,CAAC;IAClE+D,WAAW,EAAE,MAAM;IACnBC,WAAW,EAAE;EACf,CAAC;AACH,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA,OAAO,MAAMI,mBAAmB,GAAGA,CAAA,MAAkB;EACnD1C,MAAM,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,aAAa,EAAE,cAAc,EAAE,kBAAkB,CAAC;EACxFmC,QAAQ,EAAE,CAAC;IACTlD,KAAK,EAAE,aAAa;IACpBc,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;IACtBqC,eAAe,EAAE,wBAAwB;IACzCC,WAAW,EAAE,SAAS;IACtBM,oBAAoB,EAAE,SAAS;IAC/BC,gBAAgB,EAAE,MAAM;IACxBC,yBAAyB,EAAE,MAAM;IACjCC,qBAAqB,EAAE,SAAS;IAChCR,WAAW,EAAE;EACf,CAAC;AACH,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA,OAAO,MAAMS,qBAAqB,GAAGA,CAAA,MAAkB;EACrD/C,MAAM,EAAE,CAAC,yBAAyB,EAAE,0BAA0B,EAAE,qBAAqB,CAAC;EACtFmC,QAAQ,EAAE,CAAC;IACTpC,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAClBqC,eAAe,EAAE,CAACjE,MAAM,CAACQ,KAAK,IAAI,SAAS,EAAER,MAAM,CAACS,MAAM,IAAI,SAAS,EAAET,MAAM,CAACO,IAAI,IAAI,SAAS,CAAC;IAClG2D,WAAW,EAAE,MAAM;IACnBC,WAAW,EAAE;EACf,CAAC;AACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}