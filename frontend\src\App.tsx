// React import not needed with new JSX transform
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import './App.css';

// Pages
import ReedsoftLanding from './pages/ReedsoftLanding';
import JapanPlan from './pages/JapanPlan';
import Pillar1 from './pages/Pillar1';
import Pillar2 from './pages/Pillar2';
import Pillar3 from './pages/Pillar3';
import Pillar4 from './pages/Pillar4';

// Components
import Navigation from './components/Navigation';
import ErrorBoundary from './components/ErrorBoundary';

/**
 * Main App component that sets up routing and global layout
 * Features:
 * - Error boundary for production resilience
 * - Skip navigation link for accessibility
 * - React Router for client-side navigation
 * - Consistent navigation across all pages
 */
function App() {
  return (
    <ErrorBoundary>
      <Router>
        <div className="App min-h-screen bg-gray-100">
          {/* Skip Navigation Link */}
          <a
            href="#main-content"
            className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-reedsoft-primary text-white px-4 py-2 rounded-md z-50 focus:outline-none focus:ring-2 focus:ring-white"
          >
            Skip to main content
          </a>

          <Navigation />
          <main id="main-content" className="container mx-auto" role="main">
            <Routes>
              <Route path="/" element={<ReedsoftLanding />} />
              <Route path="/japan" element={<JapanPlan />} />
              <Route path="/japan/pillar-1" element={<Pillar1 />} />
              <Route path="/japan/pillar-2" element={<Pillar2 />} />
              <Route path="/japan/pillar-3" element={<Pillar3 />} />
              <Route path="/japan/pillar-4" element={<Pillar4 />} />
            </Routes>
          </main>
        </div>
      </Router>
    </ErrorBoundary>
  );
}

export default App;
