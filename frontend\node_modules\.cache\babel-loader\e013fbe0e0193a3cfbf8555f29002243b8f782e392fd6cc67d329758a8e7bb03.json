{"ast": null, "code": "var _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { createTimeAllocationData, createKeyLiftsData, createAnkiData, createKnowledgeData, createMindWorkoutData, createChartOptions } from '../utils/chartConfig';\n/**\n * Custom hook for managing chart data and options\n * @param chartType - Type of chart to create\n * @param title - Optional chart title\n * @returns Object with chart data, options, and loading state\n */\nexport const useChartData = (chartType, title) => {\n  _s();\n  const [data, setData] = useState(null);\n  const [options, setOptions] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    const loadChartData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n        let chartData;\n        let chartOptions;\n        switch (chartType) {\n          case 'timeAllocation':\n            chartData = createTimeAllocationData();\n            chartOptions = createChartOptions('doughnut', title || 'Weekly Hour Distribution');\n            break;\n          case 'keyLifts':\n            chartData = createKeyLiftsData();\n            chartOptions = createChartOptions('bar', title);\n            // Add custom tooltip for key lifts\n            chartOptions.plugins.tooltip.callbacks.label = function (context) {\n              const lift = context.label;\n              let muscles;\n              switch (lift) {\n                case 'Squat':\n                  muscles = 'Legs, Glutes, Core';\n                  break;\n                case 'Deadlift':\n                  muscles = 'Entire Posterior Chain';\n                  break;\n                case 'Bench Press':\n                  muscles = 'Chest, Shoulders, Triceps';\n                  break;\n                case 'Overhead Press':\n                  muscles = 'Shoulders, Triceps, Core';\n                  break;\n                case 'Barbell Row':\n                  muscles = 'Back, Biceps';\n                  break;\n                default:\n                  muscles = '';\n              }\n              return ` Works: ${muscles}`;\n            };\n            break;\n          case 'anki':\n            chartData = createAnkiData();\n            chartOptions = createChartOptions('doughnut', title || 'The Daily SRS Cycle');\n            break;\n          case 'knowledge':\n            chartData = createKnowledgeData();\n            chartOptions = createChartOptions('radar', title);\n            break;\n          case 'mindWorkout':\n            chartData = createMindWorkoutData();\n            chartOptions = createChartOptions('pie', title || '90-Minute Focus Block');\n            break;\n          default:\n            throw new Error(`Unknown chart type: ${chartType}`);\n        }\n        setData(chartData);\n        setOptions(chartOptions);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'Failed to load chart data');\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadChartData();\n  }, [chartType, title]);\n  return {\n    data,\n    options,\n    loading,\n    error\n  };\n};\n\n/**\n * Custom hook for creating custom chart data\n * @param labels - Chart labels\n * @param datasets - Chart datasets\n * @param chartType - Type of chart\n * @param title - Optional chart title\n * @returns Object with chart data, options, and utility functions\n */\n_s(useChartData, \"tMrnpwmhWHjpnntIRO7TyR34yss=\");\nexport const useCustomChartData = (labels, datasets, chartType, title) => {\n  _s2();\n  const [data, setData] = useState({\n    labels,\n    datasets\n  });\n  const [options, setOptions] = useState(createChartOptions(chartType, title));\n  const updateData = (newLabels, newDatasets) => {\n    setData({\n      labels: newLabels,\n      datasets: newDatasets\n    });\n  };\n  const updateOptions = newOptions => {\n    setOptions(prev => ({\n      ...prev,\n      ...newOptions\n    }));\n  };\n  useEffect(() => {\n    setData({\n      labels,\n      datasets\n    });\n  }, [labels, datasets]);\n  useEffect(() => {\n    setOptions(createChartOptions(chartType, title));\n  }, [chartType, title]);\n  return {\n    data,\n    options,\n    updateData,\n    updateOptions\n  };\n};\n_s2(useCustomChartData, \"JjJb+uuc4b5GO1I6wQPBVcWnFm4=\");", "map": {"version": 3, "names": ["useState", "useEffect", "createTimeAllocationData", "createKeyLiftsData", "createAnkiData", "createKnowledgeData", "createMindWorkoutData", "createChartOptions", "useChartData", "chartType", "title", "_s", "data", "setData", "options", "setOptions", "loading", "setLoading", "error", "setError", "loadChartData", "chartData", "chartOptions", "plugins", "tooltip", "callbacks", "label", "context", "lift", "muscles", "Error", "err", "message", "useCustomChartData", "labels", "datasets", "_s2", "updateData", "<PERSON><PERSON><PERSON><PERSON>", "newDatasets", "updateOptions", "newOptions", "prev"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/hooks/useChartData.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { ChartData, ChartOptions } from '../types';\nimport {\n  createTimeAllocationData,\n  createKeyLiftsData,\n  createAnkiData,\n  createKnowledgeData,\n  createMindWorkoutData,\n  createChartOptions\n} from '../utils/chartConfig';\n\nexport type ChartType = 'timeAllocation' | 'keyLifts' | 'anki' | 'knowledge' | 'mindWorkout';\n\n/**\n * Custom hook for managing chart data and options\n * @param chartType - Type of chart to create\n * @param title - Optional chart title\n * @returns Object with chart data, options, and loading state\n */\nexport const useChartData = (chartType: ChartType, title?: string) => {\n  const [data, setData] = useState<ChartData | null>(null);\n  const [options, setOptions] = useState<ChartOptions | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const loadChartData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        let chartData: ChartData;\n        let chartOptions: ChartOptions;\n\n        switch (chartType) {\n          case 'timeAllocation':\n            chartData = createTimeAllocationData();\n            chartOptions = createChartOptions('doughnut', title || 'Weekly Hour Distribution');\n            break;\n\n          case 'keyLifts':\n            chartData = createKeyLiftsData();\n            chartOptions = createChartOptions('bar', title);\n            // Add custom tooltip for key lifts\n            chartOptions.plugins!.tooltip!.callbacks!.label = function(context: any) {\n              const lift = context.label;\n              let muscles: string;\n              switch(lift) {\n                case 'Squat': muscles = 'Legs, Glutes, Core'; break;\n                case 'Deadlift': muscles = 'Entire Posterior Chain'; break;\n                case 'Bench Press': muscles = 'Chest, Shoulders, Triceps'; break;\n                case 'Overhead Press': muscles = 'Shoulders, Triceps, Core'; break;\n                case 'Barbell Row': muscles = 'Back, Biceps'; break;\n                default: muscles = '';\n              }\n              return ` Works: ${muscles}`;\n            };\n            break;\n\n          case 'anki':\n            chartData = createAnkiData();\n            chartOptions = createChartOptions('doughnut', title || 'The Daily SRS Cycle');\n            break;\n\n          case 'knowledge':\n            chartData = createKnowledgeData();\n            chartOptions = createChartOptions('radar', title);\n            break;\n\n          case 'mindWorkout':\n            chartData = createMindWorkoutData();\n            chartOptions = createChartOptions('pie', title || '90-Minute Focus Block');\n            break;\n\n          default:\n            throw new Error(`Unknown chart type: ${chartType}`);\n        }\n\n        setData(chartData);\n        setOptions(chartOptions);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'Failed to load chart data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadChartData();\n  }, [chartType, title]);\n\n  return { data, options, loading, error };\n};\n\n/**\n * Custom hook for creating custom chart data\n * @param labels - Chart labels\n * @param datasets - Chart datasets\n * @param chartType - Type of chart\n * @param title - Optional chart title\n * @returns Object with chart data, options, and utility functions\n */\nexport const useCustomChartData = (\n  labels: string[],\n  datasets: any[],\n  chartType: 'doughnut' | 'bar' | 'radar' | 'pie' | 'line',\n  title?: string\n) => {\n  const [data, setData] = useState<ChartData>({ labels, datasets });\n  const [options, setOptions] = useState<ChartOptions>(createChartOptions(chartType, title));\n\n  const updateData = (newLabels: string[], newDatasets: any[]) => {\n    setData({ labels: newLabels, datasets: newDatasets });\n  };\n\n  const updateOptions = (newOptions: Partial<ChartOptions>) => {\n    setOptions(prev => ({ ...prev, ...newOptions }));\n  };\n\n  useEffect(() => {\n    setData({ labels, datasets });\n  }, [labels, datasets]);\n\n  useEffect(() => {\n    setOptions(createChartOptions(chartType, title));\n  }, [chartType, title]);\n\n  return { data, options, updateData, updateOptions };\n};\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAE3C,SACEC,wBAAwB,EACxBC,kBAAkB,EAClBC,cAAc,EACdC,mBAAmB,EACnBC,qBAAqB,EACrBC,kBAAkB,QACb,sBAAsB;AAI7B;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,YAAY,GAAGA,CAACC,SAAoB,EAAEC,KAAc,KAAK;EAAAC,EAAA;EACpE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAmB,IAAI,CAAC;EACxD,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAsB,IAAI,CAAC;EACjE,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,MAAMmB,aAAa,GAAG,MAAAA,CAAA,KAAY;MAChC,IAAI;QACFH,UAAU,CAAC,IAAI,CAAC;QAChBE,QAAQ,CAAC,IAAI,CAAC;QAEd,IAAIE,SAAoB;QACxB,IAAIC,YAA0B;QAE9B,QAAQb,SAAS;UACf,KAAK,gBAAgB;YACnBY,SAAS,GAAGnB,wBAAwB,CAAC,CAAC;YACtCoB,YAAY,GAAGf,kBAAkB,CAAC,UAAU,EAAEG,KAAK,IAAI,0BAA0B,CAAC;YAClF;UAEF,KAAK,UAAU;YACbW,SAAS,GAAGlB,kBAAkB,CAAC,CAAC;YAChCmB,YAAY,GAAGf,kBAAkB,CAAC,KAAK,EAAEG,KAAK,CAAC;YAC/C;YACAY,YAAY,CAACC,OAAO,CAAEC,OAAO,CAAEC,SAAS,CAAEC,KAAK,GAAG,UAASC,OAAY,EAAE;cACvE,MAAMC,IAAI,GAAGD,OAAO,CAACD,KAAK;cAC1B,IAAIG,OAAe;cACnB,QAAOD,IAAI;gBACT,KAAK,OAAO;kBAAEC,OAAO,GAAG,oBAAoB;kBAAE;gBAC9C,KAAK,UAAU;kBAAEA,OAAO,GAAG,wBAAwB;kBAAE;gBACrD,KAAK,aAAa;kBAAEA,OAAO,GAAG,2BAA2B;kBAAE;gBAC3D,KAAK,gBAAgB;kBAAEA,OAAO,GAAG,0BAA0B;kBAAE;gBAC7D,KAAK,aAAa;kBAAEA,OAAO,GAAG,cAAc;kBAAE;gBAC9C;kBAASA,OAAO,GAAG,EAAE;cACvB;cACA,OAAO,WAAWA,OAAO,EAAE;YAC7B,CAAC;YACD;UAEF,KAAK,MAAM;YACTR,SAAS,GAAGjB,cAAc,CAAC,CAAC;YAC5BkB,YAAY,GAAGf,kBAAkB,CAAC,UAAU,EAAEG,KAAK,IAAI,qBAAqB,CAAC;YAC7E;UAEF,KAAK,WAAW;YACdW,SAAS,GAAGhB,mBAAmB,CAAC,CAAC;YACjCiB,YAAY,GAAGf,kBAAkB,CAAC,OAAO,EAAEG,KAAK,CAAC;YACjD;UAEF,KAAK,aAAa;YAChBW,SAAS,GAAGf,qBAAqB,CAAC,CAAC;YACnCgB,YAAY,GAAGf,kBAAkB,CAAC,KAAK,EAAEG,KAAK,IAAI,uBAAuB,CAAC;YAC1E;UAEF;YACE,MAAM,IAAIoB,KAAK,CAAC,uBAAuBrB,SAAS,EAAE,CAAC;QACvD;QAEAI,OAAO,CAACQ,SAAS,CAAC;QAClBN,UAAU,CAACO,YAAY,CAAC;MAC1B,CAAC,CAAC,OAAOS,GAAG,EAAE;QACZZ,QAAQ,CAACY,GAAG,YAAYD,KAAK,GAAGC,GAAG,CAACC,OAAO,GAAG,2BAA2B,CAAC;MAC5E,CAAC,SAAS;QACRf,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACX,SAAS,EAAEC,KAAK,CAAC,CAAC;EAEtB,OAAO;IAAEE,IAAI;IAAEE,OAAO;IAAEE,OAAO;IAAEE;EAAM,CAAC;AAC1C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPAP,EAAA,CA1EaH,YAAY;AAkFzB,OAAO,MAAMyB,kBAAkB,GAAGA,CAChCC,MAAgB,EAChBC,QAAe,EACf1B,SAAwD,EACxDC,KAAc,KACX;EAAA0B,GAAA;EACH,MAAM,CAACxB,IAAI,EAAEC,OAAO,CAAC,GAAGb,QAAQ,CAAY;IAAEkC,MAAM;IAAEC;EAAS,CAAC,CAAC;EACjE,MAAM,CAACrB,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAeO,kBAAkB,CAACE,SAAS,EAAEC,KAAK,CAAC,CAAC;EAE1F,MAAM2B,UAAU,GAAGA,CAACC,SAAmB,EAAEC,WAAkB,KAAK;IAC9D1B,OAAO,CAAC;MAAEqB,MAAM,EAAEI,SAAS;MAAEH,QAAQ,EAAEI;IAAY,CAAC,CAAC;EACvD,CAAC;EAED,MAAMC,aAAa,GAAIC,UAAiC,IAAK;IAC3D1B,UAAU,CAAC2B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,GAAGD;IAAW,CAAC,CAAC,CAAC;EAClD,CAAC;EAEDxC,SAAS,CAAC,MAAM;IACdY,OAAO,CAAC;MAAEqB,MAAM;MAAEC;IAAS,CAAC,CAAC;EAC/B,CAAC,EAAE,CAACD,MAAM,EAAEC,QAAQ,CAAC,CAAC;EAEtBlC,SAAS,CAAC,MAAM;IACdc,UAAU,CAACR,kBAAkB,CAACE,SAAS,EAAEC,KAAK,CAAC,CAAC;EAClD,CAAC,EAAE,CAACD,SAAS,EAAEC,KAAK,CAAC,CAAC;EAEtB,OAAO;IAAEE,IAAI;IAAEE,OAAO;IAAEuB,UAAU;IAAEG;EAAc,CAAC;AACrD,CAAC;AAACJ,GAAA,CA1BWH,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}