{"ast": null, "code": "var _jsxFileName = \"C:\\\\Developer\\\\Web Development\\\\reedsoft\\\\frontend\\\\src\\\\components\\\\Navigation.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navigation = () => {\n  _s();\n  const location = useLocation();\n  const navItems = [{\n    path: '/',\n    label: 'Home'\n  }, {\n    path: '/japan',\n    label: 'Japan Plan'\n  }];\n  const getBreadcrumbs = pathname => {\n    const breadcrumbs = [{\n      path: '/',\n      label: 'Reedsoft'\n    }];\n    if (pathname.startsWith('/japan')) {\n      breadcrumbs.push({\n        path: '/japan',\n        label: 'Japan Plan'\n      });\n      if (pathname === '/japan/pillar-1') {\n        breadcrumbs.push({\n          path: '/japan/pillar-1',\n          label: 'Physical Fitness'\n        });\n      } else if (pathname === '/japan/pillar-2') {\n        breadcrumbs.push({\n          path: '/japan/pillar-2',\n          label: 'Japanese Language'\n        });\n      } else if (pathname === '/japan/pillar-3') {\n        breadcrumbs.push({\n          path: '/japan/pillar-3',\n          label: 'Practical Skills'\n        });\n      } else if (pathname === '/japan/pillar-4') {\n        breadcrumbs.push({\n          path: '/japan/pillar-4',\n          label: 'Cognitive Fitness'\n        });\n      }\n    }\n    return breadcrumbs;\n  };\n  const breadcrumbs = getBreadcrumbs(location.pathname);\n  const isHomePage = location.pathname === '/';\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"bg-white shadow-lg border-b border-gray-200\",\n    role: \"navigation\",\n    \"aria-label\": \"Main navigation\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-8\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"text-2xl font-black text-reedsoft-primary hover:text-reedsoft-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-reedsoft-primary focus:ring-offset-2 rounded-md\",\n            \"aria-label\": \"Reedsoft homepage\",\n            children: \"REEDSOFT\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex space-x-6\",\n            role: \"menubar\",\n            children: navItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n              to: item.path,\n              role: \"menuitem\",\n              \"aria-current\": location.pathname === item.path ? 'page' : undefined,\n              className: `px-3 py-2 rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-reedsoft-primary focus:ring-offset-2 ${location.pathname === item.path ? 'text-reedsoft-primary bg-reedsoft-light' : 'text-gray-600 hover:text-reedsoft-primary hover:bg-gray-100'}`,\n              children: item.label\n            }, item.path, false, {\n              fileName: _jsxFileName,\n              lineNumber: 54,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"text-gray-600 hover:text-reedsoft-primary focus:outline-none focus:ring-2 focus:ring-reedsoft-primary focus:ring-offset-2 rounded-md p-2\",\n            \"aria-label\": \"Open mobile menu\",\n            \"aria-expanded\": \"false\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              \"aria-hidden\": \"true\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 6h16M4 12h16M4 18h16\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), !isHomePage && breadcrumbs.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"py-3 border-t border-gray-100\",\n        children: /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"flex\",\n          \"aria-label\": \"Breadcrumb navigation\",\n          children: /*#__PURE__*/_jsxDEV(\"ol\", {\n            className: \"flex items-center space-x-2\",\n            role: \"list\",\n            children: breadcrumbs.map((crumb, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center\",\n              role: \"listitem\",\n              children: [index > 0 && /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"flex-shrink-0 h-4 w-4 text-gray-400 mx-2\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                \"aria-hidden\": \"true\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 99,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 93,\n                columnNumber: 23\n              }, this), index === breadcrumbs.length - 1 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-reedsoft-primary\",\n                \"aria-current\": \"page\",\n                children: crumb.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(Link, {\n                to: crumb.path,\n                className: \"text-sm font-medium text-gray-500 hover:text-reedsoft-primary transition-colors focus:outline-none focus:ring-2 focus:ring-reedsoft-primary focus:ring-offset-2 rounded-md\",\n                \"aria-label\": `Navigate to ${crumb.label}`,\n                children: crumb.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 23\n              }, this)]\n            }, crumb.path, true, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(Navigation, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = Navigation;\nexport default Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");", "map": {"version": 3, "names": ["React", "Link", "useLocation", "jsxDEV", "_jsxDEV", "Navigation", "_s", "location", "navItems", "path", "label", "getBreadcrumbs", "pathname", "breadcrumbs", "startsWith", "push", "isHomePage", "className", "role", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "undefined", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "length", "crumb", "index", "fillRule", "clipRule", "_c", "$RefreshReg$"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/components/Navigation.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { NavItem, BreadcrumbItem } from '../types';\n\nconst Navigation: React.FC = () => {\n  const location = useLocation();\n\n  const navItems: NavItem[] = [\n    { path: '/', label: 'Home' },\n    { path: '/japan', label: 'Japan Plan' },\n  ];\n\n  const getBreadcrumbs = (pathname: string): BreadcrumbItem[] => {\n    const breadcrumbs: BreadcrumbItem[] = [\n      { path: '/', label: 'Reedsoft' }\n    ];\n\n    if (pathname.startsWith('/japan')) {\n      breadcrumbs.push({ path: '/japan', label: 'Japan Plan' });\n\n      if (pathname === '/japan/pillar-1') {\n        breadcrumbs.push({ path: '/japan/pillar-1', label: 'Physical Fitness' });\n      } else if (pathname === '/japan/pillar-2') {\n        breadcrumbs.push({ path: '/japan/pillar-2', label: 'Japanese Language' });\n      } else if (pathname === '/japan/pillar-3') {\n        breadcrumbs.push({ path: '/japan/pillar-3', label: 'Practical Skills' });\n      } else if (pathname === '/japan/pillar-4') {\n        breadcrumbs.push({ path: '/japan/pillar-4', label: 'Cognitive Fitness' });\n      }\n    }\n\n    return breadcrumbs;\n  };\n\n  const breadcrumbs = getBreadcrumbs(location.pathname);\n  const isHomePage = location.pathname === '/';\n\n  return (\n    <nav className=\"bg-white shadow-lg border-b border-gray-200\" role=\"navigation\" aria-label=\"Main navigation\">\n      <div className=\"container mx-auto px-4\">\n        {/* Main Navigation */}\n        <div className=\"flex items-center justify-between h-16\">\n          <div className=\"flex items-center space-x-8\">\n            <Link\n              to=\"/\"\n              className=\"text-2xl font-black text-reedsoft-primary hover:text-reedsoft-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-reedsoft-primary focus:ring-offset-2 rounded-md\"\n              aria-label=\"Reedsoft homepage\"\n            >\n              REEDSOFT\n            </Link>\n\n            <div className=\"hidden md:flex space-x-6\" role=\"menubar\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.path}\n                  to={item.path}\n                  role=\"menuitem\"\n                  aria-current={location.pathname === item.path ? 'page' : undefined}\n                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-reedsoft-primary focus:ring-offset-2 ${\n                    location.pathname === item.path\n                      ? 'text-reedsoft-primary bg-reedsoft-light'\n                      : 'text-gray-600 hover:text-reedsoft-primary hover:bg-gray-100'\n                  }`}\n                >\n                  {item.label}\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              className=\"text-gray-600 hover:text-reedsoft-primary focus:outline-none focus:ring-2 focus:ring-reedsoft-primary focus:ring-offset-2 rounded-md p-2\"\n              aria-label=\"Open mobile menu\"\n              aria-expanded=\"false\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\" aria-hidden=\"true\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Breadcrumbs */}\n        {!isHomePage && breadcrumbs.length > 1 && (\n          <div className=\"py-3 border-t border-gray-100\">\n            <nav className=\"flex\" aria-label=\"Breadcrumb navigation\">\n              <ol className=\"flex items-center space-x-2\" role=\"list\">\n                {breadcrumbs.map((crumb, index) => (\n                  <li key={crumb.path} className=\"flex items-center\" role=\"listitem\">\n                    {index > 0 && (\n                      <svg\n                        className=\"flex-shrink-0 h-4 w-4 text-gray-400 mx-2\"\n                        fill=\"currentColor\"\n                        viewBox=\"0 0 20 20\"\n                        aria-hidden=\"true\"\n                      >\n                        <path\n                          fillRule=\"evenodd\"\n                          d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\"\n                          clipRule=\"evenodd\"\n                        />\n                      </svg>\n                    )}\n                    {index === breadcrumbs.length - 1 ? (\n                      <span\n                        className=\"text-sm font-medium text-reedsoft-primary\"\n                        aria-current=\"page\"\n                      >\n                        {crumb.label}\n                      </span>\n                    ) : (\n                      <Link\n                        to={crumb.path}\n                        className=\"text-sm font-medium text-gray-500 hover:text-reedsoft-primary transition-colors focus:outline-none focus:ring-2 focus:ring-reedsoft-primary focus:ring-offset-2 rounded-md\"\n                        aria-label={`Navigate to ${crumb.label}`}\n                      >\n                        {crumb.label}\n                      </Link>\n                    )}\n                  </li>\n                ))}\n              </ol>\n            </nav>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGrD,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,QAAQ,GAAGL,WAAW,CAAC,CAAC;EAE9B,MAAMM,QAAmB,GAAG,CAC1B;IAAEC,IAAI,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC5B;IAAED,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAa,CAAC,CACxC;EAED,MAAMC,cAAc,GAAIC,QAAgB,IAAuB;IAC7D,MAAMC,WAA6B,GAAG,CACpC;MAAEJ,IAAI,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAW,CAAC,CACjC;IAED,IAAIE,QAAQ,CAACE,UAAU,CAAC,QAAQ,CAAC,EAAE;MACjCD,WAAW,CAACE,IAAI,CAAC;QAAEN,IAAI,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAa,CAAC,CAAC;MAEzD,IAAIE,QAAQ,KAAK,iBAAiB,EAAE;QAClCC,WAAW,CAACE,IAAI,CAAC;UAAEN,IAAI,EAAE,iBAAiB;UAAEC,KAAK,EAAE;QAAmB,CAAC,CAAC;MAC1E,CAAC,MAAM,IAAIE,QAAQ,KAAK,iBAAiB,EAAE;QACzCC,WAAW,CAACE,IAAI,CAAC;UAAEN,IAAI,EAAE,iBAAiB;UAAEC,KAAK,EAAE;QAAoB,CAAC,CAAC;MAC3E,CAAC,MAAM,IAAIE,QAAQ,KAAK,iBAAiB,EAAE;QACzCC,WAAW,CAACE,IAAI,CAAC;UAAEN,IAAI,EAAE,iBAAiB;UAAEC,KAAK,EAAE;QAAmB,CAAC,CAAC;MAC1E,CAAC,MAAM,IAAIE,QAAQ,KAAK,iBAAiB,EAAE;QACzCC,WAAW,CAACE,IAAI,CAAC;UAAEN,IAAI,EAAE,iBAAiB;UAAEC,KAAK,EAAE;QAAoB,CAAC,CAAC;MAC3E;IACF;IAEA,OAAOG,WAAW;EACpB,CAAC;EAED,MAAMA,WAAW,GAAGF,cAAc,CAACJ,QAAQ,CAACK,QAAQ,CAAC;EACrD,MAAMI,UAAU,GAAGT,QAAQ,CAACK,QAAQ,KAAK,GAAG;EAE5C,oBACER,OAAA;IAAKa,SAAS,EAAC,6CAA6C;IAACC,IAAI,EAAC,YAAY;IAAC,cAAW,iBAAiB;IAAAC,QAAA,eACzGf,OAAA;MAAKa,SAAS,EAAC,wBAAwB;MAAAE,QAAA,gBAErCf,OAAA;QAAKa,SAAS,EAAC,wCAAwC;QAAAE,QAAA,gBACrDf,OAAA;UAAKa,SAAS,EAAC,6BAA6B;UAAAE,QAAA,gBAC1Cf,OAAA,CAACH,IAAI;YACHmB,EAAE,EAAC,GAAG;YACNH,SAAS,EAAC,sLAAsL;YAChM,cAAW,mBAAmB;YAAAE,QAAA,EAC/B;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAEPpB,OAAA;YAAKa,SAAS,EAAC,0BAA0B;YAACC,IAAI,EAAC,SAAS;YAAAC,QAAA,EACrDX,QAAQ,CAACiB,GAAG,CAAEC,IAAI,iBACjBtB,OAAA,CAACH,IAAI;cAEHmB,EAAE,EAAEM,IAAI,CAACjB,IAAK;cACdS,IAAI,EAAC,UAAU;cACf,gBAAcX,QAAQ,CAACK,QAAQ,KAAKc,IAAI,CAACjB,IAAI,GAAG,MAAM,GAAGkB,SAAU;cACnEV,SAAS,EAAE,8IACTV,QAAQ,CAACK,QAAQ,KAAKc,IAAI,CAACjB,IAAI,GAC3B,yCAAyC,GACzC,6DAA6D,EAChE;cAAAU,QAAA,EAEFO,IAAI,CAAChB;YAAK,GAVNgB,IAAI,CAACjB,IAAI;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAWV,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpB,OAAA;UAAKa,SAAS,EAAC,WAAW;UAAAE,QAAA,eACxBf,OAAA;YACEa,SAAS,EAAC,0IAA0I;YACpJ,cAAW,kBAAkB;YAC7B,iBAAc,OAAO;YAAAE,QAAA,eAErBf,OAAA;cAAKa,SAAS,EAAC,SAAS;cAACW,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAC,eAAY,MAAM;cAAAX,QAAA,eAC/Ff,OAAA;gBAAM2B,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAyB;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL,CAACR,UAAU,IAAIH,WAAW,CAACsB,MAAM,GAAG,CAAC,iBACpC/B,OAAA;QAAKa,SAAS,EAAC,+BAA+B;QAAAE,QAAA,eAC5Cf,OAAA;UAAKa,SAAS,EAAC,MAAM;UAAC,cAAW,uBAAuB;UAAAE,QAAA,eACtDf,OAAA;YAAIa,SAAS,EAAC,6BAA6B;YAACC,IAAI,EAAC,MAAM;YAAAC,QAAA,EACpDN,WAAW,CAACY,GAAG,CAAC,CAACW,KAAK,EAAEC,KAAK,kBAC5BjC,OAAA;cAAqBa,SAAS,EAAC,mBAAmB;cAACC,IAAI,EAAC,UAAU;cAAAC,QAAA,GAC/DkB,KAAK,GAAG,CAAC,iBACRjC,OAAA;gBACEa,SAAS,EAAC,0CAA0C;gBACpDW,IAAI,EAAC,cAAc;gBACnBC,OAAO,EAAC,WAAW;gBACnB,eAAY,MAAM;gBAAAV,QAAA,eAElBf,OAAA;kBACEkC,QAAQ,EAAC,SAAS;kBAClBJ,CAAC,EAAC,oHAAoH;kBACtHK,QAAQ,EAAC;gBAAS;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,EACAa,KAAK,KAAKxB,WAAW,CAACsB,MAAM,GAAG,CAAC,gBAC/B/B,OAAA;gBACEa,SAAS,EAAC,2CAA2C;gBACrD,gBAAa,MAAM;gBAAAE,QAAA,EAElBiB,KAAK,CAAC1B;cAAK;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,gBAEPpB,OAAA,CAACH,IAAI;gBACHmB,EAAE,EAAEgB,KAAK,CAAC3B,IAAK;gBACfQ,SAAS,EAAC,4KAA4K;gBACtL,cAAY,eAAemB,KAAK,CAAC1B,KAAK,EAAG;gBAAAS,QAAA,EAExCiB,KAAK,CAAC1B;cAAK;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CACP;YAAA,GA9BMY,KAAK,CAAC3B,IAAI;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+Bf,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClB,EAAA,CA9HID,UAAoB;EAAA,QACPH,WAAW;AAAA;AAAAsC,EAAA,GADxBnC,UAAoB;AAgI1B,eAAeA,UAAU;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}