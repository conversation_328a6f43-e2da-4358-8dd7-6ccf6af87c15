{"ast": null, "code": "var _jsxFileName = \"C:\\\\Developer\\\\Web Development\\\\reedsoft\\\\frontend\\\\src\\\\pages\\\\Pillar4.tsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { Chart as ChartJS, ArcElement, Tooltip, Legend, Title } from 'chart.js';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Register Chart.js components\nChartJS.register(ArcElement, Tooltip, Legend, Title);\nconst Pillar4 = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-4 md:p-8 bg-blue-50 min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"text-center mb-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"inline-block bg-blue-500 text-white rounded-full px-5 py-2 mb-4 text-sm font-bold\",\n        children: \"PILLAR 4: THE MIND\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-4xl md:text-5xl font-black text-gray-700 tracking-tight\",\n        children: \"Sharpening Your Greatest Tool\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-lg text-gray-600 mt-2\",\n        children: \"Rewire your brain for deep focus and accelerated learning.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-4xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white p-6 rounded-lg shadow-lg mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-center mb-6 text-gray-700\",\n          children: \"Coming Soon: Complete Cognitive Fitness Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-center text-gray-600 mb-6\",\n          children: \"This page will feature the complete conversion of pillar-4.html including:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-6 rounded-lg shadow-lg mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold text-center mb-6 text-gray-700\",\n            children: \"The Three Core Practices\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-6 text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-6xl\",\n                children: \"\\uD83E\\uDDD8\\u200D\\u2642\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-bold text-lg mt-3 text-blue-500\",\n                children: \"Meditation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm mt-1\",\n                children: \"Train your attention span. 10 minutes daily with an app like Headspace is non-negotiable.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 48,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-6xl\",\n                children: \"\\uD83D\\uDCDA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-bold text-lg mt-3 text-green-500\",\n                children: \"Active Reading\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm mt-1\",\n                children: \"Don't just read, process. Use the Feynman Technique to truly understand and retain information.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-6xl\",\n                children: \"\\uD83E\\uDDE9\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 60,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"font-bold text-lg mt-3 text-yellow-500\",\n                children: \"Problem Solving\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm mt-1\",\n                children: \"Engage in daily logic puzzles (Sudoku, etc.) to keep your reasoning skills sharp and flexible.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-6 rounded-lg shadow-lg mb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold text-center mb-6 text-gray-700\",\n            children: \"The Feynman Learning Technique\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-blue-500\",\n                children: \"1.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-gray-800\",\n                  children: \"Choose a Concept.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 76,\n                  columnNumber: 22\n                }, this), \" Pick a topic you are learning (e.g., a grammar point, how a tool works).\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 76,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-blue-500\",\n                children: \"2.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-gray-800\",\n                  children: \"Teach it Simply.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 80,\n                  columnNumber: 22\n                }, this), \" Explain it out loud, in the simplest terms possible, as if to a child.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-blue-500\",\n                children: \"3.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-gray-800\",\n                  children: \"Identify Gaps.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 22\n                }, this), \" When you get stuck or use complex language, you've found a weak spot in your understanding.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-blue-500\",\n                children: \"4.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-gray-800\",\n                  children: \"Review & Simplify.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 88,\n                  columnNumber: 22\n                }, this), \" Go back to the source material to fill the gap, then refine your simple explanation.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-semibold mb-4 text-gray-700\",\n            children: \"Features Coming Soon:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-50 p-4 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Mind Workout Chart\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 97,\n                columnNumber: 52\n              }, this), \"90-minute focus block breakdown\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-green-50 p-4 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Focus Tracking\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 48\n              }, this), \"Daily meditation and reading metrics\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-purple-50 p-4 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Learning Analytics\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 52\n              }, this), \"Feynman technique progress tracking\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/japan/pillar-3\",\n          className: \"bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors\",\n          children: \"\\u2190 Previous: Practical Skills\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/japan\",\n          className: \"bg-reedsoft-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors\",\n          children: \"Back to Dashboard \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"text-center mt-10 pt-6 border-t border-gray-300\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-700 font-semibold\",\n        children: \"The quality of your work is determined by the quality of your focus.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n};\n_c = Pillar4;\nexport default Pillar4;\nvar _c;\n$RefreshReg$(_c, \"Pillar4\");", "map": {"version": 3, "names": ["React", "Link", "Chart", "ChartJS", "ArcElement", "<PERSON><PERSON><PERSON>", "Legend", "Title", "jsxDEV", "_jsxDEV", "register", "Pillar4", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/pages/Pillar4.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { Pie } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  ArcElement,\n  Tooltip,\n  Legend,\n  Title\n} from 'chart.js';\nimport { useChartData } from '../hooks/useChartData';\n\n// Register Chart.js components\nChartJS.register(Arc<PERSON><PERSON>, Tooltip, Legend, Title);\n\nconst Pillar4: React.FC = () => {\n  return (\n    <div className=\"p-4 md:p-8 bg-blue-50 min-h-screen\">\n      <header className=\"text-center mb-10\">\n        <div className=\"inline-block bg-blue-500 text-white rounded-full px-5 py-2 mb-4 text-sm font-bold\">\n          PILLAR 4: THE MIND\n        </div>\n        <h1 className=\"text-4xl md:text-5xl font-black text-gray-700 tracking-tight\">\n          Sharpening Your Greatest Tool\n        </h1>\n        <p className=\"text-lg text-gray-600 mt-2\">\n          Rewire your brain for deep focus and accelerated learning.\n        </p>\n      </header>\n\n      <div className=\"max-w-4xl mx-auto\">\n        <div className=\"bg-white p-6 rounded-lg shadow-lg mb-8\">\n          <h2 className=\"text-2xl font-bold text-center mb-6 text-gray-700\">\n            Coming Soon: Complete Cognitive Fitness Dashboard\n          </h2>\n          <p className=\"text-center text-gray-600 mb-6\">\n            This page will feature the complete conversion of pillar-4.html including:\n          </p>\n          \n          <div className=\"bg-white p-6 rounded-lg shadow-lg mb-6\">\n            <h3 className=\"text-xl font-bold text-center mb-6 text-gray-700\">\n              The Three Core Practices\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 text-center\">\n              <div className=\"p-4\">\n                <div className=\"text-6xl\">🧘‍♂️</div>\n                <h4 className=\"font-bold text-lg mt-3 text-blue-500\">Meditation</h4>\n                <p className=\"text-sm mt-1\">\n                  Train your attention span. 10 minutes daily with an app like Headspace is non-negotiable.\n                </p>\n              </div>\n              <div className=\"p-4\">\n                <div className=\"text-6xl\">📚</div>\n                <h4 className=\"font-bold text-lg mt-3 text-green-500\">Active Reading</h4>\n                <p className=\"text-sm mt-1\">\n                  Don't just read, process. Use the Feynman Technique to truly understand and retain information.\n                </p>\n              </div>\n              <div className=\"p-4\">\n                <div className=\"text-6xl\">🧩</div>\n                <h4 className=\"font-bold text-lg mt-3 text-yellow-500\">Problem Solving</h4>\n                <p className=\"text-sm mt-1\">\n                  Engage in daily logic puzzles (Sudoku, etc.) to keep your reasoning skills sharp and flexible.\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white p-6 rounded-lg shadow-lg mb-6\">\n            <h3 className=\"text-xl font-bold text-center mb-6 text-gray-700\">\n              The Feynman Learning Technique\n            </h3>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\">\n                <div className=\"text-3xl font-bold text-blue-500\">1.</div>\n                <div><strong className=\"text-gray-800\">Choose a Concept.</strong> Pick a topic you are learning (e.g., a grammar point, how a tool works).</div>\n              </div>\n              <div className=\"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\">\n                <div className=\"text-3xl font-bold text-blue-500\">2.</div>\n                <div><strong className=\"text-gray-800\">Teach it Simply.</strong> Explain it out loud, in the simplest terms possible, as if to a child.</div>\n              </div>\n              <div className=\"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\">\n                <div className=\"text-3xl font-bold text-blue-500\">3.</div>\n                <div><strong className=\"text-gray-800\">Identify Gaps.</strong> When you get stuck or use complex language, you've found a weak spot in your understanding.</div>\n              </div>\n              <div className=\"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\">\n                <div className=\"text-3xl font-bold text-blue-500\">4.</div>\n                <div><strong className=\"text-gray-800\">Review & Simplify.</strong> Go back to the source material to fill the gap, then refine your simple explanation.</div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"text-center\">\n            <h3 className=\"text-xl font-semibold mb-4 text-gray-700\">Features Coming Soon:</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n              <div className=\"bg-blue-50 p-4 rounded-lg\">\n                <strong>Mind Workout Chart</strong><br />\n                90-minute focus block breakdown\n              </div>\n              <div className=\"bg-green-50 p-4 rounded-lg\">\n                <strong>Focus Tracking</strong><br />\n                Daily meditation and reading metrics\n              </div>\n              <div className=\"bg-purple-50 p-4 rounded-lg\">\n                <strong>Learning Analytics</strong><br />\n                Feynman technique progress tracking\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"flex justify-between items-center\">\n          <Link\n            to=\"/japan/pillar-3\"\n            className=\"bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors\"\n          >\n            ← Previous: Practical Skills\n          </Link>\n          <Link\n            to=\"/japan\"\n            className=\"bg-reedsoft-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors\"\n          >\n            Back to Dashboard →\n          </Link>\n        </div>\n      </div>\n\n      <footer className=\"text-center mt-10 pt-6 border-t border-gray-300\">\n        <p className=\"text-gray-700 font-semibold\">\n          The quality of your work is determined by the quality of your focus.\n        </p>\n      </footer>\n    </div>\n  );\n};\n\nexport default Pillar4;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAEvC,SACEC,KAAK,IAAIC,OAAO,EAChBC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,KAAK,QACA,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGlB;AACAN,OAAO,CAACO,QAAQ,CAACN,UAAU,EAAEC,OAAO,EAAEC,MAAM,EAAEC,KAAK,CAAC;AAEpD,MAAMI,OAAiB,GAAGA,CAAA,KAAM;EAC9B,oBACEF,OAAA;IAAKG,SAAS,EAAC,oCAAoC;IAAAC,QAAA,gBACjDJ,OAAA;MAAQG,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBACnCJ,OAAA;QAAKG,SAAS,EAAC,mFAAmF;QAAAC,QAAA,EAAC;MAEnG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACNR,OAAA;QAAIG,SAAS,EAAC,8DAA8D;QAAAC,QAAA,EAAC;MAE7E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLR,OAAA;QAAGG,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAC;MAE1C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAETR,OAAA;MAAKG,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCJ,OAAA;QAAKG,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDJ,OAAA;UAAIG,SAAS,EAAC,mDAAmD;UAAAC,QAAA,EAAC;QAElE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLR,OAAA;UAAGG,SAAS,EAAC,gCAAgC;UAAAC,QAAA,EAAC;QAE9C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJR,OAAA;UAAKG,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDJ,OAAA;YAAIG,SAAS,EAAC,kDAAkD;YAAAC,QAAA,EAAC;UAEjE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLR,OAAA;YAAKG,SAAS,EAAC,mDAAmD;YAAAC,QAAA,gBAChEJ,OAAA;cAAKG,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBJ,OAAA;gBAAKG,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrCR,OAAA;gBAAIG,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpER,OAAA;gBAAGG,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAE5B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNR,OAAA;cAAKG,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBJ,OAAA;gBAAKG,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClCR,OAAA;gBAAIG,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzER,OAAA;gBAAGG,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAE5B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNR,OAAA;cAAKG,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBJ,OAAA;gBAAKG,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClCR,OAAA;gBAAIG,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3ER,OAAA;gBAAGG,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAE5B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENR,OAAA;UAAKG,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDJ,OAAA;YAAIG,SAAS,EAAC,kDAAkD;YAAAC,QAAA,EAAC;UAEjE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLR,OAAA;YAAKG,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBJ,OAAA;cAAKG,SAAS,EAAC,sDAAsD;cAAAC,QAAA,gBACnEJ,OAAA;gBAAKG,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1DR,OAAA;gBAAAI,QAAA,gBAAKJ,OAAA;kBAAQG,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,6EAAyE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7I,CAAC,eACNR,OAAA;cAAKG,SAAS,EAAC,sDAAsD;cAAAC,QAAA,gBACnEJ,OAAA;gBAAKG,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1DR,OAAA;gBAAAI,QAAA,gBAAKJ,OAAA;kBAAQG,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,2EAAuE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1I,CAAC,eACNR,OAAA;cAAKG,SAAS,EAAC,sDAAsD;cAAAC,QAAA,gBACnEJ,OAAA;gBAAKG,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1DR,OAAA;gBAAAI,QAAA,gBAAKJ,OAAA;kBAAQG,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,gGAA4F;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7J,CAAC,eACNR,OAAA;cAAKG,SAAS,EAAC,sDAAsD;cAAAC,QAAA,gBACnEJ,OAAA;gBAAKG,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1DR,OAAA;gBAAAI,QAAA,gBAAKJ,OAAA;kBAAQG,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,yFAAqF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1J,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENR,OAAA;UAAKG,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BJ,OAAA;YAAIG,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnFR,OAAA;YAAKG,SAAS,EAAC,+CAA+C;YAAAC,QAAA,gBAC5DJ,OAAA;cAAKG,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCJ,OAAA;gBAAAI,QAAA,EAAQ;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAAAR,OAAA;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,mCAE3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNR,OAAA;cAAKG,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCJ,OAAA;gBAAAI,QAAA,EAAQ;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAAAR,OAAA;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,wCAEvC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNR,OAAA;cAAKG,SAAS,EAAC,6BAA6B;cAAAC,QAAA,gBAC1CJ,OAAA;gBAAAI,QAAA,EAAQ;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAAAR,OAAA;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,uCAE3C;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENR,OAAA;QAAKG,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDJ,OAAA,CAACR,IAAI;UACHiB,EAAE,EAAC,iBAAiB;UACpBN,SAAS,EAAC,kGAAkG;UAAAC,QAAA,EAC7G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPR,OAAA,CAACR,IAAI;UACHiB,EAAE,EAAC,QAAQ;UACXN,SAAS,EAAC,iHAAiH;UAAAC,QAAA,EAC5H;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENR,OAAA;MAAQG,SAAS,EAAC,iDAAiD;MAAAC,QAAA,eACjEJ,OAAA;QAAGG,SAAS,EAAC,6BAA6B;QAAAC,QAAA,EAAC;MAE3C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACE,EAAA,GAvHIR,OAAiB;AAyHvB,eAAeA,OAAO;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}