import { FormattedTime } from '../types';

/**
 * Converts 24-hour time format to 12-hour AM/PM format
 * @param time24 - Time in 24-hour format (e.g., "06:00", "15:30")
 * @returns FormattedTime object with original, formatted, and period
 */
export const convertTo12Hour = (time24: string): FormattedTime => {
  const timeParts = time24.split(':');
  const hours = Number(timeParts[0]);
  const minutes = Number(timeParts[1]);

  if (isNaN(hours) || isNaN(minutes) || timeParts.length !== 2) {
    return {
      original: time24,
      formatted: time24,
      period: 'AM'
    };
  }

  const period: 'AM' | 'PM' = hours >= 12 ? 'PM' : 'AM';
  const hours12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
  const formattedMinutes = minutes.toString().padStart(2, '0');
  
  return {
    original: time24,
    formatted: `${hours12}:${formattedMinutes} ${period}`,
    period
  };
};

/**
 * Formats a time range from 24-hour to 12-hour format
 * @param timeRange - Time range in format "06:00 - 07:30"
 * @returns Formatted time range string
 */
export const formatTimeRange = (timeRange: string): string => {
  const rangeParts = timeRange.split(' - ');
  if (rangeParts.length !== 2 || !rangeParts[0] || !rangeParts[1]) {
    return timeRange; // Return original if not a valid range
  }

  const startTime = convertTo12Hour(rangeParts[0].trim());
  const endTime = convertTo12Hour(rangeParts[1].trim());
  
  return `${startTime.formatted} - ${endTime.formatted}`;
};

/**
 * Extracts and formats time from a string that contains time information
 * @param text - Text containing time (e.g., "06:00 - 07:30 | 💪 Pillar 1: Physical Fitness")
 * @returns Formatted text with converted time
 */
export const formatTimeInText = (text: string): string => {
  // Regex to match time patterns like "06:00 - 07:30" or "06:00"
  const timeRangeRegex = /(\d{2}:\d{2})\s*-\s*(\d{2}:\d{2})/g;
  const singleTimeRegex = /(\d{2}:\d{2})/g;
  
  // First, handle time ranges
  let formattedText = text.replace(timeRangeRegex, (_match, start, end) => {
    const startFormatted = convertTo12Hour(start);
    const endFormatted = convertTo12Hour(end);
    return `${startFormatted.formatted} - ${endFormatted.formatted}`;
  });
  
  // Then handle single times that weren't part of ranges
  formattedText = formattedText.replace(singleTimeRegex, (match) => {
    // Check if this time is already formatted (contains AM/PM)
    if (formattedText.includes(`${match} AM`) || formattedText.includes(`${match} PM`)) {
      return match; // Already formatted, don't change
    }
    const formatted = convertTo12Hour(match);
    return formatted.formatted;
  });
  
  return formattedText;
};

/**
 * Gets the current time in 12-hour format
 * @returns Current time string in 12-hour format
 */
export const getCurrentTime12Hour = (): string => {
  const now = new Date();
  const hours = now.getHours();
  const minutes = now.getMinutes();
  const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  return convertTo12Hour(timeString).formatted;
};

/**
 * Checks if a given time is in the morning (AM)
 * @param time24 - Time in 24-hour format
 * @returns True if the time is AM, false if PM
 */
export const isAM = (time24: string): boolean => {
  const formatted = convertTo12Hour(time24);
  return formatted.period === 'AM';
};

/**
 * Sorts an array of time strings in chronological order
 * @param times - Array of time strings in 24-hour format
 * @returns Sorted array of times
 */
export const sortTimes = (times: string[]): string[] => {
  return times.sort((a, b) => {
    const partsA = a.split(':');
    const partsB = b.split(':');
    const hoursA = Number(partsA[0]) || 0;
    const minutesA = Number(partsA[1]) || 0;
    const hoursB = Number(partsB[0]) || 0;
    const minutesB = Number(partsB[1]) || 0;

    const totalMinutesA = hoursA * 60 + minutesA;
    const totalMinutesB = hoursB * 60 + minutesB;

    return totalMinutesA - totalMinutesB;
  });
};
