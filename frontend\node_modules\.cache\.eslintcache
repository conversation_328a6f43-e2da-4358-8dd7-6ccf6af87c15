[{"C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\index.tsx": "1", "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\reportWebVitals.ts": "2", "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\App.tsx": "3", "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\pages\\ReedsoftLanding.tsx": "4", "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\pages\\Pillar1.tsx": "5", "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\pages\\JapanPlan.tsx": "6", "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\pages\\Pillar2.tsx": "7", "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\pages\\Pillar3.tsx": "8", "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\pages\\Pillar4.tsx": "9", "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\components\\Navigation.tsx": "10", "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\components\\ErrorBoundary.tsx": "11", "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\hooks\\useTimeFormat.ts": "12", "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\hooks\\useChartData.ts": "13", "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\utils\\timeFormat.ts": "14", "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\utils\\chartConfig.ts": "15"}, {"size": 554, "mtime": 1757688547427, "results": "16", "hashOfConfig": "17"}, {"size": 425, "mtime": 1757688541898, "results": "18", "hashOfConfig": "17"}, {"size": 1896, "mtime": 1757693995126, "results": "19", "hashOfConfig": "17"}, {"size": 7613, "mtime": 1757689035048, "results": "20", "hashOfConfig": "17"}, {"size": 5785, "mtime": 1757689630473, "results": "21", "hashOfConfig": "17"}, {"size": 26356, "mtime": 1757693281272, "results": "22", "hashOfConfig": "17"}, {"size": 6121, "mtime": 1757690355355, "results": "23", "hashOfConfig": "17"}, {"size": 5682, "mtime": 1757692668760, "results": "24", "hashOfConfig": "17"}, {"size": 6151, "mtime": 1757692792787, "results": "25", "hashOfConfig": "17"}, {"size": 5421, "mtime": 1757693410004, "results": "26", "hashOfConfig": "17"}, {"size": 3751, "mtime": 1757694154304, "results": "27", "hashOfConfig": "17"}, {"size": 4192, "mtime": 1757688931146, "results": "28", "hashOfConfig": "17"}, {"size": 4229, "mtime": 1757688909554, "results": "29", "hashOfConfig": "17"}, {"size": 4011, "mtime": 1757694067816, "results": "30", "hashOfConfig": "17"}, {"size": 5749, "mtime": 1757694103589, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1fo0wog", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\index.tsx", [], [], "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\reportWebVitals.ts", [], [], "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\App.tsx", [], [], "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\pages\\ReedsoftLanding.tsx", [], [], "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\pages\\Pillar1.tsx", [], [], "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\pages\\JapanPlan.tsx", [], [], "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\pages\\Pillar2.tsx", [], [], "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\pages\\Pillar3.tsx", [], [], "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\pages\\Pillar4.tsx", [], [], "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\components\\Navigation.tsx", [], [], "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\components\\ErrorBoundary.tsx", [], [], "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\hooks\\useTimeFormat.ts", [], [], "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\hooks\\useChartData.ts", [], [], "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\utils\\timeFormat.ts", [], [], "C:\\Developer\\Web Development\\reedsoft\\frontend\\src\\utils\\chartConfig.ts", [], []]