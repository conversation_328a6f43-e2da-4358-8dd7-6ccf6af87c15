import { useState, useEffect } from 'react';
import { ChartData, ChartOptions } from '../types';
import {
  createTimeAllocationData,
  createKeyLiftsData,
  createAnkiData,
  createKnowledgeData,
  createMindWorkoutData,
  createChartOptions
} from '../utils/chartConfig';

export type ChartType = 'timeAllocation' | 'keyLifts' | 'anki' | 'knowledge' | 'mindWorkout';

/**
 * Custom hook for managing chart data and options
 * @param chartType - Type of chart to create
 * @param title - Optional chart title
 * @returns Object with chart data, options, and loading state
 */
export const useChartData = (chartType: ChartType, title?: string) => {
  const [data, setData] = useState<ChartData | null>(null);
  const [options, setOptions] = useState<ChartOptions | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadChartData = async () => {
      try {
        setLoading(true);
        setError(null);

        let chartData: ChartData;
        let chartOptions: ChartOptions;

        switch (chartType) {
          case 'timeAllocation':
            chartData = createTimeAllocationData();
            chartOptions = createChartOptions('doughnut', title || 'Weekly Hour Distribution');
            break;

          case 'keyLifts':
            chartData = createKeyLiftsData();
            chartOptions = createChartOptions('bar', title);
            // Add custom tooltip for key lifts
            chartOptions.plugins!.tooltip!.callbacks!.label = function(context: any) {
              const lift = context.label;
              let muscles: string;
              switch(lift) {
                case 'Squat': muscles = 'Legs, Glutes, Core'; break;
                case 'Deadlift': muscles = 'Entire Posterior Chain'; break;
                case 'Bench Press': muscles = 'Chest, Shoulders, Triceps'; break;
                case 'Overhead Press': muscles = 'Shoulders, Triceps, Core'; break;
                case 'Barbell Row': muscles = 'Back, Biceps'; break;
                default: muscles = '';
              }
              return ` Works: ${muscles}`;
            };
            break;

          case 'anki':
            chartData = createAnkiData();
            chartOptions = createChartOptions('doughnut', title || 'The Daily SRS Cycle');
            break;

          case 'knowledge':
            chartData = createKnowledgeData();
            chartOptions = createChartOptions('radar', title);
            break;

          case 'mindWorkout':
            chartData = createMindWorkoutData();
            chartOptions = createChartOptions('pie', title || '90-Minute Focus Block');
            break;

          default:
            throw new Error(`Unknown chart type: ${chartType}`);
        }

        setData(chartData);
        setOptions(chartOptions);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load chart data');
      } finally {
        setLoading(false);
      }
    };

    loadChartData();
  }, [chartType, title]);

  return { data, options, loading, error };
};

/**
 * Custom hook for creating custom chart data
 * @param labels - Chart labels
 * @param datasets - Chart datasets
 * @param chartType - Type of chart
 * @param title - Optional chart title
 * @returns Object with chart data, options, and utility functions
 */
export const useCustomChartData = (
  labels: string[],
  datasets: any[],
  chartType: 'doughnut' | 'bar' | 'radar' | 'pie' | 'line',
  title?: string
) => {
  const [data, setData] = useState<ChartData>({ labels, datasets });
  const [options, setOptions] = useState<ChartOptions>(createChartOptions(chartType, title));

  const updateData = (newLabels: string[], newDatasets: any[]) => {
    setData({ labels: newLabels, datasets: newDatasets });
  };

  const updateOptions = (newOptions: Partial<ChartOptions>) => {
    setOptions(prev => ({ ...prev, ...newOptions }));
  };

  useEffect(() => {
    setData({ labels, datasets });
  }, [labels, datasets]);

  useEffect(() => {
    setOptions(createChartOptions(chartType, title));
  }, [chartType, title]);

  return { data, options, updateData, updateOptions };
};
