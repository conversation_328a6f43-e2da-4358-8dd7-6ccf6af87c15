{"ast": null, "code": "var _jsxFileName = \"C:\\\\Developer\\\\Web Development\\\\reedsoft\\\\frontend\\\\src\\\\components\\\\Navigation.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navigation = () => {\n  _s();\n  const location = useLocation();\n  const navItems = [{\n    path: '/',\n    label: 'Home'\n  }, {\n    path: '/japan',\n    label: 'Japan Plan'\n  }];\n  const getBreadcrumbs = pathname => {\n    const breadcrumbs = [{\n      path: '/',\n      label: 'Reedsoft'\n    }];\n    if (pathname.startsWith('/japan')) {\n      breadcrumbs.push({\n        path: '/japan',\n        label: 'Japan Plan'\n      });\n      if (pathname === '/japan/pillar-1') {\n        breadcrumbs.push({\n          path: '/japan/pillar-1',\n          label: 'Physical Fitness'\n        });\n      } else if (pathname === '/japan/pillar-2') {\n        breadcrumbs.push({\n          path: '/japan/pillar-2',\n          label: 'Japanese Language'\n        });\n      } else if (pathname === '/japan/pillar-3') {\n        breadcrumbs.push({\n          path: '/japan/pillar-3',\n          label: 'Practical Skills'\n        });\n      } else if (pathname === '/japan/pillar-4') {\n        breadcrumbs.push({\n          path: '/japan/pillar-4',\n          label: 'Cognitive Fitness'\n        });\n      }\n    }\n    return breadcrumbs;\n  };\n  const breadcrumbs = getBreadcrumbs(location.pathname);\n  const isHomePage = location.pathname === '/';\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"bg-white shadow-lg border-b border-gray-200\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between h-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-8\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"text-2xl font-black text-reedsoft-primary hover:text-reedsoft-secondary transition-colors\",\n            children: \"REEDSOFT\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:flex space-x-6\",\n            children: navItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n              to: item.path,\n              className: `px-3 py-2 rounded-md text-sm font-medium transition-colors ${location.pathname === item.path ? 'text-reedsoft-primary bg-reedsoft-light' : 'text-gray-600 hover:text-reedsoft-primary hover:bg-gray-100'}`,\n              children: item.label\n            }, item.path, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:hidden\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"text-gray-600 hover:text-reedsoft-primary\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 6h16M4 12h16M4 18h16\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 70,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), !isHomePage && breadcrumbs.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"py-3 border-t border-gray-100\",\n        children: /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"flex\",\n          \"aria-label\": \"Breadcrumb\",\n          children: /*#__PURE__*/_jsxDEV(\"ol\", {\n            className: \"flex items-center space-x-2\",\n            children: breadcrumbs.map((crumb, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center\",\n              children: [index > 0 && /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"flex-shrink-0 h-4 w-4 text-gray-400 mx-2\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  fillRule: \"evenodd\",\n                  d: \"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",\n                  clipRule: \"evenodd\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 91,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 23\n              }, this), index === breadcrumbs.length - 1 ? /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm font-medium text-reedsoft-primary\",\n                children: crumb.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(Link, {\n                to: crumb.path,\n                className: \"text-sm font-medium text-gray-500 hover:text-reedsoft-primary transition-colors\",\n                children: crumb.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 23\n              }, this)]\n            }, crumb.path, true, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(Navigation, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = Navigation;\nexport default Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");", "map": {"version": 3, "names": ["React", "Link", "useLocation", "jsxDEV", "_jsxDEV", "Navigation", "_s", "location", "navItems", "path", "label", "getBreadcrumbs", "pathname", "breadcrumbs", "startsWith", "push", "isHomePage", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "length", "crumb", "index", "fillRule", "clipRule", "_c", "$RefreshReg$"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/components/Navigation.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { NavItem, BreadcrumbItem } from '../types';\n\nconst Navigation: React.FC = () => {\n  const location = useLocation();\n\n  const navItems: NavItem[] = [\n    { path: '/', label: 'Home' },\n    { path: '/japan', label: 'Japan Plan' },\n  ];\n\n  const getBreadcrumbs = (pathname: string): BreadcrumbItem[] => {\n    const breadcrumbs: BreadcrumbItem[] = [\n      { path: '/', label: 'Reedsoft' }\n    ];\n\n    if (pathname.startsWith('/japan')) {\n      breadcrumbs.push({ path: '/japan', label: 'Japan Plan' });\n\n      if (pathname === '/japan/pillar-1') {\n        breadcrumbs.push({ path: '/japan/pillar-1', label: 'Physical Fitness' });\n      } else if (pathname === '/japan/pillar-2') {\n        breadcrumbs.push({ path: '/japan/pillar-2', label: 'Japanese Language' });\n      } else if (pathname === '/japan/pillar-3') {\n        breadcrumbs.push({ path: '/japan/pillar-3', label: 'Practical Skills' });\n      } else if (pathname === '/japan/pillar-4') {\n        breadcrumbs.push({ path: '/japan/pillar-4', label: 'Cognitive Fitness' });\n      }\n    }\n\n    return breadcrumbs;\n  };\n\n  const breadcrumbs = getBreadcrumbs(location.pathname);\n  const isHomePage = location.pathname === '/';\n\n  return (\n    <nav className=\"bg-white shadow-lg border-b border-gray-200\">\n      <div className=\"container mx-auto px-4\">\n        {/* Main Navigation */}\n        <div className=\"flex items-center justify-between h-16\">\n          <div className=\"flex items-center space-x-8\">\n            <Link \n              to=\"/\" \n              className=\"text-2xl font-black text-reedsoft-primary hover:text-reedsoft-secondary transition-colors\"\n            >\n              REEDSOFT\n            </Link>\n            \n            <div className=\"hidden md:flex space-x-6\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.path}\n                  to={item.path}\n                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${\n                    location.pathname === item.path\n                      ? 'text-reedsoft-primary bg-reedsoft-light'\n                      : 'text-gray-600 hover:text-reedsoft-primary hover:bg-gray-100'\n                  }`}\n                >\n                  {item.label}\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button className=\"text-gray-600 hover:text-reedsoft-primary\">\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Breadcrumbs */}\n        {!isHomePage && breadcrumbs.length > 1 && (\n          <div className=\"py-3 border-t border-gray-100\">\n            <nav className=\"flex\" aria-label=\"Breadcrumb\">\n              <ol className=\"flex items-center space-x-2\">\n                {breadcrumbs.map((crumb, index) => (\n                  <li key={crumb.path} className=\"flex items-center\">\n                    {index > 0 && (\n                      <svg\n                        className=\"flex-shrink-0 h-4 w-4 text-gray-400 mx-2\"\n                        fill=\"currentColor\"\n                        viewBox=\"0 0 20 20\"\n                      >\n                        <path\n                          fillRule=\"evenodd\"\n                          d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\"\n                          clipRule=\"evenodd\"\n                        />\n                      </svg>\n                    )}\n                    {index === breadcrumbs.length - 1 ? (\n                      <span className=\"text-sm font-medium text-reedsoft-primary\">\n                        {crumb.label}\n                      </span>\n                    ) : (\n                      <Link\n                        to={crumb.path}\n                        className=\"text-sm font-medium text-gray-500 hover:text-reedsoft-primary transition-colors\"\n                      >\n                        {crumb.label}\n                      </Link>\n                    )}\n                  </li>\n                ))}\n              </ol>\n            </nav>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGrD,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAMC,QAAQ,GAAGL,WAAW,CAAC,CAAC;EAE9B,MAAMM,QAAmB,GAAG,CAC1B;IAAEC,IAAI,EAAE,GAAG;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC5B;IAAED,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAa,CAAC,CACxC;EAED,MAAMC,cAAc,GAAIC,QAAgB,IAAuB;IAC7D,MAAMC,WAA6B,GAAG,CACpC;MAAEJ,IAAI,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAW,CAAC,CACjC;IAED,IAAIE,QAAQ,CAACE,UAAU,CAAC,QAAQ,CAAC,EAAE;MACjCD,WAAW,CAACE,IAAI,CAAC;QAAEN,IAAI,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAa,CAAC,CAAC;MAEzD,IAAIE,QAAQ,KAAK,iBAAiB,EAAE;QAClCC,WAAW,CAACE,IAAI,CAAC;UAAEN,IAAI,EAAE,iBAAiB;UAAEC,KAAK,EAAE;QAAmB,CAAC,CAAC;MAC1E,CAAC,MAAM,IAAIE,QAAQ,KAAK,iBAAiB,EAAE;QACzCC,WAAW,CAACE,IAAI,CAAC;UAAEN,IAAI,EAAE,iBAAiB;UAAEC,KAAK,EAAE;QAAoB,CAAC,CAAC;MAC3E,CAAC,MAAM,IAAIE,QAAQ,KAAK,iBAAiB,EAAE;QACzCC,WAAW,CAACE,IAAI,CAAC;UAAEN,IAAI,EAAE,iBAAiB;UAAEC,KAAK,EAAE;QAAmB,CAAC,CAAC;MAC1E,CAAC,MAAM,IAAIE,QAAQ,KAAK,iBAAiB,EAAE;QACzCC,WAAW,CAACE,IAAI,CAAC;UAAEN,IAAI,EAAE,iBAAiB;UAAEC,KAAK,EAAE;QAAoB,CAAC,CAAC;MAC3E;IACF;IAEA,OAAOG,WAAW;EACpB,CAAC;EAED,MAAMA,WAAW,GAAGF,cAAc,CAACJ,QAAQ,CAACK,QAAQ,CAAC;EACrD,MAAMI,UAAU,GAAGT,QAAQ,CAACK,QAAQ,KAAK,GAAG;EAE5C,oBACER,OAAA;IAAKa,SAAS,EAAC,6CAA6C;IAAAC,QAAA,eAC1Dd,OAAA;MAAKa,SAAS,EAAC,wBAAwB;MAAAC,QAAA,gBAErCd,OAAA;QAAKa,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDd,OAAA;UAAKa,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1Cd,OAAA,CAACH,IAAI;YACHkB,EAAE,EAAC,GAAG;YACNF,SAAS,EAAC,2FAA2F;YAAAC,QAAA,EACtG;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAEPnB,OAAA;YAAKa,SAAS,EAAC,0BAA0B;YAAAC,QAAA,EACtCV,QAAQ,CAACgB,GAAG,CAAEC,IAAI,iBACjBrB,OAAA,CAACH,IAAI;cAEHkB,EAAE,EAAEM,IAAI,CAAChB,IAAK;cACdQ,SAAS,EAAE,8DACTV,QAAQ,CAACK,QAAQ,KAAKa,IAAI,CAAChB,IAAI,GAC3B,yCAAyC,GACzC,6DAA6D,EAChE;cAAAS,QAAA,EAEFO,IAAI,CAACf;YAAK,GARNe,IAAI,CAAChB,IAAI;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASV,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNnB,OAAA;UAAKa,SAAS,EAAC,WAAW;UAAAC,QAAA,eACxBd,OAAA;YAAQa,SAAS,EAAC,2CAA2C;YAAAC,QAAA,eAC3Dd,OAAA;cAAKa,SAAS,EAAC,SAAS;cAACS,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAV,QAAA,eAC5Ed,OAAA;gBAAMyB,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAyB;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL,CAACP,UAAU,IAAIH,WAAW,CAACoB,MAAM,GAAG,CAAC,iBACpC7B,OAAA;QAAKa,SAAS,EAAC,+BAA+B;QAAAC,QAAA,eAC5Cd,OAAA;UAAKa,SAAS,EAAC,MAAM;UAAC,cAAW,YAAY;UAAAC,QAAA,eAC3Cd,OAAA;YAAIa,SAAS,EAAC,6BAA6B;YAAAC,QAAA,EACxCL,WAAW,CAACW,GAAG,CAAC,CAACU,KAAK,EAAEC,KAAK,kBAC5B/B,OAAA;cAAqBa,SAAS,EAAC,mBAAmB;cAAAC,QAAA,GAC/CiB,KAAK,GAAG,CAAC,iBACR/B,OAAA;gBACEa,SAAS,EAAC,0CAA0C;gBACpDS,IAAI,EAAC,cAAc;gBACnBC,OAAO,EAAC,WAAW;gBAAAT,QAAA,eAEnBd,OAAA;kBACEgC,QAAQ,EAAC,SAAS;kBAClBJ,CAAC,EAAC,oHAAoH;kBACtHK,QAAQ,EAAC;gBAAS;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN,EACAY,KAAK,KAAKtB,WAAW,CAACoB,MAAM,GAAG,CAAC,gBAC/B7B,OAAA;gBAAMa,SAAS,EAAC,2CAA2C;gBAAAC,QAAA,EACxDgB,KAAK,CAACxB;cAAK;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,gBAEPnB,OAAA,CAACH,IAAI;gBACHkB,EAAE,EAAEe,KAAK,CAACzB,IAAK;gBACfQ,SAAS,EAAC,iFAAiF;gBAAAC,QAAA,EAE1FgB,KAAK,CAACxB;cAAK;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CACP;YAAA,GAzBMW,KAAK,CAACzB,IAAI;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0Bf,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjB,EAAA,CAlHID,UAAoB;EAAA,QACPH,WAAW;AAAA;AAAAoC,EAAA,GADxBjC,UAAoB;AAoH1B,eAAeA,UAAU;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}