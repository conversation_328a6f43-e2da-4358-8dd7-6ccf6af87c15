{"ast": null, "code": "import React from'react';import{Link,useLocation}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Navigation=()=>{const location=useLocation();const navItems=[{path:'/',label:'Home'},{path:'/japan',label:'Japan Plan'}];const getBreadcrumbs=pathname=>{const breadcrumbs=[{path:'/',label:'Reedsoft'}];if(pathname.startsWith('/japan')){breadcrumbs.push({path:'/japan',label:'Japan Plan'});if(pathname==='/japan/pillar-1'){breadcrumbs.push({path:'/japan/pillar-1',label:'Physical Fitness'});}else if(pathname==='/japan/pillar-2'){breadcrumbs.push({path:'/japan/pillar-2',label:'Japanese Language'});}else if(pathname==='/japan/pillar-3'){breadcrumbs.push({path:'/japan/pillar-3',label:'Practical Skills'});}else if(pathname==='/japan/pillar-4'){breadcrumbs.push({path:'/japan/pillar-4',label:'Cognitive Fitness'});}}return breadcrumbs;};const breadcrumbs=getBreadcrumbs(location.pathname);const isHomePage=location.pathname==='/';return/*#__PURE__*/_jsx(\"nav\",{className:\"bg-white shadow-lg border-b border-gray-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto px-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between h-16\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-8\",children:[/*#__PURE__*/_jsx(Link,{to:\"/\",className:\"text-2xl font-black text-reedsoft-primary hover:text-reedsoft-secondary transition-colors\",children:\"REEDSOFT\"}),/*#__PURE__*/_jsx(\"div\",{className:\"hidden md:flex space-x-6\",children:navItems.map(item=>/*#__PURE__*/_jsx(Link,{to:item.path,className:\"px-3 py-2 rounded-md text-sm font-medium transition-colors \".concat(location.pathname===item.path?'text-reedsoft-primary bg-reedsoft-light':'text-gray-600 hover:text-reedsoft-primary hover:bg-gray-100'),children:item.label},item.path))})]}),/*#__PURE__*/_jsx(\"div\",{className:\"md:hidden\",children:/*#__PURE__*/_jsx(\"button\",{className:\"text-gray-600 hover:text-reedsoft-primary\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"h-6 w-6\",fill:\"none\",viewBox:\"0 0 24 24\",stroke:\"currentColor\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M4 6h16M4 12h16M4 18h16\"})})})})]}),!isHomePage&&breadcrumbs.length>1&&/*#__PURE__*/_jsx(\"div\",{className:\"py-3 border-t border-gray-100\",children:/*#__PURE__*/_jsx(\"nav\",{className:\"flex\",\"aria-label\":\"Breadcrumb\",children:/*#__PURE__*/_jsx(\"ol\",{className:\"flex items-center space-x-2\",children:breadcrumbs.map((crumb,index)=>/*#__PURE__*/_jsxs(\"li\",{className:\"flex items-center\",children:[index>0&&/*#__PURE__*/_jsx(\"svg\",{className:\"flex-shrink-0 h-4 w-4 text-gray-400 mx-2\",fill:\"currentColor\",viewBox:\"0 0 20 20\",children:/*#__PURE__*/_jsx(\"path\",{fillRule:\"evenodd\",d:\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\",clipRule:\"evenodd\"})}),index===breadcrumbs.length-1?/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-reedsoft-primary\",children:crumb.label}):/*#__PURE__*/_jsx(Link,{to:crumb.path,className:\"text-sm font-medium text-gray-500 hover:text-reedsoft-primary transition-colors\",children:crumb.label})]},crumb.path))})})})]})});};export default Navigation;", "map": {"version": 3, "names": ["React", "Link", "useLocation", "jsx", "_jsx", "jsxs", "_jsxs", "Navigation", "location", "navItems", "path", "label", "getBreadcrumbs", "pathname", "breadcrumbs", "startsWith", "push", "isHomePage", "className", "children", "to", "map", "item", "concat", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "length", "crumb", "index", "fillRule", "clipRule"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/components/Navigation.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport { NavItem, BreadcrumbItem } from '../types';\n\nconst Navigation: React.FC = () => {\n  const location = useLocation();\n\n  const navItems: NavItem[] = [\n    { path: '/', label: 'Home' },\n    { path: '/japan', label: 'Japan Plan' },\n  ];\n\n  const getBreadcrumbs = (pathname: string): BreadcrumbItem[] => {\n    const breadcrumbs: BreadcrumbItem[] = [\n      { path: '/', label: 'Reedsoft' }\n    ];\n\n    if (pathname.startsWith('/japan')) {\n      breadcrumbs.push({ path: '/japan', label: 'Japan Plan' });\n\n      if (pathname === '/japan/pillar-1') {\n        breadcrumbs.push({ path: '/japan/pillar-1', label: 'Physical Fitness' });\n      } else if (pathname === '/japan/pillar-2') {\n        breadcrumbs.push({ path: '/japan/pillar-2', label: 'Japanese Language' });\n      } else if (pathname === '/japan/pillar-3') {\n        breadcrumbs.push({ path: '/japan/pillar-3', label: 'Practical Skills' });\n      } else if (pathname === '/japan/pillar-4') {\n        breadcrumbs.push({ path: '/japan/pillar-4', label: 'Cognitive Fitness' });\n      }\n    }\n\n    return breadcrumbs;\n  };\n\n  const breadcrumbs = getBreadcrumbs(location.pathname);\n  const isHomePage = location.pathname === '/';\n\n  return (\n    <nav className=\"bg-white shadow-lg border-b border-gray-200\">\n      <div className=\"container mx-auto px-4\">\n        {/* Main Navigation */}\n        <div className=\"flex items-center justify-between h-16\">\n          <div className=\"flex items-center space-x-8\">\n            <Link \n              to=\"/\" \n              className=\"text-2xl font-black text-reedsoft-primary hover:text-reedsoft-secondary transition-colors\"\n            >\n              REEDSOFT\n            </Link>\n            \n            <div className=\"hidden md:flex space-x-6\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.path}\n                  to={item.path}\n                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${\n                    location.pathname === item.path\n                      ? 'text-reedsoft-primary bg-reedsoft-light'\n                      : 'text-gray-600 hover:text-reedsoft-primary hover:bg-gray-100'\n                  }`}\n                >\n                  {item.label}\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button className=\"text-gray-600 hover:text-reedsoft-primary\">\n              <svg className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Breadcrumbs */}\n        {!isHomePage && breadcrumbs.length > 1 && (\n          <div className=\"py-3 border-t border-gray-100\">\n            <nav className=\"flex\" aria-label=\"Breadcrumb\">\n              <ol className=\"flex items-center space-x-2\">\n                {breadcrumbs.map((crumb, index) => (\n                  <li key={crumb.path} className=\"flex items-center\">\n                    {index > 0 && (\n                      <svg\n                        className=\"flex-shrink-0 h-4 w-4 text-gray-400 mx-2\"\n                        fill=\"currentColor\"\n                        viewBox=\"0 0 20 20\"\n                      >\n                        <path\n                          fillRule=\"evenodd\"\n                          d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\"\n                          clipRule=\"evenodd\"\n                        />\n                      </svg>\n                    )}\n                    {index === breadcrumbs.length - 1 ? (\n                      <span className=\"text-sm font-medium text-reedsoft-primary\">\n                        {crumb.label}\n                      </span>\n                    ) : (\n                      <Link\n                        to={crumb.path}\n                        className=\"text-sm font-medium text-gray-500 hover:text-reedsoft-primary transition-colors\"\n                      >\n                        {crumb.label}\n                      </Link>\n                    )}\n                  </li>\n                ))}\n              </ol>\n            </nav>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAGrD,KAAM,CAAAC,UAAoB,CAAGA,CAAA,GAAM,CACjC,KAAM,CAAAC,QAAQ,CAAGN,WAAW,CAAC,CAAC,CAE9B,KAAM,CAAAO,QAAmB,CAAG,CAC1B,CAAEC,IAAI,CAAE,GAAG,CAAEC,KAAK,CAAE,MAAO,CAAC,CAC5B,CAAED,IAAI,CAAE,QAAQ,CAAEC,KAAK,CAAE,YAAa,CAAC,CACxC,CAED,KAAM,CAAAC,cAAc,CAAIC,QAAgB,EAAuB,CAC7D,KAAM,CAAAC,WAA6B,CAAG,CACpC,CAAEJ,IAAI,CAAE,GAAG,CAAEC,KAAK,CAAE,UAAW,CAAC,CACjC,CAED,GAAIE,QAAQ,CAACE,UAAU,CAAC,QAAQ,CAAC,CAAE,CACjCD,WAAW,CAACE,IAAI,CAAC,CAAEN,IAAI,CAAE,QAAQ,CAAEC,KAAK,CAAE,YAAa,CAAC,CAAC,CAEzD,GAAIE,QAAQ,GAAK,iBAAiB,CAAE,CAClCC,WAAW,CAACE,IAAI,CAAC,CAAEN,IAAI,CAAE,iBAAiB,CAAEC,KAAK,CAAE,kBAAmB,CAAC,CAAC,CAC1E,CAAC,IAAM,IAAIE,QAAQ,GAAK,iBAAiB,CAAE,CACzCC,WAAW,CAACE,IAAI,CAAC,CAAEN,IAAI,CAAE,iBAAiB,CAAEC,KAAK,CAAE,mBAAoB,CAAC,CAAC,CAC3E,CAAC,IAAM,IAAIE,QAAQ,GAAK,iBAAiB,CAAE,CACzCC,WAAW,CAACE,IAAI,CAAC,CAAEN,IAAI,CAAE,iBAAiB,CAAEC,KAAK,CAAE,kBAAmB,CAAC,CAAC,CAC1E,CAAC,IAAM,IAAIE,QAAQ,GAAK,iBAAiB,CAAE,CACzCC,WAAW,CAACE,IAAI,CAAC,CAAEN,IAAI,CAAE,iBAAiB,CAAEC,KAAK,CAAE,mBAAoB,CAAC,CAAC,CAC3E,CACF,CAEA,MAAO,CAAAG,WAAW,CACpB,CAAC,CAED,KAAM,CAAAA,WAAW,CAAGF,cAAc,CAACJ,QAAQ,CAACK,QAAQ,CAAC,CACrD,KAAM,CAAAI,UAAU,CAAGT,QAAQ,CAACK,QAAQ,GAAK,GAAG,CAE5C,mBACET,IAAA,QAAKc,SAAS,CAAC,6CAA6C,CAAAC,QAAA,cAC1Db,KAAA,QAAKY,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eAErCb,KAAA,QAAKY,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDb,KAAA,QAAKY,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1Cf,IAAA,CAACH,IAAI,EACHmB,EAAE,CAAC,GAAG,CACNF,SAAS,CAAC,2FAA2F,CAAAC,QAAA,CACtG,UAED,CAAM,CAAC,cAEPf,IAAA,QAAKc,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CACtCV,QAAQ,CAACY,GAAG,CAAEC,IAAI,eACjBlB,IAAA,CAACH,IAAI,EAEHmB,EAAE,CAAEE,IAAI,CAACZ,IAAK,CACdQ,SAAS,+DAAAK,MAAA,CACPf,QAAQ,CAACK,QAAQ,GAAKS,IAAI,CAACZ,IAAI,CAC3B,yCAAyC,CACzC,6DAA6D,CAChE,CAAAS,QAAA,CAEFG,IAAI,CAACX,KAAK,EARNW,IAAI,CAACZ,IASN,CACP,CAAC,CACC,CAAC,EACH,CAAC,cAGNN,IAAA,QAAKc,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBf,IAAA,WAAQc,SAAS,CAAC,2CAA2C,CAAAC,QAAA,cAC3Df,IAAA,QAAKc,SAAS,CAAC,SAAS,CAACM,IAAI,CAAC,MAAM,CAACC,OAAO,CAAC,WAAW,CAACC,MAAM,CAAC,cAAc,CAAAP,QAAA,cAC5Ef,IAAA,SAAMuB,aAAa,CAAC,OAAO,CAACC,cAAc,CAAC,OAAO,CAACC,WAAW,CAAE,CAAE,CAACC,CAAC,CAAC,yBAAyB,CAAE,CAAC,CAC9F,CAAC,CACA,CAAC,CACN,CAAC,EACH,CAAC,CAGL,CAACb,UAAU,EAAIH,WAAW,CAACiB,MAAM,CAAG,CAAC,eACpC3B,IAAA,QAAKc,SAAS,CAAC,+BAA+B,CAAAC,QAAA,cAC5Cf,IAAA,QAAKc,SAAS,CAAC,MAAM,CAAC,aAAW,YAAY,CAAAC,QAAA,cAC3Cf,IAAA,OAAIc,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CACxCL,WAAW,CAACO,GAAG,CAAC,CAACW,KAAK,CAAEC,KAAK,gBAC5B3B,KAAA,OAAqBY,SAAS,CAAC,mBAAmB,CAAAC,QAAA,EAC/Cc,KAAK,CAAG,CAAC,eACR7B,IAAA,QACEc,SAAS,CAAC,0CAA0C,CACpDM,IAAI,CAAC,cAAc,CACnBC,OAAO,CAAC,WAAW,CAAAN,QAAA,cAEnBf,IAAA,SACE8B,QAAQ,CAAC,SAAS,CAClBJ,CAAC,CAAC,oHAAoH,CACtHK,QAAQ,CAAC,SAAS,CACnB,CAAC,CACC,CACN,CACAF,KAAK,GAAKnB,WAAW,CAACiB,MAAM,CAAG,CAAC,cAC/B3B,IAAA,SAAMc,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CACxDa,KAAK,CAACrB,KAAK,CACR,CAAC,cAEPP,IAAA,CAACH,IAAI,EACHmB,EAAE,CAAEY,KAAK,CAACtB,IAAK,CACfQ,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAE1Fa,KAAK,CAACrB,KAAK,CACR,CACP,GAzBMqB,KAAK,CAACtB,IA0BX,CACL,CAAC,CACA,CAAC,CACF,CAAC,CACH,CACN,EACE,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}