import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { NavItem, BreadcrumbItem } from '../types';

const Navigation: React.FC = () => {
  const location = useLocation();

  const navItems: NavItem[] = [
    { path: '/', label: 'Home' },
    { path: '/japan', label: 'Japan Plan' },
  ];

  const getBreadcrumbs = (pathname: string): BreadcrumbItem[] => {
    const breadcrumbs: BreadcrumbItem[] = [
      { path: '/', label: 'Reedsoft' }
    ];

    if (pathname.startsWith('/japan')) {
      breadcrumbs.push({ path: '/japan', label: 'Japan Plan' });

      if (pathname === '/japan/pillar-1') {
        breadcrumbs.push({ path: '/japan/pillar-1', label: 'Physical Fitness' });
      } else if (pathname === '/japan/pillar-2') {
        breadcrumbs.push({ path: '/japan/pillar-2', label: 'Japanese Language' });
      } else if (pathname === '/japan/pillar-3') {
        breadcrumbs.push({ path: '/japan/pillar-3', label: 'Practical Skills' });
      } else if (pathname === '/japan/pillar-4') {
        breadcrumbs.push({ path: '/japan/pillar-4', label: 'Cognitive Fitness' });
      }
    }

    return breadcrumbs;
  };

  const breadcrumbs = getBreadcrumbs(location.pathname);
  const isHomePage = location.pathname === '/';

  return (
    <nav className="bg-white shadow-lg border-b border-gray-200" role="navigation" aria-label="Main navigation">
      <div className="container mx-auto px-4">
        {/* Main Navigation */}
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center space-x-8">
            <Link
              to="/"
              className="text-2xl font-black text-reedsoft-primary hover:text-reedsoft-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-reedsoft-primary focus:ring-offset-2 rounded-md"
              aria-label="Reedsoft homepage"
            >
              REEDSOFT
            </Link>

            <div className="hidden md:flex space-x-6" role="menubar">
              {navItems.map((item) => (
                <Link
                  key={item.path}
                  to={item.path}
                  role="menuitem"
                  aria-current={location.pathname === item.path ? 'page' : undefined}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-reedsoft-primary focus:ring-offset-2 ${
                    location.pathname === item.path
                      ? 'text-reedsoft-primary bg-reedsoft-light'
                      : 'text-gray-600 hover:text-reedsoft-primary hover:bg-gray-100'
                  }`}
                >
                  {item.label}
                </Link>
              ))}
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              className="text-gray-600 hover:text-reedsoft-primary focus:outline-none focus:ring-2 focus:ring-reedsoft-primary focus:ring-offset-2 rounded-md p-2"
              aria-label="Open mobile menu"
              aria-expanded="false"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        {/* Breadcrumbs */}
        {!isHomePage && breadcrumbs.length > 1 && (
          <div className="py-3 border-t border-gray-100">
            <nav className="flex" aria-label="Breadcrumb navigation">
              <ol className="flex items-center space-x-2">
                {breadcrumbs.map((crumb, index) => (
                  <li key={crumb.path} className="flex items-center">
                    {index > 0 && (
                      <svg
                        className="flex-shrink-0 h-4 w-4 text-gray-400 mx-2"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    )}
                    {index === breadcrumbs.length - 1 ? (
                      <span
                        className="text-sm font-medium text-reedsoft-primary"
                        aria-current="page"
                      >
                        {crumb.label}
                      </span>
                    ) : (
                      <Link
                        to={crumb.path}
                        className="text-sm font-medium text-gray-500 hover:text-reedsoft-primary transition-colors focus:outline-none focus:ring-2 focus:ring-reedsoft-primary focus:ring-offset-2 rounded-md"
                        aria-label={`Navigate to ${crumb.label}`}
                      >
                        {crumb.label}
                      </Link>
                    )}
                  </li>
                ))}
              </ol>
            </nav>
          </div>
        )}
      </div>
    </nav>
  );
};

export default Navigation;
