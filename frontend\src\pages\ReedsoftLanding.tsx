import React from 'react';
import { Link } from 'react-router-dom';

const ReedsoftLanding: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-reedsoft-light to-white">
      {/* Hero Section */}
      <section className="pt-20 pb-16 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-5xl md:text-7xl font-black text-reedsoft-primary mb-6 tracking-tight">
            REEDSOFT
          </h1>
          <p className="text-xl md:text-2xl text-reedsoft-secondary mb-8 max-w-3xl mx-auto">
            Building innovative solutions for personal and professional development
          </p>
          <p className="text-lg text-gray-600 mb-12 max-w-2xl mx-auto">
            We create comprehensive digital experiences that transform ambitious goals into structured, achievable plans.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/japan"
              className="bg-reedsoft-primary text-white px-8 py-4 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors shadow-lg"
            >
              Explore Japan Project
            </Link>
            <button className="bg-white text-reedsoft-primary px-8 py-4 rounded-lg font-semibold border-2 border-reedsoft-primary hover:bg-reedsoft-primary hover:text-white transition-colors">
              Learn More
            </button>
          </div>
        </div>
      </section>

      {/* Featured Project Section */}
      <section className="py-16 px-4 bg-white">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-reedsoft-primary mb-12">
            Featured Project
          </h2>
          
          <div className="bg-gradient-to-r from-reedsoft-primary to-reedsoft-secondary rounded-2xl p-8 md:p-12 text-white">
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <div>
                <h3 className="text-3xl md:text-4xl font-bold mb-4">
                  Japan Work Preparation Plan
                </h3>
                <p className="text-lg mb-6 opacity-90">
                  A comprehensive 4-month training program featuring four core pillars: Physical Fitness, Japanese Language, Practical Skills, and Cognitive Fitness.
                </p>
                <ul className="space-y-2 mb-8">
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-reedsoft-light rounded-full mr-3"></span>
                    Interactive progress tracking
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-reedsoft-light rounded-full mr-3"></span>
                    Data visualizations with Chart.js
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-reedsoft-light rounded-full mr-3"></span>
                    Structured daily schedules
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-reedsoft-light rounded-full mr-3"></span>
                    Responsive design for all devices
                  </li>
                </ul>
                <Link
                  to="/japan"
                  className="inline-block bg-white text-reedsoft-primary px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-light transition-colors"
                >
                  View Project →
                </Link>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-white bg-opacity-10 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold mb-1">4</div>
                  <div className="text-sm opacity-80">Core Pillars</div>
                </div>
                <div className="bg-white bg-opacity-10 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold mb-1">16</div>
                  <div className="text-sm opacity-80">Weeks Program</div>
                </div>
                <div className="bg-white bg-opacity-10 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold mb-1">45+</div>
                  <div className="text-sm opacity-80">Hours/Week</div>
                </div>
                <div className="bg-white bg-opacity-10 rounded-lg p-4 text-center">
                  <div className="text-2xl font-bold mb-1">100%</div>
                  <div className="text-sm opacity-80">Commitment</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Technology Stack Section */}
      <section className="py-16 px-4 bg-gray-50">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-reedsoft-primary mb-12">
            Built with Modern Technology
          </h2>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-reedsoft-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold text-xl">R</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">React & TypeScript</h3>
              <p className="text-gray-600">Modern frontend with type safety and component-based architecture</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-reedsoft-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold text-xl">T</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Tailwind CSS</h3>
              <p className="text-gray-600">Utility-first CSS framework for rapid UI development</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-reedsoft-accent rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold text-xl">C</span>
              </div>
              <h3 className="text-xl font-semibold mb-2">Chart.js</h3>
              <p className="text-gray-600">Interactive data visualizations and progress tracking</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-reedsoft-primary text-white py-12 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <h3 className="text-2xl font-bold mb-4">Ready to Transform Your Goals?</h3>
          <p className="text-reedsoft-light mb-8 max-w-2xl mx-auto">
            Join the journey of structured personal development with data-driven insights and comprehensive planning.
          </p>
          <Link
            to="/japan"
            className="inline-block bg-white text-reedsoft-primary px-8 py-4 rounded-lg font-semibold hover:bg-reedsoft-light transition-colors"
          >
            Start Your Journey
          </Link>
          
          <div className="mt-12 pt-8 border-t border-reedsoft-secondary">
            <p className="text-reedsoft-light">
              © 2025 Reedsoft. Building innovative solutions for personal and professional development.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default ReedsoftLanding;
