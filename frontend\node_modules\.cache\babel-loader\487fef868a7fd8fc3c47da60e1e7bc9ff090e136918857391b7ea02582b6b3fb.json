{"ast": null, "code": "/**\n * Converts 24-hour time format to 12-hour AM/PM format\n * @param time24 - Time in 24-hour format (e.g., \"06:00\", \"15:30\")\n * @returns FormattedTime object with original, formatted, and period\n */\nexport const convertTo12Hour = time24 => {\n  const [hours, minutes] = time24.split(':').map(Number);\n  if (isNaN(hours) || isNaN(minutes)) {\n    return {\n      original: time24,\n      formatted: time24,\n      period: 'AM'\n    };\n  }\n  const period = hours >= 12 ? 'PM' : 'AM';\n  const hours12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;\n  const formattedMinutes = minutes.toString().padStart(2, '0');\n  return {\n    original: time24,\n    formatted: `${hours12}:${formattedMinutes} ${period}`,\n    period\n  };\n};\n\n/**\n * Formats a time range from 24-hour to 12-hour format\n * @param timeRange - Time range in format \"06:00 - 07:30\"\n * @returns Formatted time range string\n */\nexport const formatTimeRange = timeRange => {\n  const rangeParts = timeRange.split(' - ');\n  if (rangeParts.length !== 2) {\n    return timeRange; // Return original if not a valid range\n  }\n  const startTime = convertTo12Hour(rangeParts[0].trim());\n  const endTime = convertTo12Hour(rangeParts[1].trim());\n  return `${startTime.formatted} - ${endTime.formatted}`;\n};\n\n/**\n * Extracts and formats time from a string that contains time information\n * @param text - Text containing time (e.g., \"06:00 - 07:30 | 💪 Pillar 1: Physical Fitness\")\n * @returns Formatted text with converted time\n */\nexport const formatTimeInText = text => {\n  // Regex to match time patterns like \"06:00 - 07:30\" or \"06:00\"\n  const timeRangeRegex = /(\\d{2}:\\d{2})\\s*-\\s*(\\d{2}:\\d{2})/g;\n  const singleTimeRegex = /(\\d{2}:\\d{2})/g;\n\n  // First, handle time ranges\n  let formattedText = text.replace(timeRangeRegex, (match, start, end) => {\n    const startFormatted = convertTo12Hour(start);\n    const endFormatted = convertTo12Hour(end);\n    return `${startFormatted.formatted} - ${endFormatted.formatted}`;\n  });\n\n  // Then handle single times that weren't part of ranges\n  formattedText = formattedText.replace(singleTimeRegex, match => {\n    // Check if this time is already formatted (contains AM/PM)\n    if (formattedText.includes(`${match} AM`) || formattedText.includes(`${match} PM`)) {\n      return match; // Already formatted, don't change\n    }\n    const formatted = convertTo12Hour(match);\n    return formatted.formatted;\n  });\n  return formattedText;\n};\n\n/**\n * Gets the current time in 12-hour format\n * @returns Current time string in 12-hour format\n */\nexport const getCurrentTime12Hour = () => {\n  const now = new Date();\n  const hours = now.getHours();\n  const minutes = now.getMinutes();\n  const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;\n  return convertTo12Hour(timeString).formatted;\n};\n\n/**\n * Checks if a given time is in the morning (AM)\n * @param time24 - Time in 24-hour format\n * @returns True if the time is AM, false if PM\n */\nexport const isAM = time24 => {\n  const formatted = convertTo12Hour(time24);\n  return formatted.period === 'AM';\n};\n\n/**\n * Sorts an array of time strings in chronological order\n * @param times - Array of time strings in 24-hour format\n * @returns Sorted array of times\n */\nexport const sortTimes = times => {\n  return times.sort((a, b) => {\n    const [hoursA, minutesA] = a.split(':').map(Number);\n    const [hoursB, minutesB] = b.split(':').map(Number);\n    const totalMinutesA = hoursA * 60 + minutesA;\n    const totalMinutesB = hoursB * 60 + minutesB;\n    return totalMinutesA - totalMinutesB;\n  });\n};", "map": {"version": 3, "names": ["convertTo12Hour", "time24", "hours", "minutes", "split", "map", "Number", "isNaN", "original", "formatted", "period", "hours12", "formattedMinutes", "toString", "padStart", "formatTimeRange", "timeRange", "rangeParts", "length", "startTime", "trim", "endTime", "formatTimeInText", "text", "timeRangeRegex", "singleTimeRegex", "formattedText", "replace", "match", "start", "end", "startFormatted", "endFormatted", "includes", "getCurrentTime12Hour", "now", "Date", "getHours", "getMinutes", "timeString", "isAM", "sortTimes", "times", "sort", "a", "b", "hoursA", "minutesA", "hoursB", "minutesB", "totalMinutesA", "totalMinutesB"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/utils/timeFormat.ts"], "sourcesContent": ["import { FormattedTime } from '../types';\n\n/**\n * Converts 24-hour time format to 12-hour AM/PM format\n * @param time24 - Time in 24-hour format (e.g., \"06:00\", \"15:30\")\n * @returns FormattedTime object with original, formatted, and period\n */\nexport const convertTo12Hour = (time24: string): FormattedTime => {\n  const [hours, minutes] = time24.split(':').map(Number);\n  \n  if (isNaN(hours) || isNaN(minutes)) {\n    return {\n      original: time24,\n      formatted: time24,\n      period: 'AM'\n    };\n  }\n  \n  const period: 'AM' | 'PM' = hours >= 12 ? 'PM' : 'AM';\n  const hours12 = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;\n  const formattedMinutes = minutes.toString().padStart(2, '0');\n  \n  return {\n    original: time24,\n    formatted: `${hours12}:${formattedMinutes} ${period}`,\n    period\n  };\n};\n\n/**\n * Formats a time range from 24-hour to 12-hour format\n * @param timeRange - Time range in format \"06:00 - 07:30\"\n * @returns Formatted time range string\n */\nexport const formatTimeRange = (timeRange: string): string => {\n  const rangeParts = timeRange.split(' - ');\n  if (rangeParts.length !== 2) {\n    return timeRange; // Return original if not a valid range\n  }\n  \n  const startTime = convertTo12Hour(rangeParts[0].trim());\n  const endTime = convertTo12Hour(rangeParts[1].trim());\n  \n  return `${startTime.formatted} - ${endTime.formatted}`;\n};\n\n/**\n * Extracts and formats time from a string that contains time information\n * @param text - Text containing time (e.g., \"06:00 - 07:30 | 💪 Pillar 1: Physical Fitness\")\n * @returns Formatted text with converted time\n */\nexport const formatTimeInText = (text: string): string => {\n  // Regex to match time patterns like \"06:00 - 07:30\" or \"06:00\"\n  const timeRangeRegex = /(\\d{2}:\\d{2})\\s*-\\s*(\\d{2}:\\d{2})/g;\n  const singleTimeRegex = /(\\d{2}:\\d{2})/g;\n  \n  // First, handle time ranges\n  let formattedText = text.replace(timeRangeRegex, (match, start, end) => {\n    const startFormatted = convertTo12Hour(start);\n    const endFormatted = convertTo12Hour(end);\n    return `${startFormatted.formatted} - ${endFormatted.formatted}`;\n  });\n  \n  // Then handle single times that weren't part of ranges\n  formattedText = formattedText.replace(singleTimeRegex, (match) => {\n    // Check if this time is already formatted (contains AM/PM)\n    if (formattedText.includes(`${match} AM`) || formattedText.includes(`${match} PM`)) {\n      return match; // Already formatted, don't change\n    }\n    const formatted = convertTo12Hour(match);\n    return formatted.formatted;\n  });\n  \n  return formattedText;\n};\n\n/**\n * Gets the current time in 12-hour format\n * @returns Current time string in 12-hour format\n */\nexport const getCurrentTime12Hour = (): string => {\n  const now = new Date();\n  const hours = now.getHours();\n  const minutes = now.getMinutes();\n  const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;\n  return convertTo12Hour(timeString).formatted;\n};\n\n/**\n * Checks if a given time is in the morning (AM)\n * @param time24 - Time in 24-hour format\n * @returns True if the time is AM, false if PM\n */\nexport const isAM = (time24: string): boolean => {\n  const formatted = convertTo12Hour(time24);\n  return formatted.period === 'AM';\n};\n\n/**\n * Sorts an array of time strings in chronological order\n * @param times - Array of time strings in 24-hour format\n * @returns Sorted array of times\n */\nexport const sortTimes = (times: string[]): string[] => {\n  return times.sort((a, b) => {\n    const [hoursA, minutesA] = a.split(':').map(Number);\n    const [hoursB, minutesB] = b.split(':').map(Number);\n    \n    const totalMinutesA = hoursA * 60 + minutesA;\n    const totalMinutesB = hoursB * 60 + minutesB;\n    \n    return totalMinutesA - totalMinutesB;\n  });\n};\n"], "mappings": "AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,eAAe,GAAIC,MAAc,IAAoB;EAChE,MAAM,CAACC,KAAK,EAAEC,OAAO,CAAC,GAAGF,MAAM,CAACG,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;EAEtD,IAAIC,KAAK,CAACL,KAAK,CAAC,IAAIK,KAAK,CAACJ,OAAO,CAAC,EAAE;IAClC,OAAO;MACLK,QAAQ,EAAEP,MAAM;MAChBQ,SAAS,EAAER,MAAM;MACjBS,MAAM,EAAE;IACV,CAAC;EACH;EAEA,MAAMA,MAAmB,GAAGR,KAAK,IAAI,EAAE,GAAG,IAAI,GAAG,IAAI;EACrD,MAAMS,OAAO,GAAGT,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,KAAK,GAAG,EAAE,GAAGA,KAAK,GAAG,EAAE,GAAGA,KAAK;EAClE,MAAMU,gBAAgB,GAAGT,OAAO,CAACU,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;EAE5D,OAAO;IACLN,QAAQ,EAAEP,MAAM;IAChBQ,SAAS,EAAE,GAAGE,OAAO,IAAIC,gBAAgB,IAAIF,MAAM,EAAE;IACrDA;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,eAAe,GAAIC,SAAiB,IAAa;EAC5D,MAAMC,UAAU,GAAGD,SAAS,CAACZ,KAAK,CAAC,KAAK,CAAC;EACzC,IAAIa,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE;IAC3B,OAAOF,SAAS,CAAC,CAAC;EACpB;EAEA,MAAMG,SAAS,GAAGnB,eAAe,CAACiB,UAAU,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC;EACvD,MAAMC,OAAO,GAAGrB,eAAe,CAACiB,UAAU,CAAC,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC,CAAC;EAErD,OAAO,GAAGD,SAAS,CAACV,SAAS,MAAMY,OAAO,CAACZ,SAAS,EAAE;AACxD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMa,gBAAgB,GAAIC,IAAY,IAAa;EACxD;EACA,MAAMC,cAAc,GAAG,oCAAoC;EAC3D,MAAMC,eAAe,GAAG,gBAAgB;;EAExC;EACA,IAAIC,aAAa,GAAGH,IAAI,CAACI,OAAO,CAACH,cAAc,EAAE,CAACI,KAAK,EAAEC,KAAK,EAAEC,GAAG,KAAK;IACtE,MAAMC,cAAc,GAAG/B,eAAe,CAAC6B,KAAK,CAAC;IAC7C,MAAMG,YAAY,GAAGhC,eAAe,CAAC8B,GAAG,CAAC;IACzC,OAAO,GAAGC,cAAc,CAACtB,SAAS,MAAMuB,YAAY,CAACvB,SAAS,EAAE;EAClE,CAAC,CAAC;;EAEF;EACAiB,aAAa,GAAGA,aAAa,CAACC,OAAO,CAACF,eAAe,EAAGG,KAAK,IAAK;IAChE;IACA,IAAIF,aAAa,CAACO,QAAQ,CAAC,GAAGL,KAAK,KAAK,CAAC,IAAIF,aAAa,CAACO,QAAQ,CAAC,GAAGL,KAAK,KAAK,CAAC,EAAE;MAClF,OAAOA,KAAK,CAAC,CAAC;IAChB;IACA,MAAMnB,SAAS,GAAGT,eAAe,CAAC4B,KAAK,CAAC;IACxC,OAAOnB,SAAS,CAACA,SAAS;EAC5B,CAAC,CAAC;EAEF,OAAOiB,aAAa;AACtB,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMQ,oBAAoB,GAAGA,CAAA,KAAc;EAChD,MAAMC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;EACtB,MAAMlC,KAAK,GAAGiC,GAAG,CAACE,QAAQ,CAAC,CAAC;EAC5B,MAAMlC,OAAO,GAAGgC,GAAG,CAACG,UAAU,CAAC,CAAC;EAChC,MAAMC,UAAU,GAAG,GAAGrC,KAAK,CAACW,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIX,OAAO,CAACU,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAChG,OAAOd,eAAe,CAACuC,UAAU,CAAC,CAAC9B,SAAS;AAC9C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM+B,IAAI,GAAIvC,MAAc,IAAc;EAC/C,MAAMQ,SAAS,GAAGT,eAAe,CAACC,MAAM,CAAC;EACzC,OAAOQ,SAAS,CAACC,MAAM,KAAK,IAAI;AAClC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM+B,SAAS,GAAIC,KAAe,IAAe;EACtD,OAAOA,KAAK,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IAC1B,MAAM,CAACC,MAAM,EAAEC,QAAQ,CAAC,GAAGH,CAAC,CAACxC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;IACnD,MAAM,CAAC0C,MAAM,EAAEC,QAAQ,CAAC,GAAGJ,CAAC,CAACzC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;IAEnD,MAAM4C,aAAa,GAAGJ,MAAM,GAAG,EAAE,GAAGC,QAAQ;IAC5C,MAAMI,aAAa,GAAGH,MAAM,GAAG,EAAE,GAAGC,QAAQ;IAE5C,OAAOC,aAAa,GAAGC,aAAa;EACtC,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}