{"ast": null, "code": "/**\n * react-router v7.9.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport { ENABLE_DEV_WARNINGS, ErrorResponseImpl, FrameworkContext, NO_BODY_STATUS_CODES, Outlet, RSCRouterContext, RemixErrorBoundary, RouterContextProvider, RouterProvider, SINGLE_FETCH_REDIRECT_STATUS, SingleFetchRedirectSymbol, StaticRouterProvider, StreamTransfer, convertRoutesToDataRoutes, createBrowserHistory, createContext, createMemoryRouter, createRequestInit, createRouter, createServerRoutes, createStaticHandler, createStaticRouter, decodeViaTurboStream, encode, escapeHtml, getManifestPath, getSingleFetchDataStrategyImpl, getStaticContextFromError, invariant, isDataWithResponseInit, isMutationMethod, isRedirectResponse, isRedirectStatusCode, isResponse, isRouteErrorResponse, matchRoutes, noActionDefinedError, redirect, redirectDocument, replace, setIsHydrated, shouldHydrateRouteLoader, singleFetchUrl, stripBasename, stripIndexParam, useRouteError, warnOnce, withComponentProps, withErrorBoundaryProps, withHydrateFallbackProps } from \"./chunk-S5YDGZLY.mjs\";\n\n// lib/dom/ssr/server.tsx\nimport * as React from \"react\";\nfunction ServerRouter({\n  context,\n  url,\n  nonce\n}) {\n  if (typeof url === \"string\") {\n    url = new URL(url);\n  }\n  let {\n    manifest,\n    routeModules,\n    criticalCss,\n    serverHandoffString\n  } = context;\n  let routes = createServerRoutes(manifest.routes, routeModules, context.future, context.isSpaMode);\n  context.staticHandlerContext.loaderData = {\n    ...context.staticHandlerContext.loaderData\n  };\n  for (let match of context.staticHandlerContext.matches) {\n    let routeId = match.route.id;\n    let route = routeModules[routeId];\n    let manifestRoute = context.manifest.routes[routeId];\n    if (route && manifestRoute && shouldHydrateRouteLoader(routeId, route.clientLoader, manifestRoute.hasLoader, context.isSpaMode) && (route.HydrateFallback || !manifestRoute.hasLoader)) {\n      delete context.staticHandlerContext.loaderData[routeId];\n    }\n  }\n  let router = createStaticRouter(routes, context.staticHandlerContext);\n  return /* @__PURE__ */React.createElement(React.Fragment, null, /* @__PURE__ */React.createElement(FrameworkContext.Provider, {\n    value: {\n      manifest,\n      routeModules,\n      criticalCss,\n      serverHandoffString,\n      future: context.future,\n      ssr: context.ssr,\n      isSpaMode: context.isSpaMode,\n      routeDiscovery: context.routeDiscovery,\n      serializeError: context.serializeError,\n      renderMeta: context.renderMeta\n    }\n  }, /* @__PURE__ */React.createElement(RemixErrorBoundary, {\n    location: router.state.location\n  }, /* @__PURE__ */React.createElement(StaticRouterProvider, {\n    router,\n    context: context.staticHandlerContext,\n    hydrate: false\n  }))), context.serverHandoffStream ? /* @__PURE__ */React.createElement(React.Suspense, null, /* @__PURE__ */React.createElement(StreamTransfer, {\n    context,\n    identifier: 0,\n    reader: context.serverHandoffStream.getReader(),\n    textDecoder: new TextDecoder(),\n    nonce\n  })) : null);\n}\n\n// lib/dom/ssr/routes-test-stub.tsx\nimport * as React2 from \"react\";\nfunction createRoutesStub(routes, _context) {\n  return function RoutesTestStub({\n    initialEntries,\n    initialIndex,\n    hydrationData,\n    future\n  }) {\n    let routerRef = React2.useRef();\n    let frameworkContextRef = React2.useRef();\n    if (routerRef.current == null) {\n      frameworkContextRef.current = {\n        future: {\n          unstable_subResourceIntegrity: future?.unstable_subResourceIntegrity === true,\n          v8_middleware: future?.v8_middleware === true\n        },\n        manifest: {\n          routes: {},\n          entry: {\n            imports: [],\n            module: \"\"\n          },\n          url: \"\",\n          version: \"\"\n        },\n        routeModules: {},\n        ssr: false,\n        isSpaMode: false,\n        routeDiscovery: {\n          mode: \"lazy\",\n          manifestPath: \"/__manifest\"\n        }\n      };\n      let patched = processRoutes(\n      // @ts-expect-error `StubRouteObject` is stricter about `loader`/`action`\n      // types compared to `AgnosticRouteObject`\n      convertRoutesToDataRoutes(routes, r => r), _context !== void 0 ? _context : future?.v8_middleware ? new RouterContextProvider() : {}, frameworkContextRef.current.manifest, frameworkContextRef.current.routeModules);\n      routerRef.current = createMemoryRouter(patched, {\n        initialEntries,\n        initialIndex,\n        hydrationData\n      });\n    }\n    return /* @__PURE__ */React2.createElement(FrameworkContext.Provider, {\n      value: frameworkContextRef.current\n    }, /* @__PURE__ */React2.createElement(RouterProvider, {\n      router: routerRef.current\n    }));\n  };\n}\nfunction processRoutes(routes, context, manifest, routeModules, parentId) {\n  return routes.map(route => {\n    if (!route.id) {\n      throw new Error(\"Expected a route.id in react-router processRoutes() function\");\n    }\n    let newRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      Component: route.Component ? withComponentProps(route.Component) : void 0,\n      HydrateFallback: route.HydrateFallback ? withHydrateFallbackProps(route.HydrateFallback) : void 0,\n      ErrorBoundary: route.ErrorBoundary ? withErrorBoundaryProps(route.ErrorBoundary) : void 0,\n      action: route.action ? args => route.action({\n        ...args,\n        context\n      }) : void 0,\n      loader: route.loader ? args => route.loader({\n        ...args,\n        context\n      }) : void 0,\n      handle: route.handle,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    let entryRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      parentId,\n      hasAction: route.action != null,\n      hasLoader: route.loader != null,\n      // When testing routes, you should be stubbing loader/action/middleware,\n      // not trying to re-implement the full loader/clientLoader/SSR/hydration\n      // flow. That is better tested via E2E tests.\n      hasClientAction: false,\n      hasClientLoader: false,\n      hasClientMiddleware: false,\n      hasErrorBoundary: route.ErrorBoundary != null,\n      // any need for these?\n      module: \"build/stub-path-to-module.js\",\n      clientActionModule: void 0,\n      clientLoaderModule: void 0,\n      clientMiddlewareModule: void 0,\n      hydrateFallbackModule: void 0\n    };\n    manifest.routes[newRoute.id] = entryRoute;\n    routeModules[route.id] = {\n      default: newRoute.Component || Outlet,\n      ErrorBoundary: newRoute.ErrorBoundary || void 0,\n      handle: route.handle,\n      links: route.links,\n      meta: route.meta,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    if (route.children) {\n      newRoute.children = processRoutes(route.children, context, manifest, routeModules, newRoute.id);\n    }\n    return newRoute;\n  });\n}\n\n// lib/server-runtime/cookies.ts\nimport { parse, serialize } from \"cookie\";\n\n// lib/server-runtime/crypto.ts\nvar encoder = /* @__PURE__ */new TextEncoder();\nvar sign = async (value, secret) => {\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"sign\"]);\n  let signature = await crypto.subtle.sign(\"HMAC\", key, data2);\n  let hash = btoa(String.fromCharCode(...new Uint8Array(signature))).replace(/=+$/, \"\");\n  return value + \".\" + hash;\n};\nvar unsign = async (cookie, secret) => {\n  let index = cookie.lastIndexOf(\".\");\n  let value = cookie.slice(0, index);\n  let hash = cookie.slice(index + 1);\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"verify\"]);\n  try {\n    let signature = byteStringToUint8Array(atob(hash));\n    let valid = await crypto.subtle.verify(\"HMAC\", key, signature, data2);\n    return valid ? value : false;\n  } catch (error) {\n    return false;\n  }\n};\nvar createKey = async (secret, usages) => crypto.subtle.importKey(\"raw\", encoder.encode(secret), {\n  name: \"HMAC\",\n  hash: \"SHA-256\"\n}, false, usages);\nfunction byteStringToUint8Array(byteString) {\n  let array = new Uint8Array(byteString.length);\n  for (let i = 0; i < byteString.length; i++) {\n    array[i] = byteString.charCodeAt(i);\n  }\n  return array;\n}\n\n// lib/server-runtime/cookies.ts\nvar createCookie = (name, cookieOptions = {}) => {\n  let {\n    secrets = [],\n    ...options\n  } = {\n    path: \"/\",\n    sameSite: \"lax\",\n    ...cookieOptions\n  };\n  warnOnceAboutExpiresCookie(name, options.expires);\n  return {\n    get name() {\n      return name;\n    },\n    get isSigned() {\n      return secrets.length > 0;\n    },\n    get expires() {\n      return typeof options.maxAge !== \"undefined\" ? new Date(Date.now() + options.maxAge * 1e3) : options.expires;\n    },\n    async parse(cookieHeader, parseOptions) {\n      if (!cookieHeader) return null;\n      let cookies = parse(cookieHeader, {\n        ...options,\n        ...parseOptions\n      });\n      if (name in cookies) {\n        let value = cookies[name];\n        if (typeof value === \"string\" && value !== \"\") {\n          let decoded = await decodeCookieValue(value, secrets);\n          return decoded;\n        } else {\n          return \"\";\n        }\n      } else {\n        return null;\n      }\n    },\n    async serialize(value, serializeOptions) {\n      return serialize(name, value === \"\" ? \"\" : await encodeCookieValue(value, secrets), {\n        ...options,\n        ...serializeOptions\n      });\n    }\n  };\n};\nvar isCookie = object => {\n  return object != null && typeof object.name === \"string\" && typeof object.isSigned === \"boolean\" && typeof object.parse === \"function\" && typeof object.serialize === \"function\";\n};\nasync function encodeCookieValue(value, secrets) {\n  let encoded = encodeData(value);\n  if (secrets.length > 0) {\n    encoded = await sign(encoded, secrets[0]);\n  }\n  return encoded;\n}\nasync function decodeCookieValue(value, secrets) {\n  if (secrets.length > 0) {\n    for (let secret of secrets) {\n      let unsignedValue = await unsign(value, secret);\n      if (unsignedValue !== false) {\n        return decodeData(unsignedValue);\n      }\n    }\n    return null;\n  }\n  return decodeData(value);\n}\nfunction encodeData(value) {\n  return btoa(myUnescape(encodeURIComponent(JSON.stringify(value))));\n}\nfunction decodeData(value) {\n  try {\n    return JSON.parse(decodeURIComponent(myEscape(atob(value))));\n  } catch (error) {\n    return {};\n  }\n}\nfunction myEscape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, code;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (/[\\w*+\\-./@]/.exec(chr)) {\n      result += chr;\n    } else {\n      code = chr.charCodeAt(0);\n      if (code < 256) {\n        result += \"%\" + hex(code, 2);\n      } else {\n        result += \"%u\" + hex(code, 4).toUpperCase();\n      }\n    }\n  }\n  return result;\n}\nfunction hex(code, length) {\n  let result = code.toString(16);\n  while (result.length < length) result = \"0\" + result;\n  return result;\n}\nfunction myUnescape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, part;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (chr === \"%\") {\n      if (str.charAt(index) === \"u\") {\n        part = str.slice(index + 1, index + 5);\n        if (/^[\\da-f]{4}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 5;\n          continue;\n        }\n      } else {\n        part = str.slice(index, index + 2);\n        if (/^[\\da-f]{2}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 2;\n          continue;\n        }\n      }\n    }\n    result += chr;\n  }\n  return result;\n}\nfunction warnOnceAboutExpiresCookie(name, expires) {\n  warnOnce(!expires, `The \"${name}\" cookie has an \"expires\" property set. This will cause the expires value to not be updated when the session is committed. Instead, you should set the expires value when serializing the cookie. You can use \\`commitSession(session, { expires })\\` if using a session storage object, or \\`cookie.serialize(\"value\", { expires })\\` if you're using the cookie directly.`);\n}\n\n// lib/server-runtime/entry.ts\nfunction createEntryRouteModules(manifest) {\n  return Object.keys(manifest).reduce((memo, routeId) => {\n    let route = manifest[routeId];\n    if (route) {\n      memo[routeId] = route.module;\n    }\n    return memo;\n  }, {});\n}\n\n// lib/server-runtime/mode.ts\nvar ServerMode = /* @__PURE__ */(ServerMode2 => {\n  ServerMode2[\"Development\"] = \"development\";\n  ServerMode2[\"Production\"] = \"production\";\n  ServerMode2[\"Test\"] = \"test\";\n  return ServerMode2;\n})(ServerMode || {});\nfunction isServerMode(value) {\n  return value === \"development\" /* Development */ || value === \"production\" /* Production */ || value === \"test\" /* Test */;\n}\n\n// lib/server-runtime/errors.ts\nfunction sanitizeError(error, serverMode) {\n  if (error instanceof Error && serverMode !== \"development\" /* Development */) {\n    let sanitized = new Error(\"Unexpected Server Error\");\n    sanitized.stack = void 0;\n    return sanitized;\n  }\n  return error;\n}\nfunction sanitizeErrors(errors, serverMode) {\n  return Object.entries(errors).reduce((acc, [routeId, error]) => {\n    return Object.assign(acc, {\n      [routeId]: sanitizeError(error, serverMode)\n    });\n  }, {});\n}\nfunction serializeError(error, serverMode) {\n  let sanitized = sanitizeError(error, serverMode);\n  return {\n    message: sanitized.message,\n    stack: sanitized.stack\n  };\n}\nfunction serializeErrors(errors, serverMode) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (isRouteErrorResponse(val)) {\n      serialized[key] = {\n        ...val,\n        __type: \"RouteErrorResponse\"\n      };\n    } else if (val instanceof Error) {\n      let sanitized = sanitizeError(val, serverMode);\n      serialized[key] = {\n        message: sanitized.message,\n        stack: sanitized.stack,\n        __type: \"Error\",\n        // If this is a subclass (i.e., ReferenceError), send up the type so we\n        // can re-create the same type during hydration.  This will only apply\n        // in dev mode since all production errors are sanitized to normal\n        // Error instances\n        ...(sanitized.name !== \"Error\" ? {\n          __subType: sanitized.name\n        } : {})\n      };\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n// lib/server-runtime/routeMatching.ts\nfunction matchServerRoutes(routes, pathname, basename) {\n  let matches = matchRoutes(routes, pathname, basename);\n  if (!matches) return null;\n  return matches.map(match => ({\n    params: match.params,\n    pathname: match.pathname,\n    route: match.route\n  }));\n}\n\n// lib/server-runtime/data.ts\nasync function callRouteHandler(handler, args) {\n  let result = await handler({\n    request: stripRoutesParam(stripIndexParam2(args.request)),\n    params: args.params,\n    context: args.context\n  });\n  if (isDataWithResponseInit(result) && result.init && result.init.status && isRedirectStatusCode(result.init.status)) {\n    throw new Response(null, result.init);\n  }\n  return result;\n}\nfunction stripIndexParam2(request) {\n  let url = new URL(request.url);\n  let indexValues = url.searchParams.getAll(\"index\");\n  url.searchParams.delete(\"index\");\n  let indexValuesToKeep = [];\n  for (let indexValue of indexValues) {\n    if (indexValue) {\n      indexValuesToKeep.push(indexValue);\n    }\n  }\n  for (let toKeep of indexValuesToKeep) {\n    url.searchParams.append(\"index\", toKeep);\n  }\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\nfunction stripRoutesParam(request) {\n  let url = new URL(request.url);\n  url.searchParams.delete(\"_routes\");\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\n\n// lib/server-runtime/invariant.ts\nfunction invariant2(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    console.error(\"The following error is a bug in React Router; please open an issue! https://github.com/remix-run/react-router/issues/new/choose\");\n    throw new Error(message);\n  }\n}\n\n// lib/server-runtime/dev.ts\nvar globalDevServerHooksKey = \"__reactRouterDevServerHooks\";\nfunction setDevServerHooks(devServerHooks) {\n  globalThis[globalDevServerHooksKey] = devServerHooks;\n}\nfunction getDevServerHooks() {\n  return globalThis[globalDevServerHooksKey];\n}\nfunction getBuildTimeHeader(request, headerName) {\n  if (typeof process !== \"undefined\") {\n    try {\n      if (process.env?.IS_RR_BUILD_REQUEST === \"yes\") {\n        return request.headers.get(headerName);\n      }\n    } catch (e) {}\n  }\n  return null;\n}\n\n// lib/server-runtime/routes.ts\nfunction groupRoutesByParentId(manifest) {\n  let routes = {};\n  Object.values(manifest).forEach(route => {\n    if (route) {\n      let parentId = route.parentId || \"\";\n      if (!routes[parentId]) {\n        routes[parentId] = [];\n      }\n      routes[parentId].push(route);\n    }\n  });\n  return routes;\n}\nfunction createRoutes(manifest, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest)) {\n  return (routesByParentId[parentId] || []).map(route => ({\n    ...route,\n    children: createRoutes(manifest, route.id, routesByParentId)\n  }));\n}\nfunction createStaticHandlerDataRoutes(manifest, future, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest)) {\n  return (routesByParentId[parentId] || []).map(route => {\n    let commonRoute = {\n      // Always include root due to default boundaries\n      hasErrorBoundary: route.id === \"root\" || route.module.ErrorBoundary != null,\n      id: route.id,\n      path: route.path,\n      middleware: route.module.middleware,\n      // Need to use RR's version in the param typed here to permit the optional\n      // context even though we know it'll always be provided in remix\n      loader: route.module.loader ? async args => {\n        let preRenderedData = getBuildTimeHeader(args.request, \"X-React-Router-Prerender-Data\");\n        if (preRenderedData != null) {\n          let encoded = preRenderedData ? decodeURI(preRenderedData) : preRenderedData;\n          invariant2(encoded, \"Missing prerendered data for route\");\n          let uint8array = new TextEncoder().encode(encoded);\n          let stream = new ReadableStream({\n            start(controller) {\n              controller.enqueue(uint8array);\n              controller.close();\n            }\n          });\n          let decoded = await decodeViaTurboStream(stream, global);\n          let data2 = decoded.value;\n          if (data2 && SingleFetchRedirectSymbol in data2) {\n            let result = data2[SingleFetchRedirectSymbol];\n            let init = {\n              status: result.status\n            };\n            if (result.reload) {\n              throw redirectDocument(result.redirect, init);\n            } else if (result.replace) {\n              throw replace(result.redirect, init);\n            } else {\n              throw redirect(result.redirect, init);\n            }\n          } else {\n            invariant2(data2 && route.id in data2, \"Unable to decode prerendered data\");\n            let result = data2[route.id];\n            invariant2(\"data\" in result, \"Unable to process prerendered data\");\n            return result.data;\n          }\n        }\n        let val = await callRouteHandler(route.module.loader, args);\n        return val;\n      } : void 0,\n      action: route.module.action ? args => callRouteHandler(route.module.action, args) : void 0,\n      handle: route.module.handle\n    };\n    return route.index ? {\n      index: true,\n      ...commonRoute\n    } : {\n      caseSensitive: route.caseSensitive,\n      children: createStaticHandlerDataRoutes(manifest, future, route.id, routesByParentId),\n      ...commonRoute\n    };\n  });\n}\n\n// lib/server-runtime/serverHandoff.ts\nfunction createServerHandoffString(serverHandoff) {\n  return escapeHtml(JSON.stringify(serverHandoff));\n}\n\n// lib/server-runtime/headers.ts\nimport { splitCookiesString } from \"set-cookie-parser\";\nfunction getDocumentHeaders(context, build) {\n  return getDocumentHeadersImpl(context, m => {\n    let route = build.routes[m.route.id];\n    invariant2(route, `Route with id \"${m.route.id}\" not found in build`);\n    return route.module.headers;\n  });\n}\nfunction getDocumentHeadersImpl(context, getRouteHeadersFn, _defaultHeaders) {\n  let boundaryIdx = context.errors ? context.matches.findIndex(m => context.errors[m.route.id]) : -1;\n  let matches = boundaryIdx >= 0 ? context.matches.slice(0, boundaryIdx + 1) : context.matches;\n  let errorHeaders;\n  if (boundaryIdx >= 0) {\n    let {\n      actionHeaders,\n      actionData,\n      loaderHeaders,\n      loaderData\n    } = context;\n    context.matches.slice(boundaryIdx).some(match => {\n      let id = match.route.id;\n      if (actionHeaders[id] && (!actionData || !actionData.hasOwnProperty(id))) {\n        errorHeaders = actionHeaders[id];\n      } else if (loaderHeaders[id] && !loaderData.hasOwnProperty(id)) {\n        errorHeaders = loaderHeaders[id];\n      }\n      return errorHeaders != null;\n    });\n  }\n  const defaultHeaders = new Headers(_defaultHeaders);\n  return matches.reduce((parentHeaders, match, idx) => {\n    let {\n      id\n    } = match.route;\n    let loaderHeaders = context.loaderHeaders[id] || new Headers();\n    let actionHeaders = context.actionHeaders[id] || new Headers();\n    let includeErrorHeaders = errorHeaders != null && idx === matches.length - 1;\n    let includeErrorCookies = includeErrorHeaders && errorHeaders !== loaderHeaders && errorHeaders !== actionHeaders;\n    let headersFn = getRouteHeadersFn(match);\n    if (headersFn == null) {\n      let headers2 = new Headers(parentHeaders);\n      if (includeErrorCookies) {\n        prependCookies(errorHeaders, headers2);\n      }\n      prependCookies(actionHeaders, headers2);\n      prependCookies(loaderHeaders, headers2);\n      return headers2;\n    }\n    let headers = new Headers(typeof headersFn === \"function\" ? headersFn({\n      loaderHeaders,\n      parentHeaders,\n      actionHeaders,\n      errorHeaders: includeErrorHeaders ? errorHeaders : void 0\n    }) : headersFn);\n    if (includeErrorCookies) {\n      prependCookies(errorHeaders, headers);\n    }\n    prependCookies(actionHeaders, headers);\n    prependCookies(loaderHeaders, headers);\n    prependCookies(parentHeaders, headers);\n    return headers;\n  }, new Headers(defaultHeaders));\n}\nfunction prependCookies(parentHeaders, childHeaders) {\n  let parentSetCookieString = parentHeaders.get(\"Set-Cookie\");\n  if (parentSetCookieString) {\n    let cookies = splitCookiesString(parentSetCookieString);\n    let childCookies = new Set(childHeaders.getSetCookie());\n    cookies.forEach(cookie => {\n      if (!childCookies.has(cookie)) {\n        childHeaders.append(\"Set-Cookie\", cookie);\n      }\n    });\n  }\n}\n\n// lib/server-runtime/single-fetch.ts\nvar SERVER_NO_BODY_STATUS_CODES = /* @__PURE__ */new Set([...NO_BODY_STATUS_CODES, 304]);\nasync function singleFetchAction(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  try {\n    let handlerRequest = new Request(handlerUrl, {\n      method: request.method,\n      body: request.body,\n      headers: request.headers,\n      signal: request.signal,\n      ...(request.body ? {\n        duplex: \"half\"\n      } : void 0)\n    });\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      skipLoaderErrorBubbling: true,\n      skipRevalidation: true,\n      generateMiddlewareResponse: build.future.v8_middleware ? async query => {\n        try {\n          let innerResult = await query(handlerRequest);\n          return handleQueryResult(innerResult);\n        } catch (error) {\n          return handleQueryError(error);\n        }\n      } : void 0\n    });\n    return handleQueryResult(result);\n  } catch (error) {\n    return handleQueryError(error);\n  }\n  function handleQueryResult(result) {\n    return isResponse(result) ? result : staticContextToResponse(result);\n  }\n  function handleQueryError(error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: {\n        error\n      },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n  function staticContextToResponse(context) {\n    let headers = getDocumentHeaders(context, build);\n    if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n      return new Response(null, {\n        status: context.statusCode,\n        headers\n      });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach(err => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let singleFetchResult;\n    if (context.errors) {\n      singleFetchResult = {\n        error: Object.values(context.errors)[0]\n      };\n    } else {\n      singleFetchResult = {\n        data: Object.values(context.actionData || {})[0]\n      };\n    }\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: singleFetchResult,\n      headers,\n      status: context.statusCode\n    });\n  }\n}\nasync function singleFetchLoaders(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  let routesParam = new URL(request.url).searchParams.get(\"_routes\");\n  let loadRouteIds = routesParam ? new Set(routesParam.split(\",\")) : null;\n  try {\n    let handlerRequest = new Request(handlerUrl, {\n      headers: request.headers,\n      signal: request.signal\n    });\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      filterMatchesToLoad: m => !loadRouteIds || loadRouteIds.has(m.route.id),\n      skipLoaderErrorBubbling: true,\n      generateMiddlewareResponse: build.future.v8_middleware ? async query => {\n        try {\n          let innerResult = await query(handlerRequest);\n          return handleQueryResult(innerResult);\n        } catch (error) {\n          return handleQueryError(error);\n        }\n      } : void 0\n    });\n    return handleQueryResult(result);\n  } catch (error) {\n    return handleQueryError(error);\n  }\n  function handleQueryResult(result) {\n    return isResponse(result) ? result : staticContextToResponse(result);\n  }\n  function handleQueryError(error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: {\n        error\n      },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n  function staticContextToResponse(context) {\n    let headers = getDocumentHeaders(context, build);\n    if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n      return new Response(null, {\n        status: context.statusCode,\n        headers\n      });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach(err => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let results = {};\n    let loadedMatches = new Set(context.matches.filter(m => loadRouteIds ? loadRouteIds.has(m.route.id) : m.route.loader != null).map(m => m.route.id));\n    if (context.errors) {\n      for (let [id, error] of Object.entries(context.errors)) {\n        results[id] = {\n          error\n        };\n      }\n    }\n    for (let [id, data2] of Object.entries(context.loaderData)) {\n      if (!(id in results) && loadedMatches.has(id)) {\n        results[id] = {\n          data: data2\n        };\n      }\n    }\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: results,\n      headers,\n      status: context.statusCode\n    });\n  }\n}\nfunction generateSingleFetchResponse(request, build, serverMode, {\n  result,\n  headers,\n  status\n}) {\n  let resultHeaders = new Headers(headers);\n  resultHeaders.set(\"X-Remix-Response\", \"yes\");\n  if (SERVER_NO_BODY_STATUS_CODES.has(status)) {\n    return new Response(null, {\n      status,\n      headers: resultHeaders\n    });\n  }\n  resultHeaders.set(\"Content-Type\", \"text/x-script\");\n  resultHeaders.delete(\"Content-Length\");\n  return new Response(encodeViaTurboStream(result, request.signal, build.entry.module.streamTimeout, serverMode), {\n    status: status || 200,\n    headers: resultHeaders\n  });\n}\nfunction generateSingleFetchRedirectResponse(redirectResponse, request, build, serverMode) {\n  let redirect2 = getSingleFetchRedirect(redirectResponse.status, redirectResponse.headers, build.basename);\n  let headers = new Headers(redirectResponse.headers);\n  headers.delete(\"Location\");\n  headers.set(\"Content-Type\", \"text/x-script\");\n  return generateSingleFetchResponse(request, build, serverMode, {\n    result: request.method === \"GET\" ? {\n      [SingleFetchRedirectSymbol]: redirect2\n    } : redirect2,\n    headers,\n    status: SINGLE_FETCH_REDIRECT_STATUS\n  });\n}\nfunction getSingleFetchRedirect(status, headers, basename) {\n  let redirect2 = headers.get(\"Location\");\n  if (basename) {\n    redirect2 = stripBasename(redirect2, basename) || redirect2;\n  }\n  return {\n    redirect: redirect2,\n    status,\n    revalidate:\n    // Technically X-Remix-Revalidate isn't needed here - that was an implementation\n    // detail of ?_data requests as our way to tell the front end to revalidate when\n    // we didn't have a response body to include that information in.\n    // With single fetch, we tell the front end via this revalidate boolean field.\n    // However, we're respecting it for now because it may be something folks have\n    // used in their own responses\n    // TODO(v3): Consider removing or making this official public API\n    headers.has(\"X-Remix-Revalidate\") || headers.has(\"Set-Cookie\"),\n    reload: headers.has(\"X-Remix-Reload-Document\"),\n    replace: headers.has(\"X-Remix-Replace\")\n  };\n}\nfunction encodeViaTurboStream(data2, requestSignal, streamTimeout, serverMode) {\n  let controller = new AbortController();\n  let timeoutId = setTimeout(() => controller.abort(new Error(\"Server Timeout\")), typeof streamTimeout === \"number\" ? streamTimeout : 4950);\n  requestSignal.addEventListener(\"abort\", () => clearTimeout(timeoutId));\n  return encode(data2, {\n    signal: controller.signal,\n    plugins: [value => {\n      if (value instanceof Error) {\n        let {\n          name,\n          message,\n          stack\n        } = serverMode === \"production\" /* Production */ ? sanitizeError(value, serverMode) : value;\n        return [\"SanitizedError\", name, message, stack];\n      }\n      if (value instanceof ErrorResponseImpl) {\n        let {\n          data: data3,\n          status,\n          statusText\n        } = value;\n        return [\"ErrorResponse\", data3, status, statusText];\n      }\n      if (value && typeof value === \"object\" && SingleFetchRedirectSymbol in value) {\n        return [\"SingleFetchRedirect\", value[SingleFetchRedirectSymbol]];\n      }\n    }],\n    postPlugins: [value => {\n      if (!value) return;\n      if (typeof value !== \"object\") return;\n      return [\"SingleFetchClassInstance\", Object.fromEntries(Object.entries(value))];\n    }, () => [\"SingleFetchFallback\"]]\n  });\n}\n\n// lib/server-runtime/server.ts\nfunction derive(build, mode) {\n  let routes = createRoutes(build.routes);\n  let dataRoutes = createStaticHandlerDataRoutes(build.routes, build.future);\n  let serverMode = isServerMode(mode) ? mode : \"production\" /* Production */;\n  let staticHandler = createStaticHandler(dataRoutes, {\n    basename: build.basename\n  });\n  let errorHandler = build.entry.module.handleError || ((error, {\n    request\n  }) => {\n    if (serverMode !== \"test\" /* Test */ && !request.signal.aborted) {\n      console.error(\n      // @ts-expect-error This is \"private\" from users but intended for internal use\n      isRouteErrorResponse(error) && error.error ? error.error : error);\n    }\n  });\n  return {\n    routes,\n    dataRoutes,\n    serverMode,\n    staticHandler,\n    errorHandler\n  };\n}\nvar createRequestHandler = (build, mode) => {\n  let _build;\n  let routes;\n  let serverMode;\n  let staticHandler;\n  let errorHandler;\n  return async function requestHandler(request, initialContext) {\n    _build = typeof build === \"function\" ? await build() : build;\n    if (typeof build === \"function\") {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    } else if (!routes || !serverMode || !staticHandler || !errorHandler) {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    }\n    let params = {};\n    let loadContext;\n    let handleError = error => {\n      if (mode === \"development\" /* Development */) {\n        getDevServerHooks()?.processRequestError?.(error);\n      }\n      errorHandler(error, {\n        context: loadContext,\n        params,\n        request\n      });\n    };\n    if (_build.future.v8_middleware) {\n      if (initialContext && !(initialContext instanceof RouterContextProvider)) {\n        let error = new Error(\"Invalid `context` value provided to `handleRequest`. When middleware is enabled you must return an instance of `RouterContextProvider` from your `getLoadContext` function.\");\n        handleError(error);\n        return returnLastResortErrorResponse(error, serverMode);\n      }\n      loadContext = initialContext || new RouterContextProvider();\n    } else {\n      loadContext = initialContext || {};\n    }\n    let url = new URL(request.url);\n    let normalizedBasename = _build.basename || \"/\";\n    let normalizedPath = url.pathname;\n    if (stripBasename(normalizedPath, normalizedBasename) === \"/_root.data\") {\n      normalizedPath = normalizedBasename;\n    } else if (normalizedPath.endsWith(\".data\")) {\n      normalizedPath = normalizedPath.replace(/\\.data$/, \"\");\n    }\n    if (stripBasename(normalizedPath, normalizedBasename) !== \"/\" && normalizedPath.endsWith(\"/\")) {\n      normalizedPath = normalizedPath.slice(0, -1);\n    }\n    let isSpaMode = getBuildTimeHeader(request, \"X-React-Router-SPA-Mode\") === \"yes\";\n    if (!_build.ssr) {\n      let decodedPath = decodeURI(normalizedPath);\n      if (normalizedBasename !== \"/\") {\n        let strippedPath = stripBasename(decodedPath, normalizedBasename);\n        if (strippedPath == null) {\n          errorHandler(new ErrorResponseImpl(404, \"Not Found\", `Refusing to prerender the \\`${decodedPath}\\` path because it does not start with the basename \\`${normalizedBasename}\\``), {\n            context: loadContext,\n            params,\n            request\n          });\n          return new Response(\"Not Found\", {\n            status: 404,\n            statusText: \"Not Found\"\n          });\n        }\n        decodedPath = strippedPath;\n      }\n      if (_build.prerender.length === 0) {\n        isSpaMode = true;\n      } else if (!_build.prerender.includes(decodedPath) && !_build.prerender.includes(decodedPath + \"/\")) {\n        if (url.pathname.endsWith(\".data\")) {\n          errorHandler(new ErrorResponseImpl(404, \"Not Found\", `Refusing to SSR the path \\`${decodedPath}\\` because \\`ssr:false\\` is set and the path is not included in the \\`prerender\\` config, so in production the path will be a 404.`), {\n            context: loadContext,\n            params,\n            request\n          });\n          return new Response(\"Not Found\", {\n            status: 404,\n            statusText: \"Not Found\"\n          });\n        } else {\n          isSpaMode = true;\n        }\n      }\n    }\n    let manifestUrl = getManifestPath(_build.routeDiscovery.manifestPath, normalizedBasename);\n    if (url.pathname === manifestUrl) {\n      try {\n        let res = await handleManifestRequest(_build, routes, url);\n        return res;\n      } catch (e) {\n        handleError(e);\n        return new Response(\"Unknown Server Error\", {\n          status: 500\n        });\n      }\n    }\n    let matches = matchServerRoutes(routes, normalizedPath, _build.basename);\n    if (matches && matches.length > 0) {\n      Object.assign(params, matches[0].params);\n    }\n    let response;\n    if (url.pathname.endsWith(\".data\")) {\n      let handlerUrl = new URL(request.url);\n      handlerUrl.pathname = normalizedPath;\n      let singleFetchMatches = matchServerRoutes(routes, handlerUrl.pathname, _build.basename);\n      response = await handleSingleFetchRequest(serverMode, _build, staticHandler, request, handlerUrl, loadContext, handleError);\n      if (isRedirectResponse(response)) {\n        response = generateSingleFetchRedirectResponse(response, request, _build, serverMode);\n      }\n      if (_build.entry.module.handleDataRequest) {\n        response = await _build.entry.module.handleDataRequest(response, {\n          context: loadContext,\n          params: singleFetchMatches ? singleFetchMatches[0].params : {},\n          request\n        });\n        if (isRedirectResponse(response)) {\n          response = generateSingleFetchRedirectResponse(response, request, _build, serverMode);\n        }\n      }\n    } else if (!isSpaMode && matches && matches[matches.length - 1].route.module.default == null && matches[matches.length - 1].route.module.ErrorBoundary == null) {\n      response = await handleResourceRequest(serverMode, _build, staticHandler, matches.slice(-1)[0].route.id, request, loadContext, handleError);\n    } else {\n      let {\n        pathname\n      } = url;\n      let criticalCss = void 0;\n      if (_build.unstable_getCriticalCss) {\n        criticalCss = await _build.unstable_getCriticalCss({\n          pathname\n        });\n      } else if (mode === \"development\" /* Development */ && getDevServerHooks()?.getCriticalCss) {\n        criticalCss = await getDevServerHooks()?.getCriticalCss?.(pathname);\n      }\n      response = await handleDocumentRequest(serverMode, _build, staticHandler, request, loadContext, handleError, isSpaMode, criticalCss);\n    }\n    if (request.method === \"HEAD\") {\n      return new Response(null, {\n        headers: response.headers,\n        status: response.status,\n        statusText: response.statusText\n      });\n    }\n    return response;\n  };\n};\nasync function handleManifestRequest(build, routes, url) {\n  if (build.assets.version !== url.searchParams.get(\"version\")) {\n    return new Response(null, {\n      status: 204,\n      headers: {\n        \"X-Remix-Reload-Document\": \"true\"\n      }\n    });\n  }\n  let patches = {};\n  if (url.searchParams.has(\"p\")) {\n    let paths = /* @__PURE__ */new Set();\n    url.searchParams.getAll(\"p\").forEach(path => {\n      if (!path.startsWith(\"/\")) {\n        path = `/${path}`;\n      }\n      let segments = path.split(\"/\").slice(1);\n      segments.forEach((_, i) => {\n        let partialPath = segments.slice(0, i + 1).join(\"/\");\n        paths.add(`/${partialPath}`);\n      });\n    });\n    for (let path of paths) {\n      let matches = matchServerRoutes(routes, path, build.basename);\n      if (matches) {\n        for (let match of matches) {\n          let routeId = match.route.id;\n          let route = build.assets.routes[routeId];\n          if (route) {\n            patches[routeId] = route;\n          }\n        }\n      }\n    }\n    return Response.json(patches, {\n      headers: {\n        \"Cache-Control\": \"public, max-age=31536000, immutable\"\n      }\n    });\n  }\n  return new Response(\"Invalid Request\", {\n    status: 400\n  });\n}\nasync function handleSingleFetchRequest(serverMode, build, staticHandler, request, handlerUrl, loadContext, handleError) {\n  let response = request.method !== \"GET\" ? await singleFetchAction(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) : await singleFetchLoaders(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError);\n  return response;\n}\nasync function handleDocumentRequest(serverMode, build, staticHandler, request, loadContext, handleError, isSpaMode, criticalCss) {\n  try {\n    let result = await staticHandler.query(request, {\n      requestContext: loadContext,\n      generateMiddlewareResponse: build.future.v8_middleware ? async query => {\n        try {\n          let innerResult = await query(request);\n          if (!isResponse(innerResult)) {\n            innerResult = await renderHtml(innerResult, isSpaMode);\n          }\n          return innerResult;\n        } catch (error) {\n          handleError(error);\n          return new Response(null, {\n            status: 500\n          });\n        }\n      } : void 0\n    });\n    if (!isResponse(result)) {\n      result = await renderHtml(result, isSpaMode);\n    }\n    return result;\n  } catch (error) {\n    handleError(error);\n    return new Response(null, {\n      status: 500\n    });\n  }\n  async function renderHtml(context, isSpaMode2) {\n    let headers = getDocumentHeaders(context, build);\n    if (SERVER_NO_BODY_STATUS_CODES.has(context.statusCode)) {\n      return new Response(null, {\n        status: context.statusCode,\n        headers\n      });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach(err => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let state = {\n      loaderData: context.loaderData,\n      actionData: context.actionData,\n      errors: serializeErrors(context.errors, serverMode)\n    };\n    let baseServerHandoff = {\n      basename: build.basename,\n      future: build.future,\n      routeDiscovery: build.routeDiscovery,\n      ssr: build.ssr,\n      isSpaMode: isSpaMode2\n    };\n    let entryContext = {\n      manifest: build.assets,\n      routeModules: createEntryRouteModules(build.routes),\n      staticHandlerContext: context,\n      criticalCss,\n      serverHandoffString: createServerHandoffString({\n        ...baseServerHandoff,\n        criticalCss\n      }),\n      serverHandoffStream: encodeViaTurboStream(state, request.signal, build.entry.module.streamTimeout, serverMode),\n      renderMeta: {},\n      future: build.future,\n      ssr: build.ssr,\n      routeDiscovery: build.routeDiscovery,\n      isSpaMode: isSpaMode2,\n      serializeError: err => serializeError(err, serverMode)\n    };\n    let handleDocumentRequestFunction = build.entry.module.default;\n    try {\n      return await handleDocumentRequestFunction(request, context.statusCode, headers, entryContext, loadContext);\n    } catch (error) {\n      handleError(error);\n      let errorForSecondRender = error;\n      if (isResponse(error)) {\n        try {\n          let data2 = await unwrapResponse(error);\n          errorForSecondRender = new ErrorResponseImpl(error.status, error.statusText, data2);\n        } catch (e) {}\n      }\n      context = getStaticContextFromError(staticHandler.dataRoutes, context, errorForSecondRender);\n      if (context.errors) {\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let state2 = {\n        loaderData: context.loaderData,\n        actionData: context.actionData,\n        errors: serializeErrors(context.errors, serverMode)\n      };\n      entryContext = {\n        ...entryContext,\n        staticHandlerContext: context,\n        serverHandoffString: createServerHandoffString(baseServerHandoff),\n        serverHandoffStream: encodeViaTurboStream(state2, request.signal, build.entry.module.streamTimeout, serverMode),\n        renderMeta: {}\n      };\n      try {\n        return await handleDocumentRequestFunction(request, context.statusCode, headers, entryContext, loadContext);\n      } catch (error2) {\n        handleError(error2);\n        return returnLastResortErrorResponse(error2, serverMode);\n      }\n    }\n  }\n}\nasync function handleResourceRequest(serverMode, build, staticHandler, routeId, request, loadContext, handleError) {\n  try {\n    let result = await staticHandler.queryRoute(request, {\n      routeId,\n      requestContext: loadContext,\n      generateMiddlewareResponse: build.future.v8_middleware ? async queryRoute => {\n        try {\n          let innerResult = await queryRoute(request);\n          return handleQueryRouteResult(innerResult);\n        } catch (error) {\n          return handleQueryRouteError(error);\n        }\n      } : void 0\n    });\n    return handleQueryRouteResult(result);\n  } catch (error) {\n    return handleQueryRouteError(error);\n  }\n  function handleQueryRouteResult(result) {\n    if (isResponse(result)) {\n      return result;\n    }\n    if (typeof result === \"string\") {\n      return new Response(result);\n    }\n    return Response.json(result);\n  }\n  function handleQueryRouteError(error) {\n    if (isResponse(error)) {\n      error.headers.set(\"X-Remix-Catch\", \"yes\");\n      return error;\n    }\n    if (isRouteErrorResponse(error)) {\n      handleError(error);\n      return errorResponseToJson(error, serverMode);\n    }\n    if (error instanceof Error && error.message === \"Expected a response from queryRoute\") {\n      let newError = new Error(\"Expected a Response to be returned from resource route handler\");\n      handleError(newError);\n      return returnLastResortErrorResponse(newError, serverMode);\n    }\n    handleError(error);\n    return returnLastResortErrorResponse(error, serverMode);\n  }\n}\nfunction errorResponseToJson(errorResponse, serverMode) {\n  return Response.json(serializeError(\n  // @ts-expect-error This is \"private\" from users but intended for internal use\n  errorResponse.error || new Error(\"Unexpected Server Error\"), serverMode), {\n    status: errorResponse.status,\n    statusText: errorResponse.statusText,\n    headers: {\n      \"X-Remix-Error\": \"yes\"\n    }\n  });\n}\nfunction returnLastResortErrorResponse(error, serverMode) {\n  let message = \"Unexpected Server Error\";\n  if (serverMode !== \"production\" /* Production */) {\n    message += `\n\n${String(error)}`;\n  }\n  return new Response(message, {\n    status: 500,\n    headers: {\n      \"Content-Type\": \"text/plain\"\n    }\n  });\n}\nfunction unwrapResponse(response) {\n  let contentType = response.headers.get(\"Content-Type\");\n  return contentType && /\\bapplication\\/json\\b/.test(contentType) ? response.body == null ? null : response.json() : response.text();\n}\n\n// lib/server-runtime/sessions.ts\nfunction flash(name) {\n  return `__flash_${name}__`;\n}\nvar createSession = (initialData = {}, id = \"\") => {\n  let map = new Map(Object.entries(initialData));\n  return {\n    get id() {\n      return id;\n    },\n    get data() {\n      return Object.fromEntries(map);\n    },\n    has(name) {\n      return map.has(name) || map.has(flash(name));\n    },\n    get(name) {\n      if (map.has(name)) return map.get(name);\n      let flashName = flash(name);\n      if (map.has(flashName)) {\n        let value = map.get(flashName);\n        map.delete(flashName);\n        return value;\n      }\n      return void 0;\n    },\n    set(name, value) {\n      map.set(name, value);\n    },\n    flash(name, value) {\n      map.set(flash(name), value);\n    },\n    unset(name) {\n      map.delete(name);\n    }\n  };\n};\nvar isSession = object => {\n  return object != null && typeof object.id === \"string\" && typeof object.data !== \"undefined\" && typeof object.has === \"function\" && typeof object.get === \"function\" && typeof object.set === \"function\" && typeof object.flash === \"function\" && typeof object.unset === \"function\";\n};\nfunction createSessionStorage({\n  cookie: cookieArg,\n  createData,\n  readData,\n  updateData,\n  deleteData\n}) {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      let id = cookieHeader && (await cookie.parse(cookieHeader, options));\n      let data2 = id && (await readData(id));\n      return createSession(data2 || {}, id || \"\");\n    },\n    async commitSession(session, options) {\n      let {\n        id,\n        data: data2\n      } = session;\n      let expires = options?.maxAge != null ? new Date(Date.now() + options.maxAge * 1e3) : options?.expires != null ? options.expires : cookie.expires;\n      if (id) {\n        await updateData(id, data2, expires);\n      } else {\n        id = await createData(data2, expires);\n      }\n      return cookie.serialize(id, options);\n    },\n    async destroySession(session, options) {\n      await deleteData(session.id);\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */new Date(0)\n      });\n    }\n  };\n}\nfunction warnOnceAboutSigningSessionCookie(cookie) {\n  warnOnce(cookie.isSigned, `The \"${cookie.name}\" cookie is not signed, but session cookies should be signed to prevent tampering on the client before they are sent back to the server. See https://reactrouter.com/explanation/sessions-and-cookies#signing-cookies for more information.`);\n}\n\n// lib/server-runtime/sessions/cookieStorage.ts\nfunction createCookieSessionStorage({\n  cookie: cookieArg\n} = {}) {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      return createSession(cookieHeader && (await cookie.parse(cookieHeader, options)) || {});\n    },\n    async commitSession(session, options) {\n      let serializedCookie = await cookie.serialize(session.data, options);\n      if (serializedCookie.length > 4096) {\n        throw new Error(\"Cookie length will exceed browser maximum. Length: \" + serializedCookie.length);\n      }\n      return serializedCookie;\n    },\n    async destroySession(_session, options) {\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */new Date(0)\n      });\n    }\n  };\n}\n\n// lib/server-runtime/sessions/memoryStorage.ts\nfunction createMemorySessionStorage({\n  cookie\n} = {}) {\n  let map = /* @__PURE__ */new Map();\n  return createSessionStorage({\n    cookie,\n    async createData(data2, expires) {\n      let id = Math.random().toString(36).substring(2, 10);\n      map.set(id, {\n        data: data2,\n        expires\n      });\n      return id;\n    },\n    async readData(id) {\n      if (map.has(id)) {\n        let {\n          data: data2,\n          expires\n        } = map.get(id);\n        if (!expires || expires > /* @__PURE__ */new Date()) {\n          return data2;\n        }\n        if (expires) map.delete(id);\n      }\n      return null;\n    },\n    async updateData(id, data2, expires) {\n      map.set(id, {\n        data: data2,\n        expires\n      });\n    },\n    async deleteData(id) {\n      map.delete(id);\n    }\n  });\n}\n\n// lib/href.ts\nfunction href(path, ...args) {\n  let params = args[0];\n  let result = path.replace(/\\/*\\*?$/, \"\").replace(/\\/:([\\w-]+)(\\?)?/g,\n  // same regex as in .\\router\\utils.ts: compilePath().\n  (_, param, questionMark) => {\n    const isRequired = questionMark === void 0;\n    const value = params ? params[param] : void 0;\n    if (isRequired && value === void 0) {\n      throw new Error(`Path '${path}' requires param '${param}' but it was not provided`);\n    }\n    return value === void 0 ? \"\" : \"/\" + value;\n  });\n  if (path.endsWith(\"*\")) {\n    const value = params ? params[\"*\"] : void 0;\n    if (value !== void 0) {\n      result += \"/\" + value;\n    }\n  }\n  return result || \"/\";\n}\n\n// lib/rsc/browser.tsx\nimport * as React4 from \"react\";\nimport * as ReactDOM from \"react-dom\";\n\n// lib/dom/ssr/hydration.tsx\nfunction getHydrationData({\n  state,\n  routes,\n  getRouteInfo,\n  location: location2,\n  basename,\n  isSpaMode\n}) {\n  let hydrationData = {\n    ...state,\n    loaderData: {\n      ...state.loaderData\n    }\n  };\n  let initialMatches = matchRoutes(routes, location2, basename);\n  if (initialMatches) {\n    for (let match of initialMatches) {\n      let routeId = match.route.id;\n      let routeInfo = getRouteInfo(routeId);\n      if (shouldHydrateRouteLoader(routeId, routeInfo.clientLoader, routeInfo.hasLoader, isSpaMode) && (routeInfo.hasHydrateFallback || !routeInfo.hasLoader)) {\n        delete hydrationData.loaderData[routeId];\n      } else if (!routeInfo.hasLoader) {\n        hydrationData.loaderData[routeId] = null;\n      }\n    }\n  }\n  return hydrationData;\n}\n\n// lib/rsc/errorBoundaries.tsx\nimport React3 from \"react\";\nvar RSCRouterGlobalErrorBoundary = class extends React3.Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      error: null,\n      location: props.location\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      error\n    };\n  }\n  static getDerivedStateFromProps(props, state) {\n    if (state.location !== props.location) {\n      return {\n        error: null,\n        location: props.location\n      };\n    }\n    return {\n      error: state.error,\n      location: state.location\n    };\n  }\n  render() {\n    if (this.state.error) {\n      return /* @__PURE__ */React3.createElement(RSCDefaultRootErrorBoundaryImpl, {\n        error: this.state.error,\n        renderAppShell: true\n      });\n    } else {\n      return this.props.children;\n    }\n  }\n};\nfunction ErrorWrapper({\n  renderAppShell,\n  title,\n  children\n}) {\n  if (!renderAppShell) {\n    return children;\n  }\n  return /* @__PURE__ */React3.createElement(\"html\", {\n    lang: \"en\"\n  }, /* @__PURE__ */React3.createElement(\"head\", null, /* @__PURE__ */React3.createElement(\"meta\", {\n    charSet: \"utf-8\"\n  }), /* @__PURE__ */React3.createElement(\"meta\", {\n    name: \"viewport\",\n    content: \"width=device-width,initial-scale=1,viewport-fit=cover\"\n  }), /* @__PURE__ */React3.createElement(\"title\", null, title)), /* @__PURE__ */React3.createElement(\"body\", null, /* @__PURE__ */React3.createElement(\"main\", {\n    style: {\n      fontFamily: \"system-ui, sans-serif\",\n      padding: \"2rem\"\n    }\n  }, children)));\n}\nfunction RSCDefaultRootErrorBoundaryImpl({\n  error,\n  renderAppShell\n}) {\n  console.error(error);\n  let heyDeveloper = /* @__PURE__ */React3.createElement(\"script\", {\n    dangerouslySetInnerHTML: {\n      __html: `\n        console.log(\n          \"\\u{1F4BF} Hey developer \\u{1F44B}. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information.\"\n        );\n      `\n    }\n  });\n  if (isRouteErrorResponse(error)) {\n    return /* @__PURE__ */React3.createElement(ErrorWrapper, {\n      renderAppShell,\n      title: \"Unhandled Thrown Response!\"\n    }, /* @__PURE__ */React3.createElement(\"h1\", {\n      style: {\n        fontSize: \"24px\"\n      }\n    }, error.status, \" \", error.statusText), ENABLE_DEV_WARNINGS ? heyDeveloper : null);\n  }\n  let errorInstance;\n  if (error instanceof Error) {\n    errorInstance = error;\n  } else {\n    let errorString = error == null ? \"Unknown Error\" : typeof error === \"object\" && \"toString\" in error ? error.toString() : JSON.stringify(error);\n    errorInstance = new Error(errorString);\n  }\n  return /* @__PURE__ */React3.createElement(ErrorWrapper, {\n    renderAppShell,\n    title: \"Application Error!\"\n  }, /* @__PURE__ */React3.createElement(\"h1\", {\n    style: {\n      fontSize: \"24px\"\n    }\n  }, \"Application Error\"), /* @__PURE__ */React3.createElement(\"pre\", {\n    style: {\n      padding: \"2rem\",\n      background: \"hsla(10, 50%, 50%, 0.1)\",\n      color: \"red\",\n      overflow: \"auto\"\n    }\n  }, errorInstance.stack), heyDeveloper);\n}\nfunction RSCDefaultRootErrorBoundary({\n  hasRootLayout\n}) {\n  let error = useRouteError();\n  if (hasRootLayout === void 0) {\n    throw new Error(\"Missing 'hasRootLayout' prop\");\n  }\n  return /* @__PURE__ */React3.createElement(RSCDefaultRootErrorBoundaryImpl, {\n    renderAppShell: !hasRootLayout,\n    error\n  });\n}\n\n// lib/rsc/route-modules.ts\nfunction createRSCRouteModules(payload) {\n  const routeModules = {};\n  for (const match of payload.matches) {\n    populateRSCRouteModules(routeModules, match);\n  }\n  return routeModules;\n}\nfunction populateRSCRouteModules(routeModules, matches) {\n  matches = Array.isArray(matches) ? matches : [matches];\n  for (const match of matches) {\n    routeModules[match.id] = {\n      links: match.links,\n      meta: match.meta,\n      default: noopComponent\n    };\n  }\n}\nvar noopComponent = () => null;\n\n// lib/rsc/browser.tsx\nfunction createCallServer({\n  createFromReadableStream,\n  createTemporaryReferenceSet,\n  encodeReply,\n  fetch: fetchImplementation = fetch\n}) {\n  const globalVar = window;\n  let landedActionId = 0;\n  return async (id, args) => {\n    let actionId = globalVar.__routerActionID = (globalVar.__routerActionID ?? (globalVar.__routerActionID = 0)) + 1;\n    const temporaryReferences = createTemporaryReferenceSet();\n    const response = await fetchImplementation(new Request(location.href, {\n      body: await encodeReply(args, {\n        temporaryReferences\n      }),\n      method: \"POST\",\n      headers: {\n        Accept: \"text/x-component\",\n        \"rsc-action-id\": id\n      }\n    }));\n    if (!response.body) {\n      throw new Error(\"No response body\");\n    }\n    const payload = await createFromReadableStream(response.body, {\n      temporaryReferences\n    });\n    if (payload.type === \"redirect\") {\n      if (payload.reload) {\n        window.location.href = payload.location;\n        return;\n      }\n      globalVar.__reactRouterDataRouter.navigate(payload.location, {\n        replace: payload.replace\n      });\n      return payload.actionResult;\n    }\n    if (payload.type !== \"action\") {\n      throw new Error(\"Unexpected payload type\");\n    }\n    if (payload.rerender) {\n      React4.startTransition(\n      // @ts-expect-error - We have old react types that don't know this can be async\n      async () => {\n        const rerender = await payload.rerender;\n        if (!rerender) return;\n        if (landedActionId < actionId && globalVar.__routerActionID <= actionId) {\n          landedActionId = actionId;\n          if (rerender.type === \"redirect\") {\n            if (rerender.reload) {\n              window.location.href = rerender.location;\n              return;\n            }\n            globalVar.__reactRouterDataRouter.navigate(rerender.location, {\n              replace: rerender.replace\n            });\n            return;\n          }\n          let lastMatch;\n          for (const match of rerender.matches) {\n            globalVar.__reactRouterDataRouter.patchRoutes(lastMatch?.id ?? null, [createRouteFromServerManifest(match)], true);\n            lastMatch = match;\n          }\n          window.__reactRouterDataRouter._internalSetStateDoNotUseOrYouWillBreakYourApp({});\n          React4.startTransition(() => {\n            window.__reactRouterDataRouter._internalSetStateDoNotUseOrYouWillBreakYourApp({\n              loaderData: Object.assign({}, globalVar.__reactRouterDataRouter.state.loaderData, rerender.loaderData),\n              errors: rerender.errors ? Object.assign({}, globalVar.__reactRouterDataRouter.state.errors, rerender.errors) : null\n            });\n          });\n        }\n      });\n    }\n    return payload.actionResult;\n  };\n}\nfunction createRouterFromPayload({\n  fetchImplementation,\n  createFromReadableStream,\n  getContext,\n  payload\n}) {\n  const globalVar = window;\n  if (globalVar.__reactRouterDataRouter && globalVar.__reactRouterRouteModules) return {\n    router: globalVar.__reactRouterDataRouter,\n    routeModules: globalVar.__reactRouterRouteModules\n  };\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  globalVar.__reactRouterRouteModules = globalVar.__reactRouterRouteModules ?? {};\n  populateRSCRouteModules(globalVar.__reactRouterRouteModules, payload.matches);\n  let patches = /* @__PURE__ */new Map();\n  payload.patches?.forEach(patch => {\n    invariant(patch.parentId, \"Invalid patch parentId\");\n    if (!patches.has(patch.parentId)) {\n      patches.set(patch.parentId, []);\n    }\n    patches.get(patch.parentId)?.push(patch);\n  });\n  let routes = payload.matches.reduceRight((previous, match) => {\n    const route = createRouteFromServerManifest(match, payload);\n    if (previous.length > 0) {\n      route.children = previous;\n      let childrenToPatch = patches.get(match.id);\n      if (childrenToPatch) {\n        route.children.push(...childrenToPatch.map(r => createRouteFromServerManifest(r)));\n      }\n    }\n    return [route];\n  }, []);\n  globalVar.__reactRouterDataRouter = createRouter({\n    routes,\n    getContext,\n    basename: payload.basename,\n    history: createBrowserHistory(),\n    hydrationData: getHydrationData({\n      state: {\n        loaderData: payload.loaderData,\n        actionData: payload.actionData,\n        errors: payload.errors\n      },\n      routes,\n      getRouteInfo: routeId => {\n        let match = payload.matches.find(m => m.id === routeId);\n        invariant(match, \"Route not found in payload\");\n        return {\n          clientLoader: match.clientLoader,\n          hasLoader: match.hasLoader,\n          hasHydrateFallback: match.hydrateFallbackElement != null\n        };\n      },\n      location: payload.location,\n      basename: payload.basename,\n      isSpaMode: false\n    }),\n    async patchRoutesOnNavigation({\n      path,\n      signal\n    }) {\n      if (discoveredPaths.has(path)) {\n        return;\n      }\n      await fetchAndApplyManifestPatches([path], createFromReadableStream, fetchImplementation, signal);\n    },\n    // FIXME: Pass `build.ssr` into this function\n    dataStrategy: getRSCSingleFetchDataStrategy(() => globalVar.__reactRouterDataRouter, true, payload.basename, createFromReadableStream, fetchImplementation)\n  });\n  if (globalVar.__reactRouterDataRouter.state.initialized) {\n    globalVar.__routerInitialized = true;\n    globalVar.__reactRouterDataRouter.initialize();\n  } else {\n    globalVar.__routerInitialized = false;\n  }\n  let lastLoaderData = void 0;\n  globalVar.__reactRouterDataRouter.subscribe(({\n    loaderData,\n    actionData\n  }) => {\n    if (lastLoaderData !== loaderData) {\n      globalVar.__routerActionID = (globalVar.__routerActionID ?? (globalVar.__routerActionID = 0)) + 1;\n    }\n  });\n  globalVar.__reactRouterDataRouter._updateRoutesForHMR = routeUpdateByRouteId => {\n    const oldRoutes = window.__reactRouterDataRouter.routes;\n    const newRoutes = [];\n    function walkRoutes(routes2, parentId) {\n      return routes2.map(route => {\n        const routeUpdate = routeUpdateByRouteId.get(route.id);\n        if (routeUpdate) {\n          const {\n            routeModule,\n            hasAction,\n            hasComponent,\n            hasErrorBoundary,\n            hasLoader\n          } = routeUpdate;\n          const newRoute = createRouteFromServerManifest({\n            clientAction: routeModule.clientAction,\n            clientLoader: routeModule.clientLoader,\n            element: route.element,\n            errorElement: route.errorElement,\n            handle: route.handle,\n            hasAction,\n            hasComponent,\n            hasErrorBoundary,\n            hasLoader,\n            hydrateFallbackElement: route.hydrateFallbackElement,\n            id: route.id,\n            index: route.index,\n            links: routeModule.links,\n            meta: routeModule.meta,\n            parentId,\n            path: route.path,\n            shouldRevalidate: routeModule.shouldRevalidate\n          });\n          if (route.children) {\n            newRoute.children = walkRoutes(route.children, route.id);\n          }\n          return newRoute;\n        }\n        const updatedRoute = {\n          ...route\n        };\n        if (route.children) {\n          updatedRoute.children = walkRoutes(route.children, route.id);\n        }\n        return updatedRoute;\n      });\n    }\n    newRoutes.push(...walkRoutes(oldRoutes, void 0));\n    window.__reactRouterDataRouter._internalSetRoutes(newRoutes);\n  };\n  return {\n    router: globalVar.__reactRouterDataRouter,\n    routeModules: globalVar.__reactRouterRouteModules\n  };\n}\nvar renderedRoutesContext = createContext();\nfunction getRSCSingleFetchDataStrategy(getRouter, ssr, basename, createFromReadableStream, fetchImplementation) {\n  let dataStrategy = getSingleFetchDataStrategyImpl(getRouter, match => {\n    let M = match;\n    return {\n      hasLoader: M.route.hasLoader,\n      hasClientLoader: M.route.hasClientLoader,\n      hasComponent: M.route.hasComponent,\n      hasAction: M.route.hasAction,\n      hasClientAction: M.route.hasClientAction,\n      hasShouldRevalidate: M.route.hasShouldRevalidate\n    };\n  },\n  // pass map into fetchAndDecode so it can add payloads\n  getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation), ssr, basename,\n  // If the route has a component but we don't have an element, we need to hit\n  // the server loader flow regardless of whether the client loader calls\n  // `serverLoader` or not, otherwise we'll have nothing to render.\n  match => {\n    let M = match;\n    return M.route.hasComponent && !M.route.element;\n  });\n  return async args => args.runClientMiddleware(async () => {\n    let context = args.context;\n    context.set(renderedRoutesContext, []);\n    let results = await dataStrategy(args);\n    const renderedRoutesById = /* @__PURE__ */new Map();\n    for (const route of context.get(renderedRoutesContext)) {\n      if (!renderedRoutesById.has(route.id)) {\n        renderedRoutesById.set(route.id, []);\n      }\n      renderedRoutesById.get(route.id).push(route);\n    }\n    for (const match of args.matches) {\n      const renderedRoutes = renderedRoutesById.get(match.route.id);\n      if (renderedRoutes) {\n        for (const rendered of renderedRoutes) {\n          window.__reactRouterDataRouter.patchRoutes(rendered.parentId ?? null, [createRouteFromServerManifest(rendered)], true);\n        }\n      }\n    }\n    return results;\n  });\n}\nfunction getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation) {\n  return async (args, basename, targetRoutes) => {\n    let {\n      request,\n      context\n    } = args;\n    let url = singleFetchUrl(request.url, basename, \"rsc\");\n    if (request.method === \"GET\") {\n      url = stripIndexParam(url);\n      if (targetRoutes) {\n        url.searchParams.set(\"_routes\", targetRoutes.join(\",\"));\n      }\n    }\n    let res = await fetchImplementation(new Request(url, await createRequestInit(request)));\n    if (res.status === 404 && !res.headers.has(\"X-Remix-Response\")) {\n      throw new ErrorResponseImpl(404, \"Not Found\", true);\n    }\n    invariant(res.body, \"No response body to decode\");\n    try {\n      const payload = await createFromReadableStream(res.body, {\n        temporaryReferences: void 0\n      });\n      if (payload.type === \"redirect\") {\n        return {\n          status: res.status,\n          data: {\n            redirect: {\n              redirect: payload.location,\n              reload: payload.reload,\n              replace: payload.replace,\n              revalidate: false,\n              status: payload.status\n            }\n          }\n        };\n      }\n      if (payload.type !== \"render\") {\n        throw new Error(\"Unexpected payload type\");\n      }\n      context.get(renderedRoutesContext).push(...payload.matches);\n      let results = {\n        routes: {}\n      };\n      const dataKey = isMutationMethod(request.method) ? \"actionData\" : \"loaderData\";\n      for (let [routeId, data2] of Object.entries(payload[dataKey] || {})) {\n        results.routes[routeId] = {\n          data: data2\n        };\n      }\n      if (payload.errors) {\n        for (let [routeId, error] of Object.entries(payload.errors)) {\n          results.routes[routeId] = {\n            error\n          };\n        }\n      }\n      return {\n        status: res.status,\n        data: results\n      };\n    } catch (e) {\n      throw new Error(\"Unable to decode RSC response\");\n    }\n  };\n}\nfunction RSCHydratedRouter({\n  createFromReadableStream,\n  fetch: fetchImplementation = fetch,\n  payload,\n  routeDiscovery = \"eager\",\n  getContext\n}) {\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  let {\n    router,\n    routeModules\n  } = React4.useMemo(() => createRouterFromPayload({\n    payload,\n    fetchImplementation,\n    getContext,\n    createFromReadableStream\n  }), [createFromReadableStream, payload, fetchImplementation, getContext]);\n  React4.useEffect(() => {\n    setIsHydrated();\n  }, []);\n  React4.useLayoutEffect(() => {\n    const globalVar = window;\n    if (!globalVar.__routerInitialized) {\n      globalVar.__routerInitialized = true;\n      globalVar.__reactRouterDataRouter.initialize();\n    }\n  }, []);\n  let [location2, setLocation] = React4.useState(router.state.location);\n  React4.useLayoutEffect(() => router.subscribe(newState => {\n    if (newState.location !== location2) {\n      setLocation(newState.location);\n    }\n  }), [router, location2]);\n  React4.useEffect(() => {\n    if (routeDiscovery === \"lazy\" ||\n    // @ts-expect-error - TS doesn't know about this yet\n    window.navigator?.connection?.saveData === true) {\n      return;\n    }\n    function registerElement(el) {\n      let path = el.tagName === \"FORM\" ? el.getAttribute(\"action\") : el.getAttribute(\"href\");\n      if (!path) {\n        return;\n      }\n      let pathname = el.tagName === \"A\" ? el.pathname : new URL(path, window.location.origin).pathname;\n      if (!discoveredPaths.has(pathname)) {\n        nextPaths.add(pathname);\n      }\n    }\n    async function fetchPatches() {\n      document.querySelectorAll(\"a[data-discover], form[data-discover]\").forEach(registerElement);\n      let paths = Array.from(nextPaths.keys()).filter(path => {\n        if (discoveredPaths.has(path)) {\n          nextPaths.delete(path);\n          return false;\n        }\n        return true;\n      });\n      if (paths.length === 0) {\n        return;\n      }\n      try {\n        await fetchAndApplyManifestPatches(paths, createFromReadableStream, fetchImplementation);\n      } catch (e) {\n        console.error(\"Failed to fetch manifest patches\", e);\n      }\n    }\n    let debouncedFetchPatches = debounce(fetchPatches, 100);\n    fetchPatches();\n    let observer = new MutationObserver(() => debouncedFetchPatches());\n    observer.observe(document.documentElement, {\n      subtree: true,\n      childList: true,\n      attributes: true,\n      attributeFilter: [\"data-discover\", \"href\", \"action\"]\n    });\n  }, [routeDiscovery, createFromReadableStream, fetchImplementation]);\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      v8_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: false,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: {\n      mode: \"lazy\",\n      manifestPath: \"/__manifest\"\n    },\n    routeModules\n  };\n  return /* @__PURE__ */React4.createElement(RSCRouterContext.Provider, {\n    value: true\n  }, /* @__PURE__ */React4.createElement(RSCRouterGlobalErrorBoundary, {\n    location: location2\n  }, /* @__PURE__ */React4.createElement(FrameworkContext.Provider, {\n    value: frameworkContext\n  }, /* @__PURE__ */React4.createElement(RouterProvider, {\n    router,\n    flushSync: ReactDOM.flushSync\n  }))));\n}\nfunction createRouteFromServerManifest(match, payload) {\n  let hasInitialData = payload && match.id in payload.loaderData;\n  let initialData = payload?.loaderData[match.id];\n  let hasInitialError = payload?.errors && match.id in payload.errors;\n  let initialError = payload?.errors?.[match.id];\n  let isHydrationRequest = match.clientLoader?.hydrate === true || !match.hasLoader ||\n  // If the route has a component but we don't have an element, we need to hit\n  // the server loader flow regardless of whether the client loader calls\n  // `serverLoader` or not, otherwise we'll have nothing to render.\n  match.hasComponent && !match.element;\n  invariant(window.__reactRouterRouteModules);\n  populateRSCRouteModules(window.__reactRouterRouteModules, match);\n  let dataRoute = {\n    id: match.id,\n    element: match.element,\n    errorElement: match.errorElement,\n    handle: match.handle,\n    hasErrorBoundary: match.hasErrorBoundary,\n    hydrateFallbackElement: match.hydrateFallbackElement,\n    index: match.index,\n    loader: match.clientLoader ? async (args, singleFetch) => {\n      try {\n        let result = await match.clientLoader({\n          ...args,\n          serverLoader: () => {\n            preventInvalidServerHandlerCall(\"loader\", match.id, match.hasLoader);\n            if (isHydrationRequest) {\n              if (hasInitialData) {\n                return initialData;\n              }\n              if (hasInitialError) {\n                throw initialError;\n              }\n            }\n            return callSingleFetch(singleFetch);\n          }\n        });\n        return result;\n      } finally {\n        isHydrationRequest = false;\n      }\n    } :\n    // We always make the call in this RSC world since even if we don't\n    // have a `loader` we may need to get the `element` implementation\n    (_, singleFetch) => callSingleFetch(singleFetch),\n    action: match.clientAction ? (args, singleFetch) => match.clientAction({\n      ...args,\n      serverAction: async () => {\n        preventInvalidServerHandlerCall(\"action\", match.id, match.hasLoader);\n        return await callSingleFetch(singleFetch);\n      }\n    }) : match.hasAction ? (_, singleFetch) => callSingleFetch(singleFetch) : () => {\n      throw noActionDefinedError(\"action\", match.id);\n    },\n    path: match.path,\n    shouldRevalidate: match.shouldRevalidate,\n    // We always have a \"loader\" in this RSC world since even if we don't\n    // have a `loader` we may need to get the `element` implementation\n    hasLoader: true,\n    hasClientLoader: match.clientLoader != null,\n    hasAction: match.hasAction,\n    hasClientAction: match.clientAction != null,\n    hasShouldRevalidate: match.shouldRevalidate != null\n  };\n  if (typeof dataRoute.loader === \"function\") {\n    dataRoute.loader.hydrate = shouldHydrateRouteLoader(match.id, match.clientLoader, match.hasLoader, false);\n  }\n  return dataRoute;\n}\nfunction callSingleFetch(singleFetch) {\n  invariant(typeof singleFetch === \"function\", \"Invalid singleFetch parameter\");\n  return singleFetch();\n}\nfunction preventInvalidServerHandlerCall(type, routeId, hasHandler) {\n  if (!hasHandler) {\n    let fn = type === \"action\" ? \"serverAction()\" : \"serverLoader()\";\n    let msg = `You are trying to call ${fn} on a route that does not have a server ${type} (routeId: \"${routeId}\")`;\n    console.error(msg);\n    throw new ErrorResponseImpl(400, \"Bad Request\", new Error(msg), true);\n  }\n}\nvar nextPaths = /* @__PURE__ */new Set();\nvar discoveredPathsMaxSize = 1e3;\nvar discoveredPaths = /* @__PURE__ */new Set();\nvar URL_LIMIT = 7680;\nfunction getManifestUrl(paths) {\n  if (paths.length === 0) {\n    return null;\n  }\n  if (paths.length === 1) {\n    return new URL(`${paths[0]}.manifest`, window.location.origin);\n  }\n  const globalVar = window;\n  let basename = (globalVar.__reactRouterDataRouter.basename ?? \"\").replace(/^\\/|\\/$/g, \"\");\n  let url = new URL(`${basename}/.manifest`, window.location.origin);\n  paths.sort().forEach(path => url.searchParams.append(\"p\", path));\n  return url;\n}\nasync function fetchAndApplyManifestPatches(paths, createFromReadableStream, fetchImplementation, signal) {\n  let url = getManifestUrl(paths);\n  if (url == null) {\n    return;\n  }\n  if (url.toString().length > URL_LIMIT) {\n    nextPaths.clear();\n    return;\n  }\n  let response = await fetchImplementation(new Request(url, {\n    signal\n  }));\n  if (!response.body || response.status < 200 || response.status >= 300) {\n    throw new Error(\"Unable to fetch new route matches from the server\");\n  }\n  let payload = await createFromReadableStream(response.body, {\n    temporaryReferences: void 0\n  });\n  if (payload.type !== \"manifest\") {\n    throw new Error(\"Failed to patch routes\");\n  }\n  paths.forEach(p => addToFifoQueue(p, discoveredPaths));\n  payload.patches.forEach(p => {\n    window.__reactRouterDataRouter.patchRoutes(p.parentId ?? null, [createRouteFromServerManifest(p)]);\n  });\n}\nfunction addToFifoQueue(path, queue) {\n  if (queue.size >= discoveredPathsMaxSize) {\n    let first = queue.values().next().value;\n    queue.delete(first);\n  }\n  queue.add(path);\n}\nfunction debounce(callback, wait) {\n  let timeoutId;\n  return (...args) => {\n    window.clearTimeout(timeoutId);\n    timeoutId = window.setTimeout(() => callback(...args), wait);\n  };\n}\n\n// lib/rsc/server.ssr.tsx\nimport * as React5 from \"react\";\n\n// lib/rsc/html-stream/server.ts\nvar encoder2 = new TextEncoder();\nvar trailer = \"</body></html>\";\nfunction injectRSCPayload(rscStream) {\n  let decoder = new TextDecoder();\n  let resolveFlightDataPromise;\n  let flightDataPromise = new Promise(resolve => resolveFlightDataPromise = resolve);\n  let startedRSC = false;\n  let buffered = [];\n  let timeout = null;\n  function flushBufferedChunks(controller) {\n    for (let chunk of buffered) {\n      let buf = decoder.decode(chunk, {\n        stream: true\n      });\n      if (buf.endsWith(trailer)) {\n        buf = buf.slice(0, -trailer.length);\n      }\n      controller.enqueue(encoder2.encode(buf));\n    }\n    buffered.length = 0;\n    timeout = null;\n  }\n  return new TransformStream({\n    transform(chunk, controller) {\n      buffered.push(chunk);\n      if (timeout) {\n        return;\n      }\n      timeout = setTimeout(async () => {\n        flushBufferedChunks(controller);\n        if (!startedRSC) {\n          startedRSC = true;\n          writeRSCStream(rscStream, controller).catch(err => controller.error(err)).then(resolveFlightDataPromise);\n        }\n      }, 0);\n    },\n    async flush(controller) {\n      await flightDataPromise;\n      if (timeout) {\n        clearTimeout(timeout);\n        flushBufferedChunks(controller);\n      }\n      controller.enqueue(encoder2.encode(\"</body></html>\"));\n    }\n  });\n}\nasync function writeRSCStream(rscStream, controller) {\n  let decoder = new TextDecoder(\"utf-8\", {\n    fatal: true\n  });\n  const reader = rscStream.getReader();\n  try {\n    let read;\n    while ((read = await reader.read()) && !read.done) {\n      const chunk = read.value;\n      try {\n        writeChunk(JSON.stringify(decoder.decode(chunk, {\n          stream: true\n        })), controller);\n      } catch (err) {\n        let base64 = JSON.stringify(btoa(String.fromCodePoint(...chunk)));\n        writeChunk(`Uint8Array.from(atob(${base64}), m => m.codePointAt(0))`, controller);\n      }\n    }\n  } finally {\n    reader.releaseLock();\n  }\n  let remaining = decoder.decode();\n  if (remaining.length) {\n    writeChunk(JSON.stringify(remaining), controller);\n  }\n}\nfunction writeChunk(chunk, controller) {\n  controller.enqueue(encoder2.encode(`<script>${escapeScript(`(self.__FLIGHT_DATA||=[]).push(${chunk})`)}</script>`));\n}\nfunction escapeScript(script) {\n  return script.replace(/<!--/g, \"<\\\\!--\").replace(/<\\/(script)/gi, \"</\\\\$1\");\n}\n\n// lib/rsc/server.ssr.tsx\nvar REACT_USE = \"use\";\nvar useImpl = React5[REACT_USE];\nfunction useSafe(promise) {\n  if (useImpl) {\n    return useImpl(promise);\n  }\n  throw new Error(\"React Router v7 requires React 19+ for RSC features.\");\n}\nasync function routeRSCServerRequest({\n  request,\n  fetchServer,\n  createFromReadableStream,\n  renderHTML,\n  hydrate = true\n}) {\n  const url = new URL(request.url);\n  const isDataRequest = isReactServerRequest(url);\n  const respondWithRSCPayload = isDataRequest || isManifestRequest(url) || request.headers.has(\"rsc-action-id\");\n  const serverResponse = await fetchServer(request);\n  if (respondWithRSCPayload || serverResponse.headers.get(\"React-Router-Resource\") === \"true\") {\n    return serverResponse;\n  }\n  if (!serverResponse.body) {\n    throw new Error(\"Missing body in server response\");\n  }\n  const detectRedirectResponse = serverResponse.clone();\n  let serverResponseB = null;\n  if (hydrate) {\n    serverResponseB = serverResponse.clone();\n  }\n  const body = serverResponse.body;\n  let buffer;\n  let streamControllers = [];\n  const createStream = () => {\n    if (!buffer) {\n      buffer = [];\n      return body.pipeThrough(new TransformStream({\n        transform(chunk, controller) {\n          buffer.push(chunk);\n          controller.enqueue(chunk);\n          streamControllers.forEach(c => c.enqueue(chunk));\n        },\n        flush() {\n          streamControllers.forEach(c => c.close());\n          streamControllers = [];\n        }\n      }));\n    }\n    return new ReadableStream({\n      start(controller) {\n        buffer.forEach(chunk => controller.enqueue(chunk));\n        streamControllers.push(controller);\n      }\n    });\n  };\n  const getPayload = async () => {\n    return createFromReadableStream(createStream());\n  };\n  try {\n    if (!detectRedirectResponse.body) {\n      throw new Error(\"Failed to clone server response\");\n    }\n    const payload = await createFromReadableStream(detectRedirectResponse.body);\n    if (serverResponse.status === SINGLE_FETCH_REDIRECT_STATUS && payload.type === \"redirect\") {\n      const headers2 = new Headers(serverResponse.headers);\n      headers2.delete(\"Content-Encoding\");\n      headers2.delete(\"Content-Length\");\n      headers2.delete(\"Content-Type\");\n      headers2.delete(\"x-remix-response\");\n      headers2.set(\"Location\", payload.location);\n      return new Response(serverResponseB?.body || \"\", {\n        headers: headers2,\n        status: payload.status,\n        statusText: serverResponse.statusText\n      });\n    }\n    const html = await renderHTML(getPayload);\n    const headers = new Headers(serverResponse.headers);\n    headers.set(\"Content-Type\", \"text/html\");\n    if (!hydrate) {\n      return new Response(html, {\n        status: serverResponse.status,\n        headers\n      });\n    }\n    if (!serverResponseB?.body) {\n      throw new Error(\"Failed to clone server response\");\n    }\n    const body2 = html.pipeThrough(injectRSCPayload(serverResponseB.body));\n    return new Response(body2, {\n      status: serverResponse.status,\n      headers\n    });\n  } catch (reason) {\n    if (reason instanceof Response) {\n      return reason;\n    }\n    throw reason;\n  }\n}\nfunction RSCStaticRouter({\n  getPayload\n}) {\n  const payload = useSafe(getPayload());\n  if (payload.type === \"redirect\") {\n    throw new Response(null, {\n      status: payload.status,\n      headers: {\n        Location: payload.location\n      }\n    });\n  }\n  if (payload.type !== \"render\") return null;\n  let patchedLoaderData = {\n    ...payload.loaderData\n  };\n  for (const match of payload.matches) {\n    if (shouldHydrateRouteLoader(match.id, match.clientLoader, match.hasLoader, false) && (match.hydrateFallbackElement || !match.hasLoader)) {\n      delete patchedLoaderData[match.id];\n    }\n  }\n  const context = {\n    actionData: payload.actionData,\n    actionHeaders: {},\n    basename: payload.basename,\n    errors: payload.errors,\n    loaderData: patchedLoaderData,\n    loaderHeaders: {},\n    location: payload.location,\n    statusCode: 200,\n    matches: payload.matches.map(match => ({\n      params: match.params,\n      pathname: match.pathname,\n      pathnameBase: match.pathnameBase,\n      route: {\n        id: match.id,\n        action: match.hasAction || !!match.clientAction,\n        handle: match.handle,\n        hasErrorBoundary: match.hasErrorBoundary,\n        loader: match.hasLoader || !!match.clientLoader,\n        index: match.index,\n        path: match.path,\n        shouldRevalidate: match.shouldRevalidate\n      }\n    }))\n  };\n  const router = createStaticRouter(payload.matches.reduceRight((previous, match) => {\n    const route = {\n      id: match.id,\n      action: match.hasAction || !!match.clientAction,\n      element: match.element,\n      errorElement: match.errorElement,\n      handle: match.handle,\n      hasErrorBoundary: !!match.errorElement,\n      hydrateFallbackElement: match.hydrateFallbackElement,\n      index: match.index,\n      loader: match.hasLoader || !!match.clientLoader,\n      path: match.path,\n      shouldRevalidate: match.shouldRevalidate\n    };\n    if (previous.length > 0) {\n      route.children = previous;\n    }\n    return [route];\n  }, []), context);\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      v8_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: false,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: {\n      mode: \"lazy\",\n      manifestPath: \"/__manifest\"\n    },\n    routeModules: createRSCRouteModules(payload)\n  };\n  return /* @__PURE__ */React5.createElement(RSCRouterContext.Provider, {\n    value: true\n  }, /* @__PURE__ */React5.createElement(RSCRouterGlobalErrorBoundary, {\n    location: payload.location\n  }, /* @__PURE__ */React5.createElement(FrameworkContext.Provider, {\n    value: frameworkContext\n  }, /* @__PURE__ */React5.createElement(StaticRouterProvider, {\n    context,\n    router,\n    hydrate: false,\n    nonce: payload.nonce\n  }))));\n}\nfunction isReactServerRequest(url) {\n  return url.pathname.endsWith(\".rsc\");\n}\nfunction isManifestRequest(url) {\n  return url.pathname.endsWith(\".manifest\");\n}\n\n// lib/rsc/html-stream/browser.ts\nfunction getRSCStream() {\n  let encoder3 = new TextEncoder();\n  let streamController = null;\n  let rscStream = new ReadableStream({\n    start(controller) {\n      if (typeof window === \"undefined\") {\n        return;\n      }\n      let handleChunk = chunk => {\n        if (typeof chunk === \"string\") {\n          controller.enqueue(encoder3.encode(chunk));\n        } else {\n          controller.enqueue(chunk);\n        }\n      };\n      window.__FLIGHT_DATA || (window.__FLIGHT_DATA = []);\n      window.__FLIGHT_DATA.forEach(handleChunk);\n      window.__FLIGHT_DATA.push = chunk => {\n        handleChunk(chunk);\n        return 0;\n      };\n      streamController = controller;\n    }\n  });\n  if (typeof document !== \"undefined\" && document.readyState === \"loading\") {\n    document.addEventListener(\"DOMContentLoaded\", () => {\n      streamController?.close();\n    });\n  } else {\n    streamController?.close();\n  }\n  return rscStream;\n}\n\n// lib/dom/ssr/errors.ts\nfunction deserializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(val.status, val.statusText, val.data, val.internal === true);\n    } else if (val && val.__type === \"Error\") {\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            let error = new ErrorConstructor(val.message);\n            error.stack = val.stack;\n            serialized[key] = error;\n          } catch (e) {}\n        }\n      }\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        error.stack = val.stack;\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\nexport { ServerRouter, createRoutesStub, createCookie, isCookie, ServerMode, setDevServerHooks, createRequestHandler, createSession, isSession, createSessionStorage, createCookieSessionStorage, createMemorySessionStorage, href, getHydrationData, RSCDefaultRootErrorBoundary, createCallServer, RSCHydratedRouter, routeRSCServerRequest, RSCStaticRouter, getRSCStream, deserializeErrors };", "map": {"version": 3, "names": ["ENABLE_DEV_WARNINGS", "ErrorResponseImpl", "FrameworkContext", "NO_BODY_STATUS_CODES", "Outlet", "RSCRouterContext", "RemixErrorBoundary", "RouterContextProvider", "RouterProvider", "SINGLE_FETCH_REDIRECT_STATUS", "SingleFetchRedirectSymbol", "StaticRouterProvider", "StreamTransfer", "convertRoutesToDataRoutes", "createBrowserHistory", "createContext", "createMemoryRouter", "createRequestInit", "createRouter", "createServerRoutes", "createStaticHandler", "createStaticRouter", "decodeViaTurboStream", "encode", "escapeHtml", "getManifestPath", "getSingleFetchDataStrategyImpl", "getStaticContextFromError", "invariant", "isDataWithResponseInit", "isMutationMethod", "isRedirectResponse", "isRedirectStatusCode", "isResponse", "isRouteErrorResponse", "matchRoutes", "noActionDefinedError", "redirect", "redirectDocument", "replace", "setIsHydrated", "shouldHydrateRouteLoader", "singleFetchUrl", "stripBasename", "stripIndexParam", "useRouteError", "warnOnce", "withComponentProps", "withErrorBoundaryProps", "withHydrateFallbackProps", "React", "ServerRouter", "context", "url", "nonce", "URL", "manifest", "routeModules", "criticalCss", "serverHandoffString", "routes", "future", "isSpaMode", "staticHandlerContext", "loaderData", "match", "matches", "routeId", "route", "id", "manifestRoute", "clientLoader", "<PERSON><PERSON><PERSON><PERSON>", "HydrateFallback", "router", "createElement", "Fragment", "Provider", "value", "ssr", "routeDiscovery", "serializeError", "renderMeta", "location", "state", "hydrate", "serverHandoffStream", "Suspense", "identifier", "reader", "<PERSON><PERSON><PERSON><PERSON>", "textDecoder", "TextDecoder", "React2", "createRoutesStub", "_context", "RoutesTestStub", "initialEntries", "initialIndex", "hydrationData", "routerRef", "useRef", "frameworkContextRef", "current", "unstable_subResourceIntegrity", "v8_middleware", "entry", "imports", "module", "version", "mode", "manifestPath", "patched", "processRoutes", "r", "parentId", "map", "Error", "newRoute", "path", "index", "Component", "Error<PERSON>ou<PERSON><PERSON>", "action", "args", "loader", "handle", "shouldRevalidate", "entryRoute", "hasAction", "hasClientAction", "hasClientLoader", "hasClientMiddleware", "hasErrorBou<PERSON>ry", "clientActionModule", "clientLoaderModule", "clientMiddlewareModule", "hydrateFallbackModule", "default", "links", "meta", "children", "parse", "serialize", "encoder", "TextEncoder", "sign", "secret", "data2", "key", "create<PERSON><PERSON>", "signature", "crypto", "subtle", "hash", "btoa", "String", "fromCharCode", "Uint8Array", "unsign", "cookie", "lastIndexOf", "slice", "byteStringToUint8Array", "atob", "valid", "verify", "error", "usages", "importKey", "name", "byteString", "array", "length", "i", "charCodeAt", "createCookie", "cookieOptions", "secrets", "options", "sameSite", "warnOnceAboutExpiresCookie", "expires", "isSigned", "maxAge", "Date", "now", "cookieHeader", "parseOptions", "cookies", "decoded", "decodeCookieValue", "serializeOptions", "encodeCookieValue", "<PERSON><PERSON><PERSON><PERSON>", "object", "encoded", "encodeData", "unsignedValue", "decodeData", "myUnescape", "encodeURIComponent", "JSON", "stringify", "decodeURIComponent", "myEscape", "str", "toString", "result", "chr", "code", "char<PERSON>t", "exec", "hex", "toUpperCase", "part", "parseInt", "createEntryRouteModules", "Object", "keys", "reduce", "memo", "ServerMode", "ServerMode2", "isServerMode", "sanitizeError", "serverMode", "sanitized", "stack", "sanitizeErrors", "errors", "entries", "acc", "assign", "message", "serializeErrors", "serialized", "val", "__type", "__subType", "matchServerRoutes", "pathname", "basename", "params", "callRouteHandler", "handler", "request", "stripRoutesParam", "stripIndexParam2", "init", "status", "Response", "indexValues", "searchParams", "getAll", "delete", "indexValuesToKeep", "indexValue", "push", "<PERSON><PERSON><PERSON>", "append", "method", "body", "headers", "signal", "duplex", "Request", "href", "invariant2", "console", "globalDevServerHooksKey", "setDevServerHooks", "devServerHooks", "globalThis", "getDevServerHooks", "getBuildTimeHeader", "headerName", "process", "env", "IS_RR_BUILD_REQUEST", "get", "e", "groupRoutesByParentId", "values", "for<PERSON>ach", "createRoutes", "routesByParentId", "createStaticHandlerDataRoutes", "commonRoute", "middleware", "preRenderedData", "decodeURI", "uint8array", "stream", "ReadableStream", "start", "controller", "enqueue", "close", "global", "reload", "data", "caseSensitive", "createServerHandoffString", "serverHandoff", "splitCookiesString", "getDocumentHeaders", "build", "getDocumentHeadersImpl", "m", "getRouteHeadersFn", "_defaultHeaders", "boundaryIdx", "findIndex", "errorHeaders", "actionHeaders", "actionData", "loaderHeaders", "some", "hasOwnProperty", "defaultHeaders", "Headers", "parentHeaders", "idx", "includeErrorHeaders", "includeErrorCookies", "headersFn", "headers2", "prependCookies", "childHeaders", "parentSetCookieString", "childCookies", "Set", "getSetCookie", "has", "SERVER_NO_BODY_STATUS_CODES", "singleFetchAction", "static<PERSON><PERSON><PERSON>", "handlerUrl", "loadContext", "handleError", "handlerRequest", "query", "requestContext", "skipL<PERSON>derError<PERSON><PERSON>bling", "skipRevalidation", "generateMiddlewareResponse", "innerResult", "handleQueryResult", "handleQueryError", "staticContextToResponse", "generateSingleFetchResponse", "statusCode", "err", "singleFetchResult", "singleFetchLoaders", "routesParam", "loadRouteIds", "split", "filterMatchesToLoad", "results", "loadedMatches", "filter", "resultHeaders", "set", "encodeViaTurboStream", "streamTimeout", "generateSingleFetchRedirectResponse", "redirectResponse", "redirect2", "getSingleFetchRedirect", "revalidate", "requestSignal", "AbortController", "timeoutId", "setTimeout", "abort", "addEventListener", "clearTimeout", "plugins", "data3", "statusText", "postPlugins", "fromEntries", "derive", "dataRoutes", "<PERSON><PERSON><PERSON><PERSON>", "aborted", "createRequestHandler", "_build", "requestHandler", "initialContext", "derived", "processRequestError", "returnLastResortErrorResponse", "normalizedBasename", "normalizedPath", "endsWith", "decodedPath", "<PERSON><PERSON><PERSON>", "prerender", "includes", "manifestUrl", "res", "handleManifestRequest", "response", "singleFetchMatches", "handleSingleFetchRequest", "handleDataRequest", "handleResourceRequest", "unstable_getCriticalCss", "getCriticalCss", "handleDocumentRequest", "assets", "patches", "paths", "startsWith", "segments", "_", "partialPath", "join", "add", "json", "renderHtml", "isSpaMode2", "baseServerHandoff", "entryContext", "handleDocumentRequestFunction", "errorForSecondRender", "unwrapResponse", "state2", "error2", "queryRoute", "handleQueryRouteResult", "handleQueryRouteError", "errorResponseToJson", "newError", "errorResponse", "contentType", "test", "text", "flash", "createSession", "initialData", "Map", "flashName", "unset", "isSession", "createSessionStorage", "cookieArg", "createData", "readData", "updateData", "deleteData", "warnOnceAboutSigningSessionCookie", "getSession", "commitSession", "session", "destroySession", "createCookieSessionStorage", "serializedCookie", "_session", "createMemorySessionStorage", "Math", "random", "substring", "param", "questionMark", "isRequired", "React4", "ReactDOM", "getHydrationData", "getRouteInfo", "location2", "initialMatches", "routeInfo", "hasHydrateFallback", "React3", "RSCRouterGlobalErrorBoundary", "constructor", "props", "getDerivedStateFromError", "getDerivedStateFromProps", "render", "RSCDefaultRootErrorBoundaryImpl", "renderAppShell", "ErrorWrapper", "title", "lang", "charSet", "content", "style", "fontFamily", "padding", "heyDeveloper", "dangerouslySetInnerHTML", "__html", "fontSize", "errorInstance", "errorString", "background", "color", "overflow", "RSCDefaultRootErrorBoundary", "hasRootLayout", "createRSCRouteModules", "payload", "populateRSCRouteModules", "Array", "isArray", "noopComponent", "createCallServer", "createFromReadableStream", "createTemporaryReferenceSet", "encodeReply", "fetch", "fetchImplementation", "globalVar", "window", "landedActionId", "actionId", "__routerActionID", "temporaryReferences", "Accept", "type", "__reactRouterDataRouter", "navigate", "actionResult", "rerender", "startTransition", "lastMatch", "patchRoutes", "createRouteFromServerManifest", "_internalSetStateDoNotUseOrYouWillBreakYourApp", "createRouterFromPayload", "getContext", "__reactRouterRouteModules", "patch", "reduceRight", "previous", "childrenToPatch", "history", "find", "hydrateFallbackElement", "patchRoutesOnNavigation", "discoveredPaths", "fetchAndApplyManifestPatches", "dataStrategy", "getRSCSingleFetchDataStrategy", "initialized", "__routerInitialized", "initialize", "lastLoaderData", "subscribe", "_updateRoutesForHMR", "routeUpdateByRouteId", "oldRoutes", "newRoutes", "walkRoutes", "routes2", "routeUpdate", "routeModule", "hasComponent", "clientAction", "element", "errorElement", "updatedRoute", "_internalSetRoutes", "renderedRoutesContext", "getRouter", "M", "hasShouldRevalidate", "getFetchAndDecodeViaRSC", "runClientMiddleware", "renderedRoutesById", "renderedRoutes", "rendered", "targetRoutes", "dataKey", "RSCHydratedRouter", "useMemo", "useEffect", "useLayoutEffect", "setLocation", "useState", "newState", "navigator", "connection", "saveData", "registerElement", "el", "tagName", "getAttribute", "origin", "nextPaths", "fetchPatches", "document", "querySelectorAll", "from", "debouncedFetchPatches", "debounce", "observer", "MutationObserver", "observe", "documentElement", "subtree", "childList", "attributes", "attributeFilter", "frameworkContext", "flushSync", "hasInitialData", "hasInitialError", "initialError", "isHydrationRequest", "dataRoute", "singleFetch", "serverLoader", "preventInvalidServerHandlerCall", "callSingleFetch", "serverAction", "<PERSON><PERSON><PERSON><PERSON>", "fn", "msg", "discoveredPathsMaxSize", "URL_LIMIT", "getManifestUrl", "sort", "clear", "p", "addToFifoQueue", "queue", "size", "first", "next", "callback", "wait", "React5", "encoder2", "trailer", "injectRSCPayload", "rscStream", "decoder", "resolveFlightDataPromise", "flightDataPromise", "Promise", "resolve", "startedRSC", "buffered", "timeout", "flushBufferedChunks", "chunk", "buf", "decode", "TransformStream", "transform", "writeRSCStream", "catch", "then", "flush", "fatal", "read", "done", "writeChunk", "base64", "fromCodePoint", "releaseLock", "remaining", "escapeScript", "script", "REACT_USE", "useImpl", "useSafe", "promise", "routeRSCServerRequest", "fetchServer", "renderHTML", "isDataRequest", "isReactServerRequest", "respondWithRSCPayload", "isManifestRequest", "serverResponse", "detectRedirectResponse", "clone", "serverResponseB", "buffer", "streamControllers", "createStream", "pipeThrough", "c", "getPayload", "html", "body2", "reason", "RSCStatic<PERSON><PERSON><PERSON>", "Location", "patchedLoaderData", "pathnameBase", "getRSCStream", "encoder3", "streamController", "handleChunk", "__FLIGHT_DATA", "readyState", "deserializeErrors", "internal", "ErrorConstructor"], "sources": ["C:/Developer/Web Development/reedsoft/node_modules/react-router/dist/development/chunk-AKKEMMKB.mjs"], "sourcesContent": ["/**\n * react-router v7.9.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\nimport {\n  ENABLE_DEV_WARNINGS,\n  ErrorResponseImpl,\n  FrameworkContext,\n  NO_BODY_STATUS_CODES,\n  Outlet,\n  RSCRouterContext,\n  RemixErrorBoundary,\n  RouterContextProvider,\n  RouterProvider,\n  SINGLE_FETCH_REDIRECT_STATUS,\n  SingleFetchRedirectSymbol,\n  StaticRouterProvider,\n  StreamTransfer,\n  convertRoutesToDataRoutes,\n  createBrowserHistory,\n  createContext,\n  createMemoryRouter,\n  createRequestInit,\n  createRouter,\n  createServerRoutes,\n  createStaticHandler,\n  createStaticRouter,\n  decodeViaTurboStream,\n  encode,\n  escapeHtml,\n  getManifestPath,\n  getSingleFetchDataStrategyImpl,\n  getStaticContextFromError,\n  invariant,\n  isDataWithResponseInit,\n  isMutationMethod,\n  isRedirectResponse,\n  isRedirectStatusCode,\n  isResponse,\n  isRouteErrorResponse,\n  matchRoutes,\n  noActionDefinedError,\n  redirect,\n  redirectDocument,\n  replace,\n  setIsHydrated,\n  shouldHydrateRouteLoader,\n  singleFetchUrl,\n  stripBasename,\n  stripIndexParam,\n  useRouteError,\n  warnOnce,\n  withComponentProps,\n  withErrorBoundaryProps,\n  withHydrateFallbackProps\n} from \"./chunk-S5YDGZLY.mjs\";\n\n// lib/dom/ssr/server.tsx\nimport * as React from \"react\";\nfunction ServerRouter({\n  context,\n  url,\n  nonce\n}) {\n  if (typeof url === \"string\") {\n    url = new URL(url);\n  }\n  let { manifest, routeModules, criticalCss, serverHandoffString } = context;\n  let routes = createServerRoutes(\n    manifest.routes,\n    routeModules,\n    context.future,\n    context.isSpaMode\n  );\n  context.staticHandlerContext.loaderData = {\n    ...context.staticHandlerContext.loaderData\n  };\n  for (let match of context.staticHandlerContext.matches) {\n    let routeId = match.route.id;\n    let route = routeModules[routeId];\n    let manifestRoute = context.manifest.routes[routeId];\n    if (route && manifestRoute && shouldHydrateRouteLoader(\n      routeId,\n      route.clientLoader,\n      manifestRoute.hasLoader,\n      context.isSpaMode\n    ) && (route.HydrateFallback || !manifestRoute.hasLoader)) {\n      delete context.staticHandlerContext.loaderData[routeId];\n    }\n  }\n  let router = createStaticRouter(routes, context.staticHandlerContext);\n  return /* @__PURE__ */ React.createElement(React.Fragment, null, /* @__PURE__ */ React.createElement(\n    FrameworkContext.Provider,\n    {\n      value: {\n        manifest,\n        routeModules,\n        criticalCss,\n        serverHandoffString,\n        future: context.future,\n        ssr: context.ssr,\n        isSpaMode: context.isSpaMode,\n        routeDiscovery: context.routeDiscovery,\n        serializeError: context.serializeError,\n        renderMeta: context.renderMeta\n      }\n    },\n    /* @__PURE__ */ React.createElement(RemixErrorBoundary, { location: router.state.location }, /* @__PURE__ */ React.createElement(\n      StaticRouterProvider,\n      {\n        router,\n        context: context.staticHandlerContext,\n        hydrate: false\n      }\n    ))\n  ), context.serverHandoffStream ? /* @__PURE__ */ React.createElement(React.Suspense, null, /* @__PURE__ */ React.createElement(\n    StreamTransfer,\n    {\n      context,\n      identifier: 0,\n      reader: context.serverHandoffStream.getReader(),\n      textDecoder: new TextDecoder(),\n      nonce\n    }\n  )) : null);\n}\n\n// lib/dom/ssr/routes-test-stub.tsx\nimport * as React2 from \"react\";\nfunction createRoutesStub(routes, _context) {\n  return function RoutesTestStub({\n    initialEntries,\n    initialIndex,\n    hydrationData,\n    future\n  }) {\n    let routerRef = React2.useRef();\n    let frameworkContextRef = React2.useRef();\n    if (routerRef.current == null) {\n      frameworkContextRef.current = {\n        future: {\n          unstable_subResourceIntegrity: future?.unstable_subResourceIntegrity === true,\n          v8_middleware: future?.v8_middleware === true\n        },\n        manifest: {\n          routes: {},\n          entry: { imports: [], module: \"\" },\n          url: \"\",\n          version: \"\"\n        },\n        routeModules: {},\n        ssr: false,\n        isSpaMode: false,\n        routeDiscovery: { mode: \"lazy\", manifestPath: \"/__manifest\" }\n      };\n      let patched = processRoutes(\n        // @ts-expect-error `StubRouteObject` is stricter about `loader`/`action`\n        // types compared to `AgnosticRouteObject`\n        convertRoutesToDataRoutes(routes, (r) => r),\n        _context !== void 0 ? _context : future?.v8_middleware ? new RouterContextProvider() : {},\n        frameworkContextRef.current.manifest,\n        frameworkContextRef.current.routeModules\n      );\n      routerRef.current = createMemoryRouter(patched, {\n        initialEntries,\n        initialIndex,\n        hydrationData\n      });\n    }\n    return /* @__PURE__ */ React2.createElement(FrameworkContext.Provider, { value: frameworkContextRef.current }, /* @__PURE__ */ React2.createElement(RouterProvider, { router: routerRef.current }));\n  };\n}\nfunction processRoutes(routes, context, manifest, routeModules, parentId) {\n  return routes.map((route) => {\n    if (!route.id) {\n      throw new Error(\n        \"Expected a route.id in react-router processRoutes() function\"\n      );\n    }\n    let newRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      Component: route.Component ? withComponentProps(route.Component) : void 0,\n      HydrateFallback: route.HydrateFallback ? withHydrateFallbackProps(route.HydrateFallback) : void 0,\n      ErrorBoundary: route.ErrorBoundary ? withErrorBoundaryProps(route.ErrorBoundary) : void 0,\n      action: route.action ? (args) => route.action({ ...args, context }) : void 0,\n      loader: route.loader ? (args) => route.loader({ ...args, context }) : void 0,\n      handle: route.handle,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    let entryRoute = {\n      id: route.id,\n      path: route.path,\n      index: route.index,\n      parentId,\n      hasAction: route.action != null,\n      hasLoader: route.loader != null,\n      // When testing routes, you should be stubbing loader/action/middleware,\n      // not trying to re-implement the full loader/clientLoader/SSR/hydration\n      // flow. That is better tested via E2E tests.\n      hasClientAction: false,\n      hasClientLoader: false,\n      hasClientMiddleware: false,\n      hasErrorBoundary: route.ErrorBoundary != null,\n      // any need for these?\n      module: \"build/stub-path-to-module.js\",\n      clientActionModule: void 0,\n      clientLoaderModule: void 0,\n      clientMiddlewareModule: void 0,\n      hydrateFallbackModule: void 0\n    };\n    manifest.routes[newRoute.id] = entryRoute;\n    routeModules[route.id] = {\n      default: newRoute.Component || Outlet,\n      ErrorBoundary: newRoute.ErrorBoundary || void 0,\n      handle: route.handle,\n      links: route.links,\n      meta: route.meta,\n      shouldRevalidate: route.shouldRevalidate\n    };\n    if (route.children) {\n      newRoute.children = processRoutes(\n        route.children,\n        context,\n        manifest,\n        routeModules,\n        newRoute.id\n      );\n    }\n    return newRoute;\n  });\n}\n\n// lib/server-runtime/cookies.ts\nimport { parse, serialize } from \"cookie\";\n\n// lib/server-runtime/crypto.ts\nvar encoder = /* @__PURE__ */ new TextEncoder();\nvar sign = async (value, secret) => {\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"sign\"]);\n  let signature = await crypto.subtle.sign(\"HMAC\", key, data2);\n  let hash = btoa(String.fromCharCode(...new Uint8Array(signature))).replace(\n    /=+$/,\n    \"\"\n  );\n  return value + \".\" + hash;\n};\nvar unsign = async (cookie, secret) => {\n  let index = cookie.lastIndexOf(\".\");\n  let value = cookie.slice(0, index);\n  let hash = cookie.slice(index + 1);\n  let data2 = encoder.encode(value);\n  let key = await createKey(secret, [\"verify\"]);\n  try {\n    let signature = byteStringToUint8Array(atob(hash));\n    let valid = await crypto.subtle.verify(\"HMAC\", key, signature, data2);\n    return valid ? value : false;\n  } catch (error) {\n    return false;\n  }\n};\nvar createKey = async (secret, usages) => crypto.subtle.importKey(\n  \"raw\",\n  encoder.encode(secret),\n  { name: \"HMAC\", hash: \"SHA-256\" },\n  false,\n  usages\n);\nfunction byteStringToUint8Array(byteString) {\n  let array = new Uint8Array(byteString.length);\n  for (let i = 0; i < byteString.length; i++) {\n    array[i] = byteString.charCodeAt(i);\n  }\n  return array;\n}\n\n// lib/server-runtime/cookies.ts\nvar createCookie = (name, cookieOptions = {}) => {\n  let { secrets = [], ...options } = {\n    path: \"/\",\n    sameSite: \"lax\",\n    ...cookieOptions\n  };\n  warnOnceAboutExpiresCookie(name, options.expires);\n  return {\n    get name() {\n      return name;\n    },\n    get isSigned() {\n      return secrets.length > 0;\n    },\n    get expires() {\n      return typeof options.maxAge !== \"undefined\" ? new Date(Date.now() + options.maxAge * 1e3) : options.expires;\n    },\n    async parse(cookieHeader, parseOptions) {\n      if (!cookieHeader) return null;\n      let cookies = parse(cookieHeader, { ...options, ...parseOptions });\n      if (name in cookies) {\n        let value = cookies[name];\n        if (typeof value === \"string\" && value !== \"\") {\n          let decoded = await decodeCookieValue(value, secrets);\n          return decoded;\n        } else {\n          return \"\";\n        }\n      } else {\n        return null;\n      }\n    },\n    async serialize(value, serializeOptions) {\n      return serialize(\n        name,\n        value === \"\" ? \"\" : await encodeCookieValue(value, secrets),\n        {\n          ...options,\n          ...serializeOptions\n        }\n      );\n    }\n  };\n};\nvar isCookie = (object) => {\n  return object != null && typeof object.name === \"string\" && typeof object.isSigned === \"boolean\" && typeof object.parse === \"function\" && typeof object.serialize === \"function\";\n};\nasync function encodeCookieValue(value, secrets) {\n  let encoded = encodeData(value);\n  if (secrets.length > 0) {\n    encoded = await sign(encoded, secrets[0]);\n  }\n  return encoded;\n}\nasync function decodeCookieValue(value, secrets) {\n  if (secrets.length > 0) {\n    for (let secret of secrets) {\n      let unsignedValue = await unsign(value, secret);\n      if (unsignedValue !== false) {\n        return decodeData(unsignedValue);\n      }\n    }\n    return null;\n  }\n  return decodeData(value);\n}\nfunction encodeData(value) {\n  return btoa(myUnescape(encodeURIComponent(JSON.stringify(value))));\n}\nfunction decodeData(value) {\n  try {\n    return JSON.parse(decodeURIComponent(myEscape(atob(value))));\n  } catch (error) {\n    return {};\n  }\n}\nfunction myEscape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, code;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (/[\\w*+\\-./@]/.exec(chr)) {\n      result += chr;\n    } else {\n      code = chr.charCodeAt(0);\n      if (code < 256) {\n        result += \"%\" + hex(code, 2);\n      } else {\n        result += \"%u\" + hex(code, 4).toUpperCase();\n      }\n    }\n  }\n  return result;\n}\nfunction hex(code, length) {\n  let result = code.toString(16);\n  while (result.length < length) result = \"0\" + result;\n  return result;\n}\nfunction myUnescape(value) {\n  let str = value.toString();\n  let result = \"\";\n  let index = 0;\n  let chr, part;\n  while (index < str.length) {\n    chr = str.charAt(index++);\n    if (chr === \"%\") {\n      if (str.charAt(index) === \"u\") {\n        part = str.slice(index + 1, index + 5);\n        if (/^[\\da-f]{4}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 5;\n          continue;\n        }\n      } else {\n        part = str.slice(index, index + 2);\n        if (/^[\\da-f]{2}$/i.exec(part)) {\n          result += String.fromCharCode(parseInt(part, 16));\n          index += 2;\n          continue;\n        }\n      }\n    }\n    result += chr;\n  }\n  return result;\n}\nfunction warnOnceAboutExpiresCookie(name, expires) {\n  warnOnce(\n    !expires,\n    `The \"${name}\" cookie has an \"expires\" property set. This will cause the expires value to not be updated when the session is committed. Instead, you should set the expires value when serializing the cookie. You can use \\`commitSession(session, { expires })\\` if using a session storage object, or \\`cookie.serialize(\"value\", { expires })\\` if you're using the cookie directly.`\n  );\n}\n\n// lib/server-runtime/entry.ts\nfunction createEntryRouteModules(manifest) {\n  return Object.keys(manifest).reduce((memo, routeId) => {\n    let route = manifest[routeId];\n    if (route) {\n      memo[routeId] = route.module;\n    }\n    return memo;\n  }, {});\n}\n\n// lib/server-runtime/mode.ts\nvar ServerMode = /* @__PURE__ */ ((ServerMode2) => {\n  ServerMode2[\"Development\"] = \"development\";\n  ServerMode2[\"Production\"] = \"production\";\n  ServerMode2[\"Test\"] = \"test\";\n  return ServerMode2;\n})(ServerMode || {});\nfunction isServerMode(value) {\n  return value === \"development\" /* Development */ || value === \"production\" /* Production */ || value === \"test\" /* Test */;\n}\n\n// lib/server-runtime/errors.ts\nfunction sanitizeError(error, serverMode) {\n  if (error instanceof Error && serverMode !== \"development\" /* Development */) {\n    let sanitized = new Error(\"Unexpected Server Error\");\n    sanitized.stack = void 0;\n    return sanitized;\n  }\n  return error;\n}\nfunction sanitizeErrors(errors, serverMode) {\n  return Object.entries(errors).reduce((acc, [routeId, error]) => {\n    return Object.assign(acc, { [routeId]: sanitizeError(error, serverMode) });\n  }, {});\n}\nfunction serializeError(error, serverMode) {\n  let sanitized = sanitizeError(error, serverMode);\n  return {\n    message: sanitized.message,\n    stack: sanitized.stack\n  };\n}\nfunction serializeErrors(errors, serverMode) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (isRouteErrorResponse(val)) {\n      serialized[key] = { ...val, __type: \"RouteErrorResponse\" };\n    } else if (val instanceof Error) {\n      let sanitized = sanitizeError(val, serverMode);\n      serialized[key] = {\n        message: sanitized.message,\n        stack: sanitized.stack,\n        __type: \"Error\",\n        // If this is a subclass (i.e., ReferenceError), send up the type so we\n        // can re-create the same type during hydration.  This will only apply\n        // in dev mode since all production errors are sanitized to normal\n        // Error instances\n        ...sanitized.name !== \"Error\" ? {\n          __subType: sanitized.name\n        } : {}\n      };\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n// lib/server-runtime/routeMatching.ts\nfunction matchServerRoutes(routes, pathname, basename) {\n  let matches = matchRoutes(\n    routes,\n    pathname,\n    basename\n  );\n  if (!matches) return null;\n  return matches.map((match) => ({\n    params: match.params,\n    pathname: match.pathname,\n    route: match.route\n  }));\n}\n\n// lib/server-runtime/data.ts\nasync function callRouteHandler(handler, args) {\n  let result = await handler({\n    request: stripRoutesParam(stripIndexParam2(args.request)),\n    params: args.params,\n    context: args.context\n  });\n  if (isDataWithResponseInit(result) && result.init && result.init.status && isRedirectStatusCode(result.init.status)) {\n    throw new Response(null, result.init);\n  }\n  return result;\n}\nfunction stripIndexParam2(request) {\n  let url = new URL(request.url);\n  let indexValues = url.searchParams.getAll(\"index\");\n  url.searchParams.delete(\"index\");\n  let indexValuesToKeep = [];\n  for (let indexValue of indexValues) {\n    if (indexValue) {\n      indexValuesToKeep.push(indexValue);\n    }\n  }\n  for (let toKeep of indexValuesToKeep) {\n    url.searchParams.append(\"index\", toKeep);\n  }\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\nfunction stripRoutesParam(request) {\n  let url = new URL(request.url);\n  url.searchParams.delete(\"_routes\");\n  let init = {\n    method: request.method,\n    body: request.body,\n    headers: request.headers,\n    signal: request.signal\n  };\n  if (init.body) {\n    init.duplex = \"half\";\n  }\n  return new Request(url.href, init);\n}\n\n// lib/server-runtime/invariant.ts\nfunction invariant2(value, message) {\n  if (value === false || value === null || typeof value === \"undefined\") {\n    console.error(\n      \"The following error is a bug in React Router; please open an issue! https://github.com/remix-run/react-router/issues/new/choose\"\n    );\n    throw new Error(message);\n  }\n}\n\n// lib/server-runtime/dev.ts\nvar globalDevServerHooksKey = \"__reactRouterDevServerHooks\";\nfunction setDevServerHooks(devServerHooks) {\n  globalThis[globalDevServerHooksKey] = devServerHooks;\n}\nfunction getDevServerHooks() {\n  return globalThis[globalDevServerHooksKey];\n}\nfunction getBuildTimeHeader(request, headerName) {\n  if (typeof process !== \"undefined\") {\n    try {\n      if (process.env?.IS_RR_BUILD_REQUEST === \"yes\") {\n        return request.headers.get(headerName);\n      }\n    } catch (e) {\n    }\n  }\n  return null;\n}\n\n// lib/server-runtime/routes.ts\nfunction groupRoutesByParentId(manifest) {\n  let routes = {};\n  Object.values(manifest).forEach((route) => {\n    if (route) {\n      let parentId = route.parentId || \"\";\n      if (!routes[parentId]) {\n        routes[parentId] = [];\n      }\n      routes[parentId].push(route);\n    }\n  });\n  return routes;\n}\nfunction createRoutes(manifest, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest)) {\n  return (routesByParentId[parentId] || []).map((route) => ({\n    ...route,\n    children: createRoutes(manifest, route.id, routesByParentId)\n  }));\n}\nfunction createStaticHandlerDataRoutes(manifest, future, parentId = \"\", routesByParentId = groupRoutesByParentId(manifest)) {\n  return (routesByParentId[parentId] || []).map((route) => {\n    let commonRoute = {\n      // Always include root due to default boundaries\n      hasErrorBoundary: route.id === \"root\" || route.module.ErrorBoundary != null,\n      id: route.id,\n      path: route.path,\n      middleware: route.module.middleware,\n      // Need to use RR's version in the param typed here to permit the optional\n      // context even though we know it'll always be provided in remix\n      loader: route.module.loader ? async (args) => {\n        let preRenderedData = getBuildTimeHeader(\n          args.request,\n          \"X-React-Router-Prerender-Data\"\n        );\n        if (preRenderedData != null) {\n          let encoded = preRenderedData ? decodeURI(preRenderedData) : preRenderedData;\n          invariant2(encoded, \"Missing prerendered data for route\");\n          let uint8array = new TextEncoder().encode(encoded);\n          let stream = new ReadableStream({\n            start(controller) {\n              controller.enqueue(uint8array);\n              controller.close();\n            }\n          });\n          let decoded = await decodeViaTurboStream(stream, global);\n          let data2 = decoded.value;\n          if (data2 && SingleFetchRedirectSymbol in data2) {\n            let result = data2[SingleFetchRedirectSymbol];\n            let init = { status: result.status };\n            if (result.reload) {\n              throw redirectDocument(result.redirect, init);\n            } else if (result.replace) {\n              throw replace(result.redirect, init);\n            } else {\n              throw redirect(result.redirect, init);\n            }\n          } else {\n            invariant2(\n              data2 && route.id in data2,\n              \"Unable to decode prerendered data\"\n            );\n            let result = data2[route.id];\n            invariant2(\n              \"data\" in result,\n              \"Unable to process prerendered data\"\n            );\n            return result.data;\n          }\n        }\n        let val = await callRouteHandler(route.module.loader, args);\n        return val;\n      } : void 0,\n      action: route.module.action ? (args) => callRouteHandler(route.module.action, args) : void 0,\n      handle: route.module.handle\n    };\n    return route.index ? {\n      index: true,\n      ...commonRoute\n    } : {\n      caseSensitive: route.caseSensitive,\n      children: createStaticHandlerDataRoutes(\n        manifest,\n        future,\n        route.id,\n        routesByParentId\n      ),\n      ...commonRoute\n    };\n  });\n}\n\n// lib/server-runtime/serverHandoff.ts\nfunction createServerHandoffString(serverHandoff) {\n  return escapeHtml(JSON.stringify(serverHandoff));\n}\n\n// lib/server-runtime/headers.ts\nimport { splitCookiesString } from \"set-cookie-parser\";\nfunction getDocumentHeaders(context, build) {\n  return getDocumentHeadersImpl(context, (m) => {\n    let route = build.routes[m.route.id];\n    invariant2(route, `Route with id \"${m.route.id}\" not found in build`);\n    return route.module.headers;\n  });\n}\nfunction getDocumentHeadersImpl(context, getRouteHeadersFn, _defaultHeaders) {\n  let boundaryIdx = context.errors ? context.matches.findIndex((m) => context.errors[m.route.id]) : -1;\n  let matches = boundaryIdx >= 0 ? context.matches.slice(0, boundaryIdx + 1) : context.matches;\n  let errorHeaders;\n  if (boundaryIdx >= 0) {\n    let { actionHeaders, actionData, loaderHeaders, loaderData } = context;\n    context.matches.slice(boundaryIdx).some((match) => {\n      let id = match.route.id;\n      if (actionHeaders[id] && (!actionData || !actionData.hasOwnProperty(id))) {\n        errorHeaders = actionHeaders[id];\n      } else if (loaderHeaders[id] && !loaderData.hasOwnProperty(id)) {\n        errorHeaders = loaderHeaders[id];\n      }\n      return errorHeaders != null;\n    });\n  }\n  const defaultHeaders = new Headers(_defaultHeaders);\n  return matches.reduce((parentHeaders, match, idx) => {\n    let { id } = match.route;\n    let loaderHeaders = context.loaderHeaders[id] || new Headers();\n    let actionHeaders = context.actionHeaders[id] || new Headers();\n    let includeErrorHeaders = errorHeaders != null && idx === matches.length - 1;\n    let includeErrorCookies = includeErrorHeaders && errorHeaders !== loaderHeaders && errorHeaders !== actionHeaders;\n    let headersFn = getRouteHeadersFn(match);\n    if (headersFn == null) {\n      let headers2 = new Headers(parentHeaders);\n      if (includeErrorCookies) {\n        prependCookies(errorHeaders, headers2);\n      }\n      prependCookies(actionHeaders, headers2);\n      prependCookies(loaderHeaders, headers2);\n      return headers2;\n    }\n    let headers = new Headers(\n      typeof headersFn === \"function\" ? headersFn({\n        loaderHeaders,\n        parentHeaders,\n        actionHeaders,\n        errorHeaders: includeErrorHeaders ? errorHeaders : void 0\n      }) : headersFn\n    );\n    if (includeErrorCookies) {\n      prependCookies(errorHeaders, headers);\n    }\n    prependCookies(actionHeaders, headers);\n    prependCookies(loaderHeaders, headers);\n    prependCookies(parentHeaders, headers);\n    return headers;\n  }, new Headers(defaultHeaders));\n}\nfunction prependCookies(parentHeaders, childHeaders) {\n  let parentSetCookieString = parentHeaders.get(\"Set-Cookie\");\n  if (parentSetCookieString) {\n    let cookies = splitCookiesString(parentSetCookieString);\n    let childCookies = new Set(childHeaders.getSetCookie());\n    cookies.forEach((cookie) => {\n      if (!childCookies.has(cookie)) {\n        childHeaders.append(\"Set-Cookie\", cookie);\n      }\n    });\n  }\n}\n\n// lib/server-runtime/single-fetch.ts\nvar SERVER_NO_BODY_STATUS_CODES = /* @__PURE__ */ new Set([\n  ...NO_BODY_STATUS_CODES,\n  304\n]);\nasync function singleFetchAction(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  try {\n    let handlerRequest = new Request(handlerUrl, {\n      method: request.method,\n      body: request.body,\n      headers: request.headers,\n      signal: request.signal,\n      ...request.body ? { duplex: \"half\" } : void 0\n    });\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      skipLoaderErrorBubbling: true,\n      skipRevalidation: true,\n      generateMiddlewareResponse: build.future.v8_middleware ? async (query) => {\n        try {\n          let innerResult = await query(handlerRequest);\n          return handleQueryResult(innerResult);\n        } catch (error) {\n          return handleQueryError(error);\n        }\n      } : void 0\n    });\n    return handleQueryResult(result);\n  } catch (error) {\n    return handleQueryError(error);\n  }\n  function handleQueryResult(result) {\n    return isResponse(result) ? result : staticContextToResponse(result);\n  }\n  function handleQueryError(error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: { error },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n  function staticContextToResponse(context) {\n    let headers = getDocumentHeaders(context, build);\n    if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n      return new Response(null, { status: context.statusCode, headers });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach((err) => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let singleFetchResult;\n    if (context.errors) {\n      singleFetchResult = { error: Object.values(context.errors)[0] };\n    } else {\n      singleFetchResult = {\n        data: Object.values(context.actionData || {})[0]\n      };\n    }\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: singleFetchResult,\n      headers,\n      status: context.statusCode\n    });\n  }\n}\nasync function singleFetchLoaders(build, serverMode, staticHandler, request, handlerUrl, loadContext, handleError) {\n  let routesParam = new URL(request.url).searchParams.get(\"_routes\");\n  let loadRouteIds = routesParam ? new Set(routesParam.split(\",\")) : null;\n  try {\n    let handlerRequest = new Request(handlerUrl, {\n      headers: request.headers,\n      signal: request.signal\n    });\n    let result = await staticHandler.query(handlerRequest, {\n      requestContext: loadContext,\n      filterMatchesToLoad: (m) => !loadRouteIds || loadRouteIds.has(m.route.id),\n      skipLoaderErrorBubbling: true,\n      generateMiddlewareResponse: build.future.v8_middleware ? async (query) => {\n        try {\n          let innerResult = await query(handlerRequest);\n          return handleQueryResult(innerResult);\n        } catch (error) {\n          return handleQueryError(error);\n        }\n      } : void 0\n    });\n    return handleQueryResult(result);\n  } catch (error) {\n    return handleQueryError(error);\n  }\n  function handleQueryResult(result) {\n    return isResponse(result) ? result : staticContextToResponse(result);\n  }\n  function handleQueryError(error) {\n    handleError(error);\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: { error },\n      headers: new Headers(),\n      status: 500\n    });\n  }\n  function staticContextToResponse(context) {\n    let headers = getDocumentHeaders(context, build);\n    if (isRedirectStatusCode(context.statusCode) && headers.has(\"Location\")) {\n      return new Response(null, { status: context.statusCode, headers });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach((err) => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let results = {};\n    let loadedMatches = new Set(\n      context.matches.filter(\n        (m) => loadRouteIds ? loadRouteIds.has(m.route.id) : m.route.loader != null\n      ).map((m) => m.route.id)\n    );\n    if (context.errors) {\n      for (let [id, error] of Object.entries(context.errors)) {\n        results[id] = { error };\n      }\n    }\n    for (let [id, data2] of Object.entries(context.loaderData)) {\n      if (!(id in results) && loadedMatches.has(id)) {\n        results[id] = { data: data2 };\n      }\n    }\n    return generateSingleFetchResponse(request, build, serverMode, {\n      result: results,\n      headers,\n      status: context.statusCode\n    });\n  }\n}\nfunction generateSingleFetchResponse(request, build, serverMode, {\n  result,\n  headers,\n  status\n}) {\n  let resultHeaders = new Headers(headers);\n  resultHeaders.set(\"X-Remix-Response\", \"yes\");\n  if (SERVER_NO_BODY_STATUS_CODES.has(status)) {\n    return new Response(null, { status, headers: resultHeaders });\n  }\n  resultHeaders.set(\"Content-Type\", \"text/x-script\");\n  resultHeaders.delete(\"Content-Length\");\n  return new Response(\n    encodeViaTurboStream(\n      result,\n      request.signal,\n      build.entry.module.streamTimeout,\n      serverMode\n    ),\n    {\n      status: status || 200,\n      headers: resultHeaders\n    }\n  );\n}\nfunction generateSingleFetchRedirectResponse(redirectResponse, request, build, serverMode) {\n  let redirect2 = getSingleFetchRedirect(\n    redirectResponse.status,\n    redirectResponse.headers,\n    build.basename\n  );\n  let headers = new Headers(redirectResponse.headers);\n  headers.delete(\"Location\");\n  headers.set(\"Content-Type\", \"text/x-script\");\n  return generateSingleFetchResponse(request, build, serverMode, {\n    result: request.method === \"GET\" ? { [SingleFetchRedirectSymbol]: redirect2 } : redirect2,\n    headers,\n    status: SINGLE_FETCH_REDIRECT_STATUS\n  });\n}\nfunction getSingleFetchRedirect(status, headers, basename) {\n  let redirect2 = headers.get(\"Location\");\n  if (basename) {\n    redirect2 = stripBasename(redirect2, basename) || redirect2;\n  }\n  return {\n    redirect: redirect2,\n    status,\n    revalidate: (\n      // Technically X-Remix-Revalidate isn't needed here - that was an implementation\n      // detail of ?_data requests as our way to tell the front end to revalidate when\n      // we didn't have a response body to include that information in.\n      // With single fetch, we tell the front end via this revalidate boolean field.\n      // However, we're respecting it for now because it may be something folks have\n      // used in their own responses\n      // TODO(v3): Consider removing or making this official public API\n      headers.has(\"X-Remix-Revalidate\") || headers.has(\"Set-Cookie\")\n    ),\n    reload: headers.has(\"X-Remix-Reload-Document\"),\n    replace: headers.has(\"X-Remix-Replace\")\n  };\n}\nfunction encodeViaTurboStream(data2, requestSignal, streamTimeout, serverMode) {\n  let controller = new AbortController();\n  let timeoutId = setTimeout(\n    () => controller.abort(new Error(\"Server Timeout\")),\n    typeof streamTimeout === \"number\" ? streamTimeout : 4950\n  );\n  requestSignal.addEventListener(\"abort\", () => clearTimeout(timeoutId));\n  return encode(data2, {\n    signal: controller.signal,\n    plugins: [\n      (value) => {\n        if (value instanceof Error) {\n          let { name, message, stack } = serverMode === \"production\" /* Production */ ? sanitizeError(value, serverMode) : value;\n          return [\"SanitizedError\", name, message, stack];\n        }\n        if (value instanceof ErrorResponseImpl) {\n          let { data: data3, status, statusText } = value;\n          return [\"ErrorResponse\", data3, status, statusText];\n        }\n        if (value && typeof value === \"object\" && SingleFetchRedirectSymbol in value) {\n          return [\"SingleFetchRedirect\", value[SingleFetchRedirectSymbol]];\n        }\n      }\n    ],\n    postPlugins: [\n      (value) => {\n        if (!value) return;\n        if (typeof value !== \"object\") return;\n        return [\n          \"SingleFetchClassInstance\",\n          Object.fromEntries(Object.entries(value))\n        ];\n      },\n      () => [\"SingleFetchFallback\"]\n    ]\n  });\n}\n\n// lib/server-runtime/server.ts\nfunction derive(build, mode) {\n  let routes = createRoutes(build.routes);\n  let dataRoutes = createStaticHandlerDataRoutes(build.routes, build.future);\n  let serverMode = isServerMode(mode) ? mode : \"production\" /* Production */;\n  let staticHandler = createStaticHandler(dataRoutes, {\n    basename: build.basename\n  });\n  let errorHandler = build.entry.module.handleError || ((error, { request }) => {\n    if (serverMode !== \"test\" /* Test */ && !request.signal.aborted) {\n      console.error(\n        // @ts-expect-error This is \"private\" from users but intended for internal use\n        isRouteErrorResponse(error) && error.error ? error.error : error\n      );\n    }\n  });\n  return {\n    routes,\n    dataRoutes,\n    serverMode,\n    staticHandler,\n    errorHandler\n  };\n}\nvar createRequestHandler = (build, mode) => {\n  let _build;\n  let routes;\n  let serverMode;\n  let staticHandler;\n  let errorHandler;\n  return async function requestHandler(request, initialContext) {\n    _build = typeof build === \"function\" ? await build() : build;\n    if (typeof build === \"function\") {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    } else if (!routes || !serverMode || !staticHandler || !errorHandler) {\n      let derived = derive(_build, mode);\n      routes = derived.routes;\n      serverMode = derived.serverMode;\n      staticHandler = derived.staticHandler;\n      errorHandler = derived.errorHandler;\n    }\n    let params = {};\n    let loadContext;\n    let handleError = (error) => {\n      if (mode === \"development\" /* Development */) {\n        getDevServerHooks()?.processRequestError?.(error);\n      }\n      errorHandler(error, {\n        context: loadContext,\n        params,\n        request\n      });\n    };\n    if (_build.future.v8_middleware) {\n      if (initialContext && !(initialContext instanceof RouterContextProvider)) {\n        let error = new Error(\n          \"Invalid `context` value provided to `handleRequest`. When middleware is enabled you must return an instance of `RouterContextProvider` from your `getLoadContext` function.\"\n        );\n        handleError(error);\n        return returnLastResortErrorResponse(error, serverMode);\n      }\n      loadContext = initialContext || new RouterContextProvider();\n    } else {\n      loadContext = initialContext || {};\n    }\n    let url = new URL(request.url);\n    let normalizedBasename = _build.basename || \"/\";\n    let normalizedPath = url.pathname;\n    if (stripBasename(normalizedPath, normalizedBasename) === \"/_root.data\") {\n      normalizedPath = normalizedBasename;\n    } else if (normalizedPath.endsWith(\".data\")) {\n      normalizedPath = normalizedPath.replace(/\\.data$/, \"\");\n    }\n    if (stripBasename(normalizedPath, normalizedBasename) !== \"/\" && normalizedPath.endsWith(\"/\")) {\n      normalizedPath = normalizedPath.slice(0, -1);\n    }\n    let isSpaMode = getBuildTimeHeader(request, \"X-React-Router-SPA-Mode\") === \"yes\";\n    if (!_build.ssr) {\n      let decodedPath = decodeURI(normalizedPath);\n      if (normalizedBasename !== \"/\") {\n        let strippedPath = stripBasename(decodedPath, normalizedBasename);\n        if (strippedPath == null) {\n          errorHandler(\n            new ErrorResponseImpl(\n              404,\n              \"Not Found\",\n              `Refusing to prerender the \\`${decodedPath}\\` path because it does not start with the basename \\`${normalizedBasename}\\``\n            ),\n            {\n              context: loadContext,\n              params,\n              request\n            }\n          );\n          return new Response(\"Not Found\", {\n            status: 404,\n            statusText: \"Not Found\"\n          });\n        }\n        decodedPath = strippedPath;\n      }\n      if (_build.prerender.length === 0) {\n        isSpaMode = true;\n      } else if (!_build.prerender.includes(decodedPath) && !_build.prerender.includes(decodedPath + \"/\")) {\n        if (url.pathname.endsWith(\".data\")) {\n          errorHandler(\n            new ErrorResponseImpl(\n              404,\n              \"Not Found\",\n              `Refusing to SSR the path \\`${decodedPath}\\` because \\`ssr:false\\` is set and the path is not included in the \\`prerender\\` config, so in production the path will be a 404.`\n            ),\n            {\n              context: loadContext,\n              params,\n              request\n            }\n          );\n          return new Response(\"Not Found\", {\n            status: 404,\n            statusText: \"Not Found\"\n          });\n        } else {\n          isSpaMode = true;\n        }\n      }\n    }\n    let manifestUrl = getManifestPath(\n      _build.routeDiscovery.manifestPath,\n      normalizedBasename\n    );\n    if (url.pathname === manifestUrl) {\n      try {\n        let res = await handleManifestRequest(_build, routes, url);\n        return res;\n      } catch (e) {\n        handleError(e);\n        return new Response(\"Unknown Server Error\", { status: 500 });\n      }\n    }\n    let matches = matchServerRoutes(routes, normalizedPath, _build.basename);\n    if (matches && matches.length > 0) {\n      Object.assign(params, matches[0].params);\n    }\n    let response;\n    if (url.pathname.endsWith(\".data\")) {\n      let handlerUrl = new URL(request.url);\n      handlerUrl.pathname = normalizedPath;\n      let singleFetchMatches = matchServerRoutes(\n        routes,\n        handlerUrl.pathname,\n        _build.basename\n      );\n      response = await handleSingleFetchRequest(\n        serverMode,\n        _build,\n        staticHandler,\n        request,\n        handlerUrl,\n        loadContext,\n        handleError\n      );\n      if (isRedirectResponse(response)) {\n        response = generateSingleFetchRedirectResponse(\n          response,\n          request,\n          _build,\n          serverMode\n        );\n      }\n      if (_build.entry.module.handleDataRequest) {\n        response = await _build.entry.module.handleDataRequest(response, {\n          context: loadContext,\n          params: singleFetchMatches ? singleFetchMatches[0].params : {},\n          request\n        });\n        if (isRedirectResponse(response)) {\n          response = generateSingleFetchRedirectResponse(\n            response,\n            request,\n            _build,\n            serverMode\n          );\n        }\n      }\n    } else if (!isSpaMode && matches && matches[matches.length - 1].route.module.default == null && matches[matches.length - 1].route.module.ErrorBoundary == null) {\n      response = await handleResourceRequest(\n        serverMode,\n        _build,\n        staticHandler,\n        matches.slice(-1)[0].route.id,\n        request,\n        loadContext,\n        handleError\n      );\n    } else {\n      let { pathname } = url;\n      let criticalCss = void 0;\n      if (_build.unstable_getCriticalCss) {\n        criticalCss = await _build.unstable_getCriticalCss({ pathname });\n      } else if (mode === \"development\" /* Development */ && getDevServerHooks()?.getCriticalCss) {\n        criticalCss = await getDevServerHooks()?.getCriticalCss?.(pathname);\n      }\n      response = await handleDocumentRequest(\n        serverMode,\n        _build,\n        staticHandler,\n        request,\n        loadContext,\n        handleError,\n        isSpaMode,\n        criticalCss\n      );\n    }\n    if (request.method === \"HEAD\") {\n      return new Response(null, {\n        headers: response.headers,\n        status: response.status,\n        statusText: response.statusText\n      });\n    }\n    return response;\n  };\n};\nasync function handleManifestRequest(build, routes, url) {\n  if (build.assets.version !== url.searchParams.get(\"version\")) {\n    return new Response(null, {\n      status: 204,\n      headers: {\n        \"X-Remix-Reload-Document\": \"true\"\n      }\n    });\n  }\n  let patches = {};\n  if (url.searchParams.has(\"p\")) {\n    let paths = /* @__PURE__ */ new Set();\n    url.searchParams.getAll(\"p\").forEach((path) => {\n      if (!path.startsWith(\"/\")) {\n        path = `/${path}`;\n      }\n      let segments = path.split(\"/\").slice(1);\n      segments.forEach((_, i) => {\n        let partialPath = segments.slice(0, i + 1).join(\"/\");\n        paths.add(`/${partialPath}`);\n      });\n    });\n    for (let path of paths) {\n      let matches = matchServerRoutes(routes, path, build.basename);\n      if (matches) {\n        for (let match of matches) {\n          let routeId = match.route.id;\n          let route = build.assets.routes[routeId];\n          if (route) {\n            patches[routeId] = route;\n          }\n        }\n      }\n    }\n    return Response.json(patches, {\n      headers: {\n        \"Cache-Control\": \"public, max-age=31536000, immutable\"\n      }\n    });\n  }\n  return new Response(\"Invalid Request\", { status: 400 });\n}\nasync function handleSingleFetchRequest(serverMode, build, staticHandler, request, handlerUrl, loadContext, handleError) {\n  let response = request.method !== \"GET\" ? await singleFetchAction(\n    build,\n    serverMode,\n    staticHandler,\n    request,\n    handlerUrl,\n    loadContext,\n    handleError\n  ) : await singleFetchLoaders(\n    build,\n    serverMode,\n    staticHandler,\n    request,\n    handlerUrl,\n    loadContext,\n    handleError\n  );\n  return response;\n}\nasync function handleDocumentRequest(serverMode, build, staticHandler, request, loadContext, handleError, isSpaMode, criticalCss) {\n  try {\n    let result = await staticHandler.query(request, {\n      requestContext: loadContext,\n      generateMiddlewareResponse: build.future.v8_middleware ? async (query) => {\n        try {\n          let innerResult = await query(request);\n          if (!isResponse(innerResult)) {\n            innerResult = await renderHtml(innerResult, isSpaMode);\n          }\n          return innerResult;\n        } catch (error) {\n          handleError(error);\n          return new Response(null, { status: 500 });\n        }\n      } : void 0\n    });\n    if (!isResponse(result)) {\n      result = await renderHtml(result, isSpaMode);\n    }\n    return result;\n  } catch (error) {\n    handleError(error);\n    return new Response(null, { status: 500 });\n  }\n  async function renderHtml(context, isSpaMode2) {\n    let headers = getDocumentHeaders(context, build);\n    if (SERVER_NO_BODY_STATUS_CODES.has(context.statusCode)) {\n      return new Response(null, { status: context.statusCode, headers });\n    }\n    if (context.errors) {\n      Object.values(context.errors).forEach((err) => {\n        if (!isRouteErrorResponse(err) || err.error) {\n          handleError(err);\n        }\n      });\n      context.errors = sanitizeErrors(context.errors, serverMode);\n    }\n    let state = {\n      loaderData: context.loaderData,\n      actionData: context.actionData,\n      errors: serializeErrors(context.errors, serverMode)\n    };\n    let baseServerHandoff = {\n      basename: build.basename,\n      future: build.future,\n      routeDiscovery: build.routeDiscovery,\n      ssr: build.ssr,\n      isSpaMode: isSpaMode2\n    };\n    let entryContext = {\n      manifest: build.assets,\n      routeModules: createEntryRouteModules(build.routes),\n      staticHandlerContext: context,\n      criticalCss,\n      serverHandoffString: createServerHandoffString({\n        ...baseServerHandoff,\n        criticalCss\n      }),\n      serverHandoffStream: encodeViaTurboStream(\n        state,\n        request.signal,\n        build.entry.module.streamTimeout,\n        serverMode\n      ),\n      renderMeta: {},\n      future: build.future,\n      ssr: build.ssr,\n      routeDiscovery: build.routeDiscovery,\n      isSpaMode: isSpaMode2,\n      serializeError: (err) => serializeError(err, serverMode)\n    };\n    let handleDocumentRequestFunction = build.entry.module.default;\n    try {\n      return await handleDocumentRequestFunction(\n        request,\n        context.statusCode,\n        headers,\n        entryContext,\n        loadContext\n      );\n    } catch (error) {\n      handleError(error);\n      let errorForSecondRender = error;\n      if (isResponse(error)) {\n        try {\n          let data2 = await unwrapResponse(error);\n          errorForSecondRender = new ErrorResponseImpl(\n            error.status,\n            error.statusText,\n            data2\n          );\n        } catch (e) {\n        }\n      }\n      context = getStaticContextFromError(\n        staticHandler.dataRoutes,\n        context,\n        errorForSecondRender\n      );\n      if (context.errors) {\n        context.errors = sanitizeErrors(context.errors, serverMode);\n      }\n      let state2 = {\n        loaderData: context.loaderData,\n        actionData: context.actionData,\n        errors: serializeErrors(context.errors, serverMode)\n      };\n      entryContext = {\n        ...entryContext,\n        staticHandlerContext: context,\n        serverHandoffString: createServerHandoffString(baseServerHandoff),\n        serverHandoffStream: encodeViaTurboStream(\n          state2,\n          request.signal,\n          build.entry.module.streamTimeout,\n          serverMode\n        ),\n        renderMeta: {}\n      };\n      try {\n        return await handleDocumentRequestFunction(\n          request,\n          context.statusCode,\n          headers,\n          entryContext,\n          loadContext\n        );\n      } catch (error2) {\n        handleError(error2);\n        return returnLastResortErrorResponse(error2, serverMode);\n      }\n    }\n  }\n}\nasync function handleResourceRequest(serverMode, build, staticHandler, routeId, request, loadContext, handleError) {\n  try {\n    let result = await staticHandler.queryRoute(request, {\n      routeId,\n      requestContext: loadContext,\n      generateMiddlewareResponse: build.future.v8_middleware ? async (queryRoute) => {\n        try {\n          let innerResult = await queryRoute(request);\n          return handleQueryRouteResult(innerResult);\n        } catch (error) {\n          return handleQueryRouteError(error);\n        }\n      } : void 0\n    });\n    return handleQueryRouteResult(result);\n  } catch (error) {\n    return handleQueryRouteError(error);\n  }\n  function handleQueryRouteResult(result) {\n    if (isResponse(result)) {\n      return result;\n    }\n    if (typeof result === \"string\") {\n      return new Response(result);\n    }\n    return Response.json(result);\n  }\n  function handleQueryRouteError(error) {\n    if (isResponse(error)) {\n      error.headers.set(\"X-Remix-Catch\", \"yes\");\n      return error;\n    }\n    if (isRouteErrorResponse(error)) {\n      handleError(error);\n      return errorResponseToJson(error, serverMode);\n    }\n    if (error instanceof Error && error.message === \"Expected a response from queryRoute\") {\n      let newError = new Error(\n        \"Expected a Response to be returned from resource route handler\"\n      );\n      handleError(newError);\n      return returnLastResortErrorResponse(newError, serverMode);\n    }\n    handleError(error);\n    return returnLastResortErrorResponse(error, serverMode);\n  }\n}\nfunction errorResponseToJson(errorResponse, serverMode) {\n  return Response.json(\n    serializeError(\n      // @ts-expect-error This is \"private\" from users but intended for internal use\n      errorResponse.error || new Error(\"Unexpected Server Error\"),\n      serverMode\n    ),\n    {\n      status: errorResponse.status,\n      statusText: errorResponse.statusText,\n      headers: {\n        \"X-Remix-Error\": \"yes\"\n      }\n    }\n  );\n}\nfunction returnLastResortErrorResponse(error, serverMode) {\n  let message = \"Unexpected Server Error\";\n  if (serverMode !== \"production\" /* Production */) {\n    message += `\n\n${String(error)}`;\n  }\n  return new Response(message, {\n    status: 500,\n    headers: {\n      \"Content-Type\": \"text/plain\"\n    }\n  });\n}\nfunction unwrapResponse(response) {\n  let contentType = response.headers.get(\"Content-Type\");\n  return contentType && /\\bapplication\\/json\\b/.test(contentType) ? response.body == null ? null : response.json() : response.text();\n}\n\n// lib/server-runtime/sessions.ts\nfunction flash(name) {\n  return `__flash_${name}__`;\n}\nvar createSession = (initialData = {}, id = \"\") => {\n  let map = new Map(Object.entries(initialData));\n  return {\n    get id() {\n      return id;\n    },\n    get data() {\n      return Object.fromEntries(map);\n    },\n    has(name) {\n      return map.has(name) || map.has(flash(name));\n    },\n    get(name) {\n      if (map.has(name)) return map.get(name);\n      let flashName = flash(name);\n      if (map.has(flashName)) {\n        let value = map.get(flashName);\n        map.delete(flashName);\n        return value;\n      }\n      return void 0;\n    },\n    set(name, value) {\n      map.set(name, value);\n    },\n    flash(name, value) {\n      map.set(flash(name), value);\n    },\n    unset(name) {\n      map.delete(name);\n    }\n  };\n};\nvar isSession = (object) => {\n  return object != null && typeof object.id === \"string\" && typeof object.data !== \"undefined\" && typeof object.has === \"function\" && typeof object.get === \"function\" && typeof object.set === \"function\" && typeof object.flash === \"function\" && typeof object.unset === \"function\";\n};\nfunction createSessionStorage({\n  cookie: cookieArg,\n  createData,\n  readData,\n  updateData,\n  deleteData\n}) {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      let id = cookieHeader && await cookie.parse(cookieHeader, options);\n      let data2 = id && await readData(id);\n      return createSession(data2 || {}, id || \"\");\n    },\n    async commitSession(session, options) {\n      let { id, data: data2 } = session;\n      let expires = options?.maxAge != null ? new Date(Date.now() + options.maxAge * 1e3) : options?.expires != null ? options.expires : cookie.expires;\n      if (id) {\n        await updateData(id, data2, expires);\n      } else {\n        id = await createData(data2, expires);\n      }\n      return cookie.serialize(id, options);\n    },\n    async destroySession(session, options) {\n      await deleteData(session.id);\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */ new Date(0)\n      });\n    }\n  };\n}\nfunction warnOnceAboutSigningSessionCookie(cookie) {\n  warnOnce(\n    cookie.isSigned,\n    `The \"${cookie.name}\" cookie is not signed, but session cookies should be signed to prevent tampering on the client before they are sent back to the server. See https://reactrouter.com/explanation/sessions-and-cookies#signing-cookies for more information.`\n  );\n}\n\n// lib/server-runtime/sessions/cookieStorage.ts\nfunction createCookieSessionStorage({ cookie: cookieArg } = {}) {\n  let cookie = isCookie(cookieArg) ? cookieArg : createCookie(cookieArg?.name || \"__session\", cookieArg);\n  warnOnceAboutSigningSessionCookie(cookie);\n  return {\n    async getSession(cookieHeader, options) {\n      return createSession(\n        cookieHeader && await cookie.parse(cookieHeader, options) || {}\n      );\n    },\n    async commitSession(session, options) {\n      let serializedCookie = await cookie.serialize(session.data, options);\n      if (serializedCookie.length > 4096) {\n        throw new Error(\n          \"Cookie length will exceed browser maximum. Length: \" + serializedCookie.length\n        );\n      }\n      return serializedCookie;\n    },\n    async destroySession(_session, options) {\n      return cookie.serialize(\"\", {\n        ...options,\n        maxAge: void 0,\n        expires: /* @__PURE__ */ new Date(0)\n      });\n    }\n  };\n}\n\n// lib/server-runtime/sessions/memoryStorage.ts\nfunction createMemorySessionStorage({ cookie } = {}) {\n  let map = /* @__PURE__ */ new Map();\n  return createSessionStorage({\n    cookie,\n    async createData(data2, expires) {\n      let id = Math.random().toString(36).substring(2, 10);\n      map.set(id, { data: data2, expires });\n      return id;\n    },\n    async readData(id) {\n      if (map.has(id)) {\n        let { data: data2, expires } = map.get(id);\n        if (!expires || expires > /* @__PURE__ */ new Date()) {\n          return data2;\n        }\n        if (expires) map.delete(id);\n      }\n      return null;\n    },\n    async updateData(id, data2, expires) {\n      map.set(id, { data: data2, expires });\n    },\n    async deleteData(id) {\n      map.delete(id);\n    }\n  });\n}\n\n// lib/href.ts\nfunction href(path, ...args) {\n  let params = args[0];\n  let result = path.replace(/\\/*\\*?$/, \"\").replace(\n    /\\/:([\\w-]+)(\\?)?/g,\n    // same regex as in .\\router\\utils.ts: compilePath().\n    (_, param, questionMark) => {\n      const isRequired = questionMark === void 0;\n      const value = params ? params[param] : void 0;\n      if (isRequired && value === void 0) {\n        throw new Error(\n          `Path '${path}' requires param '${param}' but it was not provided`\n        );\n      }\n      return value === void 0 ? \"\" : \"/\" + value;\n    }\n  );\n  if (path.endsWith(\"*\")) {\n    const value = params ? params[\"*\"] : void 0;\n    if (value !== void 0) {\n      result += \"/\" + value;\n    }\n  }\n  return result || \"/\";\n}\n\n// lib/rsc/browser.tsx\nimport * as React4 from \"react\";\nimport * as ReactDOM from \"react-dom\";\n\n// lib/dom/ssr/hydration.tsx\nfunction getHydrationData({\n  state,\n  routes,\n  getRouteInfo,\n  location: location2,\n  basename,\n  isSpaMode\n}) {\n  let hydrationData = {\n    ...state,\n    loaderData: { ...state.loaderData }\n  };\n  let initialMatches = matchRoutes(routes, location2, basename);\n  if (initialMatches) {\n    for (let match of initialMatches) {\n      let routeId = match.route.id;\n      let routeInfo = getRouteInfo(routeId);\n      if (shouldHydrateRouteLoader(\n        routeId,\n        routeInfo.clientLoader,\n        routeInfo.hasLoader,\n        isSpaMode\n      ) && (routeInfo.hasHydrateFallback || !routeInfo.hasLoader)) {\n        delete hydrationData.loaderData[routeId];\n      } else if (!routeInfo.hasLoader) {\n        hydrationData.loaderData[routeId] = null;\n      }\n    }\n  }\n  return hydrationData;\n}\n\n// lib/rsc/errorBoundaries.tsx\nimport React3 from \"react\";\nvar RSCRouterGlobalErrorBoundary = class extends React3.Component {\n  constructor(props) {\n    super(props);\n    this.state = { error: null, location: props.location };\n  }\n  static getDerivedStateFromError(error) {\n    return { error };\n  }\n  static getDerivedStateFromProps(props, state) {\n    if (state.location !== props.location) {\n      return { error: null, location: props.location };\n    }\n    return { error: state.error, location: state.location };\n  }\n  render() {\n    if (this.state.error) {\n      return /* @__PURE__ */ React3.createElement(\n        RSCDefaultRootErrorBoundaryImpl,\n        {\n          error: this.state.error,\n          renderAppShell: true\n        }\n      );\n    } else {\n      return this.props.children;\n    }\n  }\n};\nfunction ErrorWrapper({\n  renderAppShell,\n  title,\n  children\n}) {\n  if (!renderAppShell) {\n    return children;\n  }\n  return /* @__PURE__ */ React3.createElement(\"html\", { lang: \"en\" }, /* @__PURE__ */ React3.createElement(\"head\", null, /* @__PURE__ */ React3.createElement(\"meta\", { charSet: \"utf-8\" }), /* @__PURE__ */ React3.createElement(\n    \"meta\",\n    {\n      name: \"viewport\",\n      content: \"width=device-width,initial-scale=1,viewport-fit=cover\"\n    }\n  ), /* @__PURE__ */ React3.createElement(\"title\", null, title)), /* @__PURE__ */ React3.createElement(\"body\", null, /* @__PURE__ */ React3.createElement(\"main\", { style: { fontFamily: \"system-ui, sans-serif\", padding: \"2rem\" } }, children)));\n}\nfunction RSCDefaultRootErrorBoundaryImpl({\n  error,\n  renderAppShell\n}) {\n  console.error(error);\n  let heyDeveloper = /* @__PURE__ */ React3.createElement(\n    \"script\",\n    {\n      dangerouslySetInnerHTML: {\n        __html: `\n        console.log(\n          \"\\u{1F4BF} Hey developer \\u{1F44B}. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information.\"\n        );\n      `\n      }\n    }\n  );\n  if (isRouteErrorResponse(error)) {\n    return /* @__PURE__ */ React3.createElement(\n      ErrorWrapper,\n      {\n        renderAppShell,\n        title: \"Unhandled Thrown Response!\"\n      },\n      /* @__PURE__ */ React3.createElement(\"h1\", { style: { fontSize: \"24px\" } }, error.status, \" \", error.statusText),\n      ENABLE_DEV_WARNINGS ? heyDeveloper : null\n    );\n  }\n  let errorInstance;\n  if (error instanceof Error) {\n    errorInstance = error;\n  } else {\n    let errorString = error == null ? \"Unknown Error\" : typeof error === \"object\" && \"toString\" in error ? error.toString() : JSON.stringify(error);\n    errorInstance = new Error(errorString);\n  }\n  return /* @__PURE__ */ React3.createElement(ErrorWrapper, { renderAppShell, title: \"Application Error!\" }, /* @__PURE__ */ React3.createElement(\"h1\", { style: { fontSize: \"24px\" } }, \"Application Error\"), /* @__PURE__ */ React3.createElement(\n    \"pre\",\n    {\n      style: {\n        padding: \"2rem\",\n        background: \"hsla(10, 50%, 50%, 0.1)\",\n        color: \"red\",\n        overflow: \"auto\"\n      }\n    },\n    errorInstance.stack\n  ), heyDeveloper);\n}\nfunction RSCDefaultRootErrorBoundary({\n  hasRootLayout\n}) {\n  let error = useRouteError();\n  if (hasRootLayout === void 0) {\n    throw new Error(\"Missing 'hasRootLayout' prop\");\n  }\n  return /* @__PURE__ */ React3.createElement(\n    RSCDefaultRootErrorBoundaryImpl,\n    {\n      renderAppShell: !hasRootLayout,\n      error\n    }\n  );\n}\n\n// lib/rsc/route-modules.ts\nfunction createRSCRouteModules(payload) {\n  const routeModules = {};\n  for (const match of payload.matches) {\n    populateRSCRouteModules(routeModules, match);\n  }\n  return routeModules;\n}\nfunction populateRSCRouteModules(routeModules, matches) {\n  matches = Array.isArray(matches) ? matches : [matches];\n  for (const match of matches) {\n    routeModules[match.id] = {\n      links: match.links,\n      meta: match.meta,\n      default: noopComponent\n    };\n  }\n}\nvar noopComponent = () => null;\n\n// lib/rsc/browser.tsx\nfunction createCallServer({\n  createFromReadableStream,\n  createTemporaryReferenceSet,\n  encodeReply,\n  fetch: fetchImplementation = fetch\n}) {\n  const globalVar = window;\n  let landedActionId = 0;\n  return async (id, args) => {\n    let actionId = globalVar.__routerActionID = (globalVar.__routerActionID ?? (globalVar.__routerActionID = 0)) + 1;\n    const temporaryReferences = createTemporaryReferenceSet();\n    const response = await fetchImplementation(\n      new Request(location.href, {\n        body: await encodeReply(args, { temporaryReferences }),\n        method: \"POST\",\n        headers: {\n          Accept: \"text/x-component\",\n          \"rsc-action-id\": id\n        }\n      })\n    );\n    if (!response.body) {\n      throw new Error(\"No response body\");\n    }\n    const payload = await createFromReadableStream(response.body, {\n      temporaryReferences\n    });\n    if (payload.type === \"redirect\") {\n      if (payload.reload) {\n        window.location.href = payload.location;\n        return;\n      }\n      globalVar.__reactRouterDataRouter.navigate(payload.location, {\n        replace: payload.replace\n      });\n      return payload.actionResult;\n    }\n    if (payload.type !== \"action\") {\n      throw new Error(\"Unexpected payload type\");\n    }\n    if (payload.rerender) {\n      React4.startTransition(\n        // @ts-expect-error - We have old react types that don't know this can be async\n        async () => {\n          const rerender = await payload.rerender;\n          if (!rerender) return;\n          if (landedActionId < actionId && globalVar.__routerActionID <= actionId) {\n            landedActionId = actionId;\n            if (rerender.type === \"redirect\") {\n              if (rerender.reload) {\n                window.location.href = rerender.location;\n                return;\n              }\n              globalVar.__reactRouterDataRouter.navigate(rerender.location, {\n                replace: rerender.replace\n              });\n              return;\n            }\n            let lastMatch;\n            for (const match of rerender.matches) {\n              globalVar.__reactRouterDataRouter.patchRoutes(\n                lastMatch?.id ?? null,\n                [createRouteFromServerManifest(match)],\n                true\n              );\n              lastMatch = match;\n            }\n            window.__reactRouterDataRouter._internalSetStateDoNotUseOrYouWillBreakYourApp(\n              {}\n            );\n            React4.startTransition(() => {\n              window.__reactRouterDataRouter._internalSetStateDoNotUseOrYouWillBreakYourApp(\n                {\n                  loaderData: Object.assign(\n                    {},\n                    globalVar.__reactRouterDataRouter.state.loaderData,\n                    rerender.loaderData\n                  ),\n                  errors: rerender.errors ? Object.assign(\n                    {},\n                    globalVar.__reactRouterDataRouter.state.errors,\n                    rerender.errors\n                  ) : null\n                }\n              );\n            });\n          }\n        }\n      );\n    }\n    return payload.actionResult;\n  };\n}\nfunction createRouterFromPayload({\n  fetchImplementation,\n  createFromReadableStream,\n  getContext,\n  payload\n}) {\n  const globalVar = window;\n  if (globalVar.__reactRouterDataRouter && globalVar.__reactRouterRouteModules)\n    return {\n      router: globalVar.__reactRouterDataRouter,\n      routeModules: globalVar.__reactRouterRouteModules\n    };\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  globalVar.__reactRouterRouteModules = globalVar.__reactRouterRouteModules ?? {};\n  populateRSCRouteModules(globalVar.__reactRouterRouteModules, payload.matches);\n  let patches = /* @__PURE__ */ new Map();\n  payload.patches?.forEach((patch) => {\n    invariant(patch.parentId, \"Invalid patch parentId\");\n    if (!patches.has(patch.parentId)) {\n      patches.set(patch.parentId, []);\n    }\n    patches.get(patch.parentId)?.push(patch);\n  });\n  let routes = payload.matches.reduceRight((previous, match) => {\n    const route = createRouteFromServerManifest(\n      match,\n      payload\n    );\n    if (previous.length > 0) {\n      route.children = previous;\n      let childrenToPatch = patches.get(match.id);\n      if (childrenToPatch) {\n        route.children.push(\n          ...childrenToPatch.map((r) => createRouteFromServerManifest(r))\n        );\n      }\n    }\n    return [route];\n  }, []);\n  globalVar.__reactRouterDataRouter = createRouter({\n    routes,\n    getContext,\n    basename: payload.basename,\n    history: createBrowserHistory(),\n    hydrationData: getHydrationData({\n      state: {\n        loaderData: payload.loaderData,\n        actionData: payload.actionData,\n        errors: payload.errors\n      },\n      routes,\n      getRouteInfo: (routeId) => {\n        let match = payload.matches.find((m) => m.id === routeId);\n        invariant(match, \"Route not found in payload\");\n        return {\n          clientLoader: match.clientLoader,\n          hasLoader: match.hasLoader,\n          hasHydrateFallback: match.hydrateFallbackElement != null\n        };\n      },\n      location: payload.location,\n      basename: payload.basename,\n      isSpaMode: false\n    }),\n    async patchRoutesOnNavigation({ path, signal }) {\n      if (discoveredPaths.has(path)) {\n        return;\n      }\n      await fetchAndApplyManifestPatches(\n        [path],\n        createFromReadableStream,\n        fetchImplementation,\n        signal\n      );\n    },\n    // FIXME: Pass `build.ssr` into this function\n    dataStrategy: getRSCSingleFetchDataStrategy(\n      () => globalVar.__reactRouterDataRouter,\n      true,\n      payload.basename,\n      createFromReadableStream,\n      fetchImplementation\n    )\n  });\n  if (globalVar.__reactRouterDataRouter.state.initialized) {\n    globalVar.__routerInitialized = true;\n    globalVar.__reactRouterDataRouter.initialize();\n  } else {\n    globalVar.__routerInitialized = false;\n  }\n  let lastLoaderData = void 0;\n  globalVar.__reactRouterDataRouter.subscribe(({ loaderData, actionData }) => {\n    if (lastLoaderData !== loaderData) {\n      globalVar.__routerActionID = (globalVar.__routerActionID ?? (globalVar.__routerActionID = 0)) + 1;\n    }\n  });\n  globalVar.__reactRouterDataRouter._updateRoutesForHMR = (routeUpdateByRouteId) => {\n    const oldRoutes = window.__reactRouterDataRouter.routes;\n    const newRoutes = [];\n    function walkRoutes(routes2, parentId) {\n      return routes2.map((route) => {\n        const routeUpdate = routeUpdateByRouteId.get(route.id);\n        if (routeUpdate) {\n          const {\n            routeModule,\n            hasAction,\n            hasComponent,\n            hasErrorBoundary,\n            hasLoader\n          } = routeUpdate;\n          const newRoute = createRouteFromServerManifest({\n            clientAction: routeModule.clientAction,\n            clientLoader: routeModule.clientLoader,\n            element: route.element,\n            errorElement: route.errorElement,\n            handle: route.handle,\n            hasAction,\n            hasComponent,\n            hasErrorBoundary,\n            hasLoader,\n            hydrateFallbackElement: route.hydrateFallbackElement,\n            id: route.id,\n            index: route.index,\n            links: routeModule.links,\n            meta: routeModule.meta,\n            parentId,\n            path: route.path,\n            shouldRevalidate: routeModule.shouldRevalidate\n          });\n          if (route.children) {\n            newRoute.children = walkRoutes(route.children, route.id);\n          }\n          return newRoute;\n        }\n        const updatedRoute = { ...route };\n        if (route.children) {\n          updatedRoute.children = walkRoutes(route.children, route.id);\n        }\n        return updatedRoute;\n      });\n    }\n    newRoutes.push(\n      ...walkRoutes(oldRoutes, void 0)\n    );\n    window.__reactRouterDataRouter._internalSetRoutes(newRoutes);\n  };\n  return {\n    router: globalVar.__reactRouterDataRouter,\n    routeModules: globalVar.__reactRouterRouteModules\n  };\n}\nvar renderedRoutesContext = createContext();\nfunction getRSCSingleFetchDataStrategy(getRouter, ssr, basename, createFromReadableStream, fetchImplementation) {\n  let dataStrategy = getSingleFetchDataStrategyImpl(\n    getRouter,\n    (match) => {\n      let M = match;\n      return {\n        hasLoader: M.route.hasLoader,\n        hasClientLoader: M.route.hasClientLoader,\n        hasComponent: M.route.hasComponent,\n        hasAction: M.route.hasAction,\n        hasClientAction: M.route.hasClientAction,\n        hasShouldRevalidate: M.route.hasShouldRevalidate\n      };\n    },\n    // pass map into fetchAndDecode so it can add payloads\n    getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation),\n    ssr,\n    basename,\n    // If the route has a component but we don't have an element, we need to hit\n    // the server loader flow regardless of whether the client loader calls\n    // `serverLoader` or not, otherwise we'll have nothing to render.\n    (match) => {\n      let M = match;\n      return M.route.hasComponent && !M.route.element;\n    }\n  );\n  return async (args) => args.runClientMiddleware(async () => {\n    let context = args.context;\n    context.set(renderedRoutesContext, []);\n    let results = await dataStrategy(args);\n    const renderedRoutesById = /* @__PURE__ */ new Map();\n    for (const route of context.get(renderedRoutesContext)) {\n      if (!renderedRoutesById.has(route.id)) {\n        renderedRoutesById.set(route.id, []);\n      }\n      renderedRoutesById.get(route.id).push(route);\n    }\n    for (const match of args.matches) {\n      const renderedRoutes = renderedRoutesById.get(match.route.id);\n      if (renderedRoutes) {\n        for (const rendered of renderedRoutes) {\n          window.__reactRouterDataRouter.patchRoutes(\n            rendered.parentId ?? null,\n            [createRouteFromServerManifest(rendered)],\n            true\n          );\n        }\n      }\n    }\n    return results;\n  });\n}\nfunction getFetchAndDecodeViaRSC(createFromReadableStream, fetchImplementation) {\n  return async (args, basename, targetRoutes) => {\n    let { request, context } = args;\n    let url = singleFetchUrl(request.url, basename, \"rsc\");\n    if (request.method === \"GET\") {\n      url = stripIndexParam(url);\n      if (targetRoutes) {\n        url.searchParams.set(\"_routes\", targetRoutes.join(\",\"));\n      }\n    }\n    let res = await fetchImplementation(\n      new Request(url, await createRequestInit(request))\n    );\n    if (res.status === 404 && !res.headers.has(\"X-Remix-Response\")) {\n      throw new ErrorResponseImpl(404, \"Not Found\", true);\n    }\n    invariant(res.body, \"No response body to decode\");\n    try {\n      const payload = await createFromReadableStream(res.body, {\n        temporaryReferences: void 0\n      });\n      if (payload.type === \"redirect\") {\n        return {\n          status: res.status,\n          data: {\n            redirect: {\n              redirect: payload.location,\n              reload: payload.reload,\n              replace: payload.replace,\n              revalidate: false,\n              status: payload.status\n            }\n          }\n        };\n      }\n      if (payload.type !== \"render\") {\n        throw new Error(\"Unexpected payload type\");\n      }\n      context.get(renderedRoutesContext).push(...payload.matches);\n      let results = { routes: {} };\n      const dataKey = isMutationMethod(request.method) ? \"actionData\" : \"loaderData\";\n      for (let [routeId, data2] of Object.entries(payload[dataKey] || {})) {\n        results.routes[routeId] = { data: data2 };\n      }\n      if (payload.errors) {\n        for (let [routeId, error] of Object.entries(payload.errors)) {\n          results.routes[routeId] = { error };\n        }\n      }\n      return { status: res.status, data: results };\n    } catch (e) {\n      throw new Error(\"Unable to decode RSC response\");\n    }\n  };\n}\nfunction RSCHydratedRouter({\n  createFromReadableStream,\n  fetch: fetchImplementation = fetch,\n  payload,\n  routeDiscovery = \"eager\",\n  getContext\n}) {\n  if (payload.type !== \"render\") throw new Error(\"Invalid payload type\");\n  let { router, routeModules } = React4.useMemo(\n    () => createRouterFromPayload({\n      payload,\n      fetchImplementation,\n      getContext,\n      createFromReadableStream\n    }),\n    [createFromReadableStream, payload, fetchImplementation, getContext]\n  );\n  React4.useEffect(() => {\n    setIsHydrated();\n  }, []);\n  React4.useLayoutEffect(() => {\n    const globalVar = window;\n    if (!globalVar.__routerInitialized) {\n      globalVar.__routerInitialized = true;\n      globalVar.__reactRouterDataRouter.initialize();\n    }\n  }, []);\n  let [location2, setLocation] = React4.useState(router.state.location);\n  React4.useLayoutEffect(\n    () => router.subscribe((newState) => {\n      if (newState.location !== location2) {\n        setLocation(newState.location);\n      }\n    }),\n    [router, location2]\n  );\n  React4.useEffect(() => {\n    if (routeDiscovery === \"lazy\" || // @ts-expect-error - TS doesn't know about this yet\n    window.navigator?.connection?.saveData === true) {\n      return;\n    }\n    function registerElement(el) {\n      let path = el.tagName === \"FORM\" ? el.getAttribute(\"action\") : el.getAttribute(\"href\");\n      if (!path) {\n        return;\n      }\n      let pathname = el.tagName === \"A\" ? el.pathname : new URL(path, window.location.origin).pathname;\n      if (!discoveredPaths.has(pathname)) {\n        nextPaths.add(pathname);\n      }\n    }\n    async function fetchPatches() {\n      document.querySelectorAll(\"a[data-discover], form[data-discover]\").forEach(registerElement);\n      let paths = Array.from(nextPaths.keys()).filter((path) => {\n        if (discoveredPaths.has(path)) {\n          nextPaths.delete(path);\n          return false;\n        }\n        return true;\n      });\n      if (paths.length === 0) {\n        return;\n      }\n      try {\n        await fetchAndApplyManifestPatches(\n          paths,\n          createFromReadableStream,\n          fetchImplementation\n        );\n      } catch (e) {\n        console.error(\"Failed to fetch manifest patches\", e);\n      }\n    }\n    let debouncedFetchPatches = debounce(fetchPatches, 100);\n    fetchPatches();\n    let observer = new MutationObserver(() => debouncedFetchPatches());\n    observer.observe(document.documentElement, {\n      subtree: true,\n      childList: true,\n      attributes: true,\n      attributeFilter: [\"data-discover\", \"href\", \"action\"]\n    });\n  }, [routeDiscovery, createFromReadableStream, fetchImplementation]);\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      v8_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: false,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: { mode: \"lazy\", manifestPath: \"/__manifest\" },\n    routeModules\n  };\n  return /* @__PURE__ */ React4.createElement(RSCRouterContext.Provider, { value: true }, /* @__PURE__ */ React4.createElement(RSCRouterGlobalErrorBoundary, { location: location2 }, /* @__PURE__ */ React4.createElement(FrameworkContext.Provider, { value: frameworkContext }, /* @__PURE__ */ React4.createElement(RouterProvider, { router, flushSync: ReactDOM.flushSync }))));\n}\nfunction createRouteFromServerManifest(match, payload) {\n  let hasInitialData = payload && match.id in payload.loaderData;\n  let initialData = payload?.loaderData[match.id];\n  let hasInitialError = payload?.errors && match.id in payload.errors;\n  let initialError = payload?.errors?.[match.id];\n  let isHydrationRequest = match.clientLoader?.hydrate === true || !match.hasLoader || // If the route has a component but we don't have an element, we need to hit\n  // the server loader flow regardless of whether the client loader calls\n  // `serverLoader` or not, otherwise we'll have nothing to render.\n  match.hasComponent && !match.element;\n  invariant(window.__reactRouterRouteModules);\n  populateRSCRouteModules(window.__reactRouterRouteModules, match);\n  let dataRoute = {\n    id: match.id,\n    element: match.element,\n    errorElement: match.errorElement,\n    handle: match.handle,\n    hasErrorBoundary: match.hasErrorBoundary,\n    hydrateFallbackElement: match.hydrateFallbackElement,\n    index: match.index,\n    loader: match.clientLoader ? async (args, singleFetch) => {\n      try {\n        let result = await match.clientLoader({\n          ...args,\n          serverLoader: () => {\n            preventInvalidServerHandlerCall(\n              \"loader\",\n              match.id,\n              match.hasLoader\n            );\n            if (isHydrationRequest) {\n              if (hasInitialData) {\n                return initialData;\n              }\n              if (hasInitialError) {\n                throw initialError;\n              }\n            }\n            return callSingleFetch(singleFetch);\n          }\n        });\n        return result;\n      } finally {\n        isHydrationRequest = false;\n      }\n    } : (\n      // We always make the call in this RSC world since even if we don't\n      // have a `loader` we may need to get the `element` implementation\n      (_, singleFetch) => callSingleFetch(singleFetch)\n    ),\n    action: match.clientAction ? (args, singleFetch) => match.clientAction({\n      ...args,\n      serverAction: async () => {\n        preventInvalidServerHandlerCall(\n          \"action\",\n          match.id,\n          match.hasLoader\n        );\n        return await callSingleFetch(singleFetch);\n      }\n    }) : match.hasAction ? (_, singleFetch) => callSingleFetch(singleFetch) : () => {\n      throw noActionDefinedError(\"action\", match.id);\n    },\n    path: match.path,\n    shouldRevalidate: match.shouldRevalidate,\n    // We always have a \"loader\" in this RSC world since even if we don't\n    // have a `loader` we may need to get the `element` implementation\n    hasLoader: true,\n    hasClientLoader: match.clientLoader != null,\n    hasAction: match.hasAction,\n    hasClientAction: match.clientAction != null,\n    hasShouldRevalidate: match.shouldRevalidate != null\n  };\n  if (typeof dataRoute.loader === \"function\") {\n    dataRoute.loader.hydrate = shouldHydrateRouteLoader(\n      match.id,\n      match.clientLoader,\n      match.hasLoader,\n      false\n    );\n  }\n  return dataRoute;\n}\nfunction callSingleFetch(singleFetch) {\n  invariant(typeof singleFetch === \"function\", \"Invalid singleFetch parameter\");\n  return singleFetch();\n}\nfunction preventInvalidServerHandlerCall(type, routeId, hasHandler) {\n  if (!hasHandler) {\n    let fn = type === \"action\" ? \"serverAction()\" : \"serverLoader()\";\n    let msg = `You are trying to call ${fn} on a route that does not have a server ${type} (routeId: \"${routeId}\")`;\n    console.error(msg);\n    throw new ErrorResponseImpl(400, \"Bad Request\", new Error(msg), true);\n  }\n}\nvar nextPaths = /* @__PURE__ */ new Set();\nvar discoveredPathsMaxSize = 1e3;\nvar discoveredPaths = /* @__PURE__ */ new Set();\nvar URL_LIMIT = 7680;\nfunction getManifestUrl(paths) {\n  if (paths.length === 0) {\n    return null;\n  }\n  if (paths.length === 1) {\n    return new URL(`${paths[0]}.manifest`, window.location.origin);\n  }\n  const globalVar = window;\n  let basename = (globalVar.__reactRouterDataRouter.basename ?? \"\").replace(\n    /^\\/|\\/$/g,\n    \"\"\n  );\n  let url = new URL(`${basename}/.manifest`, window.location.origin);\n  paths.sort().forEach((path) => url.searchParams.append(\"p\", path));\n  return url;\n}\nasync function fetchAndApplyManifestPatches(paths, createFromReadableStream, fetchImplementation, signal) {\n  let url = getManifestUrl(paths);\n  if (url == null) {\n    return;\n  }\n  if (url.toString().length > URL_LIMIT) {\n    nextPaths.clear();\n    return;\n  }\n  let response = await fetchImplementation(new Request(url, { signal }));\n  if (!response.body || response.status < 200 || response.status >= 300) {\n    throw new Error(\"Unable to fetch new route matches from the server\");\n  }\n  let payload = await createFromReadableStream(response.body, {\n    temporaryReferences: void 0\n  });\n  if (payload.type !== \"manifest\") {\n    throw new Error(\"Failed to patch routes\");\n  }\n  paths.forEach((p) => addToFifoQueue(p, discoveredPaths));\n  payload.patches.forEach((p) => {\n    window.__reactRouterDataRouter.patchRoutes(\n      p.parentId ?? null,\n      [createRouteFromServerManifest(p)]\n    );\n  });\n}\nfunction addToFifoQueue(path, queue) {\n  if (queue.size >= discoveredPathsMaxSize) {\n    let first = queue.values().next().value;\n    queue.delete(first);\n  }\n  queue.add(path);\n}\nfunction debounce(callback, wait) {\n  let timeoutId;\n  return (...args) => {\n    window.clearTimeout(timeoutId);\n    timeoutId = window.setTimeout(() => callback(...args), wait);\n  };\n}\n\n// lib/rsc/server.ssr.tsx\nimport * as React5 from \"react\";\n\n// lib/rsc/html-stream/server.ts\nvar encoder2 = new TextEncoder();\nvar trailer = \"</body></html>\";\nfunction injectRSCPayload(rscStream) {\n  let decoder = new TextDecoder();\n  let resolveFlightDataPromise;\n  let flightDataPromise = new Promise(\n    (resolve) => resolveFlightDataPromise = resolve\n  );\n  let startedRSC = false;\n  let buffered = [];\n  let timeout = null;\n  function flushBufferedChunks(controller) {\n    for (let chunk of buffered) {\n      let buf = decoder.decode(chunk, { stream: true });\n      if (buf.endsWith(trailer)) {\n        buf = buf.slice(0, -trailer.length);\n      }\n      controller.enqueue(encoder2.encode(buf));\n    }\n    buffered.length = 0;\n    timeout = null;\n  }\n  return new TransformStream({\n    transform(chunk, controller) {\n      buffered.push(chunk);\n      if (timeout) {\n        return;\n      }\n      timeout = setTimeout(async () => {\n        flushBufferedChunks(controller);\n        if (!startedRSC) {\n          startedRSC = true;\n          writeRSCStream(rscStream, controller).catch((err) => controller.error(err)).then(resolveFlightDataPromise);\n        }\n      }, 0);\n    },\n    async flush(controller) {\n      await flightDataPromise;\n      if (timeout) {\n        clearTimeout(timeout);\n        flushBufferedChunks(controller);\n      }\n      controller.enqueue(encoder2.encode(\"</body></html>\"));\n    }\n  });\n}\nasync function writeRSCStream(rscStream, controller) {\n  let decoder = new TextDecoder(\"utf-8\", { fatal: true });\n  const reader = rscStream.getReader();\n  try {\n    let read;\n    while ((read = await reader.read()) && !read.done) {\n      const chunk = read.value;\n      try {\n        writeChunk(\n          JSON.stringify(decoder.decode(chunk, { stream: true })),\n          controller\n        );\n      } catch (err) {\n        let base64 = JSON.stringify(btoa(String.fromCodePoint(...chunk)));\n        writeChunk(\n          `Uint8Array.from(atob(${base64}), m => m.codePointAt(0))`,\n          controller\n        );\n      }\n    }\n  } finally {\n    reader.releaseLock();\n  }\n  let remaining = decoder.decode();\n  if (remaining.length) {\n    writeChunk(JSON.stringify(remaining), controller);\n  }\n}\nfunction writeChunk(chunk, controller) {\n  controller.enqueue(\n    encoder2.encode(\n      `<script>${escapeScript(\n        `(self.__FLIGHT_DATA||=[]).push(${chunk})`\n      )}</script>`\n    )\n  );\n}\nfunction escapeScript(script) {\n  return script.replace(/<!--/g, \"<\\\\!--\").replace(/<\\/(script)/gi, \"</\\\\$1\");\n}\n\n// lib/rsc/server.ssr.tsx\nvar REACT_USE = \"use\";\nvar useImpl = React5[REACT_USE];\nfunction useSafe(promise) {\n  if (useImpl) {\n    return useImpl(promise);\n  }\n  throw new Error(\"React Router v7 requires React 19+ for RSC features.\");\n}\nasync function routeRSCServerRequest({\n  request,\n  fetchServer,\n  createFromReadableStream,\n  renderHTML,\n  hydrate = true\n}) {\n  const url = new URL(request.url);\n  const isDataRequest = isReactServerRequest(url);\n  const respondWithRSCPayload = isDataRequest || isManifestRequest(url) || request.headers.has(\"rsc-action-id\");\n  const serverResponse = await fetchServer(request);\n  if (respondWithRSCPayload || serverResponse.headers.get(\"React-Router-Resource\") === \"true\") {\n    return serverResponse;\n  }\n  if (!serverResponse.body) {\n    throw new Error(\"Missing body in server response\");\n  }\n  const detectRedirectResponse = serverResponse.clone();\n  let serverResponseB = null;\n  if (hydrate) {\n    serverResponseB = serverResponse.clone();\n  }\n  const body = serverResponse.body;\n  let buffer;\n  let streamControllers = [];\n  const createStream = () => {\n    if (!buffer) {\n      buffer = [];\n      return body.pipeThrough(\n        new TransformStream({\n          transform(chunk, controller) {\n            buffer.push(chunk);\n            controller.enqueue(chunk);\n            streamControllers.forEach((c) => c.enqueue(chunk));\n          },\n          flush() {\n            streamControllers.forEach((c) => c.close());\n            streamControllers = [];\n          }\n        })\n      );\n    }\n    return new ReadableStream({\n      start(controller) {\n        buffer.forEach((chunk) => controller.enqueue(chunk));\n        streamControllers.push(controller);\n      }\n    });\n  };\n  const getPayload = async () => {\n    return createFromReadableStream(createStream());\n  };\n  try {\n    if (!detectRedirectResponse.body) {\n      throw new Error(\"Failed to clone server response\");\n    }\n    const payload = await createFromReadableStream(\n      detectRedirectResponse.body\n    );\n    if (serverResponse.status === SINGLE_FETCH_REDIRECT_STATUS && payload.type === \"redirect\") {\n      const headers2 = new Headers(serverResponse.headers);\n      headers2.delete(\"Content-Encoding\");\n      headers2.delete(\"Content-Length\");\n      headers2.delete(\"Content-Type\");\n      headers2.delete(\"x-remix-response\");\n      headers2.set(\"Location\", payload.location);\n      return new Response(serverResponseB?.body || \"\", {\n        headers: headers2,\n        status: payload.status,\n        statusText: serverResponse.statusText\n      });\n    }\n    const html = await renderHTML(getPayload);\n    const headers = new Headers(serverResponse.headers);\n    headers.set(\"Content-Type\", \"text/html\");\n    if (!hydrate) {\n      return new Response(html, {\n        status: serverResponse.status,\n        headers\n      });\n    }\n    if (!serverResponseB?.body) {\n      throw new Error(\"Failed to clone server response\");\n    }\n    const body2 = html.pipeThrough(injectRSCPayload(serverResponseB.body));\n    return new Response(body2, {\n      status: serverResponse.status,\n      headers\n    });\n  } catch (reason) {\n    if (reason instanceof Response) {\n      return reason;\n    }\n    throw reason;\n  }\n}\nfunction RSCStaticRouter({ getPayload }) {\n  const payload = useSafe(getPayload());\n  if (payload.type === \"redirect\") {\n    throw new Response(null, {\n      status: payload.status,\n      headers: {\n        Location: payload.location\n      }\n    });\n  }\n  if (payload.type !== \"render\") return null;\n  let patchedLoaderData = { ...payload.loaderData };\n  for (const match of payload.matches) {\n    if (shouldHydrateRouteLoader(\n      match.id,\n      match.clientLoader,\n      match.hasLoader,\n      false\n    ) && (match.hydrateFallbackElement || !match.hasLoader)) {\n      delete patchedLoaderData[match.id];\n    }\n  }\n  const context = {\n    actionData: payload.actionData,\n    actionHeaders: {},\n    basename: payload.basename,\n    errors: payload.errors,\n    loaderData: patchedLoaderData,\n    loaderHeaders: {},\n    location: payload.location,\n    statusCode: 200,\n    matches: payload.matches.map((match) => ({\n      params: match.params,\n      pathname: match.pathname,\n      pathnameBase: match.pathnameBase,\n      route: {\n        id: match.id,\n        action: match.hasAction || !!match.clientAction,\n        handle: match.handle,\n        hasErrorBoundary: match.hasErrorBoundary,\n        loader: match.hasLoader || !!match.clientLoader,\n        index: match.index,\n        path: match.path,\n        shouldRevalidate: match.shouldRevalidate\n      }\n    }))\n  };\n  const router = createStaticRouter(\n    payload.matches.reduceRight((previous, match) => {\n      const route = {\n        id: match.id,\n        action: match.hasAction || !!match.clientAction,\n        element: match.element,\n        errorElement: match.errorElement,\n        handle: match.handle,\n        hasErrorBoundary: !!match.errorElement,\n        hydrateFallbackElement: match.hydrateFallbackElement,\n        index: match.index,\n        loader: match.hasLoader || !!match.clientLoader,\n        path: match.path,\n        shouldRevalidate: match.shouldRevalidate\n      };\n      if (previous.length > 0) {\n        route.children = previous;\n      }\n      return [route];\n    }, []),\n    context\n  );\n  const frameworkContext = {\n    future: {\n      // These flags have no runtime impact so can always be false.  If we add\n      // flags that drive runtime behavior they'll need to be proxied through.\n      v8_middleware: false,\n      unstable_subResourceIntegrity: false\n    },\n    isSpaMode: false,\n    ssr: true,\n    criticalCss: \"\",\n    manifest: {\n      routes: {},\n      version: \"1\",\n      url: \"\",\n      entry: {\n        module: \"\",\n        imports: []\n      }\n    },\n    routeDiscovery: { mode: \"lazy\", manifestPath: \"/__manifest\" },\n    routeModules: createRSCRouteModules(payload)\n  };\n  return /* @__PURE__ */ React5.createElement(RSCRouterContext.Provider, { value: true }, /* @__PURE__ */ React5.createElement(RSCRouterGlobalErrorBoundary, { location: payload.location }, /* @__PURE__ */ React5.createElement(FrameworkContext.Provider, { value: frameworkContext }, /* @__PURE__ */ React5.createElement(\n    StaticRouterProvider,\n    {\n      context,\n      router,\n      hydrate: false,\n      nonce: payload.nonce\n    }\n  ))));\n}\nfunction isReactServerRequest(url) {\n  return url.pathname.endsWith(\".rsc\");\n}\nfunction isManifestRequest(url) {\n  return url.pathname.endsWith(\".manifest\");\n}\n\n// lib/rsc/html-stream/browser.ts\nfunction getRSCStream() {\n  let encoder3 = new TextEncoder();\n  let streamController = null;\n  let rscStream = new ReadableStream({\n    start(controller) {\n      if (typeof window === \"undefined\") {\n        return;\n      }\n      let handleChunk = (chunk) => {\n        if (typeof chunk === \"string\") {\n          controller.enqueue(encoder3.encode(chunk));\n        } else {\n          controller.enqueue(chunk);\n        }\n      };\n      window.__FLIGHT_DATA || (window.__FLIGHT_DATA = []);\n      window.__FLIGHT_DATA.forEach(handleChunk);\n      window.__FLIGHT_DATA.push = (chunk) => {\n        handleChunk(chunk);\n        return 0;\n      };\n      streamController = controller;\n    }\n  });\n  if (typeof document !== \"undefined\" && document.readyState === \"loading\") {\n    document.addEventListener(\"DOMContentLoaded\", () => {\n      streamController?.close();\n    });\n  } else {\n    streamController?.close();\n  }\n  return rscStream;\n}\n\n// lib/dom/ssr/errors.ts\nfunction deserializeErrors(errors) {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized = {};\n  for (let [key, val] of entries) {\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(\n        val.status,\n        val.statusText,\n        val.data,\n        val.internal === true\n      );\n    } else if (val && val.__type === \"Error\") {\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            let error = new ErrorConstructor(val.message);\n            error.stack = val.stack;\n            serialized[key] = error;\n          } catch (e) {\n          }\n        }\n      }\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        error.stack = val.stack;\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\nexport {\n  ServerRouter,\n  createRoutesStub,\n  createCookie,\n  isCookie,\n  ServerMode,\n  setDevServerHooks,\n  createRequestHandler,\n  createSession,\n  isSession,\n  createSessionStorage,\n  createCookieSessionStorage,\n  createMemorySessionStorage,\n  href,\n  getHydrationData,\n  RSCDefaultRootErrorBoundary,\n  createCallServer,\n  RSCHydratedRouter,\n  routeRSCServerRequest,\n  RSCStaticRouter,\n  getRSCStream,\n  deserializeErrors\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SACEA,mBAAmB,EACnBC,iBAAiB,EACjBC,gBAAgB,EAChBC,oBAAoB,EACpBC,MAAM,EACNC,gBAAgB,EAChBC,kBAAkB,EAClBC,qBAAqB,EACrBC,cAAc,EACdC,4BAA4B,EAC5BC,yBAAyB,EACzBC,oBAAoB,EACpBC,cAAc,EACdC,yBAAyB,EACzBC,oBAAoB,EACpBC,aAAa,EACbC,kBAAkB,EAClBC,iBAAiB,EACjBC,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,EACnBC,kBAAkB,EAClBC,oBAAoB,EACpBC,MAAM,EACNC,UAAU,EACVC,eAAe,EACfC,8BAA8B,EAC9BC,yBAAyB,EACzBC,SAAS,EACTC,sBAAsB,EACtBC,gBAAgB,EAChBC,kBAAkB,EAClBC,oBAAoB,EACpBC,UAAU,EACVC,oBAAoB,EACpBC,WAAW,EACXC,oBAAoB,EACpBC,QAAQ,EACRC,gBAAgB,EAChBC,OAAO,EACPC,aAAa,EACbC,wBAAwB,EACxBC,cAAc,EACdC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,QAAQ,EACRC,kBAAkB,EAClBC,sBAAsB,EACtBC,wBAAwB,QACnB,sBAAsB;;AAE7B;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAYA,CAAC;EACpBC,OAAO;EACPC,GAAG;EACHC;AACF,CAAC,EAAE;EACD,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;IAC3BA,GAAG,GAAG,IAAIE,GAAG,CAACF,GAAG,CAAC;EACpB;EACA,IAAI;IAAEG,QAAQ;IAAEC,YAAY;IAAEC,WAAW;IAAEC;EAAoB,CAAC,GAAGP,OAAO;EAC1E,IAAIQ,MAAM,GAAGzC,kBAAkB,CAC7BqC,QAAQ,CAACI,MAAM,EACfH,YAAY,EACZL,OAAO,CAACS,MAAM,EACdT,OAAO,CAACU,SACV,CAAC;EACDV,OAAO,CAACW,oBAAoB,CAACC,UAAU,GAAG;IACxC,GAAGZ,OAAO,CAACW,oBAAoB,CAACC;EAClC,CAAC;EACD,KAAK,IAAIC,KAAK,IAAIb,OAAO,CAACW,oBAAoB,CAACG,OAAO,EAAE;IACtD,IAAIC,OAAO,GAAGF,KAAK,CAACG,KAAK,CAACC,EAAE;IAC5B,IAAID,KAAK,GAAGX,YAAY,CAACU,OAAO,CAAC;IACjC,IAAIG,aAAa,GAAGlB,OAAO,CAACI,QAAQ,CAACI,MAAM,CAACO,OAAO,CAAC;IACpD,IAAIC,KAAK,IAAIE,aAAa,IAAI7B,wBAAwB,CACpD0B,OAAO,EACPC,KAAK,CAACG,YAAY,EAClBD,aAAa,CAACE,SAAS,EACvBpB,OAAO,CAACU,SACV,CAAC,KAAKM,KAAK,CAACK,eAAe,IAAI,CAACH,aAAa,CAACE,SAAS,CAAC,EAAE;MACxD,OAAOpB,OAAO,CAACW,oBAAoB,CAACC,UAAU,CAACG,OAAO,CAAC;IACzD;EACF;EACA,IAAIO,MAAM,GAAGrD,kBAAkB,CAACuC,MAAM,EAAER,OAAO,CAACW,oBAAoB,CAAC;EACrE,OAAO,eAAgBb,KAAK,CAACyB,aAAa,CAACzB,KAAK,CAAC0B,QAAQ,EAAE,IAAI,EAAE,eAAgB1B,KAAK,CAACyB,aAAa,CAClGzE,gBAAgB,CAAC2E,QAAQ,EACzB;IACEC,KAAK,EAAE;MACLtB,QAAQ;MACRC,YAAY;MACZC,WAAW;MACXC,mBAAmB;MACnBE,MAAM,EAAET,OAAO,CAACS,MAAM;MACtBkB,GAAG,EAAE3B,OAAO,CAAC2B,GAAG;MAChBjB,SAAS,EAAEV,OAAO,CAACU,SAAS;MAC5BkB,cAAc,EAAE5B,OAAO,CAAC4B,cAAc;MACtCC,cAAc,EAAE7B,OAAO,CAAC6B,cAAc;MACtCC,UAAU,EAAE9B,OAAO,CAAC8B;IACtB;EACF,CAAC,EACD,eAAgBhC,KAAK,CAACyB,aAAa,CAACrE,kBAAkB,EAAE;IAAE6E,QAAQ,EAAET,MAAM,CAACU,KAAK,CAACD;EAAS,CAAC,EAAE,eAAgBjC,KAAK,CAACyB,aAAa,CAC9HhE,oBAAoB,EACpB;IACE+D,MAAM;IACNtB,OAAO,EAAEA,OAAO,CAACW,oBAAoB;IACrCsB,OAAO,EAAE;EACX,CACF,CAAC,CACH,CAAC,EAAEjC,OAAO,CAACkC,mBAAmB,GAAG,eAAgBpC,KAAK,CAACyB,aAAa,CAACzB,KAAK,CAACqC,QAAQ,EAAE,IAAI,EAAE,eAAgBrC,KAAK,CAACyB,aAAa,CAC5H/D,cAAc,EACd;IACEwC,OAAO;IACPoC,UAAU,EAAE,CAAC;IACbC,MAAM,EAAErC,OAAO,CAACkC,mBAAmB,CAACI,SAAS,CAAC,CAAC;IAC/CC,WAAW,EAAE,IAAIC,WAAW,CAAC,CAAC;IAC9BtC;EACF,CACF,CAAC,CAAC,GAAG,IAAI,CAAC;AACZ;;AAEA;AACA,OAAO,KAAKuC,MAAM,MAAM,OAAO;AAC/B,SAASC,gBAAgBA,CAAClC,MAAM,EAAEmC,QAAQ,EAAE;EAC1C,OAAO,SAASC,cAAcA,CAAC;IAC7BC,cAAc;IACdC,YAAY;IACZC,aAAa;IACbtC;EACF,CAAC,EAAE;IACD,IAAIuC,SAAS,GAAGP,MAAM,CAACQ,MAAM,CAAC,CAAC;IAC/B,IAAIC,mBAAmB,GAAGT,MAAM,CAACQ,MAAM,CAAC,CAAC;IACzC,IAAID,SAAS,CAACG,OAAO,IAAI,IAAI,EAAE;MAC7BD,mBAAmB,CAACC,OAAO,GAAG;QAC5B1C,MAAM,EAAE;UACN2C,6BAA6B,EAAE3C,MAAM,EAAE2C,6BAA6B,KAAK,IAAI;UAC7EC,aAAa,EAAE5C,MAAM,EAAE4C,aAAa,KAAK;QAC3C,CAAC;QACDjD,QAAQ,EAAE;UACRI,MAAM,EAAE,CAAC,CAAC;UACV8C,KAAK,EAAE;YAAEC,OAAO,EAAE,EAAE;YAAEC,MAAM,EAAE;UAAG,CAAC;UAClCvD,GAAG,EAAE,EAAE;UACPwD,OAAO,EAAE;QACX,CAAC;QACDpD,YAAY,EAAE,CAAC,CAAC;QAChBsB,GAAG,EAAE,KAAK;QACVjB,SAAS,EAAE,KAAK;QAChBkB,cAAc,EAAE;UAAE8B,IAAI,EAAE,MAAM;UAAEC,YAAY,EAAE;QAAc;MAC9D,CAAC;MACD,IAAIC,OAAO,GAAGC,aAAa;MACzB;MACA;MACApG,yBAAyB,CAAC+C,MAAM,EAAGsD,CAAC,IAAKA,CAAC,CAAC,EAC3CnB,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGlC,MAAM,EAAE4C,aAAa,GAAG,IAAIlG,qBAAqB,CAAC,CAAC,GAAG,CAAC,CAAC,EACzF+F,mBAAmB,CAACC,OAAO,CAAC/C,QAAQ,EACpC8C,mBAAmB,CAACC,OAAO,CAAC9C,YAC9B,CAAC;MACD2C,SAAS,CAACG,OAAO,GAAGvF,kBAAkB,CAACgG,OAAO,EAAE;QAC9Cf,cAAc;QACdC,YAAY;QACZC;MACF,CAAC,CAAC;IACJ;IACA,OAAO,eAAgBN,MAAM,CAAClB,aAAa,CAACzE,gBAAgB,CAAC2E,QAAQ,EAAE;MAAEC,KAAK,EAAEwB,mBAAmB,CAACC;IAAQ,CAAC,EAAE,eAAgBV,MAAM,CAAClB,aAAa,CAACnE,cAAc,EAAE;MAAEkE,MAAM,EAAE0B,SAAS,CAACG;IAAQ,CAAC,CAAC,CAAC;EACrM,CAAC;AACH;AACA,SAASU,aAAaA,CAACrD,MAAM,EAAER,OAAO,EAAEI,QAAQ,EAAEC,YAAY,EAAE0D,QAAQ,EAAE;EACxE,OAAOvD,MAAM,CAACwD,GAAG,CAAEhD,KAAK,IAAK;IAC3B,IAAI,CAACA,KAAK,CAACC,EAAE,EAAE;MACb,MAAM,IAAIgD,KAAK,CACb,8DACF,CAAC;IACH;IACA,IAAIC,QAAQ,GAAG;MACbjD,EAAE,EAAED,KAAK,CAACC,EAAE;MACZkD,IAAI,EAAEnD,KAAK,CAACmD,IAAI;MAChBC,KAAK,EAAEpD,KAAK,CAACoD,KAAK;MAClBC,SAAS,EAAErD,KAAK,CAACqD,SAAS,GAAG1E,kBAAkB,CAACqB,KAAK,CAACqD,SAAS,CAAC,GAAG,KAAK,CAAC;MACzEhD,eAAe,EAAEL,KAAK,CAACK,eAAe,GAAGxB,wBAAwB,CAACmB,KAAK,CAACK,eAAe,CAAC,GAAG,KAAK,CAAC;MACjGiD,aAAa,EAAEtD,KAAK,CAACsD,aAAa,GAAG1E,sBAAsB,CAACoB,KAAK,CAACsD,aAAa,CAAC,GAAG,KAAK,CAAC;MACzFC,MAAM,EAAEvD,KAAK,CAACuD,MAAM,GAAIC,IAAI,IAAKxD,KAAK,CAACuD,MAAM,CAAC;QAAE,GAAGC,IAAI;QAAExE;MAAQ,CAAC,CAAC,GAAG,KAAK,CAAC;MAC5EyE,MAAM,EAAEzD,KAAK,CAACyD,MAAM,GAAID,IAAI,IAAKxD,KAAK,CAACyD,MAAM,CAAC;QAAE,GAAGD,IAAI;QAAExE;MAAQ,CAAC,CAAC,GAAG,KAAK,CAAC;MAC5E0E,MAAM,EAAE1D,KAAK,CAAC0D,MAAM;MACpBC,gBAAgB,EAAE3D,KAAK,CAAC2D;IAC1B,CAAC;IACD,IAAIC,UAAU,GAAG;MACf3D,EAAE,EAAED,KAAK,CAACC,EAAE;MACZkD,IAAI,EAAEnD,KAAK,CAACmD,IAAI;MAChBC,KAAK,EAAEpD,KAAK,CAACoD,KAAK;MAClBL,QAAQ;MACRc,SAAS,EAAE7D,KAAK,CAACuD,MAAM,IAAI,IAAI;MAC/BnD,SAAS,EAAEJ,KAAK,CAACyD,MAAM,IAAI,IAAI;MAC/B;MACA;MACA;MACAK,eAAe,EAAE,KAAK;MACtBC,eAAe,EAAE,KAAK;MACtBC,mBAAmB,EAAE,KAAK;MAC1BC,gBAAgB,EAAEjE,KAAK,CAACsD,aAAa,IAAI,IAAI;MAC7C;MACAd,MAAM,EAAE,8BAA8B;MACtC0B,kBAAkB,EAAE,KAAK,CAAC;MAC1BC,kBAAkB,EAAE,KAAK,CAAC;MAC1BC,sBAAsB,EAAE,KAAK,CAAC;MAC9BC,qBAAqB,EAAE,KAAK;IAC9B,CAAC;IACDjF,QAAQ,CAACI,MAAM,CAAC0D,QAAQ,CAACjD,EAAE,CAAC,GAAG2D,UAAU;IACzCvE,YAAY,CAACW,KAAK,CAACC,EAAE,CAAC,GAAG;MACvBqE,OAAO,EAAEpB,QAAQ,CAACG,SAAS,IAAIrH,MAAM;MACrCsH,aAAa,EAAEJ,QAAQ,CAACI,aAAa,IAAI,KAAK,CAAC;MAC/CI,MAAM,EAAE1D,KAAK,CAAC0D,MAAM;MACpBa,KAAK,EAAEvE,KAAK,CAACuE,KAAK;MAClBC,IAAI,EAAExE,KAAK,CAACwE,IAAI;MAChBb,gBAAgB,EAAE3D,KAAK,CAAC2D;IAC1B,CAAC;IACD,IAAI3D,KAAK,CAACyE,QAAQ,EAAE;MAClBvB,QAAQ,CAACuB,QAAQ,GAAG5B,aAAa,CAC/B7C,KAAK,CAACyE,QAAQ,EACdzF,OAAO,EACPI,QAAQ,EACRC,YAAY,EACZ6D,QAAQ,CAACjD,EACX,CAAC;IACH;IACA,OAAOiD,QAAQ;EACjB,CAAC,CAAC;AACJ;;AAEA;AACA,SAASwB,KAAK,EAAEC,SAAS,QAAQ,QAAQ;;AAEzC;AACA,IAAIC,OAAO,GAAG,eAAgB,IAAIC,WAAW,CAAC,CAAC;AAC/C,IAAIC,IAAI,GAAG,MAAAA,CAAOpE,KAAK,EAAEqE,MAAM,KAAK;EAClC,IAAIC,KAAK,GAAGJ,OAAO,CAACzH,MAAM,CAACuD,KAAK,CAAC;EACjC,IAAIuE,GAAG,GAAG,MAAMC,SAAS,CAACH,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC;EAC3C,IAAII,SAAS,GAAG,MAAMC,MAAM,CAACC,MAAM,CAACP,IAAI,CAAC,MAAM,EAAEG,GAAG,EAAED,KAAK,CAAC;EAC5D,IAAIM,IAAI,GAAGC,IAAI,CAACC,MAAM,CAACC,YAAY,CAAC,GAAG,IAAIC,UAAU,CAACP,SAAS,CAAC,CAAC,CAAC,CAAChH,OAAO,CACxE,KAAK,EACL,EACF,CAAC;EACD,OAAOuC,KAAK,GAAG,GAAG,GAAG4E,IAAI;AAC3B,CAAC;AACD,IAAIK,MAAM,GAAG,MAAAA,CAAOC,MAAM,EAAEb,MAAM,KAAK;EACrC,IAAI3B,KAAK,GAAGwC,MAAM,CAACC,WAAW,CAAC,GAAG,CAAC;EACnC,IAAInF,KAAK,GAAGkF,MAAM,CAACE,KAAK,CAAC,CAAC,EAAE1C,KAAK,CAAC;EAClC,IAAIkC,IAAI,GAAGM,MAAM,CAACE,KAAK,CAAC1C,KAAK,GAAG,CAAC,CAAC;EAClC,IAAI4B,KAAK,GAAGJ,OAAO,CAACzH,MAAM,CAACuD,KAAK,CAAC;EACjC,IAAIuE,GAAG,GAAG,MAAMC,SAAS,CAACH,MAAM,EAAE,CAAC,QAAQ,CAAC,CAAC;EAC7C,IAAI;IACF,IAAII,SAAS,GAAGY,sBAAsB,CAACC,IAAI,CAACV,IAAI,CAAC,CAAC;IAClD,IAAIW,KAAK,GAAG,MAAMb,MAAM,CAACC,MAAM,CAACa,MAAM,CAAC,MAAM,EAAEjB,GAAG,EAAEE,SAAS,EAAEH,KAAK,CAAC;IACrE,OAAOiB,KAAK,GAAGvF,KAAK,GAAG,KAAK;EAC9B,CAAC,CAAC,OAAOyF,KAAK,EAAE;IACd,OAAO,KAAK;EACd;AACF,CAAC;AACD,IAAIjB,SAAS,GAAG,MAAAA,CAAOH,MAAM,EAAEqB,MAAM,KAAKhB,MAAM,CAACC,MAAM,CAACgB,SAAS,CAC/D,KAAK,EACLzB,OAAO,CAACzH,MAAM,CAAC4H,MAAM,CAAC,EACtB;EAAEuB,IAAI,EAAE,MAAM;EAAEhB,IAAI,EAAE;AAAU,CAAC,EACjC,KAAK,EACLc,MACF,CAAC;AACD,SAASL,sBAAsBA,CAACQ,UAAU,EAAE;EAC1C,IAAIC,KAAK,GAAG,IAAId,UAAU,CAACa,UAAU,CAACE,MAAM,CAAC;EAC7C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,UAAU,CAACE,MAAM,EAAEC,CAAC,EAAE,EAAE;IAC1CF,KAAK,CAACE,CAAC,CAAC,GAAGH,UAAU,CAACI,UAAU,CAACD,CAAC,CAAC;EACrC;EACA,OAAOF,KAAK;AACd;;AAEA;AACA,IAAII,YAAY,GAAGA,CAACN,IAAI,EAAEO,aAAa,GAAG,CAAC,CAAC,KAAK;EAC/C,IAAI;IAAEC,OAAO,GAAG,EAAE;IAAE,GAAGC;EAAQ,CAAC,GAAG;IACjC5D,IAAI,EAAE,GAAG;IACT6D,QAAQ,EAAE,KAAK;IACf,GAAGH;EACL,CAAC;EACDI,0BAA0B,CAACX,IAAI,EAAES,OAAO,CAACG,OAAO,CAAC;EACjD,OAAO;IACL,IAAIZ,IAAIA,CAAA,EAAG;MACT,OAAOA,IAAI;IACb,CAAC;IACD,IAAIa,QAAQA,CAAA,EAAG;MACb,OAAOL,OAAO,CAACL,MAAM,GAAG,CAAC;IAC3B,CAAC;IACD,IAAIS,OAAOA,CAAA,EAAG;MACZ,OAAO,OAAOH,OAAO,CAACK,MAAM,KAAK,WAAW,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGP,OAAO,CAACK,MAAM,GAAG,GAAG,CAAC,GAAGL,OAAO,CAACG,OAAO;IAC9G,CAAC;IACD,MAAMxC,KAAKA,CAAC6C,YAAY,EAAEC,YAAY,EAAE;MACtC,IAAI,CAACD,YAAY,EAAE,OAAO,IAAI;MAC9B,IAAIE,OAAO,GAAG/C,KAAK,CAAC6C,YAAY,EAAE;QAAE,GAAGR,OAAO;QAAE,GAAGS;MAAa,CAAC,CAAC;MAClE,IAAIlB,IAAI,IAAImB,OAAO,EAAE;QACnB,IAAI/G,KAAK,GAAG+G,OAAO,CAACnB,IAAI,CAAC;QACzB,IAAI,OAAO5F,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,EAAE,EAAE;UAC7C,IAAIgH,OAAO,GAAG,MAAMC,iBAAiB,CAACjH,KAAK,EAAEoG,OAAO,CAAC;UACrD,OAAOY,OAAO;QAChB,CAAC,MAAM;UACL,OAAO,EAAE;QACX;MACF,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF,CAAC;IACD,MAAM/C,SAASA,CAACjE,KAAK,EAAEkH,gBAAgB,EAAE;MACvC,OAAOjD,SAAS,CACd2B,IAAI,EACJ5F,KAAK,KAAK,EAAE,GAAG,EAAE,GAAG,MAAMmH,iBAAiB,CAACnH,KAAK,EAAEoG,OAAO,CAAC,EAC3D;QACE,GAAGC,OAAO;QACV,GAAGa;MACL,CACF,CAAC;IACH;EACF,CAAC;AACH,CAAC;AACD,IAAIE,QAAQ,GAAIC,MAAM,IAAK;EACzB,OAAOA,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,CAACzB,IAAI,KAAK,QAAQ,IAAI,OAAOyB,MAAM,CAACZ,QAAQ,KAAK,SAAS,IAAI,OAAOY,MAAM,CAACrD,KAAK,KAAK,UAAU,IAAI,OAAOqD,MAAM,CAACpD,SAAS,KAAK,UAAU;AAClL,CAAC;AACD,eAAekD,iBAAiBA,CAACnH,KAAK,EAAEoG,OAAO,EAAE;EAC/C,IAAIkB,OAAO,GAAGC,UAAU,CAACvH,KAAK,CAAC;EAC/B,IAAIoG,OAAO,CAACL,MAAM,GAAG,CAAC,EAAE;IACtBuB,OAAO,GAAG,MAAMlD,IAAI,CAACkD,OAAO,EAAElB,OAAO,CAAC,CAAC,CAAC,CAAC;EAC3C;EACA,OAAOkB,OAAO;AAChB;AACA,eAAeL,iBAAiBA,CAACjH,KAAK,EAAEoG,OAAO,EAAE;EAC/C,IAAIA,OAAO,CAACL,MAAM,GAAG,CAAC,EAAE;IACtB,KAAK,IAAI1B,MAAM,IAAI+B,OAAO,EAAE;MAC1B,IAAIoB,aAAa,GAAG,MAAMvC,MAAM,CAACjF,KAAK,EAAEqE,MAAM,CAAC;MAC/C,IAAImD,aAAa,KAAK,KAAK,EAAE;QAC3B,OAAOC,UAAU,CAACD,aAAa,CAAC;MAClC;IACF;IACA,OAAO,IAAI;EACb;EACA,OAAOC,UAAU,CAACzH,KAAK,CAAC;AAC1B;AACA,SAASuH,UAAUA,CAACvH,KAAK,EAAE;EACzB,OAAO6E,IAAI,CAAC6C,UAAU,CAACC,kBAAkB,CAACC,IAAI,CAACC,SAAS,CAAC7H,KAAK,CAAC,CAAC,CAAC,CAAC;AACpE;AACA,SAASyH,UAAUA,CAACzH,KAAK,EAAE;EACzB,IAAI;IACF,OAAO4H,IAAI,CAAC5D,KAAK,CAAC8D,kBAAkB,CAACC,QAAQ,CAACzC,IAAI,CAACtF,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9D,CAAC,CAAC,OAAOyF,KAAK,EAAE;IACd,OAAO,CAAC,CAAC;EACX;AACF;AACA,SAASsC,QAAQA,CAAC/H,KAAK,EAAE;EACvB,IAAIgI,GAAG,GAAGhI,KAAK,CAACiI,QAAQ,CAAC,CAAC;EAC1B,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIxF,KAAK,GAAG,CAAC;EACb,IAAIyF,GAAG,EAAEC,IAAI;EACb,OAAO1F,KAAK,GAAGsF,GAAG,CAACjC,MAAM,EAAE;IACzBoC,GAAG,GAAGH,GAAG,CAACK,MAAM,CAAC3F,KAAK,EAAE,CAAC;IACzB,IAAI,aAAa,CAAC4F,IAAI,CAACH,GAAG,CAAC,EAAE;MAC3BD,MAAM,IAAIC,GAAG;IACf,CAAC,MAAM;MACLC,IAAI,GAAGD,GAAG,CAAClC,UAAU,CAAC,CAAC,CAAC;MACxB,IAAImC,IAAI,GAAG,GAAG,EAAE;QACdF,MAAM,IAAI,GAAG,GAAGK,GAAG,CAACH,IAAI,EAAE,CAAC,CAAC;MAC9B,CAAC,MAAM;QACLF,MAAM,IAAI,IAAI,GAAGK,GAAG,CAACH,IAAI,EAAE,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;MAC7C;IACF;EACF;EACA,OAAON,MAAM;AACf;AACA,SAASK,GAAGA,CAACH,IAAI,EAAErC,MAAM,EAAE;EACzB,IAAImC,MAAM,GAAGE,IAAI,CAACH,QAAQ,CAAC,EAAE,CAAC;EAC9B,OAAOC,MAAM,CAACnC,MAAM,GAAGA,MAAM,EAAEmC,MAAM,GAAG,GAAG,GAAGA,MAAM;EACpD,OAAOA,MAAM;AACf;AACA,SAASR,UAAUA,CAAC1H,KAAK,EAAE;EACzB,IAAIgI,GAAG,GAAGhI,KAAK,CAACiI,QAAQ,CAAC,CAAC;EAC1B,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIxF,KAAK,GAAG,CAAC;EACb,IAAIyF,GAAG,EAAEM,IAAI;EACb,OAAO/F,KAAK,GAAGsF,GAAG,CAACjC,MAAM,EAAE;IACzBoC,GAAG,GAAGH,GAAG,CAACK,MAAM,CAAC3F,KAAK,EAAE,CAAC;IACzB,IAAIyF,GAAG,KAAK,GAAG,EAAE;MACf,IAAIH,GAAG,CAACK,MAAM,CAAC3F,KAAK,CAAC,KAAK,GAAG,EAAE;QAC7B+F,IAAI,GAAGT,GAAG,CAAC5C,KAAK,CAAC1C,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,CAAC,CAAC;QACtC,IAAI,eAAe,CAAC4F,IAAI,CAACG,IAAI,CAAC,EAAE;UAC9BP,MAAM,IAAIpD,MAAM,CAACC,YAAY,CAAC2D,QAAQ,CAACD,IAAI,EAAE,EAAE,CAAC,CAAC;UACjD/F,KAAK,IAAI,CAAC;UACV;QACF;MACF,CAAC,MAAM;QACL+F,IAAI,GAAGT,GAAG,CAAC5C,KAAK,CAAC1C,KAAK,EAAEA,KAAK,GAAG,CAAC,CAAC;QAClC,IAAI,eAAe,CAAC4F,IAAI,CAACG,IAAI,CAAC,EAAE;UAC9BP,MAAM,IAAIpD,MAAM,CAACC,YAAY,CAAC2D,QAAQ,CAACD,IAAI,EAAE,EAAE,CAAC,CAAC;UACjD/F,KAAK,IAAI,CAAC;UACV;QACF;MACF;IACF;IACAwF,MAAM,IAAIC,GAAG;EACf;EACA,OAAOD,MAAM;AACf;AACA,SAAS3B,0BAA0BA,CAACX,IAAI,EAAEY,OAAO,EAAE;EACjDxI,QAAQ,CACN,CAACwI,OAAO,EACR,QAAQZ,IAAI,6WACd,CAAC;AACH;;AAEA;AACA,SAAS+C,uBAAuBA,CAACjK,QAAQ,EAAE;EACzC,OAAOkK,MAAM,CAACC,IAAI,CAACnK,QAAQ,CAAC,CAACoK,MAAM,CAAC,CAACC,IAAI,EAAE1J,OAAO,KAAK;IACrD,IAAIC,KAAK,GAAGZ,QAAQ,CAACW,OAAO,CAAC;IAC7B,IAAIC,KAAK,EAAE;MACTyJ,IAAI,CAAC1J,OAAO,CAAC,GAAGC,KAAK,CAACwC,MAAM;IAC9B;IACA,OAAOiH,IAAI;EACb,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;;AAEA;AACA,IAAIC,UAAU,GAAG,eAAgB,CAAEC,WAAW,IAAK;EACjDA,WAAW,CAAC,aAAa,CAAC,GAAG,aAAa;EAC1CA,WAAW,CAAC,YAAY,CAAC,GAAG,YAAY;EACxCA,WAAW,CAAC,MAAM,CAAC,GAAG,MAAM;EAC5B,OAAOA,WAAW;AACpB,CAAC,EAAED,UAAU,IAAI,CAAC,CAAC,CAAC;AACpB,SAASE,YAAYA,CAAClJ,KAAK,EAAE;EAC3B,OAAOA,KAAK,KAAK,aAAa,CAAC,qBAAqBA,KAAK,KAAK,YAAY,CAAC,oBAAoBA,KAAK,KAAK,MAAM,CAAC;AAClH;;AAEA;AACA,SAASmJ,aAAaA,CAAC1D,KAAK,EAAE2D,UAAU,EAAE;EACxC,IAAI3D,KAAK,YAAYlD,KAAK,IAAI6G,UAAU,KAAK,aAAa,CAAC,mBAAmB;IAC5E,IAAIC,SAAS,GAAG,IAAI9G,KAAK,CAAC,yBAAyB,CAAC;IACpD8G,SAAS,CAACC,KAAK,GAAG,KAAK,CAAC;IACxB,OAAOD,SAAS;EAClB;EACA,OAAO5D,KAAK;AACd;AACA,SAAS8D,cAAcA,CAACC,MAAM,EAAEJ,UAAU,EAAE;EAC1C,OAAOR,MAAM,CAACa,OAAO,CAACD,MAAM,CAAC,CAACV,MAAM,CAAC,CAACY,GAAG,EAAE,CAACrK,OAAO,EAAEoG,KAAK,CAAC,KAAK;IAC9D,OAAOmD,MAAM,CAACe,MAAM,CAACD,GAAG,EAAE;MAAE,CAACrK,OAAO,GAAG8J,aAAa,CAAC1D,KAAK,EAAE2D,UAAU;IAAE,CAAC,CAAC;EAC5E,CAAC,EAAE,CAAC,CAAC,CAAC;AACR;AACA,SAASjJ,cAAcA,CAACsF,KAAK,EAAE2D,UAAU,EAAE;EACzC,IAAIC,SAAS,GAAGF,aAAa,CAAC1D,KAAK,EAAE2D,UAAU,CAAC;EAChD,OAAO;IACLQ,OAAO,EAAEP,SAAS,CAACO,OAAO;IAC1BN,KAAK,EAAED,SAAS,CAACC;EACnB,CAAC;AACH;AACA,SAASO,eAAeA,CAACL,MAAM,EAAEJ,UAAU,EAAE;EAC3C,IAAI,CAACI,MAAM,EAAE,OAAO,IAAI;EACxB,IAAIC,OAAO,GAAGb,MAAM,CAACa,OAAO,CAACD,MAAM,CAAC;EACpC,IAAIM,UAAU,GAAG,CAAC,CAAC;EACnB,KAAK,IAAI,CAACvF,GAAG,EAAEwF,GAAG,CAAC,IAAIN,OAAO,EAAE;IAC9B,IAAIrM,oBAAoB,CAAC2M,GAAG,CAAC,EAAE;MAC7BD,UAAU,CAACvF,GAAG,CAAC,GAAG;QAAE,GAAGwF,GAAG;QAAEC,MAAM,EAAE;MAAqB,CAAC;IAC5D,CAAC,MAAM,IAAID,GAAG,YAAYxH,KAAK,EAAE;MAC/B,IAAI8G,SAAS,GAAGF,aAAa,CAACY,GAAG,EAAEX,UAAU,CAAC;MAC9CU,UAAU,CAACvF,GAAG,CAAC,GAAG;QAChBqF,OAAO,EAAEP,SAAS,CAACO,OAAO;QAC1BN,KAAK,EAAED,SAAS,CAACC,KAAK;QACtBU,MAAM,EAAE,OAAO;QACf;QACA;QACA;QACA;QACA,IAAGX,SAAS,CAACzD,IAAI,KAAK,OAAO,GAAG;UAC9BqE,SAAS,EAAEZ,SAAS,CAACzD;QACvB,CAAC,GAAG,CAAC,CAAC;MACR,CAAC;IACH,CAAC,MAAM;MACLkE,UAAU,CAACvF,GAAG,CAAC,GAAGwF,GAAG;IACvB;EACF;EACA,OAAOD,UAAU;AACnB;;AAEA;AACA,SAASI,iBAAiBA,CAACpL,MAAM,EAAEqL,QAAQ,EAAEC,QAAQ,EAAE;EACrD,IAAIhL,OAAO,GAAG/B,WAAW,CACvByB,MAAM,EACNqL,QAAQ,EACRC,QACF,CAAC;EACD,IAAI,CAAChL,OAAO,EAAE,OAAO,IAAI;EACzB,OAAOA,OAAO,CAACkD,GAAG,CAAEnD,KAAK,KAAM;IAC7BkL,MAAM,EAAElL,KAAK,CAACkL,MAAM;IACpBF,QAAQ,EAAEhL,KAAK,CAACgL,QAAQ;IACxB7K,KAAK,EAAEH,KAAK,CAACG;EACf,CAAC,CAAC,CAAC;AACL;;AAEA;AACA,eAAegL,gBAAgBA,CAACC,OAAO,EAAEzH,IAAI,EAAE;EAC7C,IAAIoF,MAAM,GAAG,MAAMqC,OAAO,CAAC;IACzBC,OAAO,EAAEC,gBAAgB,CAACC,gBAAgB,CAAC5H,IAAI,CAAC0H,OAAO,CAAC,CAAC;IACzDH,MAAM,EAAEvH,IAAI,CAACuH,MAAM;IACnB/L,OAAO,EAAEwE,IAAI,CAACxE;EAChB,CAAC,CAAC;EACF,IAAIvB,sBAAsB,CAACmL,MAAM,CAAC,IAAIA,MAAM,CAACyC,IAAI,IAAIzC,MAAM,CAACyC,IAAI,CAACC,MAAM,IAAI1N,oBAAoB,CAACgL,MAAM,CAACyC,IAAI,CAACC,MAAM,CAAC,EAAE;IACnH,MAAM,IAAIC,QAAQ,CAAC,IAAI,EAAE3C,MAAM,CAACyC,IAAI,CAAC;EACvC;EACA,OAAOzC,MAAM;AACf;AACA,SAASwC,gBAAgBA,CAACF,OAAO,EAAE;EACjC,IAAIjM,GAAG,GAAG,IAAIE,GAAG,CAAC+L,OAAO,CAACjM,GAAG,CAAC;EAC9B,IAAIuM,WAAW,GAAGvM,GAAG,CAACwM,YAAY,CAACC,MAAM,CAAC,OAAO,CAAC;EAClDzM,GAAG,CAACwM,YAAY,CAACE,MAAM,CAAC,OAAO,CAAC;EAChC,IAAIC,iBAAiB,GAAG,EAAE;EAC1B,KAAK,IAAIC,UAAU,IAAIL,WAAW,EAAE;IAClC,IAAIK,UAAU,EAAE;MACdD,iBAAiB,CAACE,IAAI,CAACD,UAAU,CAAC;IACpC;EACF;EACA,KAAK,IAAIE,MAAM,IAAIH,iBAAiB,EAAE;IACpC3M,GAAG,CAACwM,YAAY,CAACO,MAAM,CAAC,OAAO,EAAED,MAAM,CAAC;EAC1C;EACA,IAAIV,IAAI,GAAG;IACTY,MAAM,EAAEf,OAAO,CAACe,MAAM;IACtBC,IAAI,EAAEhB,OAAO,CAACgB,IAAI;IAClBC,OAAO,EAAEjB,OAAO,CAACiB,OAAO;IACxBC,MAAM,EAAElB,OAAO,CAACkB;EAClB,CAAC;EACD,IAAIf,IAAI,CAACa,IAAI,EAAE;IACbb,IAAI,CAACgB,MAAM,GAAG,MAAM;EACtB;EACA,OAAO,IAAIC,OAAO,CAACrN,GAAG,CAACsN,IAAI,EAAElB,IAAI,CAAC;AACpC;AACA,SAASF,gBAAgBA,CAACD,OAAO,EAAE;EACjC,IAAIjM,GAAG,GAAG,IAAIE,GAAG,CAAC+L,OAAO,CAACjM,GAAG,CAAC;EAC9BA,GAAG,CAACwM,YAAY,CAACE,MAAM,CAAC,SAAS,CAAC;EAClC,IAAIN,IAAI,GAAG;IACTY,MAAM,EAAEf,OAAO,CAACe,MAAM;IACtBC,IAAI,EAAEhB,OAAO,CAACgB,IAAI;IAClBC,OAAO,EAAEjB,OAAO,CAACiB,OAAO;IACxBC,MAAM,EAAElB,OAAO,CAACkB;EAClB,CAAC;EACD,IAAIf,IAAI,CAACa,IAAI,EAAE;IACbb,IAAI,CAACgB,MAAM,GAAG,MAAM;EACtB;EACA,OAAO,IAAIC,OAAO,CAACrN,GAAG,CAACsN,IAAI,EAAElB,IAAI,CAAC;AACpC;;AAEA;AACA,SAASmB,UAAUA,CAAC9L,KAAK,EAAE4J,OAAO,EAAE;EAClC,IAAI5J,KAAK,KAAK,KAAK,IAAIA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;IACrE+L,OAAO,CAACtG,KAAK,CACX,iIACF,CAAC;IACD,MAAM,IAAIlD,KAAK,CAACqH,OAAO,CAAC;EAC1B;AACF;;AAEA;AACA,IAAIoC,uBAAuB,GAAG,6BAA6B;AAC3D,SAASC,iBAAiBA,CAACC,cAAc,EAAE;EACzCC,UAAU,CAACH,uBAAuB,CAAC,GAAGE,cAAc;AACtD;AACA,SAASE,iBAAiBA,CAAA,EAAG;EAC3B,OAAOD,UAAU,CAACH,uBAAuB,CAAC;AAC5C;AACA,SAASK,kBAAkBA,CAAC7B,OAAO,EAAE8B,UAAU,EAAE;EAC/C,IAAI,OAAOC,OAAO,KAAK,WAAW,EAAE;IAClC,IAAI;MACF,IAAIA,OAAO,CAACC,GAAG,EAAEC,mBAAmB,KAAK,KAAK,EAAE;QAC9C,OAAOjC,OAAO,CAACiB,OAAO,CAACiB,GAAG,CAACJ,UAAU,CAAC;MACxC;IACF,CAAC,CAAC,OAAOK,CAAC,EAAE,CACZ;EACF;EACA,OAAO,IAAI;AACb;;AAEA;AACA,SAASC,qBAAqBA,CAAClO,QAAQ,EAAE;EACvC,IAAII,MAAM,GAAG,CAAC,CAAC;EACf8J,MAAM,CAACiE,MAAM,CAACnO,QAAQ,CAAC,CAACoO,OAAO,CAAExN,KAAK,IAAK;IACzC,IAAIA,KAAK,EAAE;MACT,IAAI+C,QAAQ,GAAG/C,KAAK,CAAC+C,QAAQ,IAAI,EAAE;MACnC,IAAI,CAACvD,MAAM,CAACuD,QAAQ,CAAC,EAAE;QACrBvD,MAAM,CAACuD,QAAQ,CAAC,GAAG,EAAE;MACvB;MACAvD,MAAM,CAACuD,QAAQ,CAAC,CAAC+I,IAAI,CAAC9L,KAAK,CAAC;IAC9B;EACF,CAAC,CAAC;EACF,OAAOR,MAAM;AACf;AACA,SAASiO,YAAYA,CAACrO,QAAQ,EAAE2D,QAAQ,GAAG,EAAE,EAAE2K,gBAAgB,GAAGJ,qBAAqB,CAAClO,QAAQ,CAAC,EAAE;EACjG,OAAO,CAACsO,gBAAgB,CAAC3K,QAAQ,CAAC,IAAI,EAAE,EAAEC,GAAG,CAAEhD,KAAK,KAAM;IACxD,GAAGA,KAAK;IACRyE,QAAQ,EAAEgJ,YAAY,CAACrO,QAAQ,EAAEY,KAAK,CAACC,EAAE,EAAEyN,gBAAgB;EAC7D,CAAC,CAAC,CAAC;AACL;AACA,SAASC,6BAA6BA,CAACvO,QAAQ,EAAEK,MAAM,EAAEsD,QAAQ,GAAG,EAAE,EAAE2K,gBAAgB,GAAGJ,qBAAqB,CAAClO,QAAQ,CAAC,EAAE;EAC1H,OAAO,CAACsO,gBAAgB,CAAC3K,QAAQ,CAAC,IAAI,EAAE,EAAEC,GAAG,CAAEhD,KAAK,IAAK;IACvD,IAAI4N,WAAW,GAAG;MAChB;MACA3J,gBAAgB,EAAEjE,KAAK,CAACC,EAAE,KAAK,MAAM,IAAID,KAAK,CAACwC,MAAM,CAACc,aAAa,IAAI,IAAI;MAC3ErD,EAAE,EAAED,KAAK,CAACC,EAAE;MACZkD,IAAI,EAAEnD,KAAK,CAACmD,IAAI;MAChB0K,UAAU,EAAE7N,KAAK,CAACwC,MAAM,CAACqL,UAAU;MACnC;MACA;MACApK,MAAM,EAAEzD,KAAK,CAACwC,MAAM,CAACiB,MAAM,GAAG,MAAOD,IAAI,IAAK;QAC5C,IAAIsK,eAAe,GAAGf,kBAAkB,CACtCvJ,IAAI,CAAC0H,OAAO,EACZ,+BACF,CAAC;QACD,IAAI4C,eAAe,IAAI,IAAI,EAAE;UAC3B,IAAI9F,OAAO,GAAG8F,eAAe,GAAGC,SAAS,CAACD,eAAe,CAAC,GAAGA,eAAe;UAC5EtB,UAAU,CAACxE,OAAO,EAAE,oCAAoC,CAAC;UACzD,IAAIgG,UAAU,GAAG,IAAInJ,WAAW,CAAC,CAAC,CAAC1H,MAAM,CAAC6K,OAAO,CAAC;UAClD,IAAIiG,MAAM,GAAG,IAAIC,cAAc,CAAC;YAC9BC,KAAKA,CAACC,UAAU,EAAE;cAChBA,UAAU,CAACC,OAAO,CAACL,UAAU,CAAC;cAC9BI,UAAU,CAACE,KAAK,CAAC,CAAC;YACpB;UACF,CAAC,CAAC;UACF,IAAI5G,OAAO,GAAG,MAAMxK,oBAAoB,CAAC+Q,MAAM,EAAEM,MAAM,CAAC;UACxD,IAAIvJ,KAAK,GAAG0C,OAAO,CAAChH,KAAK;UACzB,IAAIsE,KAAK,IAAI1I,yBAAyB,IAAI0I,KAAK,EAAE;YAC/C,IAAI4D,MAAM,GAAG5D,KAAK,CAAC1I,yBAAyB,CAAC;YAC7C,IAAI+O,IAAI,GAAG;cAAEC,MAAM,EAAE1C,MAAM,CAAC0C;YAAO,CAAC;YACpC,IAAI1C,MAAM,CAAC4F,MAAM,EAAE;cACjB,MAAMtQ,gBAAgB,CAAC0K,MAAM,CAAC3K,QAAQ,EAAEoN,IAAI,CAAC;YAC/C,CAAC,MAAM,IAAIzC,MAAM,CAACzK,OAAO,EAAE;cACzB,MAAMA,OAAO,CAACyK,MAAM,CAAC3K,QAAQ,EAAEoN,IAAI,CAAC;YACtC,CAAC,MAAM;cACL,MAAMpN,QAAQ,CAAC2K,MAAM,CAAC3K,QAAQ,EAAEoN,IAAI,CAAC;YACvC;UACF,CAAC,MAAM;YACLmB,UAAU,CACRxH,KAAK,IAAIhF,KAAK,CAACC,EAAE,IAAI+E,KAAK,EAC1B,mCACF,CAAC;YACD,IAAI4D,MAAM,GAAG5D,KAAK,CAAChF,KAAK,CAACC,EAAE,CAAC;YAC5BuM,UAAU,CACR,MAAM,IAAI5D,MAAM,EAChB,oCACF,CAAC;YACD,OAAOA,MAAM,CAAC6F,IAAI;UACpB;QACF;QACA,IAAIhE,GAAG,GAAG,MAAMO,gBAAgB,CAAChL,KAAK,CAACwC,MAAM,CAACiB,MAAM,EAAED,IAAI,CAAC;QAC3D,OAAOiH,GAAG;MACZ,CAAC,GAAG,KAAK,CAAC;MACVlH,MAAM,EAAEvD,KAAK,CAACwC,MAAM,CAACe,MAAM,GAAIC,IAAI,IAAKwH,gBAAgB,CAAChL,KAAK,CAACwC,MAAM,CAACe,MAAM,EAAEC,IAAI,CAAC,GAAG,KAAK,CAAC;MAC5FE,MAAM,EAAE1D,KAAK,CAACwC,MAAM,CAACkB;IACvB,CAAC;IACD,OAAO1D,KAAK,CAACoD,KAAK,GAAG;MACnBA,KAAK,EAAE,IAAI;MACX,GAAGwK;IACL,CAAC,GAAG;MACFc,aAAa,EAAE1O,KAAK,CAAC0O,aAAa;MAClCjK,QAAQ,EAAEkJ,6BAA6B,CACrCvO,QAAQ,EACRK,MAAM,EACNO,KAAK,CAACC,EAAE,EACRyN,gBACF,CAAC;MACD,GAAGE;IACL,CAAC;EACH,CAAC,CAAC;AACJ;;AAEA;AACA,SAASe,yBAAyBA,CAACC,aAAa,EAAE;EAChD,OAAOxR,UAAU,CAACkL,IAAI,CAACC,SAAS,CAACqG,aAAa,CAAC,CAAC;AAClD;;AAEA;AACA,SAASC,kBAAkB,QAAQ,mBAAmB;AACtD,SAASC,kBAAkBA,CAAC9P,OAAO,EAAE+P,KAAK,EAAE;EAC1C,OAAOC,sBAAsB,CAAChQ,OAAO,EAAGiQ,CAAC,IAAK;IAC5C,IAAIjP,KAAK,GAAG+O,KAAK,CAACvP,MAAM,CAACyP,CAAC,CAACjP,KAAK,CAACC,EAAE,CAAC;IACpCuM,UAAU,CAACxM,KAAK,EAAE,kBAAkBiP,CAAC,CAACjP,KAAK,CAACC,EAAE,sBAAsB,CAAC;IACrE,OAAOD,KAAK,CAACwC,MAAM,CAAC2J,OAAO;EAC7B,CAAC,CAAC;AACJ;AACA,SAAS6C,sBAAsBA,CAAChQ,OAAO,EAAEkQ,iBAAiB,EAAEC,eAAe,EAAE;EAC3E,IAAIC,WAAW,GAAGpQ,OAAO,CAACkL,MAAM,GAAGlL,OAAO,CAACc,OAAO,CAACuP,SAAS,CAAEJ,CAAC,IAAKjQ,OAAO,CAACkL,MAAM,CAAC+E,CAAC,CAACjP,KAAK,CAACC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;EACpG,IAAIH,OAAO,GAAGsP,WAAW,IAAI,CAAC,GAAGpQ,OAAO,CAACc,OAAO,CAACgG,KAAK,CAAC,CAAC,EAAEsJ,WAAW,GAAG,CAAC,CAAC,GAAGpQ,OAAO,CAACc,OAAO;EAC5F,IAAIwP,YAAY;EAChB,IAAIF,WAAW,IAAI,CAAC,EAAE;IACpB,IAAI;MAAEG,aAAa;MAAEC,UAAU;MAAEC,aAAa;MAAE7P;IAAW,CAAC,GAAGZ,OAAO;IACtEA,OAAO,CAACc,OAAO,CAACgG,KAAK,CAACsJ,WAAW,CAAC,CAACM,IAAI,CAAE7P,KAAK,IAAK;MACjD,IAAII,EAAE,GAAGJ,KAAK,CAACG,KAAK,CAACC,EAAE;MACvB,IAAIsP,aAAa,CAACtP,EAAE,CAAC,KAAK,CAACuP,UAAU,IAAI,CAACA,UAAU,CAACG,cAAc,CAAC1P,EAAE,CAAC,CAAC,EAAE;QACxEqP,YAAY,GAAGC,aAAa,CAACtP,EAAE,CAAC;MAClC,CAAC,MAAM,IAAIwP,aAAa,CAACxP,EAAE,CAAC,IAAI,CAACL,UAAU,CAAC+P,cAAc,CAAC1P,EAAE,CAAC,EAAE;QAC9DqP,YAAY,GAAGG,aAAa,CAACxP,EAAE,CAAC;MAClC;MACA,OAAOqP,YAAY,IAAI,IAAI;IAC7B,CAAC,CAAC;EACJ;EACA,MAAMM,cAAc,GAAG,IAAIC,OAAO,CAACV,eAAe,CAAC;EACnD,OAAOrP,OAAO,CAAC0J,MAAM,CAAC,CAACsG,aAAa,EAAEjQ,KAAK,EAAEkQ,GAAG,KAAK;IACnD,IAAI;MAAE9P;IAAG,CAAC,GAAGJ,KAAK,CAACG,KAAK;IACxB,IAAIyP,aAAa,GAAGzQ,OAAO,CAACyQ,aAAa,CAACxP,EAAE,CAAC,IAAI,IAAI4P,OAAO,CAAC,CAAC;IAC9D,IAAIN,aAAa,GAAGvQ,OAAO,CAACuQ,aAAa,CAACtP,EAAE,CAAC,IAAI,IAAI4P,OAAO,CAAC,CAAC;IAC9D,IAAIG,mBAAmB,GAAGV,YAAY,IAAI,IAAI,IAAIS,GAAG,KAAKjQ,OAAO,CAAC2G,MAAM,GAAG,CAAC;IAC5E,IAAIwJ,mBAAmB,GAAGD,mBAAmB,IAAIV,YAAY,KAAKG,aAAa,IAAIH,YAAY,KAAKC,aAAa;IACjH,IAAIW,SAAS,GAAGhB,iBAAiB,CAACrP,KAAK,CAAC;IACxC,IAAIqQ,SAAS,IAAI,IAAI,EAAE;MACrB,IAAIC,QAAQ,GAAG,IAAIN,OAAO,CAACC,aAAa,CAAC;MACzC,IAAIG,mBAAmB,EAAE;QACvBG,cAAc,CAACd,YAAY,EAAEa,QAAQ,CAAC;MACxC;MACAC,cAAc,CAACb,aAAa,EAAEY,QAAQ,CAAC;MACvCC,cAAc,CAACX,aAAa,EAAEU,QAAQ,CAAC;MACvC,OAAOA,QAAQ;IACjB;IACA,IAAIhE,OAAO,GAAG,IAAI0D,OAAO,CACvB,OAAOK,SAAS,KAAK,UAAU,GAAGA,SAAS,CAAC;MAC1CT,aAAa;MACbK,aAAa;MACbP,aAAa;MACbD,YAAY,EAAEU,mBAAmB,GAAGV,YAAY,GAAG,KAAK;IAC1D,CAAC,CAAC,GAAGY,SACP,CAAC;IACD,IAAID,mBAAmB,EAAE;MACvBG,cAAc,CAACd,YAAY,EAAEnD,OAAO,CAAC;IACvC;IACAiE,cAAc,CAACb,aAAa,EAAEpD,OAAO,CAAC;IACtCiE,cAAc,CAACX,aAAa,EAAEtD,OAAO,CAAC;IACtCiE,cAAc,CAACN,aAAa,EAAE3D,OAAO,CAAC;IACtC,OAAOA,OAAO;EAChB,CAAC,EAAE,IAAI0D,OAAO,CAACD,cAAc,CAAC,CAAC;AACjC;AACA,SAASQ,cAAcA,CAACN,aAAa,EAAEO,YAAY,EAAE;EACnD,IAAIC,qBAAqB,GAAGR,aAAa,CAAC1C,GAAG,CAAC,YAAY,CAAC;EAC3D,IAAIkD,qBAAqB,EAAE;IACzB,IAAI7I,OAAO,GAAGoH,kBAAkB,CAACyB,qBAAqB,CAAC;IACvD,IAAIC,YAAY,GAAG,IAAIC,GAAG,CAACH,YAAY,CAACI,YAAY,CAAC,CAAC,CAAC;IACvDhJ,OAAO,CAAC+F,OAAO,CAAE5H,MAAM,IAAK;MAC1B,IAAI,CAAC2K,YAAY,CAACG,GAAG,CAAC9K,MAAM,CAAC,EAAE;QAC7ByK,YAAY,CAACrE,MAAM,CAAC,YAAY,EAAEpG,MAAM,CAAC;MAC3C;IACF,CAAC,CAAC;EACJ;AACF;;AAEA;AACA,IAAI+K,2BAA2B,GAAG,eAAgB,IAAIH,GAAG,CAAC,CACxD,GAAGzU,oBAAoB,EACvB,GAAG,CACJ,CAAC;AACF,eAAe6U,iBAAiBA,CAAC7B,KAAK,EAAEjF,UAAU,EAAE+G,aAAa,EAAE3F,OAAO,EAAE4F,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAE;EAChH,IAAI;IACF,IAAIC,cAAc,GAAG,IAAI3E,OAAO,CAACwE,UAAU,EAAE;MAC3C7E,MAAM,EAAEf,OAAO,CAACe,MAAM;MACtBC,IAAI,EAAEhB,OAAO,CAACgB,IAAI;MAClBC,OAAO,EAAEjB,OAAO,CAACiB,OAAO;MACxBC,MAAM,EAAElB,OAAO,CAACkB,MAAM;MACtB,IAAGlB,OAAO,CAACgB,IAAI,GAAG;QAAEG,MAAM,EAAE;MAAO,CAAC,GAAG,KAAK,CAAC;IAC/C,CAAC,CAAC;IACF,IAAIzD,MAAM,GAAG,MAAMiI,aAAa,CAACK,KAAK,CAACD,cAAc,EAAE;MACrDE,cAAc,EAAEJ,WAAW;MAC3BK,uBAAuB,EAAE,IAAI;MAC7BC,gBAAgB,EAAE,IAAI;MACtBC,0BAA0B,EAAEvC,KAAK,CAACtP,MAAM,CAAC4C,aAAa,GAAG,MAAO6O,KAAK,IAAK;QACxE,IAAI;UACF,IAAIK,WAAW,GAAG,MAAML,KAAK,CAACD,cAAc,CAAC;UAC7C,OAAOO,iBAAiB,CAACD,WAAW,CAAC;QACvC,CAAC,CAAC,OAAOpL,KAAK,EAAE;UACd,OAAOsL,gBAAgB,CAACtL,KAAK,CAAC;QAChC;MACF,CAAC,GAAG,KAAK;IACX,CAAC,CAAC;IACF,OAAOqL,iBAAiB,CAAC5I,MAAM,CAAC;EAClC,CAAC,CAAC,OAAOzC,KAAK,EAAE;IACd,OAAOsL,gBAAgB,CAACtL,KAAK,CAAC;EAChC;EACA,SAASqL,iBAAiBA,CAAC5I,MAAM,EAAE;IACjC,OAAO/K,UAAU,CAAC+K,MAAM,CAAC,GAAGA,MAAM,GAAG8I,uBAAuB,CAAC9I,MAAM,CAAC;EACtE;EACA,SAAS6I,gBAAgBA,CAACtL,KAAK,EAAE;IAC/B6K,WAAW,CAAC7K,KAAK,CAAC;IAClB,OAAOwL,2BAA2B,CAACzG,OAAO,EAAE6D,KAAK,EAAEjF,UAAU,EAAE;MAC7DlB,MAAM,EAAE;QAAEzC;MAAM,CAAC;MACjBgG,OAAO,EAAE,IAAI0D,OAAO,CAAC,CAAC;MACtBvE,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;EACA,SAASoG,uBAAuBA,CAAC1S,OAAO,EAAE;IACxC,IAAImN,OAAO,GAAG2C,kBAAkB,CAAC9P,OAAO,EAAE+P,KAAK,CAAC;IAChD,IAAInR,oBAAoB,CAACoB,OAAO,CAAC4S,UAAU,CAAC,IAAIzF,OAAO,CAACuE,GAAG,CAAC,UAAU,CAAC,EAAE;MACvE,OAAO,IAAInF,QAAQ,CAAC,IAAI,EAAE;QAAED,MAAM,EAAEtM,OAAO,CAAC4S,UAAU;QAAEzF;MAAQ,CAAC,CAAC;IACpE;IACA,IAAInN,OAAO,CAACkL,MAAM,EAAE;MAClBZ,MAAM,CAACiE,MAAM,CAACvO,OAAO,CAACkL,MAAM,CAAC,CAACsD,OAAO,CAAEqE,GAAG,IAAK;QAC7C,IAAI,CAAC/T,oBAAoB,CAAC+T,GAAG,CAAC,IAAIA,GAAG,CAAC1L,KAAK,EAAE;UAC3C6K,WAAW,CAACa,GAAG,CAAC;QAClB;MACF,CAAC,CAAC;MACF7S,OAAO,CAACkL,MAAM,GAAGD,cAAc,CAACjL,OAAO,CAACkL,MAAM,EAAEJ,UAAU,CAAC;IAC7D;IACA,IAAIgI,iBAAiB;IACrB,IAAI9S,OAAO,CAACkL,MAAM,EAAE;MAClB4H,iBAAiB,GAAG;QAAE3L,KAAK,EAAEmD,MAAM,CAACiE,MAAM,CAACvO,OAAO,CAACkL,MAAM,CAAC,CAAC,CAAC;MAAE,CAAC;IACjE,CAAC,MAAM;MACL4H,iBAAiB,GAAG;QAClBrD,IAAI,EAAEnF,MAAM,CAACiE,MAAM,CAACvO,OAAO,CAACwQ,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MACjD,CAAC;IACH;IACA,OAAOmC,2BAA2B,CAACzG,OAAO,EAAE6D,KAAK,EAAEjF,UAAU,EAAE;MAC7DlB,MAAM,EAAEkJ,iBAAiB;MACzB3F,OAAO;MACPb,MAAM,EAAEtM,OAAO,CAAC4S;IAClB,CAAC,CAAC;EACJ;AACF;AACA,eAAeG,kBAAkBA,CAAChD,KAAK,EAAEjF,UAAU,EAAE+G,aAAa,EAAE3F,OAAO,EAAE4F,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAE;EACjH,IAAIgB,WAAW,GAAG,IAAI7S,GAAG,CAAC+L,OAAO,CAACjM,GAAG,CAAC,CAACwM,YAAY,CAAC2B,GAAG,CAAC,SAAS,CAAC;EAClE,IAAI6E,YAAY,GAAGD,WAAW,GAAG,IAAIxB,GAAG,CAACwB,WAAW,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;EACvE,IAAI;IACF,IAAIjB,cAAc,GAAG,IAAI3E,OAAO,CAACwE,UAAU,EAAE;MAC3C3E,OAAO,EAAEjB,OAAO,CAACiB,OAAO;MACxBC,MAAM,EAAElB,OAAO,CAACkB;IAClB,CAAC,CAAC;IACF,IAAIxD,MAAM,GAAG,MAAMiI,aAAa,CAACK,KAAK,CAACD,cAAc,EAAE;MACrDE,cAAc,EAAEJ,WAAW;MAC3BoB,mBAAmB,EAAGlD,CAAC,IAAK,CAACgD,YAAY,IAAIA,YAAY,CAACvB,GAAG,CAACzB,CAAC,CAACjP,KAAK,CAACC,EAAE,CAAC;MACzEmR,uBAAuB,EAAE,IAAI;MAC7BE,0BAA0B,EAAEvC,KAAK,CAACtP,MAAM,CAAC4C,aAAa,GAAG,MAAO6O,KAAK,IAAK;QACxE,IAAI;UACF,IAAIK,WAAW,GAAG,MAAML,KAAK,CAACD,cAAc,CAAC;UAC7C,OAAOO,iBAAiB,CAACD,WAAW,CAAC;QACvC,CAAC,CAAC,OAAOpL,KAAK,EAAE;UACd,OAAOsL,gBAAgB,CAACtL,KAAK,CAAC;QAChC;MACF,CAAC,GAAG,KAAK;IACX,CAAC,CAAC;IACF,OAAOqL,iBAAiB,CAAC5I,MAAM,CAAC;EAClC,CAAC,CAAC,OAAOzC,KAAK,EAAE;IACd,OAAOsL,gBAAgB,CAACtL,KAAK,CAAC;EAChC;EACA,SAASqL,iBAAiBA,CAAC5I,MAAM,EAAE;IACjC,OAAO/K,UAAU,CAAC+K,MAAM,CAAC,GAAGA,MAAM,GAAG8I,uBAAuB,CAAC9I,MAAM,CAAC;EACtE;EACA,SAAS6I,gBAAgBA,CAACtL,KAAK,EAAE;IAC/B6K,WAAW,CAAC7K,KAAK,CAAC;IAClB,OAAOwL,2BAA2B,CAACzG,OAAO,EAAE6D,KAAK,EAAEjF,UAAU,EAAE;MAC7DlB,MAAM,EAAE;QAAEzC;MAAM,CAAC;MACjBgG,OAAO,EAAE,IAAI0D,OAAO,CAAC,CAAC;MACtBvE,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;EACA,SAASoG,uBAAuBA,CAAC1S,OAAO,EAAE;IACxC,IAAImN,OAAO,GAAG2C,kBAAkB,CAAC9P,OAAO,EAAE+P,KAAK,CAAC;IAChD,IAAInR,oBAAoB,CAACoB,OAAO,CAAC4S,UAAU,CAAC,IAAIzF,OAAO,CAACuE,GAAG,CAAC,UAAU,CAAC,EAAE;MACvE,OAAO,IAAInF,QAAQ,CAAC,IAAI,EAAE;QAAED,MAAM,EAAEtM,OAAO,CAAC4S,UAAU;QAAEzF;MAAQ,CAAC,CAAC;IACpE;IACA,IAAInN,OAAO,CAACkL,MAAM,EAAE;MAClBZ,MAAM,CAACiE,MAAM,CAACvO,OAAO,CAACkL,MAAM,CAAC,CAACsD,OAAO,CAAEqE,GAAG,IAAK;QAC7C,IAAI,CAAC/T,oBAAoB,CAAC+T,GAAG,CAAC,IAAIA,GAAG,CAAC1L,KAAK,EAAE;UAC3C6K,WAAW,CAACa,GAAG,CAAC;QAClB;MACF,CAAC,CAAC;MACF7S,OAAO,CAACkL,MAAM,GAAGD,cAAc,CAACjL,OAAO,CAACkL,MAAM,EAAEJ,UAAU,CAAC;IAC7D;IACA,IAAIsI,OAAO,GAAG,CAAC,CAAC;IAChB,IAAIC,aAAa,GAAG,IAAI7B,GAAG,CACzBxR,OAAO,CAACc,OAAO,CAACwS,MAAM,CACnBrD,CAAC,IAAKgD,YAAY,GAAGA,YAAY,CAACvB,GAAG,CAACzB,CAAC,CAACjP,KAAK,CAACC,EAAE,CAAC,GAAGgP,CAAC,CAACjP,KAAK,CAACyD,MAAM,IAAI,IACzE,CAAC,CAACT,GAAG,CAAEiM,CAAC,IAAKA,CAAC,CAACjP,KAAK,CAACC,EAAE,CACzB,CAAC;IACD,IAAIjB,OAAO,CAACkL,MAAM,EAAE;MAClB,KAAK,IAAI,CAACjK,EAAE,EAAEkG,KAAK,CAAC,IAAImD,MAAM,CAACa,OAAO,CAACnL,OAAO,CAACkL,MAAM,CAAC,EAAE;QACtDkI,OAAO,CAACnS,EAAE,CAAC,GAAG;UAAEkG;QAAM,CAAC;MACzB;IACF;IACA,KAAK,IAAI,CAAClG,EAAE,EAAE+E,KAAK,CAAC,IAAIsE,MAAM,CAACa,OAAO,CAACnL,OAAO,CAACY,UAAU,CAAC,EAAE;MAC1D,IAAI,EAAEK,EAAE,IAAImS,OAAO,CAAC,IAAIC,aAAa,CAAC3B,GAAG,CAACzQ,EAAE,CAAC,EAAE;QAC7CmS,OAAO,CAACnS,EAAE,CAAC,GAAG;UAAEwO,IAAI,EAAEzJ;QAAM,CAAC;MAC/B;IACF;IACA,OAAO2M,2BAA2B,CAACzG,OAAO,EAAE6D,KAAK,EAAEjF,UAAU,EAAE;MAC7DlB,MAAM,EAAEwJ,OAAO;MACfjG,OAAO;MACPb,MAAM,EAAEtM,OAAO,CAAC4S;IAClB,CAAC,CAAC;EACJ;AACF;AACA,SAASD,2BAA2BA,CAACzG,OAAO,EAAE6D,KAAK,EAAEjF,UAAU,EAAE;EAC/DlB,MAAM;EACNuD,OAAO;EACPb;AACF,CAAC,EAAE;EACD,IAAIiH,aAAa,GAAG,IAAI1C,OAAO,CAAC1D,OAAO,CAAC;EACxCoG,aAAa,CAACC,GAAG,CAAC,kBAAkB,EAAE,KAAK,CAAC;EAC5C,IAAI7B,2BAA2B,CAACD,GAAG,CAACpF,MAAM,CAAC,EAAE;IAC3C,OAAO,IAAIC,QAAQ,CAAC,IAAI,EAAE;MAAED,MAAM;MAAEa,OAAO,EAAEoG;IAAc,CAAC,CAAC;EAC/D;EACAA,aAAa,CAACC,GAAG,CAAC,cAAc,EAAE,eAAe,CAAC;EAClDD,aAAa,CAAC5G,MAAM,CAAC,gBAAgB,CAAC;EACtC,OAAO,IAAIJ,QAAQ,CACjBkH,oBAAoB,CAClB7J,MAAM,EACNsC,OAAO,CAACkB,MAAM,EACd2C,KAAK,CAACzM,KAAK,CAACE,MAAM,CAACkQ,aAAa,EAChC5I,UACF,CAAC,EACD;IACEwB,MAAM,EAAEA,MAAM,IAAI,GAAG;IACrBa,OAAO,EAAEoG;EACX,CACF,CAAC;AACH;AACA,SAASI,mCAAmCA,CAACC,gBAAgB,EAAE1H,OAAO,EAAE6D,KAAK,EAAEjF,UAAU,EAAE;EACzF,IAAI+I,SAAS,GAAGC,sBAAsB,CACpCF,gBAAgB,CAACtH,MAAM,EACvBsH,gBAAgB,CAACzG,OAAO,EACxB4C,KAAK,CAACjE,QACR,CAAC;EACD,IAAIqB,OAAO,GAAG,IAAI0D,OAAO,CAAC+C,gBAAgB,CAACzG,OAAO,CAAC;EACnDA,OAAO,CAACR,MAAM,CAAC,UAAU,CAAC;EAC1BQ,OAAO,CAACqG,GAAG,CAAC,cAAc,EAAE,eAAe,CAAC;EAC5C,OAAOb,2BAA2B,CAACzG,OAAO,EAAE6D,KAAK,EAAEjF,UAAU,EAAE;IAC7DlB,MAAM,EAAEsC,OAAO,CAACe,MAAM,KAAK,KAAK,GAAG;MAAE,CAAC3P,yBAAyB,GAAGuW;IAAU,CAAC,GAAGA,SAAS;IACzF1G,OAAO;IACPb,MAAM,EAAEjP;EACV,CAAC,CAAC;AACJ;AACA,SAASyW,sBAAsBA,CAACxH,MAAM,EAAEa,OAAO,EAAErB,QAAQ,EAAE;EACzD,IAAI+H,SAAS,GAAG1G,OAAO,CAACiB,GAAG,CAAC,UAAU,CAAC;EACvC,IAAItC,QAAQ,EAAE;IACZ+H,SAAS,GAAGtU,aAAa,CAACsU,SAAS,EAAE/H,QAAQ,CAAC,IAAI+H,SAAS;EAC7D;EACA,OAAO;IACL5U,QAAQ,EAAE4U,SAAS;IACnBvH,MAAM;IACNyH,UAAU;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA5G,OAAO,CAACuE,GAAG,CAAC,oBAAoB,CAAC,IAAIvE,OAAO,CAACuE,GAAG,CAAC,YAAY,CAC9D;IACDlC,MAAM,EAAErC,OAAO,CAACuE,GAAG,CAAC,yBAAyB,CAAC;IAC9CvS,OAAO,EAAEgO,OAAO,CAACuE,GAAG,CAAC,iBAAiB;EACxC,CAAC;AACH;AACA,SAAS+B,oBAAoBA,CAACzN,KAAK,EAAEgO,aAAa,EAAEN,aAAa,EAAE5I,UAAU,EAAE;EAC7E,IAAIsE,UAAU,GAAG,IAAI6E,eAAe,CAAC,CAAC;EACtC,IAAIC,SAAS,GAAGC,UAAU,CACxB,MAAM/E,UAAU,CAACgF,KAAK,CAAC,IAAInQ,KAAK,CAAC,gBAAgB,CAAC,CAAC,EACnD,OAAOyP,aAAa,KAAK,QAAQ,GAAGA,aAAa,GAAG,IACtD,CAAC;EACDM,aAAa,CAACK,gBAAgB,CAAC,OAAO,EAAE,MAAMC,YAAY,CAACJ,SAAS,CAAC,CAAC;EACtE,OAAO/V,MAAM,CAAC6H,KAAK,EAAE;IACnBoH,MAAM,EAAEgC,UAAU,CAAChC,MAAM;IACzBmH,OAAO,EAAE,CACN7S,KAAK,IAAK;MACT,IAAIA,KAAK,YAAYuC,KAAK,EAAE;QAC1B,IAAI;UAAEqD,IAAI;UAAEgE,OAAO;UAAEN;QAAM,CAAC,GAAGF,UAAU,KAAK,YAAY,CAAC,mBAAmBD,aAAa,CAACnJ,KAAK,EAAEoJ,UAAU,CAAC,GAAGpJ,KAAK;QACtH,OAAO,CAAC,gBAAgB,EAAE4F,IAAI,EAAEgE,OAAO,EAAEN,KAAK,CAAC;MACjD;MACA,IAAItJ,KAAK,YAAY7E,iBAAiB,EAAE;QACtC,IAAI;UAAE4S,IAAI,EAAE+E,KAAK;UAAElI,MAAM;UAAEmI;QAAW,CAAC,GAAG/S,KAAK;QAC/C,OAAO,CAAC,eAAe,EAAE8S,KAAK,EAAElI,MAAM,EAAEmI,UAAU,CAAC;MACrD;MACA,IAAI/S,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIpE,yBAAyB,IAAIoE,KAAK,EAAE;QAC5E,OAAO,CAAC,qBAAqB,EAAEA,KAAK,CAACpE,yBAAyB,CAAC,CAAC;MAClE;IACF,CAAC,CACF;IACDoX,WAAW,EAAE,CACVhT,KAAK,IAAK;MACT,IAAI,CAACA,KAAK,EAAE;MACZ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC/B,OAAO,CACL,0BAA0B,EAC1B4I,MAAM,CAACqK,WAAW,CAACrK,MAAM,CAACa,OAAO,CAACzJ,KAAK,CAAC,CAAC,CAC1C;IACH,CAAC,EACD,MAAM,CAAC,qBAAqB,CAAC;EAEjC,CAAC,CAAC;AACJ;;AAEA;AACA,SAASkT,MAAMA,CAAC7E,KAAK,EAAErM,IAAI,EAAE;EAC3B,IAAIlD,MAAM,GAAGiO,YAAY,CAACsB,KAAK,CAACvP,MAAM,CAAC;EACvC,IAAIqU,UAAU,GAAGlG,6BAA6B,CAACoB,KAAK,CAACvP,MAAM,EAAEuP,KAAK,CAACtP,MAAM,CAAC;EAC1E,IAAIqK,UAAU,GAAGF,YAAY,CAAClH,IAAI,CAAC,GAAGA,IAAI,GAAG,YAAY,CAAC;EAC1D,IAAImO,aAAa,GAAG7T,mBAAmB,CAAC6W,UAAU,EAAE;IAClD/I,QAAQ,EAAEiE,KAAK,CAACjE;EAClB,CAAC,CAAC;EACF,IAAIgJ,YAAY,GAAG/E,KAAK,CAACzM,KAAK,CAACE,MAAM,CAACwO,WAAW,KAAK,CAAC7K,KAAK,EAAE;IAAE+E;EAAQ,CAAC,KAAK;IAC5E,IAAIpB,UAAU,KAAK,MAAM,CAAC,cAAc,CAACoB,OAAO,CAACkB,MAAM,CAAC2H,OAAO,EAAE;MAC/DtH,OAAO,CAACtG,KAAK;MACX;MACArI,oBAAoB,CAACqI,KAAK,CAAC,IAAIA,KAAK,CAACA,KAAK,GAAGA,KAAK,CAACA,KAAK,GAAGA,KAC7D,CAAC;IACH;EACF,CAAC,CAAC;EACF,OAAO;IACL3G,MAAM;IACNqU,UAAU;IACV/J,UAAU;IACV+G,aAAa;IACbiD;EACF,CAAC;AACH;AACA,IAAIE,oBAAoB,GAAGA,CAACjF,KAAK,EAAErM,IAAI,KAAK;EAC1C,IAAIuR,MAAM;EACV,IAAIzU,MAAM;EACV,IAAIsK,UAAU;EACd,IAAI+G,aAAa;EACjB,IAAIiD,YAAY;EAChB,OAAO,eAAeI,cAAcA,CAAChJ,OAAO,EAAEiJ,cAAc,EAAE;IAC5DF,MAAM,GAAG,OAAOlF,KAAK,KAAK,UAAU,GAAG,MAAMA,KAAK,CAAC,CAAC,GAAGA,KAAK;IAC5D,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;MAC/B,IAAIqF,OAAO,GAAGR,MAAM,CAACK,MAAM,EAAEvR,IAAI,CAAC;MAClClD,MAAM,GAAG4U,OAAO,CAAC5U,MAAM;MACvBsK,UAAU,GAAGsK,OAAO,CAACtK,UAAU;MAC/B+G,aAAa,GAAGuD,OAAO,CAACvD,aAAa;MACrCiD,YAAY,GAAGM,OAAO,CAACN,YAAY;IACrC,CAAC,MAAM,IAAI,CAACtU,MAAM,IAAI,CAACsK,UAAU,IAAI,CAAC+G,aAAa,IAAI,CAACiD,YAAY,EAAE;MACpE,IAAIM,OAAO,GAAGR,MAAM,CAACK,MAAM,EAAEvR,IAAI,CAAC;MAClClD,MAAM,GAAG4U,OAAO,CAAC5U,MAAM;MACvBsK,UAAU,GAAGsK,OAAO,CAACtK,UAAU;MAC/B+G,aAAa,GAAGuD,OAAO,CAACvD,aAAa;MACrCiD,YAAY,GAAGM,OAAO,CAACN,YAAY;IACrC;IACA,IAAI/I,MAAM,GAAG,CAAC,CAAC;IACf,IAAIgG,WAAW;IACf,IAAIC,WAAW,GAAI7K,KAAK,IAAK;MAC3B,IAAIzD,IAAI,KAAK,aAAa,CAAC,mBAAmB;QAC5CoK,iBAAiB,CAAC,CAAC,EAAEuH,mBAAmB,GAAGlO,KAAK,CAAC;MACnD;MACA2N,YAAY,CAAC3N,KAAK,EAAE;QAClBnH,OAAO,EAAE+R,WAAW;QACpBhG,MAAM;QACNG;MACF,CAAC,CAAC;IACJ,CAAC;IACD,IAAI+I,MAAM,CAACxU,MAAM,CAAC4C,aAAa,EAAE;MAC/B,IAAI8R,cAAc,IAAI,EAAEA,cAAc,YAAYhY,qBAAqB,CAAC,EAAE;QACxE,IAAIgK,KAAK,GAAG,IAAIlD,KAAK,CACnB,6KACF,CAAC;QACD+N,WAAW,CAAC7K,KAAK,CAAC;QAClB,OAAOmO,6BAA6B,CAACnO,KAAK,EAAE2D,UAAU,CAAC;MACzD;MACAiH,WAAW,GAAGoD,cAAc,IAAI,IAAIhY,qBAAqB,CAAC,CAAC;IAC7D,CAAC,MAAM;MACL4U,WAAW,GAAGoD,cAAc,IAAI,CAAC,CAAC;IACpC;IACA,IAAIlV,GAAG,GAAG,IAAIE,GAAG,CAAC+L,OAAO,CAACjM,GAAG,CAAC;IAC9B,IAAIsV,kBAAkB,GAAGN,MAAM,CAACnJ,QAAQ,IAAI,GAAG;IAC/C,IAAI0J,cAAc,GAAGvV,GAAG,CAAC4L,QAAQ;IACjC,IAAItM,aAAa,CAACiW,cAAc,EAAED,kBAAkB,CAAC,KAAK,aAAa,EAAE;MACvEC,cAAc,GAAGD,kBAAkB;IACrC,CAAC,MAAM,IAAIC,cAAc,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC3CD,cAAc,GAAGA,cAAc,CAACrW,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;IACxD;IACA,IAAII,aAAa,CAACiW,cAAc,EAAED,kBAAkB,CAAC,KAAK,GAAG,IAAIC,cAAc,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC7FD,cAAc,GAAGA,cAAc,CAAC1O,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9C;IACA,IAAIpG,SAAS,GAAGqN,kBAAkB,CAAC7B,OAAO,EAAE,yBAAyB,CAAC,KAAK,KAAK;IAChF,IAAI,CAAC+I,MAAM,CAACtT,GAAG,EAAE;MACf,IAAI+T,WAAW,GAAG3G,SAAS,CAACyG,cAAc,CAAC;MAC3C,IAAID,kBAAkB,KAAK,GAAG,EAAE;QAC9B,IAAII,YAAY,GAAGpW,aAAa,CAACmW,WAAW,EAAEH,kBAAkB,CAAC;QACjE,IAAII,YAAY,IAAI,IAAI,EAAE;UACxBb,YAAY,CACV,IAAIjY,iBAAiB,CACnB,GAAG,EACH,WAAW,EACX,+BAA+B6Y,WAAW,yDAAyDH,kBAAkB,IACvH,CAAC,EACD;YACEvV,OAAO,EAAE+R,WAAW;YACpBhG,MAAM;YACNG;UACF,CACF,CAAC;UACD,OAAO,IAAIK,QAAQ,CAAC,WAAW,EAAE;YAC/BD,MAAM,EAAE,GAAG;YACXmI,UAAU,EAAE;UACd,CAAC,CAAC;QACJ;QACAiB,WAAW,GAAGC,YAAY;MAC5B;MACA,IAAIV,MAAM,CAACW,SAAS,CAACnO,MAAM,KAAK,CAAC,EAAE;QACjC/G,SAAS,GAAG,IAAI;MAClB,CAAC,MAAM,IAAI,CAACuU,MAAM,CAACW,SAAS,CAACC,QAAQ,CAACH,WAAW,CAAC,IAAI,CAACT,MAAM,CAACW,SAAS,CAACC,QAAQ,CAACH,WAAW,GAAG,GAAG,CAAC,EAAE;QACnG,IAAIzV,GAAG,CAAC4L,QAAQ,CAAC4J,QAAQ,CAAC,OAAO,CAAC,EAAE;UAClCX,YAAY,CACV,IAAIjY,iBAAiB,CACnB,GAAG,EACH,WAAW,EACX,8BAA8B6Y,WAAW,oIAC3C,CAAC,EACD;YACE1V,OAAO,EAAE+R,WAAW;YACpBhG,MAAM;YACNG;UACF,CACF,CAAC;UACD,OAAO,IAAIK,QAAQ,CAAC,WAAW,EAAE;YAC/BD,MAAM,EAAE,GAAG;YACXmI,UAAU,EAAE;UACd,CAAC,CAAC;QACJ,CAAC,MAAM;UACL/T,SAAS,GAAG,IAAI;QAClB;MACF;IACF;IACA,IAAIoV,WAAW,GAAGzX,eAAe,CAC/B4W,MAAM,CAACrT,cAAc,CAAC+B,YAAY,EAClC4R,kBACF,CAAC;IACD,IAAItV,GAAG,CAAC4L,QAAQ,KAAKiK,WAAW,EAAE;MAChC,IAAI;QACF,IAAIC,GAAG,GAAG,MAAMC,qBAAqB,CAACf,MAAM,EAAEzU,MAAM,EAAEP,GAAG,CAAC;QAC1D,OAAO8V,GAAG;MACZ,CAAC,CAAC,OAAO1H,CAAC,EAAE;QACV2D,WAAW,CAAC3D,CAAC,CAAC;QACd,OAAO,IAAI9B,QAAQ,CAAC,sBAAsB,EAAE;UAAED,MAAM,EAAE;QAAI,CAAC,CAAC;MAC9D;IACF;IACA,IAAIxL,OAAO,GAAG8K,iBAAiB,CAACpL,MAAM,EAAEgV,cAAc,EAAEP,MAAM,CAACnJ,QAAQ,CAAC;IACxE,IAAIhL,OAAO,IAAIA,OAAO,CAAC2G,MAAM,GAAG,CAAC,EAAE;MACjC6C,MAAM,CAACe,MAAM,CAACU,MAAM,EAAEjL,OAAO,CAAC,CAAC,CAAC,CAACiL,MAAM,CAAC;IAC1C;IACA,IAAIkK,QAAQ;IACZ,IAAIhW,GAAG,CAAC4L,QAAQ,CAAC4J,QAAQ,CAAC,OAAO,CAAC,EAAE;MAClC,IAAI3D,UAAU,GAAG,IAAI3R,GAAG,CAAC+L,OAAO,CAACjM,GAAG,CAAC;MACrC6R,UAAU,CAACjG,QAAQ,GAAG2J,cAAc;MACpC,IAAIU,kBAAkB,GAAGtK,iBAAiB,CACxCpL,MAAM,EACNsR,UAAU,CAACjG,QAAQ,EACnBoJ,MAAM,CAACnJ,QACT,CAAC;MACDmK,QAAQ,GAAG,MAAME,wBAAwB,CACvCrL,UAAU,EACVmK,MAAM,EACNpD,aAAa,EACb3F,OAAO,EACP4F,UAAU,EACVC,WAAW,EACXC,WACF,CAAC;MACD,IAAIrT,kBAAkB,CAACsX,QAAQ,CAAC,EAAE;QAChCA,QAAQ,GAAGtC,mCAAmC,CAC5CsC,QAAQ,EACR/J,OAAO,EACP+I,MAAM,EACNnK,UACF,CAAC;MACH;MACA,IAAImK,MAAM,CAAC3R,KAAK,CAACE,MAAM,CAAC4S,iBAAiB,EAAE;QACzCH,QAAQ,GAAG,MAAMhB,MAAM,CAAC3R,KAAK,CAACE,MAAM,CAAC4S,iBAAiB,CAACH,QAAQ,EAAE;UAC/DjW,OAAO,EAAE+R,WAAW;UACpBhG,MAAM,EAAEmK,kBAAkB,GAAGA,kBAAkB,CAAC,CAAC,CAAC,CAACnK,MAAM,GAAG,CAAC,CAAC;UAC9DG;QACF,CAAC,CAAC;QACF,IAAIvN,kBAAkB,CAACsX,QAAQ,CAAC,EAAE;UAChCA,QAAQ,GAAGtC,mCAAmC,CAC5CsC,QAAQ,EACR/J,OAAO,EACP+I,MAAM,EACNnK,UACF,CAAC;QACH;MACF;IACF,CAAC,MAAM,IAAI,CAACpK,SAAS,IAAII,OAAO,IAAIA,OAAO,CAACA,OAAO,CAAC2G,MAAM,GAAG,CAAC,CAAC,CAACzG,KAAK,CAACwC,MAAM,CAAC8B,OAAO,IAAI,IAAI,IAAIxE,OAAO,CAACA,OAAO,CAAC2G,MAAM,GAAG,CAAC,CAAC,CAACzG,KAAK,CAACwC,MAAM,CAACc,aAAa,IAAI,IAAI,EAAE;MAC9J2R,QAAQ,GAAG,MAAMI,qBAAqB,CACpCvL,UAAU,EACVmK,MAAM,EACNpD,aAAa,EACb/Q,OAAO,CAACgG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC9F,KAAK,CAACC,EAAE,EAC7BiL,OAAO,EACP6F,WAAW,EACXC,WACF,CAAC;IACH,CAAC,MAAM;MACL,IAAI;QAAEnG;MAAS,CAAC,GAAG5L,GAAG;MACtB,IAAIK,WAAW,GAAG,KAAK,CAAC;MACxB,IAAI2U,MAAM,CAACqB,uBAAuB,EAAE;QAClChW,WAAW,GAAG,MAAM2U,MAAM,CAACqB,uBAAuB,CAAC;UAAEzK;QAAS,CAAC,CAAC;MAClE,CAAC,MAAM,IAAInI,IAAI,KAAK,aAAa,CAAC,qBAAqBoK,iBAAiB,CAAC,CAAC,EAAEyI,cAAc,EAAE;QAC1FjW,WAAW,GAAG,MAAMwN,iBAAiB,CAAC,CAAC,EAAEyI,cAAc,GAAG1K,QAAQ,CAAC;MACrE;MACAoK,QAAQ,GAAG,MAAMO,qBAAqB,CACpC1L,UAAU,EACVmK,MAAM,EACNpD,aAAa,EACb3F,OAAO,EACP6F,WAAW,EACXC,WAAW,EACXtR,SAAS,EACTJ,WACF,CAAC;IACH;IACA,IAAI4L,OAAO,CAACe,MAAM,KAAK,MAAM,EAAE;MAC7B,OAAO,IAAIV,QAAQ,CAAC,IAAI,EAAE;QACxBY,OAAO,EAAE8I,QAAQ,CAAC9I,OAAO;QACzBb,MAAM,EAAE2J,QAAQ,CAAC3J,MAAM;QACvBmI,UAAU,EAAEwB,QAAQ,CAACxB;MACvB,CAAC,CAAC;IACJ;IACA,OAAOwB,QAAQ;EACjB,CAAC;AACH,CAAC;AACD,eAAeD,qBAAqBA,CAACjG,KAAK,EAAEvP,MAAM,EAAEP,GAAG,EAAE;EACvD,IAAI8P,KAAK,CAAC0G,MAAM,CAAChT,OAAO,KAAKxD,GAAG,CAACwM,YAAY,CAAC2B,GAAG,CAAC,SAAS,CAAC,EAAE;IAC5D,OAAO,IAAI7B,QAAQ,CAAC,IAAI,EAAE;MACxBD,MAAM,EAAE,GAAG;MACXa,OAAO,EAAE;QACP,yBAAyB,EAAE;MAC7B;IACF,CAAC,CAAC;EACJ;EACA,IAAIuJ,OAAO,GAAG,CAAC,CAAC;EAChB,IAAIzW,GAAG,CAACwM,YAAY,CAACiF,GAAG,CAAC,GAAG,CAAC,EAAE;IAC7B,IAAIiF,KAAK,GAAG,eAAgB,IAAInF,GAAG,CAAC,CAAC;IACrCvR,GAAG,CAACwM,YAAY,CAACC,MAAM,CAAC,GAAG,CAAC,CAAC8B,OAAO,CAAErK,IAAI,IAAK;MAC7C,IAAI,CAACA,IAAI,CAACyS,UAAU,CAAC,GAAG,CAAC,EAAE;QACzBzS,IAAI,GAAG,IAAIA,IAAI,EAAE;MACnB;MACA,IAAI0S,QAAQ,GAAG1S,IAAI,CAAC+O,KAAK,CAAC,GAAG,CAAC,CAACpM,KAAK,CAAC,CAAC,CAAC;MACvC+P,QAAQ,CAACrI,OAAO,CAAC,CAACsI,CAAC,EAAEpP,CAAC,KAAK;QACzB,IAAIqP,WAAW,GAAGF,QAAQ,CAAC/P,KAAK,CAAC,CAAC,EAAEY,CAAC,GAAG,CAAC,CAAC,CAACsP,IAAI,CAAC,GAAG,CAAC;QACpDL,KAAK,CAACM,GAAG,CAAC,IAAIF,WAAW,EAAE,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,KAAK,IAAI5S,IAAI,IAAIwS,KAAK,EAAE;MACtB,IAAI7V,OAAO,GAAG8K,iBAAiB,CAACpL,MAAM,EAAE2D,IAAI,EAAE4L,KAAK,CAACjE,QAAQ,CAAC;MAC7D,IAAIhL,OAAO,EAAE;QACX,KAAK,IAAID,KAAK,IAAIC,OAAO,EAAE;UACzB,IAAIC,OAAO,GAAGF,KAAK,CAACG,KAAK,CAACC,EAAE;UAC5B,IAAID,KAAK,GAAG+O,KAAK,CAAC0G,MAAM,CAACjW,MAAM,CAACO,OAAO,CAAC;UACxC,IAAIC,KAAK,EAAE;YACT0V,OAAO,CAAC3V,OAAO,CAAC,GAAGC,KAAK;UAC1B;QACF;MACF;IACF;IACA,OAAOuL,QAAQ,CAAC2K,IAAI,CAACR,OAAO,EAAE;MAC5BvJ,OAAO,EAAE;QACP,eAAe,EAAE;MACnB;IACF,CAAC,CAAC;EACJ;EACA,OAAO,IAAIZ,QAAQ,CAAC,iBAAiB,EAAE;IAAED,MAAM,EAAE;EAAI,CAAC,CAAC;AACzD;AACA,eAAe6J,wBAAwBA,CAACrL,UAAU,EAAEiF,KAAK,EAAE8B,aAAa,EAAE3F,OAAO,EAAE4F,UAAU,EAAEC,WAAW,EAAEC,WAAW,EAAE;EACvH,IAAIiE,QAAQ,GAAG/J,OAAO,CAACe,MAAM,KAAK,KAAK,GAAG,MAAM2E,iBAAiB,CAC/D7B,KAAK,EACLjF,UAAU,EACV+G,aAAa,EACb3F,OAAO,EACP4F,UAAU,EACVC,WAAW,EACXC,WACF,CAAC,GAAG,MAAMe,kBAAkB,CAC1BhD,KAAK,EACLjF,UAAU,EACV+G,aAAa,EACb3F,OAAO,EACP4F,UAAU,EACVC,WAAW,EACXC,WACF,CAAC;EACD,OAAOiE,QAAQ;AACjB;AACA,eAAeO,qBAAqBA,CAAC1L,UAAU,EAAEiF,KAAK,EAAE8B,aAAa,EAAE3F,OAAO,EAAE6F,WAAW,EAAEC,WAAW,EAAEtR,SAAS,EAAEJ,WAAW,EAAE;EAChI,IAAI;IACF,IAAIsJ,MAAM,GAAG,MAAMiI,aAAa,CAACK,KAAK,CAAChG,OAAO,EAAE;MAC9CiG,cAAc,EAAEJ,WAAW;MAC3BO,0BAA0B,EAAEvC,KAAK,CAACtP,MAAM,CAAC4C,aAAa,GAAG,MAAO6O,KAAK,IAAK;QACxE,IAAI;UACF,IAAIK,WAAW,GAAG,MAAML,KAAK,CAAChG,OAAO,CAAC;UACtC,IAAI,CAACrN,UAAU,CAAC0T,WAAW,CAAC,EAAE;YAC5BA,WAAW,GAAG,MAAM4E,UAAU,CAAC5E,WAAW,EAAE7R,SAAS,CAAC;UACxD;UACA,OAAO6R,WAAW;QACpB,CAAC,CAAC,OAAOpL,KAAK,EAAE;UACd6K,WAAW,CAAC7K,KAAK,CAAC;UAClB,OAAO,IAAIoF,QAAQ,CAAC,IAAI,EAAE;YAAED,MAAM,EAAE;UAAI,CAAC,CAAC;QAC5C;MACF,CAAC,GAAG,KAAK;IACX,CAAC,CAAC;IACF,IAAI,CAACzN,UAAU,CAAC+K,MAAM,CAAC,EAAE;MACvBA,MAAM,GAAG,MAAMuN,UAAU,CAACvN,MAAM,EAAElJ,SAAS,CAAC;IAC9C;IACA,OAAOkJ,MAAM;EACf,CAAC,CAAC,OAAOzC,KAAK,EAAE;IACd6K,WAAW,CAAC7K,KAAK,CAAC;IAClB,OAAO,IAAIoF,QAAQ,CAAC,IAAI,EAAE;MAAED,MAAM,EAAE;IAAI,CAAC,CAAC;EAC5C;EACA,eAAe6K,UAAUA,CAACnX,OAAO,EAAEoX,UAAU,EAAE;IAC7C,IAAIjK,OAAO,GAAG2C,kBAAkB,CAAC9P,OAAO,EAAE+P,KAAK,CAAC;IAChD,IAAI4B,2BAA2B,CAACD,GAAG,CAAC1R,OAAO,CAAC4S,UAAU,CAAC,EAAE;MACvD,OAAO,IAAIrG,QAAQ,CAAC,IAAI,EAAE;QAAED,MAAM,EAAEtM,OAAO,CAAC4S,UAAU;QAAEzF;MAAQ,CAAC,CAAC;IACpE;IACA,IAAInN,OAAO,CAACkL,MAAM,EAAE;MAClBZ,MAAM,CAACiE,MAAM,CAACvO,OAAO,CAACkL,MAAM,CAAC,CAACsD,OAAO,CAAEqE,GAAG,IAAK;QAC7C,IAAI,CAAC/T,oBAAoB,CAAC+T,GAAG,CAAC,IAAIA,GAAG,CAAC1L,KAAK,EAAE;UAC3C6K,WAAW,CAACa,GAAG,CAAC;QAClB;MACF,CAAC,CAAC;MACF7S,OAAO,CAACkL,MAAM,GAAGD,cAAc,CAACjL,OAAO,CAACkL,MAAM,EAAEJ,UAAU,CAAC;IAC7D;IACA,IAAI9I,KAAK,GAAG;MACVpB,UAAU,EAAEZ,OAAO,CAACY,UAAU;MAC9B4P,UAAU,EAAExQ,OAAO,CAACwQ,UAAU;MAC9BtF,MAAM,EAAEK,eAAe,CAACvL,OAAO,CAACkL,MAAM,EAAEJ,UAAU;IACpD,CAAC;IACD,IAAIuM,iBAAiB,GAAG;MACtBvL,QAAQ,EAAEiE,KAAK,CAACjE,QAAQ;MACxBrL,MAAM,EAAEsP,KAAK,CAACtP,MAAM;MACpBmB,cAAc,EAAEmO,KAAK,CAACnO,cAAc;MACpCD,GAAG,EAAEoO,KAAK,CAACpO,GAAG;MACdjB,SAAS,EAAE0W;IACb,CAAC;IACD,IAAIE,YAAY,GAAG;MACjBlX,QAAQ,EAAE2P,KAAK,CAAC0G,MAAM;MACtBpW,YAAY,EAAEgK,uBAAuB,CAAC0F,KAAK,CAACvP,MAAM,CAAC;MACnDG,oBAAoB,EAAEX,OAAO;MAC7BM,WAAW;MACXC,mBAAmB,EAAEoP,yBAAyB,CAAC;QAC7C,GAAG0H,iBAAiB;QACpB/W;MACF,CAAC,CAAC;MACF4B,mBAAmB,EAAEuR,oBAAoB,CACvCzR,KAAK,EACLkK,OAAO,CAACkB,MAAM,EACd2C,KAAK,CAACzM,KAAK,CAACE,MAAM,CAACkQ,aAAa,EAChC5I,UACF,CAAC;MACDhJ,UAAU,EAAE,CAAC,CAAC;MACdrB,MAAM,EAAEsP,KAAK,CAACtP,MAAM;MACpBkB,GAAG,EAAEoO,KAAK,CAACpO,GAAG;MACdC,cAAc,EAAEmO,KAAK,CAACnO,cAAc;MACpClB,SAAS,EAAE0W,UAAU;MACrBvV,cAAc,EAAGgR,GAAG,IAAKhR,cAAc,CAACgR,GAAG,EAAE/H,UAAU;IACzD,CAAC;IACD,IAAIyM,6BAA6B,GAAGxH,KAAK,CAACzM,KAAK,CAACE,MAAM,CAAC8B,OAAO;IAC9D,IAAI;MACF,OAAO,MAAMiS,6BAA6B,CACxCrL,OAAO,EACPlM,OAAO,CAAC4S,UAAU,EAClBzF,OAAO,EACPmK,YAAY,EACZvF,WACF,CAAC;IACH,CAAC,CAAC,OAAO5K,KAAK,EAAE;MACd6K,WAAW,CAAC7K,KAAK,CAAC;MAClB,IAAIqQ,oBAAoB,GAAGrQ,KAAK;MAChC,IAAItI,UAAU,CAACsI,KAAK,CAAC,EAAE;QACrB,IAAI;UACF,IAAInB,KAAK,GAAG,MAAMyR,cAAc,CAACtQ,KAAK,CAAC;UACvCqQ,oBAAoB,GAAG,IAAI3a,iBAAiB,CAC1CsK,KAAK,CAACmF,MAAM,EACZnF,KAAK,CAACsN,UAAU,EAChBzO,KACF,CAAC;QACH,CAAC,CAAC,OAAOqI,CAAC,EAAE,CACZ;MACF;MACArO,OAAO,GAAGzB,yBAAyB,CACjCsT,aAAa,CAACgD,UAAU,EACxB7U,OAAO,EACPwX,oBACF,CAAC;MACD,IAAIxX,OAAO,CAACkL,MAAM,EAAE;QAClBlL,OAAO,CAACkL,MAAM,GAAGD,cAAc,CAACjL,OAAO,CAACkL,MAAM,EAAEJ,UAAU,CAAC;MAC7D;MACA,IAAI4M,MAAM,GAAG;QACX9W,UAAU,EAAEZ,OAAO,CAACY,UAAU;QAC9B4P,UAAU,EAAExQ,OAAO,CAACwQ,UAAU;QAC9BtF,MAAM,EAAEK,eAAe,CAACvL,OAAO,CAACkL,MAAM,EAAEJ,UAAU;MACpD,CAAC;MACDwM,YAAY,GAAG;QACb,GAAGA,YAAY;QACf3W,oBAAoB,EAAEX,OAAO;QAC7BO,mBAAmB,EAAEoP,yBAAyB,CAAC0H,iBAAiB,CAAC;QACjEnV,mBAAmB,EAAEuR,oBAAoB,CACvCiE,MAAM,EACNxL,OAAO,CAACkB,MAAM,EACd2C,KAAK,CAACzM,KAAK,CAACE,MAAM,CAACkQ,aAAa,EAChC5I,UACF,CAAC;QACDhJ,UAAU,EAAE,CAAC;MACf,CAAC;MACD,IAAI;QACF,OAAO,MAAMyV,6BAA6B,CACxCrL,OAAO,EACPlM,OAAO,CAAC4S,UAAU,EAClBzF,OAAO,EACPmK,YAAY,EACZvF,WACF,CAAC;MACH,CAAC,CAAC,OAAO4F,MAAM,EAAE;QACf3F,WAAW,CAAC2F,MAAM,CAAC;QACnB,OAAOrC,6BAA6B,CAACqC,MAAM,EAAE7M,UAAU,CAAC;MAC1D;IACF;EACF;AACF;AACA,eAAeuL,qBAAqBA,CAACvL,UAAU,EAAEiF,KAAK,EAAE8B,aAAa,EAAE9Q,OAAO,EAAEmL,OAAO,EAAE6F,WAAW,EAAEC,WAAW,EAAE;EACjH,IAAI;IACF,IAAIpI,MAAM,GAAG,MAAMiI,aAAa,CAAC+F,UAAU,CAAC1L,OAAO,EAAE;MACnDnL,OAAO;MACPoR,cAAc,EAAEJ,WAAW;MAC3BO,0BAA0B,EAAEvC,KAAK,CAACtP,MAAM,CAAC4C,aAAa,GAAG,MAAOuU,UAAU,IAAK;QAC7E,IAAI;UACF,IAAIrF,WAAW,GAAG,MAAMqF,UAAU,CAAC1L,OAAO,CAAC;UAC3C,OAAO2L,sBAAsB,CAACtF,WAAW,CAAC;QAC5C,CAAC,CAAC,OAAOpL,KAAK,EAAE;UACd,OAAO2Q,qBAAqB,CAAC3Q,KAAK,CAAC;QACrC;MACF,CAAC,GAAG,KAAK;IACX,CAAC,CAAC;IACF,OAAO0Q,sBAAsB,CAACjO,MAAM,CAAC;EACvC,CAAC,CAAC,OAAOzC,KAAK,EAAE;IACd,OAAO2Q,qBAAqB,CAAC3Q,KAAK,CAAC;EACrC;EACA,SAAS0Q,sBAAsBA,CAACjO,MAAM,EAAE;IACtC,IAAI/K,UAAU,CAAC+K,MAAM,CAAC,EAAE;MACtB,OAAOA,MAAM;IACf;IACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9B,OAAO,IAAI2C,QAAQ,CAAC3C,MAAM,CAAC;IAC7B;IACA,OAAO2C,QAAQ,CAAC2K,IAAI,CAACtN,MAAM,CAAC;EAC9B;EACA,SAASkO,qBAAqBA,CAAC3Q,KAAK,EAAE;IACpC,IAAItI,UAAU,CAACsI,KAAK,CAAC,EAAE;MACrBA,KAAK,CAACgG,OAAO,CAACqG,GAAG,CAAC,eAAe,EAAE,KAAK,CAAC;MACzC,OAAOrM,KAAK;IACd;IACA,IAAIrI,oBAAoB,CAACqI,KAAK,CAAC,EAAE;MAC/B6K,WAAW,CAAC7K,KAAK,CAAC;MAClB,OAAO4Q,mBAAmB,CAAC5Q,KAAK,EAAE2D,UAAU,CAAC;IAC/C;IACA,IAAI3D,KAAK,YAAYlD,KAAK,IAAIkD,KAAK,CAACmE,OAAO,KAAK,qCAAqC,EAAE;MACrF,IAAI0M,QAAQ,GAAG,IAAI/T,KAAK,CACtB,gEACF,CAAC;MACD+N,WAAW,CAACgG,QAAQ,CAAC;MACrB,OAAO1C,6BAA6B,CAAC0C,QAAQ,EAAElN,UAAU,CAAC;IAC5D;IACAkH,WAAW,CAAC7K,KAAK,CAAC;IAClB,OAAOmO,6BAA6B,CAACnO,KAAK,EAAE2D,UAAU,CAAC;EACzD;AACF;AACA,SAASiN,mBAAmBA,CAACE,aAAa,EAAEnN,UAAU,EAAE;EACtD,OAAOyB,QAAQ,CAAC2K,IAAI,CAClBrV,cAAc;EACZ;EACAoW,aAAa,CAAC9Q,KAAK,IAAI,IAAIlD,KAAK,CAAC,yBAAyB,CAAC,EAC3D6G,UACF,CAAC,EACD;IACEwB,MAAM,EAAE2L,aAAa,CAAC3L,MAAM;IAC5BmI,UAAU,EAAEwD,aAAa,CAACxD,UAAU;IACpCtH,OAAO,EAAE;MACP,eAAe,EAAE;IACnB;EACF,CACF,CAAC;AACH;AACA,SAASmI,6BAA6BA,CAACnO,KAAK,EAAE2D,UAAU,EAAE;EACxD,IAAIQ,OAAO,GAAG,yBAAyB;EACvC,IAAIR,UAAU,KAAK,YAAY,CAAC,kBAAkB;IAChDQ,OAAO,IAAI;AACf;AACA,EAAE9E,MAAM,CAACW,KAAK,CAAC,EAAE;EACf;EACA,OAAO,IAAIoF,QAAQ,CAACjB,OAAO,EAAE;IAC3BgB,MAAM,EAAE,GAAG;IACXa,OAAO,EAAE;MACP,cAAc,EAAE;IAClB;EACF,CAAC,CAAC;AACJ;AACA,SAASsK,cAAcA,CAACxB,QAAQ,EAAE;EAChC,IAAIiC,WAAW,GAAGjC,QAAQ,CAAC9I,OAAO,CAACiB,GAAG,CAAC,cAAc,CAAC;EACtD,OAAO8J,WAAW,IAAI,uBAAuB,CAACC,IAAI,CAACD,WAAW,CAAC,GAAGjC,QAAQ,CAAC/I,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG+I,QAAQ,CAACiB,IAAI,CAAC,CAAC,GAAGjB,QAAQ,CAACmC,IAAI,CAAC,CAAC;AACpI;;AAEA;AACA,SAASC,KAAKA,CAAC/Q,IAAI,EAAE;EACnB,OAAO,WAAWA,IAAI,IAAI;AAC5B;AACA,IAAIgR,aAAa,GAAGA,CAACC,WAAW,GAAG,CAAC,CAAC,EAAEtX,EAAE,GAAG,EAAE,KAAK;EACjD,IAAI+C,GAAG,GAAG,IAAIwU,GAAG,CAAClO,MAAM,CAACa,OAAO,CAACoN,WAAW,CAAC,CAAC;EAC9C,OAAO;IACL,IAAItX,EAAEA,CAAA,EAAG;MACP,OAAOA,EAAE;IACX,CAAC;IACD,IAAIwO,IAAIA,CAAA,EAAG;MACT,OAAOnF,MAAM,CAACqK,WAAW,CAAC3Q,GAAG,CAAC;IAChC,CAAC;IACD0N,GAAGA,CAACpK,IAAI,EAAE;MACR,OAAOtD,GAAG,CAAC0N,GAAG,CAACpK,IAAI,CAAC,IAAItD,GAAG,CAAC0N,GAAG,CAAC2G,KAAK,CAAC/Q,IAAI,CAAC,CAAC;IAC9C,CAAC;IACD8G,GAAGA,CAAC9G,IAAI,EAAE;MACR,IAAItD,GAAG,CAAC0N,GAAG,CAACpK,IAAI,CAAC,EAAE,OAAOtD,GAAG,CAACoK,GAAG,CAAC9G,IAAI,CAAC;MACvC,IAAImR,SAAS,GAAGJ,KAAK,CAAC/Q,IAAI,CAAC;MAC3B,IAAItD,GAAG,CAAC0N,GAAG,CAAC+G,SAAS,CAAC,EAAE;QACtB,IAAI/W,KAAK,GAAGsC,GAAG,CAACoK,GAAG,CAACqK,SAAS,CAAC;QAC9BzU,GAAG,CAAC2I,MAAM,CAAC8L,SAAS,CAAC;QACrB,OAAO/W,KAAK;MACd;MACA,OAAO,KAAK,CAAC;IACf,CAAC;IACD8R,GAAGA,CAAClM,IAAI,EAAE5F,KAAK,EAAE;MACfsC,GAAG,CAACwP,GAAG,CAAClM,IAAI,EAAE5F,KAAK,CAAC;IACtB,CAAC;IACD2W,KAAKA,CAAC/Q,IAAI,EAAE5F,KAAK,EAAE;MACjBsC,GAAG,CAACwP,GAAG,CAAC6E,KAAK,CAAC/Q,IAAI,CAAC,EAAE5F,KAAK,CAAC;IAC7B,CAAC;IACDgX,KAAKA,CAACpR,IAAI,EAAE;MACVtD,GAAG,CAAC2I,MAAM,CAACrF,IAAI,CAAC;IAClB;EACF,CAAC;AACH,CAAC;AACD,IAAIqR,SAAS,GAAI5P,MAAM,IAAK;EAC1B,OAAOA,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,CAAC9H,EAAE,KAAK,QAAQ,IAAI,OAAO8H,MAAM,CAAC0G,IAAI,KAAK,WAAW,IAAI,OAAO1G,MAAM,CAAC2I,GAAG,KAAK,UAAU,IAAI,OAAO3I,MAAM,CAACqF,GAAG,KAAK,UAAU,IAAI,OAAOrF,MAAM,CAACyK,GAAG,KAAK,UAAU,IAAI,OAAOzK,MAAM,CAACsP,KAAK,KAAK,UAAU,IAAI,OAAOtP,MAAM,CAAC2P,KAAK,KAAK,UAAU;AACtR,CAAC;AACD,SAASE,oBAAoBA,CAAC;EAC5BhS,MAAM,EAAEiS,SAAS;EACjBC,UAAU;EACVC,QAAQ;EACRC,UAAU;EACVC;AACF,CAAC,EAAE;EACD,IAAIrS,MAAM,GAAGkC,QAAQ,CAAC+P,SAAS,CAAC,GAAGA,SAAS,GAAGjR,YAAY,CAACiR,SAAS,EAAEvR,IAAI,IAAI,WAAW,EAAEuR,SAAS,CAAC;EACtGK,iCAAiC,CAACtS,MAAM,CAAC;EACzC,OAAO;IACL,MAAMuS,UAAUA,CAAC5Q,YAAY,EAAER,OAAO,EAAE;MACtC,IAAI9G,EAAE,GAAGsH,YAAY,KAAI,MAAM3B,MAAM,CAAClB,KAAK,CAAC6C,YAAY,EAAER,OAAO,CAAC;MAClE,IAAI/B,KAAK,GAAG/E,EAAE,KAAI,MAAM8X,QAAQ,CAAC9X,EAAE,CAAC;MACpC,OAAOqX,aAAa,CAACtS,KAAK,IAAI,CAAC,CAAC,EAAE/E,EAAE,IAAI,EAAE,CAAC;IAC7C,CAAC;IACD,MAAMmY,aAAaA,CAACC,OAAO,EAAEtR,OAAO,EAAE;MACpC,IAAI;QAAE9G,EAAE;QAAEwO,IAAI,EAAEzJ;MAAM,CAAC,GAAGqT,OAAO;MACjC,IAAInR,OAAO,GAAGH,OAAO,EAAEK,MAAM,IAAI,IAAI,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGP,OAAO,CAACK,MAAM,GAAG,GAAG,CAAC,GAAGL,OAAO,EAAEG,OAAO,IAAI,IAAI,GAAGH,OAAO,CAACG,OAAO,GAAGtB,MAAM,CAACsB,OAAO;MACjJ,IAAIjH,EAAE,EAAE;QACN,MAAM+X,UAAU,CAAC/X,EAAE,EAAE+E,KAAK,EAAEkC,OAAO,CAAC;MACtC,CAAC,MAAM;QACLjH,EAAE,GAAG,MAAM6X,UAAU,CAAC9S,KAAK,EAAEkC,OAAO,CAAC;MACvC;MACA,OAAOtB,MAAM,CAACjB,SAAS,CAAC1E,EAAE,EAAE8G,OAAO,CAAC;IACtC,CAAC;IACD,MAAMuR,cAAcA,CAACD,OAAO,EAAEtR,OAAO,EAAE;MACrC,MAAMkR,UAAU,CAACI,OAAO,CAACpY,EAAE,CAAC;MAC5B,OAAO2F,MAAM,CAACjB,SAAS,CAAC,EAAE,EAAE;QAC1B,GAAGoC,OAAO;QACVK,MAAM,EAAE,KAAK,CAAC;QACdF,OAAO,EAAE,eAAgB,IAAIG,IAAI,CAAC,CAAC;MACrC,CAAC,CAAC;IACJ;EACF,CAAC;AACH;AACA,SAAS6Q,iCAAiCA,CAACtS,MAAM,EAAE;EACjDlH,QAAQ,CACNkH,MAAM,CAACuB,QAAQ,EACf,QAAQvB,MAAM,CAACU,IAAI,6OACrB,CAAC;AACH;;AAEA;AACA,SAASiS,0BAA0BA,CAAC;EAAE3S,MAAM,EAAEiS;AAAU,CAAC,GAAG,CAAC,CAAC,EAAE;EAC9D,IAAIjS,MAAM,GAAGkC,QAAQ,CAAC+P,SAAS,CAAC,GAAGA,SAAS,GAAGjR,YAAY,CAACiR,SAAS,EAAEvR,IAAI,IAAI,WAAW,EAAEuR,SAAS,CAAC;EACtGK,iCAAiC,CAACtS,MAAM,CAAC;EACzC,OAAO;IACL,MAAMuS,UAAUA,CAAC5Q,YAAY,EAAER,OAAO,EAAE;MACtC,OAAOuQ,aAAa,CAClB/P,YAAY,KAAI,MAAM3B,MAAM,CAAClB,KAAK,CAAC6C,YAAY,EAAER,OAAO,CAAC,KAAI,CAAC,CAChE,CAAC;IACH,CAAC;IACD,MAAMqR,aAAaA,CAACC,OAAO,EAAEtR,OAAO,EAAE;MACpC,IAAIyR,gBAAgB,GAAG,MAAM5S,MAAM,CAACjB,SAAS,CAAC0T,OAAO,CAAC5J,IAAI,EAAE1H,OAAO,CAAC;MACpE,IAAIyR,gBAAgB,CAAC/R,MAAM,GAAG,IAAI,EAAE;QAClC,MAAM,IAAIxD,KAAK,CACb,qDAAqD,GAAGuV,gBAAgB,CAAC/R,MAC3E,CAAC;MACH;MACA,OAAO+R,gBAAgB;IACzB,CAAC;IACD,MAAMF,cAAcA,CAACG,QAAQ,EAAE1R,OAAO,EAAE;MACtC,OAAOnB,MAAM,CAACjB,SAAS,CAAC,EAAE,EAAE;QAC1B,GAAGoC,OAAO;QACVK,MAAM,EAAE,KAAK,CAAC;QACdF,OAAO,EAAE,eAAgB,IAAIG,IAAI,CAAC,CAAC;MACrC,CAAC,CAAC;IACJ;EACF,CAAC;AACH;;AAEA;AACA,SAASqR,0BAA0BA,CAAC;EAAE9S;AAAO,CAAC,GAAG,CAAC,CAAC,EAAE;EACnD,IAAI5C,GAAG,GAAG,eAAgB,IAAIwU,GAAG,CAAC,CAAC;EACnC,OAAOI,oBAAoB,CAAC;IAC1BhS,MAAM;IACN,MAAMkS,UAAUA,CAAC9S,KAAK,EAAEkC,OAAO,EAAE;MAC/B,IAAIjH,EAAE,GAAG0Y,IAAI,CAACC,MAAM,CAAC,CAAC,CAACjQ,QAAQ,CAAC,EAAE,CAAC,CAACkQ,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;MACpD7V,GAAG,CAACwP,GAAG,CAACvS,EAAE,EAAE;QAAEwO,IAAI,EAAEzJ,KAAK;QAAEkC;MAAQ,CAAC,CAAC;MACrC,OAAOjH,EAAE;IACX,CAAC;IACD,MAAM8X,QAAQA,CAAC9X,EAAE,EAAE;MACjB,IAAI+C,GAAG,CAAC0N,GAAG,CAACzQ,EAAE,CAAC,EAAE;QACf,IAAI;UAAEwO,IAAI,EAAEzJ,KAAK;UAAEkC;QAAQ,CAAC,GAAGlE,GAAG,CAACoK,GAAG,CAACnN,EAAE,CAAC;QAC1C,IAAI,CAACiH,OAAO,IAAIA,OAAO,GAAG,eAAgB,IAAIG,IAAI,CAAC,CAAC,EAAE;UACpD,OAAOrC,KAAK;QACd;QACA,IAAIkC,OAAO,EAAElE,GAAG,CAAC2I,MAAM,CAAC1L,EAAE,CAAC;MAC7B;MACA,OAAO,IAAI;IACb,CAAC;IACD,MAAM+X,UAAUA,CAAC/X,EAAE,EAAE+E,KAAK,EAAEkC,OAAO,EAAE;MACnClE,GAAG,CAACwP,GAAG,CAACvS,EAAE,EAAE;QAAEwO,IAAI,EAAEzJ,KAAK;QAAEkC;MAAQ,CAAC,CAAC;IACvC,CAAC;IACD,MAAM+Q,UAAUA,CAAChY,EAAE,EAAE;MACnB+C,GAAG,CAAC2I,MAAM,CAAC1L,EAAE,CAAC;IAChB;EACF,CAAC,CAAC;AACJ;;AAEA;AACA,SAASsM,IAAIA,CAACpJ,IAAI,EAAE,GAAGK,IAAI,EAAE;EAC3B,IAAIuH,MAAM,GAAGvH,IAAI,CAAC,CAAC,CAAC;EACpB,IAAIoF,MAAM,GAAGzF,IAAI,CAAChF,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAACA,OAAO,CAC9C,mBAAmB;EACnB;EACA,CAAC2X,CAAC,EAAEgD,KAAK,EAAEC,YAAY,KAAK;IAC1B,MAAMC,UAAU,GAAGD,YAAY,KAAK,KAAK,CAAC;IAC1C,MAAMrY,KAAK,GAAGqK,MAAM,GAAGA,MAAM,CAAC+N,KAAK,CAAC,GAAG,KAAK,CAAC;IAC7C,IAAIE,UAAU,IAAItY,KAAK,KAAK,KAAK,CAAC,EAAE;MAClC,MAAM,IAAIuC,KAAK,CACb,SAASE,IAAI,qBAAqB2V,KAAK,2BACzC,CAAC;IACH;IACA,OAAOpY,KAAK,KAAK,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,GAAGA,KAAK;EAC5C,CACF,CAAC;EACD,IAAIyC,IAAI,CAACsR,QAAQ,CAAC,GAAG,CAAC,EAAE;IACtB,MAAM/T,KAAK,GAAGqK,MAAM,GAAGA,MAAM,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;IAC3C,IAAIrK,KAAK,KAAK,KAAK,CAAC,EAAE;MACpBkI,MAAM,IAAI,GAAG,GAAGlI,KAAK;IACvB;EACF;EACA,OAAOkI,MAAM,IAAI,GAAG;AACtB;;AAEA;AACA,OAAO,KAAKqQ,MAAM,MAAM,OAAO;AAC/B,OAAO,KAAKC,QAAQ,MAAM,WAAW;;AAErC;AACA,SAASC,gBAAgBA,CAAC;EACxBnY,KAAK;EACLxB,MAAM;EACN4Z,YAAY;EACZrY,QAAQ,EAAEsY,SAAS;EACnBvO,QAAQ;EACRpL;AACF,CAAC,EAAE;EACD,IAAIqC,aAAa,GAAG;IAClB,GAAGf,KAAK;IACRpB,UAAU,EAAE;MAAE,GAAGoB,KAAK,CAACpB;IAAW;EACpC,CAAC;EACD,IAAI0Z,cAAc,GAAGvb,WAAW,CAACyB,MAAM,EAAE6Z,SAAS,EAAEvO,QAAQ,CAAC;EAC7D,IAAIwO,cAAc,EAAE;IAClB,KAAK,IAAIzZ,KAAK,IAAIyZ,cAAc,EAAE;MAChC,IAAIvZ,OAAO,GAAGF,KAAK,CAACG,KAAK,CAACC,EAAE;MAC5B,IAAIsZ,SAAS,GAAGH,YAAY,CAACrZ,OAAO,CAAC;MACrC,IAAI1B,wBAAwB,CAC1B0B,OAAO,EACPwZ,SAAS,CAACpZ,YAAY,EACtBoZ,SAAS,CAACnZ,SAAS,EACnBV,SACF,CAAC,KAAK6Z,SAAS,CAACC,kBAAkB,IAAI,CAACD,SAAS,CAACnZ,SAAS,CAAC,EAAE;QAC3D,OAAO2B,aAAa,CAACnC,UAAU,CAACG,OAAO,CAAC;MAC1C,CAAC,MAAM,IAAI,CAACwZ,SAAS,CAACnZ,SAAS,EAAE;QAC/B2B,aAAa,CAACnC,UAAU,CAACG,OAAO,CAAC,GAAG,IAAI;MAC1C;IACF;EACF;EACA,OAAOgC,aAAa;AACtB;;AAEA;AACA,OAAO0X,MAAM,MAAM,OAAO;AAC1B,IAAIC,4BAA4B,GAAG,cAAcD,MAAM,CAACpW,SAAS,CAAC;EAChEsW,WAAWA,CAACC,KAAK,EAAE;IACjB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAAC5Y,KAAK,GAAG;MAAEmF,KAAK,EAAE,IAAI;MAAEpF,QAAQ,EAAE6Y,KAAK,CAAC7Y;IAAS,CAAC;EACxD;EACA,OAAO8Y,wBAAwBA,CAAC1T,KAAK,EAAE;IACrC,OAAO;MAAEA;IAAM,CAAC;EAClB;EACA,OAAO2T,wBAAwBA,CAACF,KAAK,EAAE5Y,KAAK,EAAE;IAC5C,IAAIA,KAAK,CAACD,QAAQ,KAAK6Y,KAAK,CAAC7Y,QAAQ,EAAE;MACrC,OAAO;QAAEoF,KAAK,EAAE,IAAI;QAAEpF,QAAQ,EAAE6Y,KAAK,CAAC7Y;MAAS,CAAC;IAClD;IACA,OAAO;MAAEoF,KAAK,EAAEnF,KAAK,CAACmF,KAAK;MAAEpF,QAAQ,EAAEC,KAAK,CAACD;IAAS,CAAC;EACzD;EACAgZ,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAAC/Y,KAAK,CAACmF,KAAK,EAAE;MACpB,OAAO,eAAgBsT,MAAM,CAAClZ,aAAa,CACzCyZ,+BAA+B,EAC/B;QACE7T,KAAK,EAAE,IAAI,CAACnF,KAAK,CAACmF,KAAK;QACvB8T,cAAc,EAAE;MAClB,CACF,CAAC;IACH,CAAC,MAAM;MACL,OAAO,IAAI,CAACL,KAAK,CAACnV,QAAQ;IAC5B;EACF;AACF,CAAC;AACD,SAASyV,YAAYA,CAAC;EACpBD,cAAc;EACdE,KAAK;EACL1V;AACF,CAAC,EAAE;EACD,IAAI,CAACwV,cAAc,EAAE;IACnB,OAAOxV,QAAQ;EACjB;EACA,OAAO,eAAgBgV,MAAM,CAAClZ,aAAa,CAAC,MAAM,EAAE;IAAE6Z,IAAI,EAAE;EAAK,CAAC,EAAE,eAAgBX,MAAM,CAAClZ,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,eAAgBkZ,MAAM,CAAClZ,aAAa,CAAC,MAAM,EAAE;IAAE8Z,OAAO,EAAE;EAAQ,CAAC,CAAC,EAAE,eAAgBZ,MAAM,CAAClZ,aAAa,CAC7N,MAAM,EACN;IACE+F,IAAI,EAAE,UAAU;IAChBgU,OAAO,EAAE;EACX,CACF,CAAC,EAAE,eAAgBb,MAAM,CAAClZ,aAAa,CAAC,OAAO,EAAE,IAAI,EAAE4Z,KAAK,CAAC,CAAC,EAAE,eAAgBV,MAAM,CAAClZ,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,eAAgBkZ,MAAM,CAAClZ,aAAa,CAAC,MAAM,EAAE;IAAEga,KAAK,EAAE;MAAEC,UAAU,EAAE,uBAAuB;MAAEC,OAAO,EAAE;IAAO;EAAE,CAAC,EAAEhW,QAAQ,CAAC,CAAC,CAAC;AAClP;AACA,SAASuV,+BAA+BA,CAAC;EACvC7T,KAAK;EACL8T;AACF,CAAC,EAAE;EACDxN,OAAO,CAACtG,KAAK,CAACA,KAAK,CAAC;EACpB,IAAIuU,YAAY,GAAG,eAAgBjB,MAAM,CAAClZ,aAAa,CACrD,QAAQ,EACR;IACEoa,uBAAuB,EAAE;MACvBC,MAAM,EAAE;AAChB;AACA;AACA;AACA;IACM;EACF,CACF,CAAC;EACD,IAAI9c,oBAAoB,CAACqI,KAAK,CAAC,EAAE;IAC/B,OAAO,eAAgBsT,MAAM,CAAClZ,aAAa,CACzC2Z,YAAY,EACZ;MACED,cAAc;MACdE,KAAK,EAAE;IACT,CAAC,EACD,eAAgBV,MAAM,CAAClZ,aAAa,CAAC,IAAI,EAAE;MAAEga,KAAK,EAAE;QAAEM,QAAQ,EAAE;MAAO;IAAE,CAAC,EAAE1U,KAAK,CAACmF,MAAM,EAAE,GAAG,EAAEnF,KAAK,CAACsN,UAAU,CAAC,EAChH7X,mBAAmB,GAAG8e,YAAY,GAAG,IACvC,CAAC;EACH;EACA,IAAII,aAAa;EACjB,IAAI3U,KAAK,YAAYlD,KAAK,EAAE;IAC1B6X,aAAa,GAAG3U,KAAK;EACvB,CAAC,MAAM;IACL,IAAI4U,WAAW,GAAG5U,KAAK,IAAI,IAAI,GAAG,eAAe,GAAG,OAAOA,KAAK,KAAK,QAAQ,IAAI,UAAU,IAAIA,KAAK,GAAGA,KAAK,CAACwC,QAAQ,CAAC,CAAC,GAAGL,IAAI,CAACC,SAAS,CAACpC,KAAK,CAAC;IAC/I2U,aAAa,GAAG,IAAI7X,KAAK,CAAC8X,WAAW,CAAC;EACxC;EACA,OAAO,eAAgBtB,MAAM,CAAClZ,aAAa,CAAC2Z,YAAY,EAAE;IAAED,cAAc;IAAEE,KAAK,EAAE;EAAqB,CAAC,EAAE,eAAgBV,MAAM,CAAClZ,aAAa,CAAC,IAAI,EAAE;IAAEga,KAAK,EAAE;MAAEM,QAAQ,EAAE;IAAO;EAAE,CAAC,EAAE,mBAAmB,CAAC,EAAE,eAAgBpB,MAAM,CAAClZ,aAAa,CAC/O,KAAK,EACL;IACEga,KAAK,EAAE;MACLE,OAAO,EAAE,MAAM;MACfO,UAAU,EAAE,yBAAyB;MACrCC,KAAK,EAAE,KAAK;MACZC,QAAQ,EAAE;IACZ;EACF,CAAC,EACDJ,aAAa,CAAC9Q,KAChB,CAAC,EAAE0Q,YAAY,CAAC;AAClB;AACA,SAASS,2BAA2BA,CAAC;EACnCC;AACF,CAAC,EAAE;EACD,IAAIjV,KAAK,GAAG1H,aAAa,CAAC,CAAC;EAC3B,IAAI2c,aAAa,KAAK,KAAK,CAAC,EAAE;IAC5B,MAAM,IAAInY,KAAK,CAAC,8BAA8B,CAAC;EACjD;EACA,OAAO,eAAgBwW,MAAM,CAAClZ,aAAa,CACzCyZ,+BAA+B,EAC/B;IACEC,cAAc,EAAE,CAACmB,aAAa;IAC9BjV;EACF,CACF,CAAC;AACH;;AAEA;AACA,SAASkV,qBAAqBA,CAACC,OAAO,EAAE;EACtC,MAAMjc,YAAY,GAAG,CAAC,CAAC;EACvB,KAAK,MAAMQ,KAAK,IAAIyb,OAAO,CAACxb,OAAO,EAAE;IACnCyb,uBAAuB,CAAClc,YAAY,EAAEQ,KAAK,CAAC;EAC9C;EACA,OAAOR,YAAY;AACrB;AACA,SAASkc,uBAAuBA,CAAClc,YAAY,EAAES,OAAO,EAAE;EACtDA,OAAO,GAAG0b,KAAK,CAACC,OAAO,CAAC3b,OAAO,CAAC,GAAGA,OAAO,GAAG,CAACA,OAAO,CAAC;EACtD,KAAK,MAAMD,KAAK,IAAIC,OAAO,EAAE;IAC3BT,YAAY,CAACQ,KAAK,CAACI,EAAE,CAAC,GAAG;MACvBsE,KAAK,EAAE1E,KAAK,CAAC0E,KAAK;MAClBC,IAAI,EAAE3E,KAAK,CAAC2E,IAAI;MAChBF,OAAO,EAAEoX;IACX,CAAC;EACH;AACF;AACA,IAAIA,aAAa,GAAGA,CAAA,KAAM,IAAI;;AAE9B;AACA,SAASC,gBAAgBA,CAAC;EACxBC,wBAAwB;EACxBC,2BAA2B;EAC3BC,WAAW;EACXC,KAAK,EAAEC,mBAAmB,GAAGD;AAC/B,CAAC,EAAE;EACD,MAAME,SAAS,GAAGC,MAAM;EACxB,IAAIC,cAAc,GAAG,CAAC;EACtB,OAAO,OAAOlc,EAAE,EAAEuD,IAAI,KAAK;IACzB,IAAI4Y,QAAQ,GAAGH,SAAS,CAACI,gBAAgB,GAAG,CAACJ,SAAS,CAACI,gBAAgB,KAAKJ,SAAS,CAACI,gBAAgB,GAAG,CAAC,CAAC,IAAI,CAAC;IAChH,MAAMC,mBAAmB,GAAGT,2BAA2B,CAAC,CAAC;IACzD,MAAM5G,QAAQ,GAAG,MAAM+G,mBAAmB,CACxC,IAAI1P,OAAO,CAACvL,QAAQ,CAACwL,IAAI,EAAE;MACzBL,IAAI,EAAE,MAAM4P,WAAW,CAACtY,IAAI,EAAE;QAAE8Y;MAAoB,CAAC,CAAC;MACtDrQ,MAAM,EAAE,MAAM;MACdE,OAAO,EAAE;QACPoQ,MAAM,EAAE,kBAAkB;QAC1B,eAAe,EAAEtc;MACnB;IACF,CAAC,CACH,CAAC;IACD,IAAI,CAACgV,QAAQ,CAAC/I,IAAI,EAAE;MAClB,MAAM,IAAIjJ,KAAK,CAAC,kBAAkB,CAAC;IACrC;IACA,MAAMqY,OAAO,GAAG,MAAMM,wBAAwB,CAAC3G,QAAQ,CAAC/I,IAAI,EAAE;MAC5DoQ;IACF,CAAC,CAAC;IACF,IAAIhB,OAAO,CAACkB,IAAI,KAAK,UAAU,EAAE;MAC/B,IAAIlB,OAAO,CAAC9M,MAAM,EAAE;QAClB0N,MAAM,CAACnb,QAAQ,CAACwL,IAAI,GAAG+O,OAAO,CAACva,QAAQ;QACvC;MACF;MACAkb,SAAS,CAACQ,uBAAuB,CAACC,QAAQ,CAACpB,OAAO,CAACva,QAAQ,EAAE;QAC3D5C,OAAO,EAAEmd,OAAO,CAACnd;MACnB,CAAC,CAAC;MACF,OAAOmd,OAAO,CAACqB,YAAY;IAC7B;IACA,IAAIrB,OAAO,CAACkB,IAAI,KAAK,QAAQ,EAAE;MAC7B,MAAM,IAAIvZ,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IACA,IAAIqY,OAAO,CAACsB,QAAQ,EAAE;MACpB3D,MAAM,CAAC4D,eAAe;MACpB;MACA,YAAY;QACV,MAAMD,QAAQ,GAAG,MAAMtB,OAAO,CAACsB,QAAQ;QACvC,IAAI,CAACA,QAAQ,EAAE;QACf,IAAIT,cAAc,GAAGC,QAAQ,IAAIH,SAAS,CAACI,gBAAgB,IAAID,QAAQ,EAAE;UACvED,cAAc,GAAGC,QAAQ;UACzB,IAAIQ,QAAQ,CAACJ,IAAI,KAAK,UAAU,EAAE;YAChC,IAAII,QAAQ,CAACpO,MAAM,EAAE;cACnB0N,MAAM,CAACnb,QAAQ,CAACwL,IAAI,GAAGqQ,QAAQ,CAAC7b,QAAQ;cACxC;YACF;YACAkb,SAAS,CAACQ,uBAAuB,CAACC,QAAQ,CAACE,QAAQ,CAAC7b,QAAQ,EAAE;cAC5D5C,OAAO,EAAEye,QAAQ,CAACze;YACpB,CAAC,CAAC;YACF;UACF;UACA,IAAI2e,SAAS;UACb,KAAK,MAAMjd,KAAK,IAAI+c,QAAQ,CAAC9c,OAAO,EAAE;YACpCmc,SAAS,CAACQ,uBAAuB,CAACM,WAAW,CAC3CD,SAAS,EAAE7c,EAAE,IAAI,IAAI,EACrB,CAAC+c,6BAA6B,CAACnd,KAAK,CAAC,CAAC,EACtC,IACF,CAAC;YACDid,SAAS,GAAGjd,KAAK;UACnB;UACAqc,MAAM,CAACO,uBAAuB,CAACQ,8CAA8C,CAC3E,CAAC,CACH,CAAC;UACDhE,MAAM,CAAC4D,eAAe,CAAC,MAAM;YAC3BX,MAAM,CAACO,uBAAuB,CAACQ,8CAA8C,CAC3E;cACErd,UAAU,EAAE0J,MAAM,CAACe,MAAM,CACvB,CAAC,CAAC,EACF4R,SAAS,CAACQ,uBAAuB,CAACzb,KAAK,CAACpB,UAAU,EAClDgd,QAAQ,CAAChd,UACX,CAAC;cACDsK,MAAM,EAAE0S,QAAQ,CAAC1S,MAAM,GAAGZ,MAAM,CAACe,MAAM,CACrC,CAAC,CAAC,EACF4R,SAAS,CAACQ,uBAAuB,CAACzb,KAAK,CAACkJ,MAAM,EAC9C0S,QAAQ,CAAC1S,MACX,CAAC,GAAG;YACN,CACF,CAAC;UACH,CAAC,CAAC;QACJ;MACF,CACF,CAAC;IACH;IACA,OAAOoR,OAAO,CAACqB,YAAY;EAC7B,CAAC;AACH;AACA,SAASO,uBAAuBA,CAAC;EAC/BlB,mBAAmB;EACnBJ,wBAAwB;EACxBuB,UAAU;EACV7B;AACF,CAAC,EAAE;EACD,MAAMW,SAAS,GAAGC,MAAM;EACxB,IAAID,SAAS,CAACQ,uBAAuB,IAAIR,SAAS,CAACmB,yBAAyB,EAC1E,OAAO;IACL9c,MAAM,EAAE2b,SAAS,CAACQ,uBAAuB;IACzCpd,YAAY,EAAE4c,SAAS,CAACmB;EAC1B,CAAC;EACH,IAAI9B,OAAO,CAACkB,IAAI,KAAK,QAAQ,EAAE,MAAM,IAAIvZ,KAAK,CAAC,sBAAsB,CAAC;EACtEgZ,SAAS,CAACmB,yBAAyB,GAAGnB,SAAS,CAACmB,yBAAyB,IAAI,CAAC,CAAC;EAC/E7B,uBAAuB,CAACU,SAAS,CAACmB,yBAAyB,EAAE9B,OAAO,CAACxb,OAAO,CAAC;EAC7E,IAAI4V,OAAO,GAAG,eAAgB,IAAI8B,GAAG,CAAC,CAAC;EACvC8D,OAAO,CAAC5F,OAAO,EAAElI,OAAO,CAAE6P,KAAK,IAAK;IAClC7f,SAAS,CAAC6f,KAAK,CAACta,QAAQ,EAAE,wBAAwB,CAAC;IACnD,IAAI,CAAC2S,OAAO,CAAChF,GAAG,CAAC2M,KAAK,CAACta,QAAQ,CAAC,EAAE;MAChC2S,OAAO,CAAClD,GAAG,CAAC6K,KAAK,CAACta,QAAQ,EAAE,EAAE,CAAC;IACjC;IACA2S,OAAO,CAACtI,GAAG,CAACiQ,KAAK,CAACta,QAAQ,CAAC,EAAE+I,IAAI,CAACuR,KAAK,CAAC;EAC1C,CAAC,CAAC;EACF,IAAI7d,MAAM,GAAG8b,OAAO,CAACxb,OAAO,CAACwd,WAAW,CAAC,CAACC,QAAQ,EAAE1d,KAAK,KAAK;IAC5D,MAAMG,KAAK,GAAGgd,6BAA6B,CACzCnd,KAAK,EACLyb,OACF,CAAC;IACD,IAAIiC,QAAQ,CAAC9W,MAAM,GAAG,CAAC,EAAE;MACvBzG,KAAK,CAACyE,QAAQ,GAAG8Y,QAAQ;MACzB,IAAIC,eAAe,GAAG9H,OAAO,CAACtI,GAAG,CAACvN,KAAK,CAACI,EAAE,CAAC;MAC3C,IAAIud,eAAe,EAAE;QACnBxd,KAAK,CAACyE,QAAQ,CAACqH,IAAI,CACjB,GAAG0R,eAAe,CAACxa,GAAG,CAAEF,CAAC,IAAKka,6BAA6B,CAACla,CAAC,CAAC,CAChE,CAAC;MACH;IACF;IACA,OAAO,CAAC9C,KAAK,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EACNic,SAAS,CAACQ,uBAAuB,GAAG3f,YAAY,CAAC;IAC/C0C,MAAM;IACN2d,UAAU;IACVrS,QAAQ,EAAEwQ,OAAO,CAACxQ,QAAQ;IAC1B2S,OAAO,EAAE/gB,oBAAoB,CAAC,CAAC;IAC/BqF,aAAa,EAAEoX,gBAAgB,CAAC;MAC9BnY,KAAK,EAAE;QACLpB,UAAU,EAAE0b,OAAO,CAAC1b,UAAU;QAC9B4P,UAAU,EAAE8L,OAAO,CAAC9L,UAAU;QAC9BtF,MAAM,EAAEoR,OAAO,CAACpR;MAClB,CAAC;MACD1K,MAAM;MACN4Z,YAAY,EAAGrZ,OAAO,IAAK;QACzB,IAAIF,KAAK,GAAGyb,OAAO,CAACxb,OAAO,CAAC4d,IAAI,CAAEzO,CAAC,IAAKA,CAAC,CAAChP,EAAE,KAAKF,OAAO,CAAC;QACzDvC,SAAS,CAACqC,KAAK,EAAE,4BAA4B,CAAC;QAC9C,OAAO;UACLM,YAAY,EAAEN,KAAK,CAACM,YAAY;UAChCC,SAAS,EAAEP,KAAK,CAACO,SAAS;UAC1BoZ,kBAAkB,EAAE3Z,KAAK,CAAC8d,sBAAsB,IAAI;QACtD,CAAC;MACH,CAAC;MACD5c,QAAQ,EAAEua,OAAO,CAACva,QAAQ;MAC1B+J,QAAQ,EAAEwQ,OAAO,CAACxQ,QAAQ;MAC1BpL,SAAS,EAAE;IACb,CAAC,CAAC;IACF,MAAMke,uBAAuBA,CAAC;MAAEza,IAAI;MAAEiJ;IAAO,CAAC,EAAE;MAC9C,IAAIyR,eAAe,CAACnN,GAAG,CAACvN,IAAI,CAAC,EAAE;QAC7B;MACF;MACA,MAAM2a,4BAA4B,CAChC,CAAC3a,IAAI,CAAC,EACNyY,wBAAwB,EACxBI,mBAAmB,EACnB5P,MACF,CAAC;IACH,CAAC;IACD;IACA2R,YAAY,EAAEC,6BAA6B,CACzC,MAAM/B,SAAS,CAACQ,uBAAuB,EACvC,IAAI,EACJnB,OAAO,CAACxQ,QAAQ,EAChB8Q,wBAAwB,EACxBI,mBACF;EACF,CAAC,CAAC;EACF,IAAIC,SAAS,CAACQ,uBAAuB,CAACzb,KAAK,CAACid,WAAW,EAAE;IACvDhC,SAAS,CAACiC,mBAAmB,GAAG,IAAI;IACpCjC,SAAS,CAACQ,uBAAuB,CAAC0B,UAAU,CAAC,CAAC;EAChD,CAAC,MAAM;IACLlC,SAAS,CAACiC,mBAAmB,GAAG,KAAK;EACvC;EACA,IAAIE,cAAc,GAAG,KAAK,CAAC;EAC3BnC,SAAS,CAACQ,uBAAuB,CAAC4B,SAAS,CAAC,CAAC;IAAEze,UAAU;IAAE4P;EAAW,CAAC,KAAK;IAC1E,IAAI4O,cAAc,KAAKxe,UAAU,EAAE;MACjCqc,SAAS,CAACI,gBAAgB,GAAG,CAACJ,SAAS,CAACI,gBAAgB,KAAKJ,SAAS,CAACI,gBAAgB,GAAG,CAAC,CAAC,IAAI,CAAC;IACnG;EACF,CAAC,CAAC;EACFJ,SAAS,CAACQ,uBAAuB,CAAC6B,mBAAmB,GAAIC,oBAAoB,IAAK;IAChF,MAAMC,SAAS,GAAGtC,MAAM,CAACO,uBAAuB,CAACjd,MAAM;IACvD,MAAMif,SAAS,GAAG,EAAE;IACpB,SAASC,UAAUA,CAACC,OAAO,EAAE5b,QAAQ,EAAE;MACrC,OAAO4b,OAAO,CAAC3b,GAAG,CAAEhD,KAAK,IAAK;QAC5B,MAAM4e,WAAW,GAAGL,oBAAoB,CAACnR,GAAG,CAACpN,KAAK,CAACC,EAAE,CAAC;QACtD,IAAI2e,WAAW,EAAE;UACf,MAAM;YACJC,WAAW;YACXhb,SAAS;YACTib,YAAY;YACZ7a,gBAAgB;YAChB7D;UACF,CAAC,GAAGwe,WAAW;UACf,MAAM1b,QAAQ,GAAG8Z,6BAA6B,CAAC;YAC7C+B,YAAY,EAAEF,WAAW,CAACE,YAAY;YACtC5e,YAAY,EAAE0e,WAAW,CAAC1e,YAAY;YACtC6e,OAAO,EAAEhf,KAAK,CAACgf,OAAO;YACtBC,YAAY,EAAEjf,KAAK,CAACif,YAAY;YAChCvb,MAAM,EAAE1D,KAAK,CAAC0D,MAAM;YACpBG,SAAS;YACTib,YAAY;YACZ7a,gBAAgB;YAChB7D,SAAS;YACTud,sBAAsB,EAAE3d,KAAK,CAAC2d,sBAAsB;YACpD1d,EAAE,EAAED,KAAK,CAACC,EAAE;YACZmD,KAAK,EAAEpD,KAAK,CAACoD,KAAK;YAClBmB,KAAK,EAAEsa,WAAW,CAACta,KAAK;YACxBC,IAAI,EAAEqa,WAAW,CAACra,IAAI;YACtBzB,QAAQ;YACRI,IAAI,EAAEnD,KAAK,CAACmD,IAAI;YAChBQ,gBAAgB,EAAEkb,WAAW,CAAClb;UAChC,CAAC,CAAC;UACF,IAAI3D,KAAK,CAACyE,QAAQ,EAAE;YAClBvB,QAAQ,CAACuB,QAAQ,GAAGia,UAAU,CAAC1e,KAAK,CAACyE,QAAQ,EAAEzE,KAAK,CAACC,EAAE,CAAC;UAC1D;UACA,OAAOiD,QAAQ;QACjB;QACA,MAAMgc,YAAY,GAAG;UAAE,GAAGlf;QAAM,CAAC;QACjC,IAAIA,KAAK,CAACyE,QAAQ,EAAE;UAClBya,YAAY,CAACza,QAAQ,GAAGia,UAAU,CAAC1e,KAAK,CAACyE,QAAQ,EAAEzE,KAAK,CAACC,EAAE,CAAC;QAC9D;QACA,OAAOif,YAAY;MACrB,CAAC,CAAC;IACJ;IACAT,SAAS,CAAC3S,IAAI,CACZ,GAAG4S,UAAU,CAACF,SAAS,EAAE,KAAK,CAAC,CACjC,CAAC;IACDtC,MAAM,CAACO,uBAAuB,CAAC0C,kBAAkB,CAACV,SAAS,CAAC;EAC9D,CAAC;EACD,OAAO;IACLne,MAAM,EAAE2b,SAAS,CAACQ,uBAAuB;IACzCpd,YAAY,EAAE4c,SAAS,CAACmB;EAC1B,CAAC;AACH;AACA,IAAIgC,qBAAqB,GAAGziB,aAAa,CAAC,CAAC;AAC3C,SAASqhB,6BAA6BA,CAACqB,SAAS,EAAE1e,GAAG,EAAEmK,QAAQ,EAAE8Q,wBAAwB,EAAEI,mBAAmB,EAAE;EAC9G,IAAI+B,YAAY,GAAGzgB,8BAA8B,CAC/C+hB,SAAS,EACRxf,KAAK,IAAK;IACT,IAAIyf,CAAC,GAAGzf,KAAK;IACb,OAAO;MACLO,SAAS,EAAEkf,CAAC,CAACtf,KAAK,CAACI,SAAS;MAC5B2D,eAAe,EAAEub,CAAC,CAACtf,KAAK,CAAC+D,eAAe;MACxC+a,YAAY,EAAEQ,CAAC,CAACtf,KAAK,CAAC8e,YAAY;MAClCjb,SAAS,EAAEyb,CAAC,CAACtf,KAAK,CAAC6D,SAAS;MAC5BC,eAAe,EAAEwb,CAAC,CAACtf,KAAK,CAAC8D,eAAe;MACxCyb,mBAAmB,EAAED,CAAC,CAACtf,KAAK,CAACuf;IAC/B,CAAC;EACH,CAAC;EACD;EACAC,uBAAuB,CAAC5D,wBAAwB,EAAEI,mBAAmB,CAAC,EACtErb,GAAG,EACHmK,QAAQ;EACR;EACA;EACA;EACCjL,KAAK,IAAK;IACT,IAAIyf,CAAC,GAAGzf,KAAK;IACb,OAAOyf,CAAC,CAACtf,KAAK,CAAC8e,YAAY,IAAI,CAACQ,CAAC,CAACtf,KAAK,CAACgf,OAAO;EACjD,CACF,CAAC;EACD,OAAO,MAAOxb,IAAI,IAAKA,IAAI,CAACic,mBAAmB,CAAC,YAAY;IAC1D,IAAIzgB,OAAO,GAAGwE,IAAI,CAACxE,OAAO;IAC1BA,OAAO,CAACwT,GAAG,CAAC4M,qBAAqB,EAAE,EAAE,CAAC;IACtC,IAAIhN,OAAO,GAAG,MAAM2L,YAAY,CAACva,IAAI,CAAC;IACtC,MAAMkc,kBAAkB,GAAG,eAAgB,IAAIlI,GAAG,CAAC,CAAC;IACpD,KAAK,MAAMxX,KAAK,IAAIhB,OAAO,CAACoO,GAAG,CAACgS,qBAAqB,CAAC,EAAE;MACtD,IAAI,CAACM,kBAAkB,CAAChP,GAAG,CAAC1Q,KAAK,CAACC,EAAE,CAAC,EAAE;QACrCyf,kBAAkB,CAAClN,GAAG,CAACxS,KAAK,CAACC,EAAE,EAAE,EAAE,CAAC;MACtC;MACAyf,kBAAkB,CAACtS,GAAG,CAACpN,KAAK,CAACC,EAAE,CAAC,CAAC6L,IAAI,CAAC9L,KAAK,CAAC;IAC9C;IACA,KAAK,MAAMH,KAAK,IAAI2D,IAAI,CAAC1D,OAAO,EAAE;MAChC,MAAM6f,cAAc,GAAGD,kBAAkB,CAACtS,GAAG,CAACvN,KAAK,CAACG,KAAK,CAACC,EAAE,CAAC;MAC7D,IAAI0f,cAAc,EAAE;QAClB,KAAK,MAAMC,QAAQ,IAAID,cAAc,EAAE;UACrCzD,MAAM,CAACO,uBAAuB,CAACM,WAAW,CACxC6C,QAAQ,CAAC7c,QAAQ,IAAI,IAAI,EACzB,CAACia,6BAA6B,CAAC4C,QAAQ,CAAC,CAAC,EACzC,IACF,CAAC;QACH;MACF;IACF;IACA,OAAOxN,OAAO;EAChB,CAAC,CAAC;AACJ;AACA,SAASoN,uBAAuBA,CAAC5D,wBAAwB,EAAEI,mBAAmB,EAAE;EAC9E,OAAO,OAAOxY,IAAI,EAAEsH,QAAQ,EAAE+U,YAAY,KAAK;IAC7C,IAAI;MAAE3U,OAAO;MAAElM;IAAQ,CAAC,GAAGwE,IAAI;IAC/B,IAAIvE,GAAG,GAAGX,cAAc,CAAC4M,OAAO,CAACjM,GAAG,EAAE6L,QAAQ,EAAE,KAAK,CAAC;IACtD,IAAII,OAAO,CAACe,MAAM,KAAK,KAAK,EAAE;MAC5BhN,GAAG,GAAGT,eAAe,CAACS,GAAG,CAAC;MAC1B,IAAI4gB,YAAY,EAAE;QAChB5gB,GAAG,CAACwM,YAAY,CAAC+G,GAAG,CAAC,SAAS,EAAEqN,YAAY,CAAC7J,IAAI,CAAC,GAAG,CAAC,CAAC;MACzD;IACF;IACA,IAAIjB,GAAG,GAAG,MAAMiH,mBAAmB,CACjC,IAAI1P,OAAO,CAACrN,GAAG,EAAE,MAAMpC,iBAAiB,CAACqO,OAAO,CAAC,CACnD,CAAC;IACD,IAAI6J,GAAG,CAACzJ,MAAM,KAAK,GAAG,IAAI,CAACyJ,GAAG,CAAC5I,OAAO,CAACuE,GAAG,CAAC,kBAAkB,CAAC,EAAE;MAC9D,MAAM,IAAI7U,iBAAiB,CAAC,GAAG,EAAE,WAAW,EAAE,IAAI,CAAC;IACrD;IACA2B,SAAS,CAACuX,GAAG,CAAC7I,IAAI,EAAE,4BAA4B,CAAC;IACjD,IAAI;MACF,MAAMoP,OAAO,GAAG,MAAMM,wBAAwB,CAAC7G,GAAG,CAAC7I,IAAI,EAAE;QACvDoQ,mBAAmB,EAAE,KAAK;MAC5B,CAAC,CAAC;MACF,IAAIhB,OAAO,CAACkB,IAAI,KAAK,UAAU,EAAE;QAC/B,OAAO;UACLlR,MAAM,EAAEyJ,GAAG,CAACzJ,MAAM;UAClBmD,IAAI,EAAE;YACJxQ,QAAQ,EAAE;cACRA,QAAQ,EAAEqd,OAAO,CAACva,QAAQ;cAC1ByN,MAAM,EAAE8M,OAAO,CAAC9M,MAAM;cACtBrQ,OAAO,EAAEmd,OAAO,CAACnd,OAAO;cACxB4U,UAAU,EAAE,KAAK;cACjBzH,MAAM,EAAEgQ,OAAO,CAAChQ;YAClB;UACF;QACF,CAAC;MACH;MACA,IAAIgQ,OAAO,CAACkB,IAAI,KAAK,QAAQ,EAAE;QAC7B,MAAM,IAAIvZ,KAAK,CAAC,yBAAyB,CAAC;MAC5C;MACAjE,OAAO,CAACoO,GAAG,CAACgS,qBAAqB,CAAC,CAACtT,IAAI,CAAC,GAAGwP,OAAO,CAACxb,OAAO,CAAC;MAC3D,IAAIsS,OAAO,GAAG;QAAE5S,MAAM,EAAE,CAAC;MAAE,CAAC;MAC5B,MAAMsgB,OAAO,GAAGpiB,gBAAgB,CAACwN,OAAO,CAACe,MAAM,CAAC,GAAG,YAAY,GAAG,YAAY;MAC9E,KAAK,IAAI,CAAClM,OAAO,EAAEiF,KAAK,CAAC,IAAIsE,MAAM,CAACa,OAAO,CAACmR,OAAO,CAACwE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;QACnE1N,OAAO,CAAC5S,MAAM,CAACO,OAAO,CAAC,GAAG;UAAE0O,IAAI,EAAEzJ;QAAM,CAAC;MAC3C;MACA,IAAIsW,OAAO,CAACpR,MAAM,EAAE;QAClB,KAAK,IAAI,CAACnK,OAAO,EAAEoG,KAAK,CAAC,IAAImD,MAAM,CAACa,OAAO,CAACmR,OAAO,CAACpR,MAAM,CAAC,EAAE;UAC3DkI,OAAO,CAAC5S,MAAM,CAACO,OAAO,CAAC,GAAG;YAAEoG;UAAM,CAAC;QACrC;MACF;MACA,OAAO;QAAEmF,MAAM,EAAEyJ,GAAG,CAACzJ,MAAM;QAAEmD,IAAI,EAAE2D;MAAQ,CAAC;IAC9C,CAAC,CAAC,OAAO/E,CAAC,EAAE;MACV,MAAM,IAAIpK,KAAK,CAAC,+BAA+B,CAAC;IAClD;EACF,CAAC;AACH;AACA,SAAS8c,iBAAiBA,CAAC;EACzBnE,wBAAwB;EACxBG,KAAK,EAAEC,mBAAmB,GAAGD,KAAK;EAClCT,OAAO;EACP1a,cAAc,GAAG,OAAO;EACxBuc;AACF,CAAC,EAAE;EACD,IAAI7B,OAAO,CAACkB,IAAI,KAAK,QAAQ,EAAE,MAAM,IAAIvZ,KAAK,CAAC,sBAAsB,CAAC;EACtE,IAAI;IAAE3C,MAAM;IAAEjB;EAAa,CAAC,GAAG4Z,MAAM,CAAC+G,OAAO,CAC3C,MAAM9C,uBAAuB,CAAC;IAC5B5B,OAAO;IACPU,mBAAmB;IACnBmB,UAAU;IACVvB;EACF,CAAC,CAAC,EACF,CAACA,wBAAwB,EAAEN,OAAO,EAAEU,mBAAmB,EAAEmB,UAAU,CACrE,CAAC;EACDlE,MAAM,CAACgH,SAAS,CAAC,MAAM;IACrB7hB,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EACN6a,MAAM,CAACiH,eAAe,CAAC,MAAM;IAC3B,MAAMjE,SAAS,GAAGC,MAAM;IACxB,IAAI,CAACD,SAAS,CAACiC,mBAAmB,EAAE;MAClCjC,SAAS,CAACiC,mBAAmB,GAAG,IAAI;MACpCjC,SAAS,CAACQ,uBAAuB,CAAC0B,UAAU,CAAC,CAAC;IAChD;EACF,CAAC,EAAE,EAAE,CAAC;EACN,IAAI,CAAC9E,SAAS,EAAE8G,WAAW,CAAC,GAAGlH,MAAM,CAACmH,QAAQ,CAAC9f,MAAM,CAACU,KAAK,CAACD,QAAQ,CAAC;EACrEkY,MAAM,CAACiH,eAAe,CACpB,MAAM5f,MAAM,CAAC+d,SAAS,CAAEgC,QAAQ,IAAK;IACnC,IAAIA,QAAQ,CAACtf,QAAQ,KAAKsY,SAAS,EAAE;MACnC8G,WAAW,CAACE,QAAQ,CAACtf,QAAQ,CAAC;IAChC;EACF,CAAC,CAAC,EACF,CAACT,MAAM,EAAE+Y,SAAS,CACpB,CAAC;EACDJ,MAAM,CAACgH,SAAS,CAAC,MAAM;IACrB,IAAIrf,cAAc,KAAK,MAAM;IAAI;IACjCsb,MAAM,CAACoE,SAAS,EAAEC,UAAU,EAAEC,QAAQ,KAAK,IAAI,EAAE;MAC/C;IACF;IACA,SAASC,eAAeA,CAACC,EAAE,EAAE;MAC3B,IAAIvd,IAAI,GAAGud,EAAE,CAACC,OAAO,KAAK,MAAM,GAAGD,EAAE,CAACE,YAAY,CAAC,QAAQ,CAAC,GAAGF,EAAE,CAACE,YAAY,CAAC,MAAM,CAAC;MACtF,IAAI,CAACzd,IAAI,EAAE;QACT;MACF;MACA,IAAI0H,QAAQ,GAAG6V,EAAE,CAACC,OAAO,KAAK,GAAG,GAAGD,EAAE,CAAC7V,QAAQ,GAAG,IAAI1L,GAAG,CAACgE,IAAI,EAAE+Y,MAAM,CAACnb,QAAQ,CAAC8f,MAAM,CAAC,CAAChW,QAAQ;MAChG,IAAI,CAACgT,eAAe,CAACnN,GAAG,CAAC7F,QAAQ,CAAC,EAAE;QAClCiW,SAAS,CAAC7K,GAAG,CAACpL,QAAQ,CAAC;MACzB;IACF;IACA,eAAekW,YAAYA,CAAA,EAAG;MAC5BC,QAAQ,CAACC,gBAAgB,CAAC,uCAAuC,CAAC,CAACzT,OAAO,CAACiT,eAAe,CAAC;MAC3F,IAAI9K,KAAK,GAAG6F,KAAK,CAAC0F,IAAI,CAACJ,SAAS,CAACvX,IAAI,CAAC,CAAC,CAAC,CAAC+I,MAAM,CAAEnP,IAAI,IAAK;QACxD,IAAI0a,eAAe,CAACnN,GAAG,CAACvN,IAAI,CAAC,EAAE;UAC7B2d,SAAS,CAACnV,MAAM,CAACxI,IAAI,CAAC;UACtB,OAAO,KAAK;QACd;QACA,OAAO,IAAI;MACb,CAAC,CAAC;MACF,IAAIwS,KAAK,CAAClP,MAAM,KAAK,CAAC,EAAE;QACtB;MACF;MACA,IAAI;QACF,MAAMqX,4BAA4B,CAChCnI,KAAK,EACLiG,wBAAwB,EACxBI,mBACF,CAAC;MACH,CAAC,CAAC,OAAO3O,CAAC,EAAE;QACVZ,OAAO,CAACtG,KAAK,CAAC,kCAAkC,EAAEkH,CAAC,CAAC;MACtD;IACF;IACA,IAAI8T,qBAAqB,GAAGC,QAAQ,CAACL,YAAY,EAAE,GAAG,CAAC;IACvDA,YAAY,CAAC,CAAC;IACd,IAAIM,QAAQ,GAAG,IAAIC,gBAAgB,CAAC,MAAMH,qBAAqB,CAAC,CAAC,CAAC;IAClEE,QAAQ,CAACE,OAAO,CAACP,QAAQ,CAACQ,eAAe,EAAE;MACzCC,OAAO,EAAE,IAAI;MACbC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,IAAI;MAChBC,eAAe,EAAE,CAAC,eAAe,EAAE,MAAM,EAAE,QAAQ;IACrD,CAAC,CAAC;EACJ,CAAC,EAAE,CAAChhB,cAAc,EAAEgb,wBAAwB,EAAEI,mBAAmB,CAAC,CAAC;EACnE,MAAM6F,gBAAgB,GAAG;IACvBpiB,MAAM,EAAE;MACN;MACA;MACA4C,aAAa,EAAE,KAAK;MACpBD,6BAA6B,EAAE;IACjC,CAAC;IACD1C,SAAS,EAAE,KAAK;IAChBiB,GAAG,EAAE,IAAI;IACTrB,WAAW,EAAE,EAAE;IACfF,QAAQ,EAAE;MACRI,MAAM,EAAE,CAAC,CAAC;MACViD,OAAO,EAAE,GAAG;MACZxD,GAAG,EAAE,EAAE;MACPqD,KAAK,EAAE;QACLE,MAAM,EAAE,EAAE;QACVD,OAAO,EAAE;MACX;IACF,CAAC;IACD3B,cAAc,EAAE;MAAE8B,IAAI,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAc,CAAC;IAC7DtD;EACF,CAAC;EACD,OAAO,eAAgB4Z,MAAM,CAAC1Y,aAAa,CAACtE,gBAAgB,CAACwE,QAAQ,EAAE;IAAEC,KAAK,EAAE;EAAK,CAAC,EAAE,eAAgBuY,MAAM,CAAC1Y,aAAa,CAACmZ,4BAA4B,EAAE;IAAE3Y,QAAQ,EAAEsY;EAAU,CAAC,EAAE,eAAgBJ,MAAM,CAAC1Y,aAAa,CAACzE,gBAAgB,CAAC2E,QAAQ,EAAE;IAAEC,KAAK,EAAEmhB;EAAiB,CAAC,EAAE,eAAgB5I,MAAM,CAAC1Y,aAAa,CAACnE,cAAc,EAAE;IAAEkE,MAAM;IAAEwhB,SAAS,EAAE5I,QAAQ,CAAC4I;EAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AACrX;AACA,SAAS9E,6BAA6BA,CAACnd,KAAK,EAAEyb,OAAO,EAAE;EACrD,IAAIyG,cAAc,GAAGzG,OAAO,IAAIzb,KAAK,CAACI,EAAE,IAAIqb,OAAO,CAAC1b,UAAU;EAC9D,IAAI2X,WAAW,GAAG+D,OAAO,EAAE1b,UAAU,CAACC,KAAK,CAACI,EAAE,CAAC;EAC/C,IAAI+hB,eAAe,GAAG1G,OAAO,EAAEpR,MAAM,IAAIrK,KAAK,CAACI,EAAE,IAAIqb,OAAO,CAACpR,MAAM;EACnE,IAAI+X,YAAY,GAAG3G,OAAO,EAAEpR,MAAM,GAAGrK,KAAK,CAACI,EAAE,CAAC;EAC9C,IAAIiiB,kBAAkB,GAAGriB,KAAK,CAACM,YAAY,EAAEc,OAAO,KAAK,IAAI,IAAI,CAACpB,KAAK,CAACO,SAAS;EAAI;EACrF;EACA;EACAP,KAAK,CAACif,YAAY,IAAI,CAACjf,KAAK,CAACmf,OAAO;EACpCxhB,SAAS,CAAC0e,MAAM,CAACkB,yBAAyB,CAAC;EAC3C7B,uBAAuB,CAACW,MAAM,CAACkB,yBAAyB,EAAEvd,KAAK,CAAC;EAChE,IAAIsiB,SAAS,GAAG;IACdliB,EAAE,EAAEJ,KAAK,CAACI,EAAE;IACZ+e,OAAO,EAAEnf,KAAK,CAACmf,OAAO;IACtBC,YAAY,EAAEpf,KAAK,CAACof,YAAY;IAChCvb,MAAM,EAAE7D,KAAK,CAAC6D,MAAM;IACpBO,gBAAgB,EAAEpE,KAAK,CAACoE,gBAAgB;IACxC0Z,sBAAsB,EAAE9d,KAAK,CAAC8d,sBAAsB;IACpDva,KAAK,EAAEvD,KAAK,CAACuD,KAAK;IAClBK,MAAM,EAAE5D,KAAK,CAACM,YAAY,GAAG,OAAOqD,IAAI,EAAE4e,WAAW,KAAK;MACxD,IAAI;QACF,IAAIxZ,MAAM,GAAG,MAAM/I,KAAK,CAACM,YAAY,CAAC;UACpC,GAAGqD,IAAI;UACP6e,YAAY,EAAEA,CAAA,KAAM;YAClBC,+BAA+B,CAC7B,QAAQ,EACRziB,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACO,SACR,CAAC;YACD,IAAI8hB,kBAAkB,EAAE;cACtB,IAAIH,cAAc,EAAE;gBAClB,OAAOxK,WAAW;cACpB;cACA,IAAIyK,eAAe,EAAE;gBACnB,MAAMC,YAAY;cACpB;YACF;YACA,OAAOM,eAAe,CAACH,WAAW,CAAC;UACrC;QACF,CAAC,CAAC;QACF,OAAOxZ,MAAM;MACf,CAAC,SAAS;QACRsZ,kBAAkB,GAAG,KAAK;MAC5B;IACF,CAAC;IACC;IACA;IACA,CAACpM,CAAC,EAAEsM,WAAW,KAAKG,eAAe,CAACH,WAAW,CAChD;IACD7e,MAAM,EAAE1D,KAAK,CAACkf,YAAY,GAAG,CAACvb,IAAI,EAAE4e,WAAW,KAAKviB,KAAK,CAACkf,YAAY,CAAC;MACrE,GAAGvb,IAAI;MACPgf,YAAY,EAAE,MAAAA,CAAA,KAAY;QACxBF,+BAA+B,CAC7B,QAAQ,EACRziB,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACO,SACR,CAAC;QACD,OAAO,MAAMmiB,eAAe,CAACH,WAAW,CAAC;MAC3C;IACF,CAAC,CAAC,GAAGviB,KAAK,CAACgE,SAAS,GAAG,CAACiS,CAAC,EAAEsM,WAAW,KAAKG,eAAe,CAACH,WAAW,CAAC,GAAG,MAAM;MAC9E,MAAMpkB,oBAAoB,CAAC,QAAQ,EAAE6B,KAAK,CAACI,EAAE,CAAC;IAChD,CAAC;IACDkD,IAAI,EAAEtD,KAAK,CAACsD,IAAI;IAChBQ,gBAAgB,EAAE9D,KAAK,CAAC8D,gBAAgB;IACxC;IACA;IACAvD,SAAS,EAAE,IAAI;IACf2D,eAAe,EAAElE,KAAK,CAACM,YAAY,IAAI,IAAI;IAC3C0D,SAAS,EAAEhE,KAAK,CAACgE,SAAS;IAC1BC,eAAe,EAAEjE,KAAK,CAACkf,YAAY,IAAI,IAAI;IAC3CQ,mBAAmB,EAAE1f,KAAK,CAAC8D,gBAAgB,IAAI;EACjD,CAAC;EACD,IAAI,OAAOwe,SAAS,CAAC1e,MAAM,KAAK,UAAU,EAAE;IAC1C0e,SAAS,CAAC1e,MAAM,CAACxC,OAAO,GAAG5C,wBAAwB,CACjDwB,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACM,YAAY,EAClBN,KAAK,CAACO,SAAS,EACf,KACF,CAAC;EACH;EACA,OAAO+hB,SAAS;AAClB;AACA,SAASI,eAAeA,CAACH,WAAW,EAAE;EACpC5kB,SAAS,CAAC,OAAO4kB,WAAW,KAAK,UAAU,EAAE,+BAA+B,CAAC;EAC7E,OAAOA,WAAW,CAAC,CAAC;AACtB;AACA,SAASE,+BAA+BA,CAAC9F,IAAI,EAAEzc,OAAO,EAAE0iB,UAAU,EAAE;EAClE,IAAI,CAACA,UAAU,EAAE;IACf,IAAIC,EAAE,GAAGlG,IAAI,KAAK,QAAQ,GAAG,gBAAgB,GAAG,gBAAgB;IAChE,IAAImG,GAAG,GAAG,0BAA0BD,EAAE,2CAA2ClG,IAAI,eAAezc,OAAO,IAAI;IAC/G0M,OAAO,CAACtG,KAAK,CAACwc,GAAG,CAAC;IAClB,MAAM,IAAI9mB,iBAAiB,CAAC,GAAG,EAAE,aAAa,EAAE,IAAIoH,KAAK,CAAC0f,GAAG,CAAC,EAAE,IAAI,CAAC;EACvE;AACF;AACA,IAAI7B,SAAS,GAAG,eAAgB,IAAItQ,GAAG,CAAC,CAAC;AACzC,IAAIoS,sBAAsB,GAAG,GAAG;AAChC,IAAI/E,eAAe,GAAG,eAAgB,IAAIrN,GAAG,CAAC,CAAC;AAC/C,IAAIqS,SAAS,GAAG,IAAI;AACpB,SAASC,cAAcA,CAACnN,KAAK,EAAE;EAC7B,IAAIA,KAAK,CAAClP,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,IAAI;EACb;EACA,IAAIkP,KAAK,CAAClP,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,IAAItH,GAAG,CAAC,GAAGwW,KAAK,CAAC,CAAC,CAAC,WAAW,EAAEuG,MAAM,CAACnb,QAAQ,CAAC8f,MAAM,CAAC;EAChE;EACA,MAAM5E,SAAS,GAAGC,MAAM;EACxB,IAAIpR,QAAQ,GAAG,CAACmR,SAAS,CAACQ,uBAAuB,CAAC3R,QAAQ,IAAI,EAAE,EAAE3M,OAAO,CACvE,UAAU,EACV,EACF,CAAC;EACD,IAAIc,GAAG,GAAG,IAAIE,GAAG,CAAC,GAAG2L,QAAQ,YAAY,EAAEoR,MAAM,CAACnb,QAAQ,CAAC8f,MAAM,CAAC;EAClElL,KAAK,CAACoN,IAAI,CAAC,CAAC,CAACvV,OAAO,CAAErK,IAAI,IAAKlE,GAAG,CAACwM,YAAY,CAACO,MAAM,CAAC,GAAG,EAAE7I,IAAI,CAAC,CAAC;EAClE,OAAOlE,GAAG;AACZ;AACA,eAAe6e,4BAA4BA,CAACnI,KAAK,EAAEiG,wBAAwB,EAAEI,mBAAmB,EAAE5P,MAAM,EAAE;EACxG,IAAInN,GAAG,GAAG6jB,cAAc,CAACnN,KAAK,CAAC;EAC/B,IAAI1W,GAAG,IAAI,IAAI,EAAE;IACf;EACF;EACA,IAAIA,GAAG,CAAC0J,QAAQ,CAAC,CAAC,CAAClC,MAAM,GAAGoc,SAAS,EAAE;IACrC/B,SAAS,CAACkC,KAAK,CAAC,CAAC;IACjB;EACF;EACA,IAAI/N,QAAQ,GAAG,MAAM+G,mBAAmB,CAAC,IAAI1P,OAAO,CAACrN,GAAG,EAAE;IAAEmN;EAAO,CAAC,CAAC,CAAC;EACtE,IAAI,CAAC6I,QAAQ,CAAC/I,IAAI,IAAI+I,QAAQ,CAAC3J,MAAM,GAAG,GAAG,IAAI2J,QAAQ,CAAC3J,MAAM,IAAI,GAAG,EAAE;IACrE,MAAM,IAAIrI,KAAK,CAAC,mDAAmD,CAAC;EACtE;EACA,IAAIqY,OAAO,GAAG,MAAMM,wBAAwB,CAAC3G,QAAQ,CAAC/I,IAAI,EAAE;IAC1DoQ,mBAAmB,EAAE,KAAK;EAC5B,CAAC,CAAC;EACF,IAAIhB,OAAO,CAACkB,IAAI,KAAK,UAAU,EAAE;IAC/B,MAAM,IAAIvZ,KAAK,CAAC,wBAAwB,CAAC;EAC3C;EACA0S,KAAK,CAACnI,OAAO,CAAEyV,CAAC,IAAKC,cAAc,CAACD,CAAC,EAAEpF,eAAe,CAAC,CAAC;EACxDvC,OAAO,CAAC5F,OAAO,CAAClI,OAAO,CAAEyV,CAAC,IAAK;IAC7B/G,MAAM,CAACO,uBAAuB,CAACM,WAAW,CACxCkG,CAAC,CAAClgB,QAAQ,IAAI,IAAI,EAClB,CAACia,6BAA6B,CAACiG,CAAC,CAAC,CACnC,CAAC;EACH,CAAC,CAAC;AACJ;AACA,SAASC,cAAcA,CAAC/f,IAAI,EAAEggB,KAAK,EAAE;EACnC,IAAIA,KAAK,CAACC,IAAI,IAAIR,sBAAsB,EAAE;IACxC,IAAIS,KAAK,GAAGF,KAAK,CAAC5V,MAAM,CAAC,CAAC,CAAC+V,IAAI,CAAC,CAAC,CAAC5iB,KAAK;IACvCyiB,KAAK,CAACxX,MAAM,CAAC0X,KAAK,CAAC;EACrB;EACAF,KAAK,CAAClN,GAAG,CAAC9S,IAAI,CAAC;AACjB;AACA,SAASie,QAAQA,CAACmC,QAAQ,EAAEC,IAAI,EAAE;EAChC,IAAItQ,SAAS;EACb,OAAO,CAAC,GAAG1P,IAAI,KAAK;IAClB0Y,MAAM,CAAC5I,YAAY,CAACJ,SAAS,CAAC;IAC9BA,SAAS,GAAGgJ,MAAM,CAAC/I,UAAU,CAAC,MAAMoQ,QAAQ,CAAC,GAAG/f,IAAI,CAAC,EAAEggB,IAAI,CAAC;EAC9D,CAAC;AACH;;AAEA;AACA,OAAO,KAAKC,MAAM,MAAM,OAAO;;AAE/B;AACA,IAAIC,QAAQ,GAAG,IAAI7e,WAAW,CAAC,CAAC;AAChC,IAAI8e,OAAO,GAAG,gBAAgB;AAC9B,SAASC,gBAAgBA,CAACC,SAAS,EAAE;EACnC,IAAIC,OAAO,GAAG,IAAItiB,WAAW,CAAC,CAAC;EAC/B,IAAIuiB,wBAAwB;EAC5B,IAAIC,iBAAiB,GAAG,IAAIC,OAAO,CAChCC,OAAO,IAAKH,wBAAwB,GAAGG,OAC1C,CAAC;EACD,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAIC,OAAO,GAAG,IAAI;EAClB,SAASC,mBAAmBA,CAAClW,UAAU,EAAE;IACvC,KAAK,IAAImW,KAAK,IAAIH,QAAQ,EAAE;MAC1B,IAAII,GAAG,GAAGV,OAAO,CAACW,MAAM,CAACF,KAAK,EAAE;QAAEtW,MAAM,EAAE;MAAK,CAAC,CAAC;MACjD,IAAIuW,GAAG,CAAC/P,QAAQ,CAACkP,OAAO,CAAC,EAAE;QACzBa,GAAG,GAAGA,GAAG,CAAC1e,KAAK,CAAC,CAAC,EAAE,CAAC6d,OAAO,CAACld,MAAM,CAAC;MACrC;MACA2H,UAAU,CAACC,OAAO,CAACqV,QAAQ,CAACvmB,MAAM,CAACqnB,GAAG,CAAC,CAAC;IAC1C;IACAJ,QAAQ,CAAC3d,MAAM,GAAG,CAAC;IACnB4d,OAAO,GAAG,IAAI;EAChB;EACA,OAAO,IAAIK,eAAe,CAAC;IACzBC,SAASA,CAACJ,KAAK,EAAEnW,UAAU,EAAE;MAC3BgW,QAAQ,CAACtY,IAAI,CAACyY,KAAK,CAAC;MACpB,IAAIF,OAAO,EAAE;QACX;MACF;MACAA,OAAO,GAAGlR,UAAU,CAAC,YAAY;QAC/BmR,mBAAmB,CAAClW,UAAU,CAAC;QAC/B,IAAI,CAAC+V,UAAU,EAAE;UACfA,UAAU,GAAG,IAAI;UACjBS,cAAc,CAACf,SAAS,EAAEzV,UAAU,CAAC,CAACyW,KAAK,CAAEhT,GAAG,IAAKzD,UAAU,CAACjI,KAAK,CAAC0L,GAAG,CAAC,CAAC,CAACiT,IAAI,CAACf,wBAAwB,CAAC;QAC5G;MACF,CAAC,EAAE,CAAC,CAAC;IACP,CAAC;IACD,MAAMgB,KAAKA,CAAC3W,UAAU,EAAE;MACtB,MAAM4V,iBAAiB;MACvB,IAAIK,OAAO,EAAE;QACX/Q,YAAY,CAAC+Q,OAAO,CAAC;QACrBC,mBAAmB,CAAClW,UAAU,CAAC;MACjC;MACAA,UAAU,CAACC,OAAO,CAACqV,QAAQ,CAACvmB,MAAM,CAAC,gBAAgB,CAAC,CAAC;IACvD;EACF,CAAC,CAAC;AACJ;AACA,eAAeynB,cAAcA,CAACf,SAAS,EAAEzV,UAAU,EAAE;EACnD,IAAI0V,OAAO,GAAG,IAAItiB,WAAW,CAAC,OAAO,EAAE;IAAEwjB,KAAK,EAAE;EAAK,CAAC,CAAC;EACvD,MAAM3jB,MAAM,GAAGwiB,SAAS,CAACviB,SAAS,CAAC,CAAC;EACpC,IAAI;IACF,IAAI2jB,IAAI;IACR,OAAO,CAACA,IAAI,GAAG,MAAM5jB,MAAM,CAAC4jB,IAAI,CAAC,CAAC,KAAK,CAACA,IAAI,CAACC,IAAI,EAAE;MACjD,MAAMX,KAAK,GAAGU,IAAI,CAACvkB,KAAK;MACxB,IAAI;QACFykB,UAAU,CACR7c,IAAI,CAACC,SAAS,CAACub,OAAO,CAACW,MAAM,CAACF,KAAK,EAAE;UAAEtW,MAAM,EAAE;QAAK,CAAC,CAAC,CAAC,EACvDG,UACF,CAAC;MACH,CAAC,CAAC,OAAOyD,GAAG,EAAE;QACZ,IAAIuT,MAAM,GAAG9c,IAAI,CAACC,SAAS,CAAChD,IAAI,CAACC,MAAM,CAAC6f,aAAa,CAAC,GAAGd,KAAK,CAAC,CAAC,CAAC;QACjEY,UAAU,CACR,wBAAwBC,MAAM,2BAA2B,EACzDhX,UACF,CAAC;MACH;IACF;EACF,CAAC,SAAS;IACR/M,MAAM,CAACikB,WAAW,CAAC,CAAC;EACtB;EACA,IAAIC,SAAS,GAAGzB,OAAO,CAACW,MAAM,CAAC,CAAC;EAChC,IAAIc,SAAS,CAAC9e,MAAM,EAAE;IACpB0e,UAAU,CAAC7c,IAAI,CAACC,SAAS,CAACgd,SAAS,CAAC,EAAEnX,UAAU,CAAC;EACnD;AACF;AACA,SAAS+W,UAAUA,CAACZ,KAAK,EAAEnW,UAAU,EAAE;EACrCA,UAAU,CAACC,OAAO,CAChBqV,QAAQ,CAACvmB,MAAM,CACb,WAAWqoB,YAAY,CACrB,kCAAkCjB,KAAK,GACzC,CAAC,WACH,CACF,CAAC;AACH;AACA,SAASiB,YAAYA,CAACC,MAAM,EAAE;EAC5B,OAAOA,MAAM,CAACtnB,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAACA,OAAO,CAAC,eAAe,EAAE,QAAQ,CAAC;AAC7E;;AAEA;AACA,IAAIunB,SAAS,GAAG,KAAK;AACrB,IAAIC,OAAO,GAAGlC,MAAM,CAACiC,SAAS,CAAC;AAC/B,SAASE,OAAOA,CAACC,OAAO,EAAE;EACxB,IAAIF,OAAO,EAAE;IACX,OAAOA,OAAO,CAACE,OAAO,CAAC;EACzB;EACA,MAAM,IAAI5iB,KAAK,CAAC,sDAAsD,CAAC;AACzE;AACA,eAAe6iB,qBAAqBA,CAAC;EACnC5a,OAAO;EACP6a,WAAW;EACXnK,wBAAwB;EACxBoK,UAAU;EACV/kB,OAAO,GAAG;AACZ,CAAC,EAAE;EACD,MAAMhC,GAAG,GAAG,IAAIE,GAAG,CAAC+L,OAAO,CAACjM,GAAG,CAAC;EAChC,MAAMgnB,aAAa,GAAGC,oBAAoB,CAACjnB,GAAG,CAAC;EAC/C,MAAMknB,qBAAqB,GAAGF,aAAa,IAAIG,iBAAiB,CAACnnB,GAAG,CAAC,IAAIiM,OAAO,CAACiB,OAAO,CAACuE,GAAG,CAAC,eAAe,CAAC;EAC7G,MAAM2V,cAAc,GAAG,MAAMN,WAAW,CAAC7a,OAAO,CAAC;EACjD,IAAIib,qBAAqB,IAAIE,cAAc,CAACla,OAAO,CAACiB,GAAG,CAAC,uBAAuB,CAAC,KAAK,MAAM,EAAE;IAC3F,OAAOiZ,cAAc;EACvB;EACA,IAAI,CAACA,cAAc,CAACna,IAAI,EAAE;IACxB,MAAM,IAAIjJ,KAAK,CAAC,iCAAiC,CAAC;EACpD;EACA,MAAMqjB,sBAAsB,GAAGD,cAAc,CAACE,KAAK,CAAC,CAAC;EACrD,IAAIC,eAAe,GAAG,IAAI;EAC1B,IAAIvlB,OAAO,EAAE;IACXulB,eAAe,GAAGH,cAAc,CAACE,KAAK,CAAC,CAAC;EAC1C;EACA,MAAMra,IAAI,GAAGma,cAAc,CAACna,IAAI;EAChC,IAAIua,MAAM;EACV,IAAIC,iBAAiB,GAAG,EAAE;EAC1B,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI,CAACF,MAAM,EAAE;MACXA,MAAM,GAAG,EAAE;MACX,OAAOva,IAAI,CAAC0a,WAAW,CACrB,IAAIlC,eAAe,CAAC;QAClBC,SAASA,CAACJ,KAAK,EAAEnW,UAAU,EAAE;UAC3BqY,MAAM,CAAC3a,IAAI,CAACyY,KAAK,CAAC;UAClBnW,UAAU,CAACC,OAAO,CAACkW,KAAK,CAAC;UACzBmC,iBAAiB,CAAClZ,OAAO,CAAEqZ,CAAC,IAAKA,CAAC,CAACxY,OAAO,CAACkW,KAAK,CAAC,CAAC;QACpD,CAAC;QACDQ,KAAKA,CAAA,EAAG;UACN2B,iBAAiB,CAAClZ,OAAO,CAAEqZ,CAAC,IAAKA,CAAC,CAACvY,KAAK,CAAC,CAAC,CAAC;UAC3CoY,iBAAiB,GAAG,EAAE;QACxB;MACF,CAAC,CACH,CAAC;IACH;IACA,OAAO,IAAIxY,cAAc,CAAC;MACxBC,KAAKA,CAACC,UAAU,EAAE;QAChBqY,MAAM,CAACjZ,OAAO,CAAE+W,KAAK,IAAKnW,UAAU,CAACC,OAAO,CAACkW,KAAK,CAAC,CAAC;QACpDmC,iBAAiB,CAAC5a,IAAI,CAACsC,UAAU,CAAC;MACpC;IACF,CAAC,CAAC;EACJ,CAAC;EACD,MAAM0Y,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,OAAOlL,wBAAwB,CAAC+K,YAAY,CAAC,CAAC,CAAC;EACjD,CAAC;EACD,IAAI;IACF,IAAI,CAACL,sBAAsB,CAACpa,IAAI,EAAE;MAChC,MAAM,IAAIjJ,KAAK,CAAC,iCAAiC,CAAC;IACpD;IACA,MAAMqY,OAAO,GAAG,MAAMM,wBAAwB,CAC5C0K,sBAAsB,CAACpa,IACzB,CAAC;IACD,IAAIma,cAAc,CAAC/a,MAAM,KAAKjP,4BAA4B,IAAIif,OAAO,CAACkB,IAAI,KAAK,UAAU,EAAE;MACzF,MAAMrM,QAAQ,GAAG,IAAIN,OAAO,CAACwW,cAAc,CAACla,OAAO,CAAC;MACpDgE,QAAQ,CAACxE,MAAM,CAAC,kBAAkB,CAAC;MACnCwE,QAAQ,CAACxE,MAAM,CAAC,gBAAgB,CAAC;MACjCwE,QAAQ,CAACxE,MAAM,CAAC,cAAc,CAAC;MAC/BwE,QAAQ,CAACxE,MAAM,CAAC,kBAAkB,CAAC;MACnCwE,QAAQ,CAACqC,GAAG,CAAC,UAAU,EAAE8I,OAAO,CAACva,QAAQ,CAAC;MAC1C,OAAO,IAAIwK,QAAQ,CAACib,eAAe,EAAEta,IAAI,IAAI,EAAE,EAAE;QAC/CC,OAAO,EAAEgE,QAAQ;QACjB7E,MAAM,EAAEgQ,OAAO,CAAChQ,MAAM;QACtBmI,UAAU,EAAE4S,cAAc,CAAC5S;MAC7B,CAAC,CAAC;IACJ;IACA,MAAMsT,IAAI,GAAG,MAAMf,UAAU,CAACc,UAAU,CAAC;IACzC,MAAM3a,OAAO,GAAG,IAAI0D,OAAO,CAACwW,cAAc,CAACla,OAAO,CAAC;IACnDA,OAAO,CAACqG,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC;IACxC,IAAI,CAACvR,OAAO,EAAE;MACZ,OAAO,IAAIsK,QAAQ,CAACwb,IAAI,EAAE;QACxBzb,MAAM,EAAE+a,cAAc,CAAC/a,MAAM;QAC7Ba;MACF,CAAC,CAAC;IACJ;IACA,IAAI,CAACqa,eAAe,EAAEta,IAAI,EAAE;MAC1B,MAAM,IAAIjJ,KAAK,CAAC,iCAAiC,CAAC;IACpD;IACA,MAAM+jB,KAAK,GAAGD,IAAI,CAACH,WAAW,CAAChD,gBAAgB,CAAC4C,eAAe,CAACta,IAAI,CAAC,CAAC;IACtE,OAAO,IAAIX,QAAQ,CAACyb,KAAK,EAAE;MACzB1b,MAAM,EAAE+a,cAAc,CAAC/a,MAAM;MAC7Ba;IACF,CAAC,CAAC;EACJ,CAAC,CAAC,OAAO8a,MAAM,EAAE;IACf,IAAIA,MAAM,YAAY1b,QAAQ,EAAE;MAC9B,OAAO0b,MAAM;IACf;IACA,MAAMA,MAAM;EACd;AACF;AACA,SAASC,eAAeA,CAAC;EAAEJ;AAAW,CAAC,EAAE;EACvC,MAAMxL,OAAO,GAAGsK,OAAO,CAACkB,UAAU,CAAC,CAAC,CAAC;EACrC,IAAIxL,OAAO,CAACkB,IAAI,KAAK,UAAU,EAAE;IAC/B,MAAM,IAAIjR,QAAQ,CAAC,IAAI,EAAE;MACvBD,MAAM,EAAEgQ,OAAO,CAAChQ,MAAM;MACtBa,OAAO,EAAE;QACPgb,QAAQ,EAAE7L,OAAO,CAACva;MACpB;IACF,CAAC,CAAC;EACJ;EACA,IAAIua,OAAO,CAACkB,IAAI,KAAK,QAAQ,EAAE,OAAO,IAAI;EAC1C,IAAI4K,iBAAiB,GAAG;IAAE,GAAG9L,OAAO,CAAC1b;EAAW,CAAC;EACjD,KAAK,MAAMC,KAAK,IAAIyb,OAAO,CAACxb,OAAO,EAAE;IACnC,IAAIzB,wBAAwB,CAC1BwB,KAAK,CAACI,EAAE,EACRJ,KAAK,CAACM,YAAY,EAClBN,KAAK,CAACO,SAAS,EACf,KACF,CAAC,KAAKP,KAAK,CAAC8d,sBAAsB,IAAI,CAAC9d,KAAK,CAACO,SAAS,CAAC,EAAE;MACvD,OAAOgnB,iBAAiB,CAACvnB,KAAK,CAACI,EAAE,CAAC;IACpC;EACF;EACA,MAAMjB,OAAO,GAAG;IACdwQ,UAAU,EAAE8L,OAAO,CAAC9L,UAAU;IAC9BD,aAAa,EAAE,CAAC,CAAC;IACjBzE,QAAQ,EAAEwQ,OAAO,CAACxQ,QAAQ;IAC1BZ,MAAM,EAAEoR,OAAO,CAACpR,MAAM;IACtBtK,UAAU,EAAEwnB,iBAAiB;IAC7B3X,aAAa,EAAE,CAAC,CAAC;IACjB1O,QAAQ,EAAEua,OAAO,CAACva,QAAQ;IAC1B6Q,UAAU,EAAE,GAAG;IACf9R,OAAO,EAAEwb,OAAO,CAACxb,OAAO,CAACkD,GAAG,CAAEnD,KAAK,KAAM;MACvCkL,MAAM,EAAElL,KAAK,CAACkL,MAAM;MACpBF,QAAQ,EAAEhL,KAAK,CAACgL,QAAQ;MACxBwc,YAAY,EAAExnB,KAAK,CAACwnB,YAAY;MAChCrnB,KAAK,EAAE;QACLC,EAAE,EAAEJ,KAAK,CAACI,EAAE;QACZsD,MAAM,EAAE1D,KAAK,CAACgE,SAAS,IAAI,CAAC,CAAChE,KAAK,CAACkf,YAAY;QAC/Crb,MAAM,EAAE7D,KAAK,CAAC6D,MAAM;QACpBO,gBAAgB,EAAEpE,KAAK,CAACoE,gBAAgB;QACxCR,MAAM,EAAE5D,KAAK,CAACO,SAAS,IAAI,CAAC,CAACP,KAAK,CAACM,YAAY;QAC/CiD,KAAK,EAAEvD,KAAK,CAACuD,KAAK;QAClBD,IAAI,EAAEtD,KAAK,CAACsD,IAAI;QAChBQ,gBAAgB,EAAE9D,KAAK,CAAC8D;MAC1B;IACF,CAAC,CAAC;EACJ,CAAC;EACD,MAAMrD,MAAM,GAAGrD,kBAAkB,CAC/Bqe,OAAO,CAACxb,OAAO,CAACwd,WAAW,CAAC,CAACC,QAAQ,EAAE1d,KAAK,KAAK;IAC/C,MAAMG,KAAK,GAAG;MACZC,EAAE,EAAEJ,KAAK,CAACI,EAAE;MACZsD,MAAM,EAAE1D,KAAK,CAACgE,SAAS,IAAI,CAAC,CAAChE,KAAK,CAACkf,YAAY;MAC/CC,OAAO,EAAEnf,KAAK,CAACmf,OAAO;MACtBC,YAAY,EAAEpf,KAAK,CAACof,YAAY;MAChCvb,MAAM,EAAE7D,KAAK,CAAC6D,MAAM;MACpBO,gBAAgB,EAAE,CAAC,CAACpE,KAAK,CAACof,YAAY;MACtCtB,sBAAsB,EAAE9d,KAAK,CAAC8d,sBAAsB;MACpDva,KAAK,EAAEvD,KAAK,CAACuD,KAAK;MAClBK,MAAM,EAAE5D,KAAK,CAACO,SAAS,IAAI,CAAC,CAACP,KAAK,CAACM,YAAY;MAC/CgD,IAAI,EAAEtD,KAAK,CAACsD,IAAI;MAChBQ,gBAAgB,EAAE9D,KAAK,CAAC8D;IAC1B,CAAC;IACD,IAAI4Z,QAAQ,CAAC9W,MAAM,GAAG,CAAC,EAAE;MACvBzG,KAAK,CAACyE,QAAQ,GAAG8Y,QAAQ;IAC3B;IACA,OAAO,CAACvd,KAAK,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC,EACNhB,OACF,CAAC;EACD,MAAM6iB,gBAAgB,GAAG;IACvBpiB,MAAM,EAAE;MACN;MACA;MACA4C,aAAa,EAAE,KAAK;MACpBD,6BAA6B,EAAE;IACjC,CAAC;IACD1C,SAAS,EAAE,KAAK;IAChBiB,GAAG,EAAE,IAAI;IACTrB,WAAW,EAAE,EAAE;IACfF,QAAQ,EAAE;MACRI,MAAM,EAAE,CAAC,CAAC;MACViD,OAAO,EAAE,GAAG;MACZxD,GAAG,EAAE,EAAE;MACPqD,KAAK,EAAE;QACLE,MAAM,EAAE,EAAE;QACVD,OAAO,EAAE;MACX;IACF,CAAC;IACD3B,cAAc,EAAE;MAAE8B,IAAI,EAAE,MAAM;MAAEC,YAAY,EAAE;IAAc,CAAC;IAC7DtD,YAAY,EAAEgc,qBAAqB,CAACC,OAAO;EAC7C,CAAC;EACD,OAAO,eAAgBmI,MAAM,CAACljB,aAAa,CAACtE,gBAAgB,CAACwE,QAAQ,EAAE;IAAEC,KAAK,EAAE;EAAK,CAAC,EAAE,eAAgB+iB,MAAM,CAACljB,aAAa,CAACmZ,4BAA4B,EAAE;IAAE3Y,QAAQ,EAAEua,OAAO,CAACva;EAAS,CAAC,EAAE,eAAgB0iB,MAAM,CAACljB,aAAa,CAACzE,gBAAgB,CAAC2E,QAAQ,EAAE;IAAEC,KAAK,EAAEmhB;EAAiB,CAAC,EAAE,eAAgB4B,MAAM,CAACljB,aAAa,CAC1ThE,oBAAoB,EACpB;IACEyC,OAAO;IACPsB,MAAM;IACNW,OAAO,EAAE,KAAK;IACd/B,KAAK,EAAEoc,OAAO,CAACpc;EACjB,CACF,CAAC,CAAC,CAAC,CAAC;AACN;AACA,SAASgnB,oBAAoBA,CAACjnB,GAAG,EAAE;EACjC,OAAOA,GAAG,CAAC4L,QAAQ,CAAC4J,QAAQ,CAAC,MAAM,CAAC;AACtC;AACA,SAAS2R,iBAAiBA,CAACnnB,GAAG,EAAE;EAC9B,OAAOA,GAAG,CAAC4L,QAAQ,CAAC4J,QAAQ,CAAC,WAAW,CAAC;AAC3C;;AAEA;AACA,SAAS6S,YAAYA,CAAA,EAAG;EACtB,IAAIC,QAAQ,GAAG,IAAI1iB,WAAW,CAAC,CAAC;EAChC,IAAI2iB,gBAAgB,GAAG,IAAI;EAC3B,IAAI3D,SAAS,GAAG,IAAI3V,cAAc,CAAC;IACjCC,KAAKA,CAACC,UAAU,EAAE;MAChB,IAAI,OAAO8N,MAAM,KAAK,WAAW,EAAE;QACjC;MACF;MACA,IAAIuL,WAAW,GAAIlD,KAAK,IAAK;QAC3B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAC7BnW,UAAU,CAACC,OAAO,CAACkZ,QAAQ,CAACpqB,MAAM,CAAConB,KAAK,CAAC,CAAC;QAC5C,CAAC,MAAM;UACLnW,UAAU,CAACC,OAAO,CAACkW,KAAK,CAAC;QAC3B;MACF,CAAC;MACDrI,MAAM,CAACwL,aAAa,KAAKxL,MAAM,CAACwL,aAAa,GAAG,EAAE,CAAC;MACnDxL,MAAM,CAACwL,aAAa,CAACla,OAAO,CAACia,WAAW,CAAC;MACzCvL,MAAM,CAACwL,aAAa,CAAC5b,IAAI,GAAIyY,KAAK,IAAK;QACrCkD,WAAW,CAAClD,KAAK,CAAC;QAClB,OAAO,CAAC;MACV,CAAC;MACDiD,gBAAgB,GAAGpZ,UAAU;IAC/B;EACF,CAAC,CAAC;EACF,IAAI,OAAO4S,QAAQ,KAAK,WAAW,IAAIA,QAAQ,CAAC2G,UAAU,KAAK,SAAS,EAAE;IACxE3G,QAAQ,CAAC3N,gBAAgB,CAAC,kBAAkB,EAAE,MAAM;MAClDmU,gBAAgB,EAAElZ,KAAK,CAAC,CAAC;IAC3B,CAAC,CAAC;EACJ,CAAC,MAAM;IACLkZ,gBAAgB,EAAElZ,KAAK,CAAC,CAAC;EAC3B;EACA,OAAOuV,SAAS;AAClB;;AAEA;AACA,SAAS+D,iBAAiBA,CAAC1d,MAAM,EAAE;EACjC,IAAI,CAACA,MAAM,EAAE,OAAO,IAAI;EACxB,IAAIC,OAAO,GAAGb,MAAM,CAACa,OAAO,CAACD,MAAM,CAAC;EACpC,IAAIM,UAAU,GAAG,CAAC,CAAC;EACnB,KAAK,IAAI,CAACvF,GAAG,EAAEwF,GAAG,CAAC,IAAIN,OAAO,EAAE;IAC9B,IAAIM,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,oBAAoB,EAAE;MAC9CF,UAAU,CAACvF,GAAG,CAAC,GAAG,IAAIpJ,iBAAiB,CACrC4O,GAAG,CAACa,MAAM,EACVb,GAAG,CAACgJ,UAAU,EACdhJ,GAAG,CAACgE,IAAI,EACRhE,GAAG,CAACod,QAAQ,KAAK,IACnB,CAAC;IACH,CAAC,MAAM,IAAIpd,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,OAAO,EAAE;MACxC,IAAID,GAAG,CAACE,SAAS,EAAE;QACjB,IAAImd,gBAAgB,GAAG5L,MAAM,CAACzR,GAAG,CAACE,SAAS,CAAC;QAC5C,IAAI,OAAOmd,gBAAgB,KAAK,UAAU,EAAE;UAC1C,IAAI;YACF,IAAI3hB,KAAK,GAAG,IAAI2hB,gBAAgB,CAACrd,GAAG,CAACH,OAAO,CAAC;YAC7CnE,KAAK,CAAC6D,KAAK,GAAGS,GAAG,CAACT,KAAK;YACvBQ,UAAU,CAACvF,GAAG,CAAC,GAAGkB,KAAK;UACzB,CAAC,CAAC,OAAOkH,CAAC,EAAE,CACZ;QACF;MACF;MACA,IAAI7C,UAAU,CAACvF,GAAG,CAAC,IAAI,IAAI,EAAE;QAC3B,IAAIkB,KAAK,GAAG,IAAIlD,KAAK,CAACwH,GAAG,CAACH,OAAO,CAAC;QAClCnE,KAAK,CAAC6D,KAAK,GAAGS,GAAG,CAACT,KAAK;QACvBQ,UAAU,CAACvF,GAAG,CAAC,GAAGkB,KAAK;MACzB;IACF,CAAC,MAAM;MACLqE,UAAU,CAACvF,GAAG,CAAC,GAAGwF,GAAG;IACvB;EACF;EACA,OAAOD,UAAU;AACnB;AAEA,SACEzL,YAAY,EACZ2C,gBAAgB,EAChBkF,YAAY,EACZkB,QAAQ,EACR4B,UAAU,EACViD,iBAAiB,EACjBqH,oBAAoB,EACpBsD,aAAa,EACbK,SAAS,EACTC,oBAAoB,EACpBW,0BAA0B,EAC1BG,0BAA0B,EAC1BnM,IAAI,EACJ4M,gBAAgB,EAChBgC,2BAA2B,EAC3BQ,gBAAgB,EAChBoE,iBAAiB,EACjB+F,qBAAqB,EACrBoB,eAAe,EACfI,YAAY,EACZM,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}