{"ast": null, "code": "import React from'react';import{Link}from'react-router-dom';import{Pie}from'react-chartjs-2';import{Chart as ChartJS,ArcElement,Tooltip,Legend,Title}from'chart.js';import{useChartData}from'../hooks/useChartData';// Register Chart.js components\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";ChartJS.register(ArcElement,Tooltip,Legend,Title);const Pillar4=()=>{const{data:mindWorkoutData,options:mindWorkoutOptions,loading}=useChartData('mindWorkout');return/*#__PURE__*/_jsx(\"div\",{className:\"bg-blue-50 text-gray-800 min-h-screen\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto p-4 md:p-8\",children:[/*#__PURE__*/_jsxs(\"header\",{className:\"text-center mb-10\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"inline-block bg-blue-500 text-white rounded-full px-5 py-2 mb-4 text-sm font-bold\",children:\"PILLAR 4: THE MIND\"}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-4xl md:text-5xl font-black text-slate-700 tracking-tight\",children:\"Sharpening Your Greatest Tool\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg text-gray-600 mt-2\",children:\"Rewire your brain for deep focus and accelerated learning.\"})]}),/*#__PURE__*/_jsxs(\"section\",{id:\"core-practices\",className:\"mb-12 bg-white p-6 rounded-lg shadow-lg\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-center mb-6 text-slate-700\",children:\"The Three Core Practices\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-3 gap-6 text-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"p-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-6xl\",children:\"\\uD83E\\uDDD8\\u200D\\u2642\\uFE0F\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"font-bold text-lg mt-3 text-blue-500\",children:\"Meditation\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm mt-1\",children:\"Train your attention span. 10 minutes daily with an app like Headspace is non-negotiable.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-6xl\",children:\"\\uD83D\\uDCDA\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"font-bold text-lg mt-3 text-emerald-500\",children:\"Active Reading\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm mt-1\",children:\"Don't just read, process. Use the Feynman Technique to truly understand and retain information.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-6xl\",children:\"\\uD83E\\uDDE9\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"font-bold text-lg mt-3 text-amber-500\",children:\"Problem Solving\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm mt-1\",children:\"Engage in daily logic puzzles (Sudoku, etc.) to keep your reasoning skills sharp and flexible.\"})]})]})]}),/*#__PURE__*/_jsxs(\"section\",{className:\"grid grid-cols-1 md:grid-cols-5 gap-8 mb-12 items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"md:col-span-3 bg-white p-6 rounded-lg shadow-lg\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-center mb-6 text-slate-700\",children:\"The Feynman Learning Technique\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold text-blue-500\",children:\"1.\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{className:\"text-gray-800\",children:\"Choose a Concept.\"}),\" Pick a topic you are learning (e.g., a grammar point, how a tool works).\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold text-blue-500\",children:\"2.\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{className:\"text-gray-800\",children:\"Teach it Simply.\"}),\" Explain it out loud, in the simplest terms possible, as if to a child.\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold text-blue-500\",children:\"3.\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{className:\"text-gray-800\",children:\"Identify Gaps.\"}),\" When you get stuck or use complex language, you've found a weak spot in your understanding.\"]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold text-blue-500\",children:\"4.\"}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{className:\"text-gray-800\",children:\"Review & Simplify.\"}),\" Go back to the source material to fill the gap, then refine your simple explanation.\"]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"md:col-span-2 bg-white p-6 rounded-lg shadow-lg\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-center mb-4 text-slate-700\",children:\"Daily Cognitive Workout\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-center text-gray-600 mb-4\",children:\"Your 90-minute daily block dedicated to mental fitness.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"relative w-full max-w-sm mx-auto h-80\",children:loading?/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-full\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-b-2 border-slate-700\"})}):mindWorkoutData&&mindWorkoutOptions?/*#__PURE__*/_jsx(Pie,{data:mindWorkoutData,options:mindWorkoutOptions}):/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-full text-gray-500\",children:\"Chart loading...\"})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center\",children:[/*#__PURE__*/_jsx(Link,{to:\"/japan/pillar-3\",className:\"bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors\",children:\"\\u2190 Previous: Practical Skills\"}),/*#__PURE__*/_jsx(Link,{to:\"/japan\",className:\"bg-reedsoft-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors\",children:\"Back to Dashboard \\u2192\"})]}),/*#__PURE__*/_jsx(\"footer\",{className:\"text-center mt-10 pt-6 border-t border-gray-300\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-700 font-semibold\",children:\"The quality of your work is determined by the quality of your focus.\"})})]})});};export default Pillar4;", "map": {"version": 3, "names": ["React", "Link", "Pie", "Chart", "ChartJS", "ArcElement", "<PERSON><PERSON><PERSON>", "Legend", "Title", "useChartData", "jsx", "_jsx", "jsxs", "_jsxs", "register", "Pillar4", "data", "mindWorkoutData", "options", "mindWorkoutOptions", "loading", "className", "children", "id", "to"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/pages/Pillar4.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { Pie } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  ArcElement,\n  Tooltip,\n  Legend,\n  Title\n} from 'chart.js';\nimport { useChartData } from '../hooks/useChartData';\n\n// Register Chart.js components\nChartJS.register(ArcE<PERSON>, Tooltip, Legend, Title);\n\nconst Pillar4: React.FC = () => {\n  const { data: mindWorkoutData, options: mindWorkoutOptions, loading } = useChartData('mindWorkout');\n\n  return (\n    <div className=\"bg-blue-50 text-gray-800 min-h-screen\">\n      <div className=\"container mx-auto p-4 md:p-8\">\n        <header className=\"text-center mb-10\">\n          <div className=\"inline-block bg-blue-500 text-white rounded-full px-5 py-2 mb-4 text-sm font-bold\">\n            PILLAR 4: THE MIND\n          </div>\n          <h1 className=\"text-4xl md:text-5xl font-black text-slate-700 tracking-tight\">\n            Sharpening Your Greatest Tool\n          </h1>\n          <p className=\"text-lg text-gray-600 mt-2\">\n            Rewire your brain for deep focus and accelerated learning.\n          </p>\n        </header>\n\n        {/* Core Practices Section */}\n        <section id=\"core-practices\" className=\"mb-12 bg-white p-6 rounded-lg shadow-lg\">\n          <h2 className=\"text-2xl font-bold text-center mb-6 text-slate-700\">The Three Core Practices</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 text-center\">\n            <div className=\"p-4\">\n              <div className=\"text-6xl\">🧘‍♂️</div>\n              <h3 className=\"font-bold text-lg mt-3 text-blue-500\">Meditation</h3>\n              <p className=\"text-sm mt-1\">Train your attention span. 10 minutes daily with an app like Headspace is non-negotiable.</p>\n            </div>\n            <div className=\"p-4\">\n              <div className=\"text-6xl\">📚</div>\n              <h3 className=\"font-bold text-lg mt-3 text-emerald-500\">Active Reading</h3>\n              <p className=\"text-sm mt-1\">Don't just read, process. Use the Feynman Technique to truly understand and retain information.</p>\n            </div>\n            <div className=\"p-4\">\n              <div className=\"text-6xl\">🧩</div>\n              <h3 className=\"font-bold text-lg mt-3 text-amber-500\">Problem Solving</h3>\n              <p className=\"text-sm mt-1\">Engage in daily logic puzzles (Sudoku, etc.) to keep your reasoning skills sharp and flexible.</p>\n            </div>\n          </div>\n        </section>\n\n        {/* Feynman Technique and Mind Workout Section */}\n        <section className=\"grid grid-cols-1 md:grid-cols-5 gap-8 mb-12 items-center\">\n          <div className=\"md:col-span-3 bg-white p-6 rounded-lg shadow-lg\">\n            <h2 className=\"text-2xl font-bold text-center mb-6 text-slate-700\">The Feynman Learning Technique</h2>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\">\n                <div className=\"text-3xl font-bold text-blue-500\">1.</div>\n                <div><strong className=\"text-gray-800\">Choose a Concept.</strong> Pick a topic you are learning (e.g., a grammar point, how a tool works).</div>\n              </div>\n              <div className=\"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\">\n                <div className=\"text-3xl font-bold text-blue-500\">2.</div>\n                <div><strong className=\"text-gray-800\">Teach it Simply.</strong> Explain it out loud, in the simplest terms possible, as if to a child.</div>\n              </div>\n              <div className=\"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\">\n                <div className=\"text-3xl font-bold text-blue-500\">3.</div>\n                <div><strong className=\"text-gray-800\">Identify Gaps.</strong> When you get stuck or use complex language, you've found a weak spot in your understanding.</div>\n              </div>\n              <div className=\"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\">\n                <div className=\"text-3xl font-bold text-blue-500\">4.</div>\n                <div><strong className=\"text-gray-800\">Review & Simplify.</strong> Go back to the source material to fill the gap, then refine your simple explanation.</div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"md:col-span-2 bg-white p-6 rounded-lg shadow-lg\">\n            <h2 className=\"text-2xl font-bold text-center mb-4 text-slate-700\">Daily Cognitive Workout</h2>\n            <p className=\"text-sm text-center text-gray-600 mb-4\">Your 90-minute daily block dedicated to mental fitness.</p>\n            <div className=\"relative w-full max-w-sm mx-auto h-80\">\n              {loading ? (\n                <div className=\"flex items-center justify-center h-full\">\n                  <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-slate-700\"></div>\n                </div>\n              ) : mindWorkoutData && mindWorkoutOptions ? (\n                <Pie data={mindWorkoutData} options={mindWorkoutOptions as any} />\n              ) : (\n                <div className=\"flex items-center justify-center h-full text-gray-500\">\n                  Chart loading...\n                </div>\n              )}\n            </div>\n          </div>\n        </section>\n\n        {/* Navigation */}\n        <div className=\"flex justify-between items-center\">\n          <Link\n            to=\"/japan/pillar-3\"\n            className=\"bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors\"\n          >\n            ← Previous: Practical Skills\n          </Link>\n          <Link\n            to=\"/japan\"\n            className=\"bg-reedsoft-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors\"\n          >\n            Back to Dashboard →\n          </Link>\n        </div>\n\n        <footer className=\"text-center mt-10 pt-6 border-t border-gray-300\">\n          <p className=\"text-gray-700 font-semibold\">\n            The quality of your work is determined by the quality of your focus.\n          </p>\n        </footer>\n      </div>\n    </div>\n  );\n};\n\nexport default Pillar4;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,GAAG,KAAQ,iBAAiB,CACrC,OACEC,KAAK,GAAI,CAAAC,OAAO,CAChBC,UAAU,CACVC,OAAO,CACPC,MAAM,CACNC,KAAK,KACA,UAAU,CACjB,OAASC,YAAY,KAAQ,uBAAuB,CAEpD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACAT,OAAO,CAACU,QAAQ,CAACT,UAAU,CAAEC,OAAO,CAAEC,MAAM,CAAEC,KAAK,CAAC,CAEpD,KAAM,CAAAO,OAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAEC,IAAI,CAAEC,eAAe,CAAEC,OAAO,CAAEC,kBAAkB,CAAEC,OAAQ,CAAC,CAAGX,YAAY,CAAC,aAAa,CAAC,CAEnG,mBACEE,IAAA,QAAKU,SAAS,CAAC,uCAAuC,CAAAC,QAAA,cACpDT,KAAA,QAAKQ,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3CT,KAAA,WAAQQ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACnCX,IAAA,QAAKU,SAAS,CAAC,mFAAmF,CAAAC,QAAA,CAAC,oBAEnG,CAAK,CAAC,cACNX,IAAA,OAAIU,SAAS,CAAC,+DAA+D,CAAAC,QAAA,CAAC,+BAE9E,CAAI,CAAC,cACLX,IAAA,MAAGU,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,4DAE1C,CAAG,CAAC,EACE,CAAC,cAGTT,KAAA,YAASU,EAAE,CAAC,gBAAgB,CAACF,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eAC9EX,IAAA,OAAIU,SAAS,CAAC,oDAAoD,CAAAC,QAAA,CAAC,0BAAwB,CAAI,CAAC,cAChGT,KAAA,QAAKQ,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChET,KAAA,QAAKQ,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBX,IAAA,QAAKU,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,gCAAK,CAAK,CAAC,cACrCX,IAAA,OAAIU,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAAC,YAAU,CAAI,CAAC,cACpEX,IAAA,MAAGU,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,2FAAyF,CAAG,CAAC,EACtH,CAAC,cACNT,KAAA,QAAKQ,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBX,IAAA,QAAKU,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cAClCX,IAAA,OAAIU,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cAC3EX,IAAA,MAAGU,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,iGAA+F,CAAG,CAAC,EAC5H,CAAC,cACNT,KAAA,QAAKQ,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBX,IAAA,QAAKU,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cAClCX,IAAA,OAAIU,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cAC1EX,IAAA,MAAGU,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,gGAA8F,CAAG,CAAC,EAC3H,CAAC,EACH,CAAC,EACC,CAAC,cAGVT,KAAA,YAASQ,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eAC3ET,KAAA,QAAKQ,SAAS,CAAC,iDAAiD,CAAAC,QAAA,eAC9DX,IAAA,OAAIU,SAAS,CAAC,oDAAoD,CAAAC,QAAA,CAAC,gCAA8B,CAAI,CAAC,cACtGT,KAAA,QAAKQ,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBT,KAAA,QAAKQ,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEX,IAAA,QAAKU,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,IAAE,CAAK,CAAC,cAC1DT,KAAA,QAAAS,QAAA,eAAKX,IAAA,WAAQU,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,mBAAiB,CAAQ,CAAC,4EAAyE,EAAK,CAAC,EAC7I,CAAC,cACNT,KAAA,QAAKQ,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEX,IAAA,QAAKU,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,IAAE,CAAK,CAAC,cAC1DT,KAAA,QAAAS,QAAA,eAAKX,IAAA,WAAQU,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,kBAAgB,CAAQ,CAAC,0EAAuE,EAAK,CAAC,EAC1I,CAAC,cACNT,KAAA,QAAKQ,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEX,IAAA,QAAKU,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,IAAE,CAAK,CAAC,cAC1DT,KAAA,QAAAS,QAAA,eAAKX,IAAA,WAAQU,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,gBAAc,CAAQ,CAAC,+FAA4F,EAAK,CAAC,EAC7J,CAAC,cACNT,KAAA,QAAKQ,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEX,IAAA,QAAKU,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,IAAE,CAAK,CAAC,cAC1DT,KAAA,QAAAS,QAAA,eAAKX,IAAA,WAAQU,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,oBAAkB,CAAQ,CAAC,wFAAqF,EAAK,CAAC,EAC1J,CAAC,EACH,CAAC,EACH,CAAC,cAENT,KAAA,QAAKQ,SAAS,CAAC,iDAAiD,CAAAC,QAAA,eAC9DX,IAAA,OAAIU,SAAS,CAAC,oDAAoD,CAAAC,QAAA,CAAC,yBAAuB,CAAI,CAAC,cAC/FX,IAAA,MAAGU,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,yDAAuD,CAAG,CAAC,cACjHX,IAAA,QAAKU,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CACnDF,OAAO,cACNT,IAAA,QAAKU,SAAS,CAAC,yCAAyC,CAAAC,QAAA,cACtDX,IAAA,QAAKU,SAAS,CAAC,iEAAiE,CAAM,CAAC,CACpF,CAAC,CACJJ,eAAe,EAAIE,kBAAkB,cACvCR,IAAA,CAACT,GAAG,EAACc,IAAI,CAAEC,eAAgB,CAACC,OAAO,CAAEC,kBAA0B,CAAE,CAAC,cAElER,IAAA,QAAKU,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,kBAEvE,CAAK,CACN,CACE,CAAC,EACH,CAAC,EACC,CAAC,cAGVT,KAAA,QAAKQ,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDX,IAAA,CAACV,IAAI,EACHuB,EAAE,CAAC,iBAAiB,CACpBH,SAAS,CAAC,kGAAkG,CAAAC,QAAA,CAC7G,mCAED,CAAM,CAAC,cACPX,IAAA,CAACV,IAAI,EACHuB,EAAE,CAAC,QAAQ,CACXH,SAAS,CAAC,iHAAiH,CAAAC,QAAA,CAC5H,0BAED,CAAM,CAAC,EACJ,CAAC,cAENX,IAAA,WAAQU,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cACjEX,IAAA,MAAGU,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,sEAE3C,CAAG,CAAC,CACE,CAAC,EACN,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAP,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}