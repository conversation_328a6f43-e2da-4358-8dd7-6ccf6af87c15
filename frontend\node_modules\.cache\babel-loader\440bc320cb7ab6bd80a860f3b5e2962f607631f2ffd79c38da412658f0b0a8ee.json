{"ast": null, "code": "import _objectSpread from\"C:/Developer/Web Development/reedsoft/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{useState,useEffect}from'react';import{createTimeAllocationData,createKeyLiftsData,createAnkiData,createKnowledgeData,createMindWorkoutData,createChartOptions}from'../utils/chartConfig';/**\n * Custom hook for managing chart data and options\n * @param chartType - Type of chart to create\n * @param title - Optional chart title\n * @returns Object with chart data, options, and loading state\n */export const useChartData=(chartType,title)=>{const[data,setData]=useState(null);const[options,setOptions]=useState(null);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);useEffect(()=>{const loadChartData=async()=>{try{setLoading(true);setError(null);let chartData;let chartOptions;switch(chartType){case'timeAllocation':chartData=createTimeAllocationData();chartOptions=createChartOptions('doughnut',title||'Weekly Hour Distribution');break;case'keyLifts':chartData=createKeyLiftsData();chartOptions=createChartOptions('bar',title);// Add custom tooltip for key lifts\nchartOptions.plugins.tooltip.callbacks.label=function(context){const lift=context.label;let muscles;switch(lift){case'Squat':muscles='Legs, Glutes, Core';break;case'Deadlift':muscles='Entire Posterior Chain';break;case'Bench Press':muscles='Chest, Shoulders, Triceps';break;case'Overhead Press':muscles='Shoulders, Triceps, Core';break;case'Barbell Row':muscles='Back, Biceps';break;default:muscles='';}return\" Works: \".concat(muscles);};break;case'anki':chartData=createAnkiData();chartOptions=createChartOptions('doughnut',title||'The Daily SRS Cycle');break;case'knowledge':chartData=createKnowledgeData();chartOptions=createChartOptions('radar',title);break;case'mindWorkout':chartData=createMindWorkoutData();chartOptions=createChartOptions('pie',title||'90-Minute Focus Block');break;default:throw new Error(\"Unknown chart type: \".concat(chartType));}setData(chartData);setOptions(chartOptions);}catch(err){setError(err instanceof Error?err.message:'Failed to load chart data');}finally{setLoading(false);}};loadChartData();},[chartType,title]);return{data,options,loading,error};};/**\n * Custom hook for creating custom chart data\n * @param labels - Chart labels\n * @param datasets - Chart datasets\n * @param chartType - Type of chart\n * @param title - Optional chart title\n * @returns Object with chart data, options, and utility functions\n */export const useCustomChartData=(labels,datasets,chartType,title)=>{const[data,setData]=useState({labels,datasets});const[options,setOptions]=useState(createChartOptions(chartType,title));const updateData=(newLabels,newDatasets)=>{setData({labels:newLabels,datasets:newDatasets});};const updateOptions=newOptions=>{setOptions(prev=>_objectSpread(_objectSpread({},prev),newOptions));};useEffect(()=>{setData({labels,datasets});},[labels,datasets]);useEffect(()=>{setOptions(createChartOptions(chartType,title));},[chartType,title]);return{data,options,updateData,updateOptions};};", "map": {"version": 3, "names": ["useState", "useEffect", "createTimeAllocationData", "createKeyLiftsData", "createAnkiData", "createKnowledgeData", "createMindWorkoutData", "createChartOptions", "useChartData", "chartType", "title", "data", "setData", "options", "setOptions", "loading", "setLoading", "error", "setError", "loadChartData", "chartData", "chartOptions", "plugins", "tooltip", "callbacks", "label", "context", "lift", "muscles", "concat", "Error", "err", "message", "useCustomChartData", "labels", "datasets", "updateData", "<PERSON><PERSON><PERSON><PERSON>", "newDatasets", "updateOptions", "newOptions", "prev", "_objectSpread"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/hooks/useChartData.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { ChartData, ChartOptions } from '../types';\nimport {\n  createTimeAllocationData,\n  createKeyLiftsData,\n  createAnkiData,\n  createKnowledgeData,\n  createMindWorkoutData,\n  createChartOptions\n} from '../utils/chartConfig';\n\nexport type ChartType = 'timeAllocation' | 'keyLifts' | 'anki' | 'knowledge' | 'mindWorkout';\n\n/**\n * Custom hook for managing chart data and options\n * @param chartType - Type of chart to create\n * @param title - Optional chart title\n * @returns Object with chart data, options, and loading state\n */\nexport const useChartData = (chartType: ChartType, title?: string) => {\n  const [data, setData] = useState<ChartData | null>(null);\n  const [options, setOptions] = useState<ChartOptions | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    const loadChartData = async () => {\n      try {\n        setLoading(true);\n        setError(null);\n\n        let chartData: ChartData;\n        let chartOptions: ChartOptions;\n\n        switch (chartType) {\n          case 'timeAllocation':\n            chartData = createTimeAllocationData();\n            chartOptions = createChartOptions('doughnut', title || 'Weekly Hour Distribution');\n            break;\n\n          case 'keyLifts':\n            chartData = createKeyLiftsData();\n            chartOptions = createChartOptions('bar', title);\n            // Add custom tooltip for key lifts\n            chartOptions.plugins!.tooltip!.callbacks!.label = function(context: any) {\n              const lift = context.label;\n              let muscles: string;\n              switch(lift) {\n                case 'Squat': muscles = 'Legs, Glutes, Core'; break;\n                case 'Deadlift': muscles = 'Entire Posterior Chain'; break;\n                case 'Bench Press': muscles = 'Chest, Shoulders, Triceps'; break;\n                case 'Overhead Press': muscles = 'Shoulders, Triceps, Core'; break;\n                case 'Barbell Row': muscles = 'Back, Biceps'; break;\n                default: muscles = '';\n              }\n              return ` Works: ${muscles}`;\n            };\n            break;\n\n          case 'anki':\n            chartData = createAnkiData();\n            chartOptions = createChartOptions('doughnut', title || 'The Daily SRS Cycle');\n            break;\n\n          case 'knowledge':\n            chartData = createKnowledgeData();\n            chartOptions = createChartOptions('radar', title);\n            break;\n\n          case 'mindWorkout':\n            chartData = createMindWorkoutData();\n            chartOptions = createChartOptions('pie', title || '90-Minute Focus Block');\n            break;\n\n          default:\n            throw new Error(`Unknown chart type: ${chartType}`);\n        }\n\n        setData(chartData);\n        setOptions(chartOptions);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'Failed to load chart data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadChartData();\n  }, [chartType, title]);\n\n  return { data, options, loading, error };\n};\n\n/**\n * Custom hook for creating custom chart data\n * @param labels - Chart labels\n * @param datasets - Chart datasets\n * @param chartType - Type of chart\n * @param title - Optional chart title\n * @returns Object with chart data, options, and utility functions\n */\nexport const useCustomChartData = (\n  labels: string[],\n  datasets: any[],\n  chartType: 'doughnut' | 'bar' | 'radar' | 'pie' | 'line',\n  title?: string\n) => {\n  const [data, setData] = useState<ChartData>({ labels, datasets });\n  const [options, setOptions] = useState<ChartOptions>(createChartOptions(chartType, title));\n\n  const updateData = (newLabels: string[], newDatasets: any[]) => {\n    setData({ labels: newLabels, datasets: newDatasets });\n  };\n\n  const updateOptions = (newOptions: Partial<ChartOptions>) => {\n    setOptions(prev => ({ ...prev, ...newOptions }));\n  };\n\n  useEffect(() => {\n    setData({ labels, datasets });\n  }, [labels, datasets]);\n\n  useEffect(() => {\n    setOptions(createChartOptions(chartType, title));\n  }, [chartType, title]);\n\n  return { data, options, updateData, updateOptions };\n};\n"], "mappings": "0HAAA,OAASA,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAE3C,OACEC,wBAAwB,CACxBC,kBAAkB,CAClBC,cAAc,CACdC,mBAAmB,CACnBC,qBAAqB,CACrBC,kBAAkB,KACb,sBAAsB,CAI7B;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,YAAY,CAAGA,CAACC,SAAoB,CAAEC,KAAc,GAAK,CACpE,KAAM,CAACC,IAAI,CAAEC,OAAO,CAAC,CAAGZ,QAAQ,CAAmB,IAAI,CAAC,CACxD,KAAM,CAACa,OAAO,CAAEC,UAAU,CAAC,CAAGd,QAAQ,CAAsB,IAAI,CAAC,CACjE,KAAM,CAACe,OAAO,CAAEC,UAAU,CAAC,CAAGhB,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACiB,KAAK,CAAEC,QAAQ,CAAC,CAAGlB,QAAQ,CAAgB,IAAI,CAAC,CAEvDC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAkB,aAAa,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACFH,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAI,CAAAE,SAAoB,CACxB,GAAI,CAAAC,YAA0B,CAE9B,OAAQZ,SAAS,EACf,IAAK,gBAAgB,CACnBW,SAAS,CAAGlB,wBAAwB,CAAC,CAAC,CACtCmB,YAAY,CAAGd,kBAAkB,CAAC,UAAU,CAAEG,KAAK,EAAI,0BAA0B,CAAC,CAClF,MAEF,IAAK,UAAU,CACbU,SAAS,CAAGjB,kBAAkB,CAAC,CAAC,CAChCkB,YAAY,CAAGd,kBAAkB,CAAC,KAAK,CAAEG,KAAK,CAAC,CAC/C;AACAW,YAAY,CAACC,OAAO,CAAEC,OAAO,CAAEC,SAAS,CAAEC,KAAK,CAAG,SAASC,OAAY,CAAE,CACvE,KAAM,CAAAC,IAAI,CAAGD,OAAO,CAACD,KAAK,CAC1B,GAAI,CAAAG,OAAe,CACnB,OAAOD,IAAI,EACT,IAAK,OAAO,CAAEC,OAAO,CAAG,oBAAoB,CAAE,MAC9C,IAAK,UAAU,CAAEA,OAAO,CAAG,wBAAwB,CAAE,MACrD,IAAK,aAAa,CAAEA,OAAO,CAAG,2BAA2B,CAAE,MAC3D,IAAK,gBAAgB,CAAEA,OAAO,CAAG,0BAA0B,CAAE,MAC7D,IAAK,aAAa,CAAEA,OAAO,CAAG,cAAc,CAAE,MAC9C,QAASA,OAAO,CAAG,EAAE,CACvB,CACA,iBAAAC,MAAA,CAAkBD,OAAO,EAC3B,CAAC,CACD,MAEF,IAAK,MAAM,CACTR,SAAS,CAAGhB,cAAc,CAAC,CAAC,CAC5BiB,YAAY,CAAGd,kBAAkB,CAAC,UAAU,CAAEG,KAAK,EAAI,qBAAqB,CAAC,CAC7E,MAEF,IAAK,WAAW,CACdU,SAAS,CAAGf,mBAAmB,CAAC,CAAC,CACjCgB,YAAY,CAAGd,kBAAkB,CAAC,OAAO,CAAEG,KAAK,CAAC,CACjD,MAEF,IAAK,aAAa,CAChBU,SAAS,CAAGd,qBAAqB,CAAC,CAAC,CACnCe,YAAY,CAAGd,kBAAkB,CAAC,KAAK,CAAEG,KAAK,EAAI,uBAAuB,CAAC,CAC1E,MAEF,QACE,KAAM,IAAI,CAAAoB,KAAK,wBAAAD,MAAA,CAAwBpB,SAAS,CAAE,CAAC,CACvD,CAEAG,OAAO,CAACQ,SAAS,CAAC,CAClBN,UAAU,CAACO,YAAY,CAAC,CAC1B,CAAE,MAAOU,GAAG,CAAE,CACZb,QAAQ,CAACa,GAAG,WAAY,CAAAD,KAAK,CAAGC,GAAG,CAACC,OAAO,CAAG,2BAA2B,CAAC,CAC5E,CAAC,OAAS,CACRhB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAEDG,aAAa,CAAC,CAAC,CACjB,CAAC,CAAE,CAACV,SAAS,CAAEC,KAAK,CAAC,CAAC,CAEtB,MAAO,CAAEC,IAAI,CAAEE,OAAO,CAAEE,OAAO,CAAEE,KAAM,CAAC,CAC1C,CAAC,CAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAgB,kBAAkB,CAAGA,CAChCC,MAAgB,CAChBC,QAAe,CACf1B,SAAwD,CACxDC,KAAc,GACX,CACH,KAAM,CAACC,IAAI,CAAEC,OAAO,CAAC,CAAGZ,QAAQ,CAAY,CAAEkC,MAAM,CAAEC,QAAS,CAAC,CAAC,CACjE,KAAM,CAACtB,OAAO,CAAEC,UAAU,CAAC,CAAGd,QAAQ,CAAeO,kBAAkB,CAACE,SAAS,CAAEC,KAAK,CAAC,CAAC,CAE1F,KAAM,CAAA0B,UAAU,CAAGA,CAACC,SAAmB,CAAEC,WAAkB,GAAK,CAC9D1B,OAAO,CAAC,CAAEsB,MAAM,CAAEG,SAAS,CAAEF,QAAQ,CAAEG,WAAY,CAAC,CAAC,CACvD,CAAC,CAED,KAAM,CAAAC,aAAa,CAAIC,UAAiC,EAAK,CAC3D1B,UAAU,CAAC2B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,EAAKD,UAAU,CAAG,CAAC,CAClD,CAAC,CAEDvC,SAAS,CAAC,IAAM,CACdW,OAAO,CAAC,CAAEsB,MAAM,CAAEC,QAAS,CAAC,CAAC,CAC/B,CAAC,CAAE,CAACD,MAAM,CAAEC,QAAQ,CAAC,CAAC,CAEtBlC,SAAS,CAAC,IAAM,CACda,UAAU,CAACP,kBAAkB,CAACE,SAAS,CAAEC,KAAK,CAAC,CAAC,CAClD,CAAC,CAAE,CAACD,SAAS,CAAEC,KAAK,CAAC,CAAC,CAEtB,MAAO,CAAEC,IAAI,CAAEE,OAAO,CAAEuB,UAAU,CAAEG,aAAc,CAAC,CACrD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}