import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Radar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend
} from 'chart.js';
import { useChartData } from '../hooks/useChartData';

// Register Chart.js components
ChartJS.register(RadialLinearScale, PointElement, LineElement, Filler, Tooltip, Legend);

const Pillar3: React.FC = () => {
  const { data: knowledgeData, options: knowledgeOptions, loading } = useChartData('knowledge');

  return (
    <div className="bg-gray-100 text-gray-800 min-h-screen">
      <div className="container mx-auto p-4 md:p-8">
        <header className="text-center mb-10">
          <div className="inline-block bg-gray-600 text-white rounded-full px-5 py-2 mb-4 text-sm font-bold">
            PILLAR 3: THE HANDS
          </div>
          <h1 className="text-4xl md:text-5xl font-black text-gray-800 tracking-tight">
            Acquiring Practical Knowledge
          </h1>
          <p className="text-lg text-gray-600 mt-2">
            Know the 'What' and 'Why' to quickly master the 'How' on the job.
          </p>
        </header>

        {/* Core Knowledge Categories Section */}
        <section id="knowledge-areas" className="mb-12 bg-white p-6 rounded-lg shadow-lg">
          <h2 className="text-2xl font-bold text-center mb-4 text-gray-800">Core Knowledge Categories</h2>
          <p className="text-sm text-gray-600 mb-6 text-center max-w-2xl mx-auto">
            Your study will focus on these five areas. A balanced understanding across all of them will make you a versatile and adaptable worker.
          </p>
          <div className="relative w-full max-w-lg mx-auto h-96">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"></div>
              </div>
            ) : knowledgeData && knowledgeOptions ? (
              <Radar data={knowledgeData} options={knowledgeOptions as any} />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500">
                Chart loading...
              </div>
            )}
          </div>
        </section>

        {/* Learning Loop Section */}
        <section id="learning-loop" className="mb-12 bg-white p-6 rounded-lg shadow-lg">
          <h2 className="text-2xl font-bold text-center mb-6 text-gray-800">The Learning Loop</h2>
          <div className="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-8">
            <div className="text-center">
              <div className="w-24 h-24 bg-red-500 text-white rounded-full flex items-center justify-center text-4xl mx-auto">①</div>
              <h3 className="font-bold mt-3">Watch & Learn</h3>
              <p className="text-sm">Use curated YouTube channels.</p>
            </div>
            <div className="text-4xl font-light text-gray-400">→</div>
            <div className="text-center">
              <div className="w-24 h-24 bg-yellow-500 text-white rounded-full flex items-center justify-center text-4xl mx-auto">②</div>
              <h3 className="font-bold mt-3">Note & Synthesize</h3>
              <p className="text-sm">Keep a dedicated notebook.</p>
            </div>
            <div className="text-4xl font-light text-gray-400">→</div>
            <div className="text-center">
              <div className="w-24 h-24 bg-blue-400 text-white rounded-full flex items-center justify-center text-4xl mx-auto">③</div>
              <h3 className="font-bold mt-3">Apply & Practice</h3>
              <p className="text-sm">Do weekend hands-on projects.</p>
            </div>
          </div>
        </section>

        {/* Safety Section */}
        <section id="safety" className="mb-12 bg-yellow-400 text-gray-800 p-6 rounded-lg shadow-lg">
          <h2 className="text-2xl font-bold text-center mb-4">Safety is Non-Negotiable</h2>
          <p className="text-center text-sm mb-6">
            Understanding and respecting safety protocols is the mark of a professional. Learn the purpose of each piece of Personal Protective Equipment (PPE).
          </p>
          <div className="flex justify-around text-center">
            <div><div className="text-5xl">👷</div><p className="font-semibold mt-2">Hard Hat</p></div>
            <div><div className="text-5xl">👓</div><p className="font-semibold mt-2">Safety Glasses</p></div>
            <div><div className="text-5xl">🧤</div><p className="font-semibold mt-2">Gloves</p></div>
            <div><div className="text-5xl">🥾</div><p className="font-semibold mt-2">Steel-Toe Boots</p></div>
          </div>
        </section>

        {/* Navigation */}
        <div className="flex justify-between items-center">
          <Link
            to="/japan/pillar-2"
            className="bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors"
          >
            ← Previous: Japanese Language
          </Link>
          <Link
            to="/japan/pillar-4"
            className="bg-reedsoft-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors"
          >
            Next: Cognitive Fitness →
          </Link>
        </div>

        <footer className="text-center mt-10 pt-6 border-t border-gray-300">
          <p className="text-gray-700 font-semibold">A smart hand is a safe and productive hand.</p>
        </footer>
      </div>
    </div>
  );
};

export default Pillar3;
