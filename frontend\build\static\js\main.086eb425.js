/*! For license information please see main.086eb425.js.LICENSE.txt */
(()=>{"use strict";var e={152:(e,t)=>{var n=Symbol.for("react.transitional.element"),i=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),o=Symbol.for("react.consumer"),l=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),f=Symbol.iterator;var p={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}function y(){}function x(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||p}b.prototype.isReactComponent={},b.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},b.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=b.prototype;var v=x.prototype=new y;v.constructor=x,m(v,b.prototype),v.isPureReactComponent=!0;var w=Array.isArray,k={H:null,A:null,T:null,S:null,V:null},_=Object.prototype.hasOwnProperty;function S(e,t,i,r,a,s){return i=s.ref,{$$typeof:n,type:e,key:t,ref:void 0!==i?i:null,props:s}}function N(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var M=/\/+/g;function C(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function P(){}function E(e,t,r,a,s){var o=typeof e;"undefined"!==o&&"boolean"!==o||(e=null);var l,c,u=!1;if(null===e)u=!0;else switch(o){case"bigint":case"string":case"number":u=!0;break;case"object":switch(e.$$typeof){case n:case i:u=!0;break;case h:return E((u=e._init)(e._payload),t,r,a,s)}}if(u)return s=s(e),u=""===a?"."+C(e,0):a,w(s)?(r="",null!=u&&(r=u.replace(M,"$&/")+"/"),E(s,t,r,"",function(e){return e})):null!=s&&(N(s)&&(l=s,c=r+(null==s.key||e&&e.key===s.key?"":(""+s.key).replace(M,"$&/")+"/")+u,s=S(l.type,c,void 0,0,0,l.props)),t.push(s)),1;u=0;var d,p=""===a?".":a+":";if(w(e))for(var m=0;m<e.length;m++)u+=E(a=e[m],t,r,o=p+C(a,m),s);else if("function"===typeof(m=null===(d=e)||"object"!==typeof d?null:"function"===typeof(d=f&&d[f]||d["@@iterator"])?d:null))for(e=m.call(e),m=0;!(a=e.next()).done;)u+=E(a=a.value,t,r,o=p+C(a,m++),s);else if("object"===o){if("function"===typeof e.then)return E(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"===typeof e.status?e.then(P,P):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),t,r,a,s);throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return u}function j(e,t,n){if(null==e)return e;var i=[],r=0;return E(e,i,"","",function(e){return t.call(n,e,r++)}),i}function T(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var L="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function O(){}t.Children={map:j,forEach:function(e,t,n){j(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return j(e,function(){t++}),t},toArray:function(e){return j(e,function(e){return e})||[]},only:function(e){if(!N(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=b,t.Fragment=r,t.Profiler=s,t.PureComponent=x,t.StrictMode=a,t.Suspense=u,t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=k,t.__COMPILER_RUNTIME={__proto__:null,c:function(e){return k.H.useMemoCache(e)}},t.cache=function(e){return function(){return e.apply(null,arguments)}},t.cloneElement=function(e,t,n){if(null===e||void 0===e)throw Error("The argument must be a React element, but you passed "+e+".");var i=m({},e.props),r=e.key;if(null!=t)for(a in void 0!==t.ref&&void 0,void 0!==t.key&&(r=""+t.key),t)!_.call(t,a)||"key"===a||"__self"===a||"__source"===a||"ref"===a&&void 0===t.ref||(i[a]=t[a]);var a=arguments.length-2;if(1===a)i.children=n;else if(1<a){for(var s=Array(a),o=0;o<a;o++)s[o]=arguments[o+2];i.children=s}return S(e.type,r,void 0,0,0,i)},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:o,_context:e},e},t.createElement=function(e,t,n){var i,r={},a=null;if(null!=t)for(i in void 0!==t.key&&(a=""+t.key),t)_.call(t,i)&&"key"!==i&&"__self"!==i&&"__source"!==i&&(r[i]=t[i]);var s=arguments.length-2;if(1===s)r.children=n;else if(1<s){for(var o=Array(s),l=0;l<s;l++)o[l]=arguments[l+2];r.children=o}if(e&&e.defaultProps)for(i in s=e.defaultProps)void 0===r[i]&&(r[i]=s[i]);return S(e,a,void 0,0,0,r)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=N,t.lazy=function(e){return{$$typeof:h,_payload:{_status:-1,_result:e},_init:T}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=k.T,n={};k.T=n;try{var i=e(),r=k.S;null!==r&&r(n,i),"object"===typeof i&&null!==i&&"function"===typeof i.then&&i.then(O,L)}catch(a){L(a)}finally{k.T=t}},t.unstable_useCacheRefresh=function(){return k.H.useCacheRefresh()},t.use=function(e){return k.H.use(e)},t.useActionState=function(e,t,n){return k.H.useActionState(e,t,n)},t.useCallback=function(e,t){return k.H.useCallback(e,t)},t.useContext=function(e){return k.H.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e,t){return k.H.useDeferredValue(e,t)},t.useEffect=function(e,t,n){var i=k.H;if("function"===typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return i.useEffect(e,t)},t.useId=function(){return k.H.useId()},t.useImperativeHandle=function(e,t,n){return k.H.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return k.H.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return k.H.useLayoutEffect(e,t)},t.useMemo=function(e,t){return k.H.useMemo(e,t)},t.useOptimistic=function(e,t){return k.H.useOptimistic(e,t)},t.useReducer=function(e,t,n){return k.H.useReducer(e,t,n)},t.useRef=function(e){return k.H.useRef(e)},t.useState=function(e){return k.H.useState(e)},t.useSyncExternalStore=function(e,t,n){return k.H.useSyncExternalStore(e,t,n)},t.useTransition=function(){return k.H.useTransition()},t.version="19.1.1"},261:(e,t,n)=>{e.exports=n(752)},367:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(532)},432:(e,t,n)=>{var i=n(643);function r(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function a(){}var s={d:{f:a,r:function(){throw Error(r(522))},D:a,C:a,L:a,m:a,X:a,S:a,M:a},p:0,findDOMNode:null},o=Symbol.for("react.portal");var l=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function c(e,t){return"font"===e?"":"string"===typeof t?"use-credentials"===t?t:"":void 0}t.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!t||1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType)throw Error(r(299));return function(e,t,n){var i=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:o,key:null==i?null:""+i,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.flushSync=function(e){var t=l.T,n=s.p;try{if(l.T=null,s.p=2,e)return e()}finally{l.T=t,s.p=n,s.d.f()}},t.preconnect=function(e,t){"string"===typeof e&&(t?t="string"===typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:t=null,s.d.C(e,t))},t.prefetchDNS=function(e){"string"===typeof e&&s.d.D(e)},t.preinit=function(e,t){if("string"===typeof e&&t&&"string"===typeof t.as){var n=t.as,i=c(n,t.crossOrigin),r="string"===typeof t.integrity?t.integrity:void 0,a="string"===typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?s.d.S(e,"string"===typeof t.precedence?t.precedence:void 0,{crossOrigin:i,integrity:r,fetchPriority:a}):"script"===n&&s.d.X(e,{crossOrigin:i,integrity:r,fetchPriority:a,nonce:"string"===typeof t.nonce?t.nonce:void 0})}},t.preinitModule=function(e,t){if("string"===typeof e)if("object"===typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=c(t.as,t.crossOrigin);s.d.M(e,{crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0})}}else null==t&&s.d.M(e)},t.preload=function(e,t){if("string"===typeof e&&"object"===typeof t&&null!==t&&"string"===typeof t.as){var n=t.as,i=c(n,t.crossOrigin);s.d.L(e,n,{crossOrigin:i,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0,type:"string"===typeof t.type?t.type:void 0,fetchPriority:"string"===typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"===typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"===typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"===typeof t.imageSizes?t.imageSizes:void 0,media:"string"===typeof t.media?t.media:void 0})}},t.preloadModule=function(e,t){if("string"===typeof e)if(t){var n=c(t.as,t.crossOrigin);s.d.m(e,{as:"string"===typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0})}else s.d.m(e)},t.requestFormReset=function(e){s.d.r(e)},t.unstable_batchedUpdates=function(e,t){return e(t)},t.useFormState=function(e,t,n){return l.H.useFormState(e,t,n)},t.useFormStatus=function(){return l.H.useHostTransitionStatus()},t.version="19.1.1"},507:(e,t,n)=>{e.exports=n(935)},532:(e,t,n)=>{var i=n(261),r=n(643),a=n(766);function s(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function l(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function c(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function u(e){if(l(e)!==e)throw Error(s(188))}function d(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=d(e)))return t;e=e.sibling}return null}var h=Object.assign,f=Symbol.for("react.element"),p=Symbol.for("react.transitional.element"),m=Symbol.for("react.portal"),g=Symbol.for("react.fragment"),b=Symbol.for("react.strict_mode"),y=Symbol.for("react.profiler"),x=Symbol.for("react.provider"),v=Symbol.for("react.consumer"),w=Symbol.for("react.context"),k=Symbol.for("react.forward_ref"),_=Symbol.for("react.suspense"),S=Symbol.for("react.suspense_list"),N=Symbol.for("react.memo"),M=Symbol.for("react.lazy");Symbol.for("react.scope");var C=Symbol.for("react.activity");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var P=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var E=Symbol.iterator;function j(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=E&&e[E]||e["@@iterator"])?e:null}var T=Symbol.for("react.client.reference");function L(e){if(null==e)return null;if("function"===typeof e)return e.$$typeof===T?null:e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case g:return"Fragment";case y:return"Profiler";case b:return"StrictMode";case _:return"Suspense";case S:return"SuspenseList";case C:return"Activity"}if("object"===typeof e)switch(e.$$typeof){case m:return"Portal";case w:return(e.displayName||"Context")+".Provider";case v:return(e._context.displayName||"Context")+".Consumer";case k:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case N:return null!==(t=e.displayName||null)?t:L(e.type)||"Memo";case M:t=e._payload,e=e._init;try{return L(e(t))}catch(n){}}return null}var O=Array.isArray,A=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,D=a.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,R={pending:!1,data:null,method:null,action:null},z=[],F=-1;function I(e){return{current:e}}function W(e){0>F||(e.current=z[F],z[F]=null,F--)}function B(e,t){F++,z[F]=e.current,e.current=t}var H=I(null),V=I(null),U=I(null),$=I(null);function Y(e,t){switch(B(U,t),B(V,e),B(H,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?ad(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=sd(t=ad(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}W(H),B(H,e)}function q(){W(H),W(V),W(U)}function K(e){null!==e.memoizedState&&B($,e);var t=H.current,n=sd(t,e.type);t!==n&&(B(V,e),B(H,n))}function Q(e){V.current===e&&(W(H),W(V)),$.current===e&&(W($),Qd._currentValue=R)}var X=Object.prototype.hasOwnProperty,G=i.unstable_scheduleCallback,J=i.unstable_cancelCallback,Z=i.unstable_shouldYield,ee=i.unstable_requestPaint,te=i.unstable_now,ne=i.unstable_getCurrentPriorityLevel,ie=i.unstable_ImmediatePriority,re=i.unstable_UserBlockingPriority,ae=i.unstable_NormalPriority,se=i.unstable_LowPriority,oe=i.unstable_IdlePriority,le=i.log,ce=i.unstable_setDisableYieldValue,ue=null,de=null;function he(e){if("function"===typeof le&&ce(e),de&&"function"===typeof de.setStrictMode)try{de.setStrictMode(ue,e)}catch(t){}}var fe=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(pe(e)/me|0)|0},pe=Math.log,me=Math.LN2;var ge=256,be=4194304;function ye(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function xe(e,t,n){var i=e.pendingLanes;if(0===i)return 0;var r=0,a=e.suspendedLanes,s=e.pingedLanes;e=e.warmLanes;var o=134217727&i;return 0!==o?0!==(i=o&~a)?r=ye(i):0!==(s&=o)?r=ye(s):n||0!==(n=o&~e)&&(r=ye(n)):0!==(o=i&~a)?r=ye(o):0!==s?r=ye(s):n||0!==(n=i&~e)&&(r=ye(n)),0===r?0:0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(n=t&-t)||32===a&&0!==(4194048&n))?t:r}function ve(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function we(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ke(){var e=ge;return 0===(4194048&(ge<<=1))&&(ge=256),e}function _e(){var e=be;return 0===(62914560&(be<<=1))&&(be=4194304),e}function Se(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ne(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Me(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var i=31-fe(t);e.entangledLanes|=t,e.entanglements[i]=1073741824|e.entanglements[i]|4194090&n}function Ce(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var i=31-fe(n),r=1<<i;r&t|e[i]&t&&(e[i]|=t),n&=~r}}function Pe(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Ee(e){return 2<(e&=-e)?8<e?0!==(134217727&e)?32:268435456:8:2}function je(){var e=D.p;return 0!==e?e:void 0===(e=window.event)?32:uh(e.type)}var Te=Math.random().toString(36).slice(2),Le="__reactFiber$"+Te,Oe="__reactProps$"+Te,Ae="__reactContainer$"+Te,De="__reactEvents$"+Te,Re="__reactListeners$"+Te,ze="__reactHandles$"+Te,Fe="__reactResources$"+Te,Ie="__reactMarker$"+Te;function We(e){delete e[Le],delete e[Oe],delete e[De],delete e[Re],delete e[ze]}function Be(e){var t=e[Le];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Ae]||n[Le]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=vd(e);null!==e;){if(n=e[Le])return n;e=vd(e)}return t}n=(e=n).parentNode}return null}function He(e){if(e=e[Le]||e[Ae]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function Ve(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(s(33))}function Ue(e){var t=e[Fe];return t||(t=e[Fe]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function $e(e){e[Ie]=!0}var Ye=new Set,qe={};function Ke(e,t){Qe(e,t),Qe(e+"Capture",t)}function Qe(e,t){for(qe[e]=t,e=0;e<t.length;e++)Ye.add(t[e])}var Xe,Ge,Je=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ze={},et={};function tt(e,t,n){if(r=t,X.call(et,r)||!X.call(Ze,r)&&(Je.test(r)?et[r]=!0:(Ze[r]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var i=t.toLowerCase().slice(0,5);if("data-"!==i&&"aria-"!==i)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var r}function nt(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function it(e,t,n,i){if(null===i)e.removeAttribute(n);else{switch(typeof i){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+i)}}function rt(e){if(void 0===Xe)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Xe=t&&t[1]||"",Ge=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Xe+e+Ge}var at=!1;function st(e,t){if(!e||at)return"";at=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var i={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(r){var i=r}Reflect.construct(e,[],n)}else{try{n.call()}catch(a){i=a}e.call(n.prototype)}}else{try{throw Error()}catch(s){i=s}(n=e())&&"function"===typeof n.catch&&n.catch(function(){})}}catch(ws){if(ws&&i&&"string"===typeof ws.stack)return[ws.stack,i.stack]}return[null,null]}};i.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var r=Object.getOwnPropertyDescriptor(i.DetermineComponentFrameRoot,"name");r&&r.configurable&&Object.defineProperty(i.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var a=i.DetermineComponentFrameRoot(),s=a[0],o=a[1];if(s&&o){var l=s.split("\n"),c=o.split("\n");for(r=i=0;i<l.length&&!l[i].includes("DetermineComponentFrameRoot");)i++;for(;r<c.length&&!c[r].includes("DetermineComponentFrameRoot");)r++;if(i===l.length||r===c.length)for(i=l.length-1,r=c.length-1;1<=i&&0<=r&&l[i]!==c[r];)r--;for(;1<=i&&0<=r;i--,r--)if(l[i]!==c[r]){if(1!==i||1!==r)do{if(i--,0>--r||l[i]!==c[r]){var u="\n"+l[i].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}}while(1<=i&&0<=r);break}}}finally{at=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?rt(n):""}function ot(e){switch(e.tag){case 26:case 27:case 5:return rt(e.type);case 16:return rt("Lazy");case 13:return rt("Suspense");case 19:return rt("SuspenseList");case 0:case 15:return st(e.type,!1);case 11:return st(e.type.render,!1);case 1:return st(e.type,!0);case 31:return rt("Activity");default:return""}}function lt(e){try{var t="";do{t+=ot(e),e=e.return}while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function ct(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function ut(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function dt(e){e._valueTracker||(e._valueTracker=function(e){var t=ut(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),i=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var r=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return r.call(this)},set:function(e){i=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return i},setValue:function(e){i=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function ht(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),i="";return e&&(i=ut(e)?e.checked?"true":"false":e.value),(e=i)!==n&&(t.setValue(e),!0)}function ft(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var pt=/[\n"\\]/g;function mt(e){return e.replace(pt,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function gt(e,t,n,i,r,a,s,o){e.name="",null!=s&&"function"!==typeof s&&"symbol"!==typeof s&&"boolean"!==typeof s?e.type=s:e.removeAttribute("type"),null!=t?"number"===s?(0===t&&""===e.value||e.value!=t)&&(e.value=""+ct(t)):e.value!==""+ct(t)&&(e.value=""+ct(t)):"submit"!==s&&"reset"!==s||e.removeAttribute("value"),null!=t?yt(e,s,ct(t)):null!=n?yt(e,s,ct(n)):null!=i&&e.removeAttribute("value"),null==r&&null!=a&&(e.defaultChecked=!!a),null!=r&&(e.checked=r&&"function"!==typeof r&&"symbol"!==typeof r),null!=o&&"function"!==typeof o&&"symbol"!==typeof o&&"boolean"!==typeof o?e.name=""+ct(o):e.removeAttribute("name")}function bt(e,t,n,i,r,a,s,o){if(null!=a&&"function"!==typeof a&&"symbol"!==typeof a&&"boolean"!==typeof a&&(e.type=a),null!=t||null!=n){if(!("submit"!==a&&"reset"!==a||void 0!==t&&null!==t))return;n=null!=n?""+ct(n):"",t=null!=t?""+ct(t):n,o||t===e.value||(e.value=t),e.defaultValue=t}i="function"!==typeof(i=null!=i?i:r)&&"symbol"!==typeof i&&!!i,e.checked=o?e.checked:!!i,e.defaultChecked=!!i,null!=s&&"function"!==typeof s&&"symbol"!==typeof s&&"boolean"!==typeof s&&(e.name=s)}function yt(e,t,n){"number"===t&&ft(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function xt(e,t,n,i){if(e=e.options,t){t={};for(var r=0;r<n.length;r++)t["$"+n[r]]=!0;for(n=0;n<e.length;n++)r=t.hasOwnProperty("$"+e[n].value),e[n].selected!==r&&(e[n].selected=r),r&&i&&(e[n].defaultSelected=!0)}else{for(n=""+ct(n),t=null,r=0;r<e.length;r++){if(e[r].value===n)return e[r].selected=!0,void(i&&(e[r].defaultSelected=!0));null!==t||e[r].disabled||(t=e[r])}null!==t&&(t.selected=!0)}}function vt(e,t,n){null==t||((t=""+ct(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+ct(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function wt(e,t,n,i){if(null==t){if(null!=i){if(null!=n)throw Error(s(92));if(O(i)){if(1<i.length)throw Error(s(93));i=i[0]}n=i}null==n&&(n=""),t=n}n=ct(t),e.defaultValue=n,(i=e.textContent)===n&&""!==i&&null!==i&&(e.value=i)}function kt(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var _t=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function St(e,t,n){var i=0===t.indexOf("--");null==n||"boolean"===typeof n||""===n?i?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":i?e.setProperty(t,n):"number"!==typeof n||0===n||_t.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Nt(e,t,n){if(null!=t&&"object"!==typeof t)throw Error(s(62));if(e=e.style,null!=n){for(var i in n)!n.hasOwnProperty(i)||null!=t&&t.hasOwnProperty(i)||(0===i.indexOf("--")?e.setProperty(i,""):"float"===i?e.cssFloat="":e[i]="");for(var r in t)i=t[r],t.hasOwnProperty(r)&&n[r]!==i&&St(e,r,i)}else for(var a in t)t.hasOwnProperty(a)&&St(e,a,t[a])}function Mt(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ct=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Pt=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Et(e){return Pt.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var jt=null;function Tt(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Lt=null,Ot=null;function At(e){var t=He(e);if(t&&(e=t.stateNode)){var n=e[Oe]||null;e:switch(e=t.stateNode,t.type){case"input":if(gt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+mt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var i=n[t];if(i!==e&&i.form===e.form){var r=i[Oe]||null;if(!r)throw Error(s(90));gt(i,r.value,r.defaultValue,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name)}}for(t=0;t<n.length;t++)(i=n[t]).form===e.form&&ht(i)}break e;case"textarea":vt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&xt(e,!!n.multiple,t,!1)}}}var Dt=!1;function Rt(e,t,n){if(Dt)return e(t,n);Dt=!0;try{return e(t)}finally{if(Dt=!1,(null!==Lt||null!==Ot)&&(Hc(),Lt&&(t=Lt,e=Ot,Ot=Lt=null,At(t),e)))for(t=0;t<e.length;t++)At(e[t])}}function zt(e,t){var n=e.stateNode;if(null===n)return null;var i=n[Oe]||null;if(null===i)return null;n=i[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(i=!i.disabled)||(i=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!i;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(s(231,t,typeof n));return n}var Ft=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),It=!1;if(Ft)try{var Wt={};Object.defineProperty(Wt,"passive",{get:function(){It=!0}}),window.addEventListener("test",Wt,Wt),window.removeEventListener("test",Wt,Wt)}catch(Ah){It=!1}var Bt=null,Ht=null,Vt=null;function Ut(){if(Vt)return Vt;var e,t,n=Ht,i=n.length,r="value"in Bt?Bt.value:Bt.textContent,a=r.length;for(e=0;e<i&&n[e]===r[e];e++);var s=i-e;for(t=1;t<=s&&n[i-t]===r[a-t];t++);return Vt=r.slice(e,1<t?1-t:void 0)}function $t(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function Yt(){return!0}function qt(){return!1}function Kt(e){function t(t,n,i,r,a){for(var s in this._reactName=t,this._targetInst=i,this.type=n,this.nativeEvent=r,this.target=a,this.currentTarget=null,e)e.hasOwnProperty(s)&&(t=e[s],this[s]=t?t(r):r[s]);return this.isDefaultPrevented=(null!=r.defaultPrevented?r.defaultPrevented:!1===r.returnValue)?Yt:qt,this.isPropagationStopped=qt,this}return h(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Yt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Yt)},persist:function(){},isPersistent:Yt}),t}var Qt,Xt,Gt,Jt={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Zt=Kt(Jt),en=h({},Jt,{view:0,detail:0}),tn=Kt(en),nn=h({},en,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:pn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Gt&&(Gt&&"mousemove"===e.type?(Qt=e.screenX-Gt.screenX,Xt=e.screenY-Gt.screenY):Xt=Qt=0,Gt=e),Qt)},movementY:function(e){return"movementY"in e?e.movementY:Xt}}),rn=Kt(nn),an=Kt(h({},nn,{dataTransfer:0})),sn=Kt(h({},en,{relatedTarget:0})),on=Kt(h({},Jt,{animationName:0,elapsedTime:0,pseudoElement:0})),ln=Kt(h({},Jt,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),cn=Kt(h({},Jt,{data:0})),un={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},hn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function fn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=hn[e])&&!!t[e]}function pn(){return fn}var mn=Kt(h({},en,{key:function(e){if(e.key){var t=un[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=$t(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?dn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:pn,charCode:function(e){return"keypress"===e.type?$t(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?$t(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),gn=Kt(h({},nn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),bn=Kt(h({},en,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:pn})),yn=Kt(h({},Jt,{propertyName:0,elapsedTime:0,pseudoElement:0})),xn=Kt(h({},nn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),vn=Kt(h({},Jt,{newState:0,oldState:0})),wn=[9,13,27,32],kn=Ft&&"CompositionEvent"in window,_n=null;Ft&&"documentMode"in document&&(_n=document.documentMode);var Sn=Ft&&"TextEvent"in window&&!_n,Nn=Ft&&(!kn||_n&&8<_n&&11>=_n),Mn=String.fromCharCode(32),Cn=!1;function Pn(e,t){switch(e){case"keyup":return-1!==wn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function En(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var jn=!1;var Tn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ln(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Tn[e.type]:"textarea"===t}function On(e,t,n,i){Lt?Ot?Ot.push(i):Ot=[i]:Lt=i,0<(t=$u(t,"onChange")).length&&(n=new Zt("onChange","change",null,n,i),e.push({event:n,listeners:t}))}var An=null,Dn=null;function Rn(e){zu(e,0)}function zn(e){if(ht(Ve(e)))return e}function Fn(e,t){if("change"===e)return t}var In=!1;if(Ft){var Wn;if(Ft){var Bn="oninput"in document;if(!Bn){var Hn=document.createElement("div");Hn.setAttribute("oninput","return;"),Bn="function"===typeof Hn.oninput}Wn=Bn}else Wn=!1;In=Wn&&(!document.documentMode||9<document.documentMode)}function Vn(){An&&(An.detachEvent("onpropertychange",Un),Dn=An=null)}function Un(e){if("value"===e.propertyName&&zn(Dn)){var t=[];On(t,Dn,e,Tt(e)),Rt(Rn,t)}}function $n(e,t,n){"focusin"===e?(Vn(),Dn=n,(An=t).attachEvent("onpropertychange",Un)):"focusout"===e&&Vn()}function Yn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return zn(Dn)}function qn(e,t){if("click"===e)return zn(t)}function Kn(e,t){if("input"===e||"change"===e)return zn(t)}var Qn="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function Xn(e,t){if(Qn(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),i=Object.keys(t);if(n.length!==i.length)return!1;for(i=0;i<n.length;i++){var r=n[i];if(!X.call(t,r)||!Qn(e[r],t[r]))return!1}return!0}function Gn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Jn(e,t){var n,i=Gn(e);for(e=0;i;){if(3===i.nodeType){if(n=e+i.textContent.length,e<=t&&n>=t)return{node:i,offset:t-e};e=n}e:{for(;i;){if(i.nextSibling){i=i.nextSibling;break e}i=i.parentNode}i=void 0}i=Gn(i)}}function Zn(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?Zn(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function ei(e){for(var t=ft((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(i){n=!1}if(!n)break;t=ft((e=t.contentWindow).document)}return t}function ti(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var ni=Ft&&"documentMode"in document&&11>=document.documentMode,ii=null,ri=null,ai=null,si=!1;function oi(e,t,n){var i=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;si||null==ii||ii!==ft(i)||("selectionStart"in(i=ii)&&ti(i)?i={start:i.selectionStart,end:i.selectionEnd}:i={anchorNode:(i=(i.ownerDocument&&i.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:i.anchorOffset,focusNode:i.focusNode,focusOffset:i.focusOffset},ai&&Xn(ai,i)||(ai=i,0<(i=$u(ri,"onSelect")).length&&(t=new Zt("onSelect","select",null,t,n),e.push({event:t,listeners:i}),t.target=ii)))}function li(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ci={animationend:li("Animation","AnimationEnd"),animationiteration:li("Animation","AnimationIteration"),animationstart:li("Animation","AnimationStart"),transitionrun:li("Transition","TransitionRun"),transitionstart:li("Transition","TransitionStart"),transitioncancel:li("Transition","TransitionCancel"),transitionend:li("Transition","TransitionEnd")},ui={},di={};function hi(e){if(ui[e])return ui[e];if(!ci[e])return e;var t,n=ci[e];for(t in n)if(n.hasOwnProperty(t)&&t in di)return ui[e]=n[t];return e}Ft&&(di=document.createElement("div").style,"AnimationEvent"in window||(delete ci.animationend.animation,delete ci.animationiteration.animation,delete ci.animationstart.animation),"TransitionEvent"in window||delete ci.transitionend.transition);var fi=hi("animationend"),pi=hi("animationiteration"),mi=hi("animationstart"),gi=hi("transitionrun"),bi=hi("transitionstart"),yi=hi("transitioncancel"),xi=hi("transitionend"),vi=new Map,wi="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function ki(e,t){vi.set(e,t),Ke(t,[e])}wi.push("scrollEnd");var _i=new WeakMap;function Si(e,t){if("object"===typeof e&&null!==e){var n=_i.get(e);return void 0!==n?n:(t={value:e,source:t,stack:lt(t)},_i.set(e,t),t)}return{value:e,source:t,stack:lt(t)}}var Ni=[],Mi=0,Ci=0;function Pi(){for(var e=Mi,t=Ci=Mi=0;t<e;){var n=Ni[t];Ni[t++]=null;var i=Ni[t];Ni[t++]=null;var r=Ni[t];Ni[t++]=null;var a=Ni[t];if(Ni[t++]=null,null!==i&&null!==r){var s=i.pending;null===s?r.next=r:(r.next=s.next,s.next=r),i.pending=r}0!==a&&Li(n,r,a)}}function Ei(e,t,n,i){Ni[Mi++]=e,Ni[Mi++]=t,Ni[Mi++]=n,Ni[Mi++]=i,Ci|=i,e.lanes|=i,null!==(e=e.alternate)&&(e.lanes|=i)}function ji(e,t,n,i){return Ei(e,t,n,i),Oi(e)}function Ti(e,t){return Ei(e,null,null,t),Oi(e)}function Li(e,t,n){e.lanes|=n;var i=e.alternate;null!==i&&(i.lanes|=n);for(var r=!1,a=e.return;null!==a;)a.childLanes|=n,null!==(i=a.alternate)&&(i.childLanes|=n),22===a.tag&&(null===(e=a.stateNode)||1&e._visibility||(r=!0)),e=a,a=a.return;return 3===e.tag?(a=e.stateNode,r&&null!==t&&(r=31-fe(n),null===(i=(e=a.hiddenUpdates)[r])?e[r]=[t]:i.push(t),t.lane=536870912|n),a):null}function Oi(e){if(50<Oc)throw Oc=0,Ac=null,Error(s(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var Ai={};function Di(e,t,n,i){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=i,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ri(e,t,n,i){return new Di(e,t,n,i)}function zi(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Fi(e,t){var n=e.alternate;return null===n?((n=Ri(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Ii(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Wi(e,t,n,i,r,a){var o=0;if(i=e,"function"===typeof e)zi(e)&&(o=1);else if("string"===typeof e)o=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!==typeof t.precedence||"string"!==typeof t.href||""===t.href)break;return!0;case"link":if("string"!==typeof t.rel||"string"!==typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"===typeof t.precedence&&null==e);case"script":if(t.async&&"function"!==typeof t.async&&"symbol"!==typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"===typeof t.src)return!0}return!1}(e,n,H.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case C:return(e=Ri(31,n,t,r)).elementType=C,e.lanes=a,e;case g:return Bi(n.children,r,a,t);case b:o=8,r|=24;break;case y:return(e=Ri(12,n,t,2|r)).elementType=y,e.lanes=a,e;case _:return(e=Ri(13,n,t,r)).elementType=_,e.lanes=a,e;case S:return(e=Ri(19,n,t,r)).elementType=S,e.lanes=a,e;default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case x:case w:o=10;break e;case v:o=9;break e;case k:o=11;break e;case N:o=14;break e;case M:o=16,i=null;break e}o=29,n=Error(s(130,null===e?"null":typeof e,"")),i=null}return(t=Ri(o,n,t,r)).elementType=e,t.type=i,t.lanes=a,t}function Bi(e,t,n,i){return(e=Ri(7,e,i,t)).lanes=n,e}function Hi(e,t,n){return(e=Ri(6,e,null,t)).lanes=n,e}function Vi(e,t,n){return(t=Ri(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Ui=[],$i=0,Yi=null,qi=0,Ki=[],Qi=0,Xi=null,Gi=1,Ji="";function Zi(e,t){Ui[$i++]=qi,Ui[$i++]=Yi,Yi=e,qi=t}function er(e,t,n){Ki[Qi++]=Gi,Ki[Qi++]=Ji,Ki[Qi++]=Xi,Xi=e;var i=Gi;e=Ji;var r=32-fe(i)-1;i&=~(1<<r),n+=1;var a=32-fe(t)+r;if(30<a){var s=r-r%5;a=(i&(1<<s)-1).toString(32),i>>=s,r-=s,Gi=1<<32-fe(t)+r|n<<r|i,Ji=a+e}else Gi=1<<a|n<<r|i,Ji=e}function tr(e){null!==e.return&&(Zi(e,1),er(e,1,0))}function nr(e){for(;e===Yi;)Yi=Ui[--$i],Ui[$i]=null,qi=Ui[--$i],Ui[$i]=null;for(;e===Xi;)Xi=Ki[--Qi],Ki[Qi]=null,Ji=Ki[--Qi],Ki[Qi]=null,Gi=Ki[--Qi],Ki[Qi]=null}var ir=null,rr=null,ar=!1,sr=null,or=!1,lr=Error(s(519));function cr(e){throw mr(Si(Error(s(418,"")),e)),lr}function ur(e){var t=e.stateNode,n=e.type,i=e.memoizedProps;switch(t[Le]=e,t[Oe]=i,n){case"dialog":Fu("cancel",t),Fu("close",t);break;case"iframe":case"object":case"embed":Fu("load",t);break;case"video":case"audio":for(n=0;n<Du.length;n++)Fu(Du[n],t);break;case"source":Fu("error",t);break;case"img":case"image":case"link":Fu("error",t),Fu("load",t);break;case"details":Fu("toggle",t);break;case"input":Fu("invalid",t),bt(t,i.value,i.defaultValue,i.checked,i.defaultChecked,i.type,i.name,!0),dt(t);break;case"select":Fu("invalid",t);break;case"textarea":Fu("invalid",t),wt(t,i.value,i.defaultValue,i.children),dt(t)}"string"!==typeof(n=i.children)&&"number"!==typeof n&&"bigint"!==typeof n||t.textContent===""+n||!0===i.suppressHydrationWarning||Gu(t.textContent,n)?(null!=i.popover&&(Fu("beforetoggle",t),Fu("toggle",t)),null!=i.onScroll&&Fu("scroll",t),null!=i.onScrollEnd&&Fu("scrollend",t),null!=i.onClick&&(t.onclick=Ju),t=!0):t=!1,t||cr(e)}function dr(e){for(ir=e.return;ir;)switch(ir.tag){case 5:case 13:return void(or=!1);case 27:case 3:return void(or=!0);default:ir=ir.return}}function hr(e){if(e!==ir)return!1;if(!ar)return dr(e),ar=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||od(e.type,e.memoizedProps)),t=!t),t&&rr&&cr(e),dr(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(s(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){rr=yd(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}rr=null}}else 27===n?(n=rr,pd(e.type)?(e=xd,xd=null,rr=e):rr=n):rr=ir?yd(e.stateNode.nextSibling):null;return!0}function fr(){rr=ir=null,ar=!1}function pr(){var e=sr;return null!==e&&(null===vc?vc=e:vc.push.apply(vc,e),sr=null),e}function mr(e){null===sr?sr=[e]:sr.push(e)}var gr=I(null),br=null,yr=null;function xr(e,t,n){B(gr,t._currentValue),t._currentValue=n}function vr(e){e._currentValue=gr.current,W(gr)}function wr(e,t,n){for(;null!==e;){var i=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==i&&(i.childLanes|=t)):null!==i&&(i.childLanes&t)!==t&&(i.childLanes|=t),e===n)break;e=e.return}}function kr(e,t,n,i){var r=e.child;for(null!==r&&(r.return=e);null!==r;){var a=r.dependencies;if(null!==a){var o=r.child;a=a.firstContext;e:for(;null!==a;){var l=a;a=r;for(var c=0;c<t.length;c++)if(l.context===t[c]){a.lanes|=n,null!==(l=a.alternate)&&(l.lanes|=n),wr(a.return,n,e),i||(o=null);break e}a=l.next}}else if(18===r.tag){if(null===(o=r.return))throw Error(s(341));o.lanes|=n,null!==(a=o.alternate)&&(a.lanes|=n),wr(o,n,e),o=null}else o=r.child;if(null!==o)o.return=r;else for(o=r;null!==o;){if(o===e){o=null;break}if(null!==(r=o.sibling)){r.return=o.return,o=r;break}o=o.return}r=o}}function _r(e,t,n,i){e=null;for(var r=t,a=!1;null!==r;){if(!a)if(0!==(524288&r.flags))a=!0;else if(0!==(262144&r.flags))break;if(10===r.tag){var o=r.alternate;if(null===o)throw Error(s(387));if(null!==(o=o.memoizedProps)){var l=r.type;Qn(r.pendingProps.value,o.value)||(null!==e?e.push(l):e=[l])}}else if(r===$.current){if(null===(o=r.alternate))throw Error(s(387));o.memoizedState.memoizedState!==r.memoizedState.memoizedState&&(null!==e?e.push(Qd):e=[Qd])}r=r.return}null!==e&&kr(t,e,n,i),t.flags|=262144}function Sr(e){for(e=e.firstContext;null!==e;){if(!Qn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Nr(e){br=e,yr=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function Mr(e){return Pr(br,e)}function Cr(e,t){return null===br&&Nr(e),Pr(e,t)}function Pr(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===yr){if(null===e)throw Error(s(308));yr=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else yr=yr.next=t;return n}var Er="undefined"!==typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach(function(e){return e()})}},jr=i.unstable_scheduleCallback,Tr=i.unstable_NormalPriority,Lr={$$typeof:w,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Or(){return{controller:new Er,data:new Map,refCount:0}}function Ar(e){e.refCount--,0===e.refCount&&jr(Tr,function(){e.controller.abort()})}var Dr=null,Rr=0,zr=0,Fr=null;function Ir(){if(0===--Rr&&null!==Dr){null!==Fr&&(Fr.status="fulfilled");var e=Dr;Dr=null,zr=0,Fr=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Wr=A.S;A.S=function(e,t){"object"===typeof t&&null!==t&&"function"===typeof t.then&&function(e,t){if(null===Dr){var n=Dr=[];Rr=0,zr=ju(),Fr={status:"pending",value:void 0,then:function(e){n.push(e)}}}Rr++,t.then(Ir,Ir)}(0,t),null!==Wr&&Wr(e,t)};var Br=I(null);function Hr(){var e=Br.current;return null!==e?e:rc.pooledCache}function Vr(e,t){B(Br,null===t?Br.current:t.pool)}function Ur(){var e=Hr();return null===e?null:{parent:Lr._currentValue,pool:e}}var $r=Error(s(460)),Yr=Error(s(474)),qr=Error(s(542)),Kr={then:function(){}};function Qr(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Xr(){}function Gr(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(Xr,Xr),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw ea(e=t.reason),e;default:if("string"===typeof t.status)t.then(Xr,Xr);else{if(null!==(e=rc)&&100<e.shellSuspendCounter)throw Error(s(482));(e=t).status="pending",e.then(function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}},function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw ea(e=t.reason),e}throw Jr=t,$r}}var Jr=null;function Zr(){if(null===Jr)throw Error(s(459));var e=Jr;return Jr=null,e}function ea(e){if(e===$r||e===qr)throw Error(s(483))}var ta=!1;function na(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ia(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ra(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function aa(e,t,n){var i=e.updateQueue;if(null===i)return null;if(i=i.shared,0!==(2&ic)){var r=i.pending;return null===r?t.next=t:(t.next=r.next,r.next=t),i.pending=t,t=Oi(e),Li(e,null,n),t}return Ei(e,i,t,n),Oi(e)}function sa(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194048&n))){var i=t.lanes;n|=i&=e.pendingLanes,t.lanes=n,Ce(e,n)}}function oa(e,t){var n=e.updateQueue,i=e.alternate;if(null!==i&&n===(i=i.updateQueue)){var r=null,a=null;if(null!==(n=n.firstBaseUpdate)){do{var s={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===a?r=a=s:a=a.next=s,n=n.next}while(null!==n);null===a?r=a=t:a=a.next=t}else r=a=t;return n={baseState:i.baseState,firstBaseUpdate:r,lastBaseUpdate:a,shared:i.shared,callbacks:i.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var la=!1;function ca(){if(la){if(null!==Fr)throw Fr}}function ua(e,t,n,i){la=!1;var r=e.updateQueue;ta=!1;var a=r.firstBaseUpdate,s=r.lastBaseUpdate,o=r.shared.pending;if(null!==o){r.shared.pending=null;var l=o,c=l.next;l.next=null,null===s?a=c:s.next=c,s=l;var u=e.alternate;null!==u&&((o=(u=u.updateQueue).lastBaseUpdate)!==s&&(null===o?u.firstBaseUpdate=c:o.next=c,u.lastBaseUpdate=l))}if(null!==a){var d=r.baseState;for(s=0,u=c=l=null,o=a;;){var f=-536870913&o.lane,p=f!==o.lane;if(p?(sc&f)===f:(i&f)===f){0!==f&&f===zr&&(la=!0),null!==u&&(u=u.next={lane:0,tag:o.tag,payload:o.payload,callback:null,next:null});e:{var m=e,g=o;f=t;var b=n;switch(g.tag){case 1:if("function"===typeof(m=g.payload)){d=m.call(b,d,f);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null===(f="function"===typeof(m=g.payload)?m.call(b,d,f):m)||void 0===f)break e;d=h({},d,f);break e;case 2:ta=!0}}null!==(f=o.callback)&&(e.flags|=64,p&&(e.flags|=8192),null===(p=r.callbacks)?r.callbacks=[f]:p.push(f))}else p={lane:f,tag:o.tag,payload:o.payload,callback:o.callback,next:null},null===u?(c=u=p,l=d):u=u.next=p,s|=f;if(null===(o=o.next)){if(null===(o=r.shared.pending))break;o=(p=o).next,p.next=null,r.lastBaseUpdate=p,r.shared.pending=null}}null===u&&(l=d),r.baseState=l,r.firstBaseUpdate=c,r.lastBaseUpdate=u,null===a&&(r.shared.lanes=0),pc|=s,e.lanes=s,e.memoizedState=d}}function da(e,t){if("function"!==typeof e)throw Error(s(191,e));e.call(t)}function ha(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)da(n[e],t)}var fa=I(null),pa=I(0);function ma(e,t){B(pa,e=hc),B(fa,t),hc=e|t.baseLanes}function ga(){B(pa,hc),B(fa,fa.current)}function ba(){hc=pa.current,W(fa),W(pa)}var ya=0,xa=null,va=null,wa=null,ka=!1,_a=!1,Sa=!1,Na=0,Ma=0,Ca=null,Pa=0;function Ea(){throw Error(s(321))}function ja(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Qn(e[n],t[n]))return!1;return!0}function Ta(e,t,n,i,r,a){return ya=a,xa=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,A.H=null===e||null===e.memoizedState?Ys:qs,Sa=!1,a=n(i,r),Sa=!1,_a&&(a=Oa(t,n,i,r)),La(e),a}function La(e){A.H=$s;var t=null!==va&&null!==va.next;if(ya=0,wa=va=xa=null,ka=!1,Ma=0,Ca=null,t)throw Error(s(300));null===e||Po||null!==(e=e.dependencies)&&Sr(e)&&(Po=!0)}function Oa(e,t,n,i){xa=e;var r=0;do{if(_a&&(Ca=null),Ma=0,_a=!1,25<=r)throw Error(s(301));if(r+=1,wa=va=null,null!=e.updateQueue){var a=e.updateQueue;a.lastEffect=null,a.events=null,a.stores=null,null!=a.memoCache&&(a.memoCache.index=0)}A.H=Ks,a=t(n,i)}while(_a);return a}function Aa(){var e=A.H,t=e.useState()[0];return t="function"===typeof t.then?Wa(t):t,e=e.useState()[0],(null!==va?va.memoizedState:null)!==e&&(xa.flags|=1024),t}function Da(){var e=0!==Na;return Na=0,e}function Ra(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function za(e){if(ka){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}ka=!1}ya=0,wa=va=xa=null,_a=!1,Ma=Na=0,Ca=null}function Fa(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===wa?xa.memoizedState=wa=e:wa=wa.next=e,wa}function Ia(){if(null===va){var e=xa.alternate;e=null!==e?e.memoizedState:null}else e=va.next;var t=null===wa?xa.memoizedState:wa.next;if(null!==t)wa=t,va=e;else{if(null===e){if(null===xa.alternate)throw Error(s(467));throw Error(s(310))}e={memoizedState:(va=e).memoizedState,baseState:va.baseState,baseQueue:va.baseQueue,queue:va.queue,next:null},null===wa?xa.memoizedState=wa=e:wa=wa.next=e}return wa}function Wa(e){var t=Ma;return Ma+=1,null===Ca&&(Ca=[]),e=Gr(Ca,e,t),t=xa,null===(null===wa?t.memoizedState:wa.next)&&(t=t.alternate,A.H=null===t||null===t.memoizedState?Ys:qs),e}function Ba(e){if(null!==e&&"object"===typeof e){if("function"===typeof e.then)return Wa(e);if(e.$$typeof===w)return Mr(e)}throw Error(s(438,String(e)))}function Ha(e){var t=null,n=xa.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var i=xa.alternate;null!==i&&(null!==(i=i.updateQueue)&&(null!=(i=i.memoCache)&&(t={data:i.data.map(function(e){return e.slice()}),index:0})))}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},xa.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),i=0;i<e;i++)n[i]=P;return t.index++,n}function Va(e,t){return"function"===typeof t?t(e):t}function Ua(e){return $a(Ia(),va,e)}function $a(e,t,n){var i=e.queue;if(null===i)throw Error(s(311));i.lastRenderedReducer=n;var r=e.baseQueue,a=i.pending;if(null!==a){if(null!==r){var o=r.next;r.next=a.next,a.next=o}t.baseQueue=r=a,i.pending=null}if(a=e.baseState,null===r)e.memoizedState=a;else{var l=o=null,c=null,u=t=r.next,d=!1;do{var h=-536870913&u.lane;if(h!==u.lane?(sc&h)===h:(ya&h)===h){var f=u.revertLane;if(0===f)null!==c&&(c=c.next={lane:0,revertLane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),h===zr&&(d=!0);else{if((ya&f)===f){u=u.next,f===zr&&(d=!0);continue}h={lane:0,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(l=c=h,o=a):c=c.next=h,xa.lanes|=f,pc|=f}h=u.action,Sa&&n(a,h),a=u.hasEagerState?u.eagerState:n(a,h)}else f={lane:h,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(l=c=f,o=a):c=c.next=f,xa.lanes|=h,pc|=h;u=u.next}while(null!==u&&u!==t);if(null===c?o=a:c.next=l,!Qn(a,e.memoizedState)&&(Po=!0,d&&null!==(n=Fr)))throw n;e.memoizedState=a,e.baseState=o,e.baseQueue=c,i.lastRenderedState=a}return null===r&&(i.lanes=0),[e.memoizedState,i.dispatch]}function Ya(e){var t=Ia(),n=t.queue;if(null===n)throw Error(s(311));n.lastRenderedReducer=e;var i=n.dispatch,r=n.pending,a=t.memoizedState;if(null!==r){n.pending=null;var o=r=r.next;do{a=e(a,o.action),o=o.next}while(o!==r);Qn(a,t.memoizedState)||(Po=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),n.lastRenderedState=a}return[a,i]}function qa(e,t,n){var i=xa,r=Ia(),a=ar;if(a){if(void 0===n)throw Error(s(407));n=n()}else n=t();var o=!Qn((va||r).memoizedState,n);if(o&&(r.memoizedState=n,Po=!0),r=r.queue,gs(2048,8,Xa.bind(null,i,r,e),[e]),r.getSnapshot!==t||o||null!==wa&&1&wa.memoizedState.tag){if(i.flags|=2048,fs(9,{destroy:void 0,resource:void 0},Qa.bind(null,i,r,n,t),null),null===rc)throw Error(s(349));a||0!==(124&ya)||Ka(i,t,n)}return n}function Ka(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=xa.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},xa.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Qa(e,t,n,i){t.value=n,t.getSnapshot=i,Ga(t)&&Ja(e)}function Xa(e,t,n){return n(function(){Ga(t)&&Ja(e)})}function Ga(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Qn(e,n)}catch(i){return!0}}function Ja(e){var t=Ti(e,2);null!==t&&zc(t,e,2)}function Za(e){var t=Fa();if("function"===typeof e){var n=e;if(e=n(),Sa){he(!0);try{n()}finally{he(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Va,lastRenderedState:e},t}function es(e,t,n,i){return e.baseState=n,$a(e,va,"function"===typeof i?i:Va)}function ts(e,t,n,i,r){if(Hs(e))throw Error(s(485));if(null!==(e=t.action)){var a={payload:r,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){a.listeners.push(e)}};null!==A.T?n(!0):a.isTransition=!1,i(a),null===(n=t.pending)?(a.next=t.pending=a,ns(t,a)):(a.next=n.next,t.pending=n.next=a)}}function ns(e,t){var n=t.action,i=t.payload,r=e.state;if(t.isTransition){var a=A.T,s={};A.T=s;try{var o=n(r,i),l=A.S;null!==l&&l(s,o),is(e,t,o)}catch(c){as(e,t,c)}finally{A.T=a}}else try{is(e,t,a=n(r,i))}catch(u){as(e,t,u)}}function is(e,t,n){null!==n&&"object"===typeof n&&"function"===typeof n.then?n.then(function(n){rs(e,t,n)},function(n){return as(e,t,n)}):rs(e,t,n)}function rs(e,t,n){t.status="fulfilled",t.value=n,ss(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,ns(e,n)))}function as(e,t,n){var i=e.pending;if(e.pending=null,null!==i){i=i.next;do{t.status="rejected",t.reason=n,ss(t),t=t.next}while(t!==i)}e.action=null}function ss(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function os(e,t){return t}function ls(e,t){if(ar){var n=rc.formState;if(null!==n){e:{var i=xa;if(ar){if(rr){t:{for(var r=rr,a=or;8!==r.nodeType;){if(!a){r=null;break t}if(null===(r=yd(r.nextSibling))){r=null;break t}}r="F!"===(a=r.data)||"F"===a?r:null}if(r){rr=yd(r.nextSibling),i="F!"===r.data;break e}}cr(i)}i=!1}i&&(t=n[0])}}return(n=Fa()).memoizedState=n.baseState=t,i={pending:null,lanes:0,dispatch:null,lastRenderedReducer:os,lastRenderedState:t},n.queue=i,n=Is.bind(null,xa,i),i.dispatch=n,i=Za(!1),a=Bs.bind(null,xa,!1,i.queue),r={state:t,dispatch:null,action:e,pending:null},(i=Fa()).queue=r,n=ts.bind(null,xa,r,a,n),r.dispatch=n,i.memoizedState=e,[t,n,!1]}function cs(e){return us(Ia(),va,e)}function us(e,t,n){if(t=$a(e,t,os)[0],e=Ua(Va)[0],"object"===typeof t&&null!==t&&"function"===typeof t.then)try{var i=Wa(t)}catch(s){if(s===$r)throw qr;throw s}else i=t;var r=(t=Ia()).queue,a=r.dispatch;return n!==t.memoizedState&&(xa.flags|=2048,fs(9,{destroy:void 0,resource:void 0},ds.bind(null,r,n),null)),[i,a,e]}function ds(e,t){e.action=t}function hs(e){var t=Ia(),n=va;if(null!==n)return us(t,n,e);Ia(),t=t.memoizedState;var i=(n=Ia()).queue.dispatch;return n.memoizedState=e,[t,i,!1]}function fs(e,t,n,i){return e={tag:e,create:n,deps:i,inst:t,next:null},null===(t=xa.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},xa.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(i=n.next,n.next=e,e.next=i,t.lastEffect=e),e}function ps(){return Ia().memoizedState}function ms(e,t,n,i){var r=Fa();i=void 0===i?null:i,xa.flags|=e,r.memoizedState=fs(1|t,{destroy:void 0,resource:void 0},n,i)}function gs(e,t,n,i){var r=Ia();i=void 0===i?null:i;var a=r.memoizedState.inst;null!==va&&null!==i&&ja(i,va.memoizedState.deps)?r.memoizedState=fs(t,a,n,i):(xa.flags|=e,r.memoizedState=fs(1|t,a,n,i))}function bs(e,t){ms(8390656,8,e,t)}function ys(e,t){gs(2048,8,e,t)}function xs(e,t){return gs(4,2,e,t)}function vs(e,t){return gs(4,4,e,t)}function ks(e,t){if("function"===typeof t){e=e();var n=t(e);return function(){"function"===typeof n?n():t(null)}}if(null!==t&&void 0!==t)return e=e(),t.current=e,function(){t.current=null}}function _s(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,gs(4,4,ks.bind(null,t,e),n)}function Ss(){}function Ns(e,t){var n=Ia();t=void 0===t?null:t;var i=n.memoizedState;return null!==t&&ja(t,i[1])?i[0]:(n.memoizedState=[e,t],e)}function Ms(e,t){var n=Ia();t=void 0===t?null:t;var i=n.memoizedState;if(null!==t&&ja(t,i[1]))return i[0];if(i=e(),Sa){he(!0);try{e()}finally{he(!1)}}return n.memoizedState=[i,t],i}function Cs(e,t,n){return void 0===n||0!==(1073741824&ya)?e.memoizedState=t:(e.memoizedState=n,e=Rc(),xa.lanes|=e,pc|=e,n)}function Ps(e,t,n,i){return Qn(n,t)?n:null!==fa.current?(e=Cs(e,n,i),Qn(e,t)||(Po=!0),e):0===(42&ya)?(Po=!0,e.memoizedState=n):(e=Rc(),xa.lanes|=e,pc|=e,t)}function Es(e,t,n,i,r){var a=D.p;D.p=0!==a&&8>a?a:8;var s=A.T,o={};A.T=o,Bs(e,!1,t,n);try{var l=r(),c=A.S;if(null!==c&&c(o,l),null!==l&&"object"===typeof l&&"function"===typeof l.then){var u=function(e,t){var n=[],i={status:"pending",value:null,reason:null,then:function(e){n.push(e)}};return e.then(function(){i.status="fulfilled",i.value=t;for(var e=0;e<n.length;e++)(0,n[e])(t)},function(e){for(i.status="rejected",i.reason=e,e=0;e<n.length;e++)(0,n[e])(void 0)}),i}(l,i);Ws(e,t,u,Dc())}else Ws(e,t,i,Dc())}catch(d){Ws(e,t,{then:function(){},status:"rejected",reason:d},Dc())}finally{D.p=a,A.T=s}}function js(){}function Ts(e,t,n,i){if(5!==e.tag)throw Error(s(476));var r=Ls(e).queue;Es(e,r,t,R,null===n?js:function(){return Os(e),n(i)})}function Ls(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:R,baseState:R,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Va,lastRenderedState:R},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Va,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function Os(e){Ws(e,Ls(e).next.queue,{},Dc())}function As(){return Mr(Qd)}function Ds(){return Ia().memoizedState}function Rs(){return Ia().memoizedState}function zs(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=Dc(),i=aa(t,e=ra(n),n);return null!==i&&(zc(i,t,n),sa(i,t,n)),t={cache:Or()},void(e.payload=t)}t=t.return}}function Fs(e,t,n){var i=Dc();n={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},Hs(e)?Vs(t,n):null!==(n=ji(e,t,n,i))&&(zc(n,e,i),Us(n,t,i))}function Is(e,t,n){Ws(e,t,n,Dc())}function Ws(e,t,n,i){var r={lane:i,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(Hs(e))Vs(t,r);else{var a=e.alternate;if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=t.lastRenderedReducer))try{var s=t.lastRenderedState,o=a(s,n);if(r.hasEagerState=!0,r.eagerState=o,Qn(o,s))return Ei(e,t,r,0),null===rc&&Pi(),!1}catch(l){}if(null!==(n=ji(e,t,r,i)))return zc(n,e,i),Us(n,t,i),!0}return!1}function Bs(e,t,n,i){if(i={lane:2,revertLane:ju(),action:i,hasEagerState:!1,eagerState:null,next:null},Hs(e)){if(t)throw Error(s(479))}else null!==(t=ji(e,n,i,2))&&zc(t,e,2)}function Hs(e){var t=e.alternate;return e===xa||null!==t&&t===xa}function Vs(e,t){_a=ka=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Us(e,t,n){if(0!==(4194048&n)){var i=t.lanes;n|=i&=e.pendingLanes,t.lanes=n,Ce(e,n)}}var $s={readContext:Mr,use:Ba,useCallback:Ea,useContext:Ea,useEffect:Ea,useImperativeHandle:Ea,useLayoutEffect:Ea,useInsertionEffect:Ea,useMemo:Ea,useReducer:Ea,useRef:Ea,useState:Ea,useDebugValue:Ea,useDeferredValue:Ea,useTransition:Ea,useSyncExternalStore:Ea,useId:Ea,useHostTransitionStatus:Ea,useFormState:Ea,useActionState:Ea,useOptimistic:Ea,useMemoCache:Ea,useCacheRefresh:Ea},Ys={readContext:Mr,use:Ba,useCallback:function(e,t){return Fa().memoizedState=[e,void 0===t?null:t],e},useContext:Mr,useEffect:bs,useImperativeHandle:function(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,ms(4194308,4,ks.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ms(4194308,4,e,t)},useInsertionEffect:function(e,t){ms(4,2,e,t)},useMemo:function(e,t){var n=Fa();t=void 0===t?null:t;var i=e();if(Sa){he(!0);try{e()}finally{he(!1)}}return n.memoizedState=[i,t],i},useReducer:function(e,t,n){var i=Fa();if(void 0!==n){var r=n(t);if(Sa){he(!0);try{n(t)}finally{he(!1)}}}else r=t;return i.memoizedState=i.baseState=r,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:r},i.queue=e,e=e.dispatch=Fs.bind(null,xa,e),[i.memoizedState,e]},useRef:function(e){return e={current:e},Fa().memoizedState=e},useState:function(e){var t=(e=Za(e)).queue,n=Is.bind(null,xa,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Ss,useDeferredValue:function(e,t){return Cs(Fa(),e,t)},useTransition:function(){var e=Za(!1);return e=Es.bind(null,xa,e.queue,!0,!1),Fa().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var i=xa,r=Fa();if(ar){if(void 0===n)throw Error(s(407));n=n()}else{if(n=t(),null===rc)throw Error(s(349));0!==(124&sc)||Ka(i,t,n)}r.memoizedState=n;var a={value:n,getSnapshot:t};return r.queue=a,bs(Xa.bind(null,i,a,e),[e]),i.flags|=2048,fs(9,{destroy:void 0,resource:void 0},Qa.bind(null,i,a,n,t),null),n},useId:function(){var e=Fa(),t=rc.identifierPrefix;if(ar){var n=Ji;t="\xab"+t+"R"+(n=(Gi&~(1<<32-fe(Gi)-1)).toString(32)+n),0<(n=Na++)&&(t+="H"+n.toString(32)),t+="\xbb"}else t="\xab"+t+"r"+(n=Pa++).toString(32)+"\xbb";return e.memoizedState=t},useHostTransitionStatus:As,useFormState:ls,useActionState:ls,useOptimistic:function(e){var t=Fa();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Bs.bind(null,xa,!0,n),n.dispatch=t,[e,t]},useMemoCache:Ha,useCacheRefresh:function(){return Fa().memoizedState=zs.bind(null,xa)}},qs={readContext:Mr,use:Ba,useCallback:Ns,useContext:Mr,useEffect:ys,useImperativeHandle:_s,useInsertionEffect:xs,useLayoutEffect:vs,useMemo:Ms,useReducer:Ua,useRef:ps,useState:function(){return Ua(Va)},useDebugValue:Ss,useDeferredValue:function(e,t){return Ps(Ia(),va.memoizedState,e,t)},useTransition:function(){var e=Ua(Va)[0],t=Ia().memoizedState;return["boolean"===typeof e?e:Wa(e),t]},useSyncExternalStore:qa,useId:Ds,useHostTransitionStatus:As,useFormState:cs,useActionState:cs,useOptimistic:function(e,t){return es(Ia(),0,e,t)},useMemoCache:Ha,useCacheRefresh:Rs},Ks={readContext:Mr,use:Ba,useCallback:Ns,useContext:Mr,useEffect:ys,useImperativeHandle:_s,useInsertionEffect:xs,useLayoutEffect:vs,useMemo:Ms,useReducer:Ya,useRef:ps,useState:function(){return Ya(Va)},useDebugValue:Ss,useDeferredValue:function(e,t){var n=Ia();return null===va?Cs(n,e,t):Ps(n,va.memoizedState,e,t)},useTransition:function(){var e=Ya(Va)[0],t=Ia().memoizedState;return["boolean"===typeof e?e:Wa(e),t]},useSyncExternalStore:qa,useId:Ds,useHostTransitionStatus:As,useFormState:hs,useActionState:hs,useOptimistic:function(e,t){var n=Ia();return null!==va?es(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Ha,useCacheRefresh:Rs},Qs=null,Xs=0;function Gs(e){var t=Xs;return Xs+=1,null===Qs&&(Qs=[]),Gr(Qs,e,t)}function Js(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function Zs(e,t){if(t.$$typeof===f)throw Error(s(525));throw e=Object.prototype.toString.call(t),Error(s(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function eo(e){return(0,e._init)(e._payload)}function to(e){function t(t,n){if(e){var i=t.deletions;null===i?(t.deletions=[n],t.flags|=16):i.push(n)}}function n(n,i){if(!e)return null;for(;null!==i;)t(n,i),i=i.sibling;return null}function i(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function r(e,t){return(e=Fi(e,t)).index=0,e.sibling=null,e}function a(t,n,i){return t.index=i,e?null!==(i=t.alternate)?(i=i.index)<n?(t.flags|=67108866,n):i:(t.flags|=67108866,n):(t.flags|=1048576,n)}function o(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function l(e,t,n,i){return null===t||6!==t.tag?((t=Hi(n,e.mode,i)).return=e,t):((t=r(t,n)).return=e,t)}function c(e,t,n,i){var a=n.type;return a===g?d(e,t,n.props.children,i,n.key):null!==t&&(t.elementType===a||"object"===typeof a&&null!==a&&a.$$typeof===M&&eo(a)===t.type)?(Js(t=r(t,n.props),n),t.return=e,t):(Js(t=Wi(n.type,n.key,n.props,null,e.mode,i),n),t.return=e,t)}function u(e,t,n,i){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Vi(n,e.mode,i)).return=e,t):((t=r(t,n.children||[])).return=e,t)}function d(e,t,n,i,a){return null===t||7!==t.tag?((t=Bi(n,e.mode,i,a)).return=e,t):((t=r(t,n)).return=e,t)}function h(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t||"bigint"===typeof t)return(t=Hi(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case p:return Js(n=Wi(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case m:return(t=Vi(t,e.mode,n)).return=e,t;case M:return h(e,t=(0,t._init)(t._payload),n)}if(O(t)||j(t))return(t=Bi(t,e.mode,n,null)).return=e,t;if("function"===typeof t.then)return h(e,Gs(t),n);if(t.$$typeof===w)return h(e,Cr(e,t),n);Zs(e,t)}return null}function f(e,t,n,i){var r=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n||"bigint"===typeof n)return null!==r?null:l(e,t,""+n,i);if("object"===typeof n&&null!==n){switch(n.$$typeof){case p:return n.key===r?c(e,t,n,i):null;case m:return n.key===r?u(e,t,n,i):null;case M:return f(e,t,n=(r=n._init)(n._payload),i)}if(O(n)||j(n))return null!==r?null:d(e,t,n,i,null);if("function"===typeof n.then)return f(e,t,Gs(n),i);if(n.$$typeof===w)return f(e,t,Cr(e,n),i);Zs(e,n)}return null}function b(e,t,n,i,r){if("string"===typeof i&&""!==i||"number"===typeof i||"bigint"===typeof i)return l(t,e=e.get(n)||null,""+i,r);if("object"===typeof i&&null!==i){switch(i.$$typeof){case p:return c(t,e=e.get(null===i.key?n:i.key)||null,i,r);case m:return u(t,e=e.get(null===i.key?n:i.key)||null,i,r);case M:return b(e,t,n,i=(0,i._init)(i._payload),r)}if(O(i)||j(i))return d(t,e=e.get(n)||null,i,r,null);if("function"===typeof i.then)return b(e,t,n,Gs(i),r);if(i.$$typeof===w)return b(e,t,n,Cr(t,i),r);Zs(t,i)}return null}function y(l,c,u,d){if("object"===typeof u&&null!==u&&u.type===g&&null===u.key&&(u=u.props.children),"object"===typeof u&&null!==u){switch(u.$$typeof){case p:e:{for(var x=u.key;null!==c;){if(c.key===x){if((x=u.type)===g){if(7===c.tag){n(l,c.sibling),(d=r(c,u.props.children)).return=l,l=d;break e}}else if(c.elementType===x||"object"===typeof x&&null!==x&&x.$$typeof===M&&eo(x)===c.type){n(l,c.sibling),Js(d=r(c,u.props),u),d.return=l,l=d;break e}n(l,c);break}t(l,c),c=c.sibling}u.type===g?((d=Bi(u.props.children,l.mode,d,u.key)).return=l,l=d):(Js(d=Wi(u.type,u.key,u.props,null,l.mode,d),u),d.return=l,l=d)}return o(l);case m:e:{for(x=u.key;null!==c;){if(c.key===x){if(4===c.tag&&c.stateNode.containerInfo===u.containerInfo&&c.stateNode.implementation===u.implementation){n(l,c.sibling),(d=r(c,u.children||[])).return=l,l=d;break e}n(l,c);break}t(l,c),c=c.sibling}(d=Vi(u,l.mode,d)).return=l,l=d}return o(l);case M:return y(l,c,u=(x=u._init)(u._payload),d)}if(O(u))return function(r,s,o,l){for(var c=null,u=null,d=s,p=s=0,m=null;null!==d&&p<o.length;p++){d.index>p?(m=d,d=null):m=d.sibling;var g=f(r,d,o[p],l);if(null===g){null===d&&(d=m);break}e&&d&&null===g.alternate&&t(r,d),s=a(g,s,p),null===u?c=g:u.sibling=g,u=g,d=m}if(p===o.length)return n(r,d),ar&&Zi(r,p),c;if(null===d){for(;p<o.length;p++)null!==(d=h(r,o[p],l))&&(s=a(d,s,p),null===u?c=d:u.sibling=d,u=d);return ar&&Zi(r,p),c}for(d=i(d);p<o.length;p++)null!==(m=b(d,r,p,o[p],l))&&(e&&null!==m.alternate&&d.delete(null===m.key?p:m.key),s=a(m,s,p),null===u?c=m:u.sibling=m,u=m);return e&&d.forEach(function(e){return t(r,e)}),ar&&Zi(r,p),c}(l,c,u,d);if(j(u)){if("function"!==typeof(x=j(u)))throw Error(s(150));return function(r,o,l,c){if(null==l)throw Error(s(151));for(var u=null,d=null,p=o,m=o=0,g=null,y=l.next();null!==p&&!y.done;m++,y=l.next()){p.index>m?(g=p,p=null):g=p.sibling;var x=f(r,p,y.value,c);if(null===x){null===p&&(p=g);break}e&&p&&null===x.alternate&&t(r,p),o=a(x,o,m),null===d?u=x:d.sibling=x,d=x,p=g}if(y.done)return n(r,p),ar&&Zi(r,m),u;if(null===p){for(;!y.done;m++,y=l.next())null!==(y=h(r,y.value,c))&&(o=a(y,o,m),null===d?u=y:d.sibling=y,d=y);return ar&&Zi(r,m),u}for(p=i(p);!y.done;m++,y=l.next())null!==(y=b(p,r,m,y.value,c))&&(e&&null!==y.alternate&&p.delete(null===y.key?m:y.key),o=a(y,o,m),null===d?u=y:d.sibling=y,d=y);return e&&p.forEach(function(e){return t(r,e)}),ar&&Zi(r,m),u}(l,c,u=x.call(u),d)}if("function"===typeof u.then)return y(l,c,Gs(u),d);if(u.$$typeof===w)return y(l,c,Cr(l,u),d);Zs(l,u)}return"string"===typeof u&&""!==u||"number"===typeof u||"bigint"===typeof u?(u=""+u,null!==c&&6===c.tag?(n(l,c.sibling),(d=r(c,u)).return=l,l=d):(n(l,c),(d=Hi(u,l.mode,d)).return=l,l=d),o(l)):n(l,c)}return function(e,t,n,i){try{Xs=0;var r=y(e,t,n,i);return Qs=null,r}catch(s){if(s===$r||s===qr)throw s;var a=Ri(29,s,null,e.mode);return a.lanes=i,a.return=e,a}}}var no=to(!0),io=to(!1),ro=I(null),ao=null;function so(e){var t=e.alternate;B(uo,1&uo.current),B(ro,e),null===ao&&(null===t||null!==fa.current||null!==t.memoizedState)&&(ao=e)}function oo(e){if(22===e.tag){if(B(uo,uo.current),B(ro,e),null===ao){var t=e.alternate;null!==t&&null!==t.memoizedState&&(ao=e)}}else lo()}function lo(){B(uo,uo.current),B(ro,ro.current)}function co(e){W(ro),ao===e&&(ao=null),W(uo)}var uo=I(0);function ho(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||bd(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function fo(e,t,n,i){n=null===(n=n(i,t=e.memoizedState))||void 0===n?t:h({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var po={enqueueSetState:function(e,t,n){e=e._reactInternals;var i=Dc(),r=ra(i);r.payload=t,void 0!==n&&null!==n&&(r.callback=n),null!==(t=aa(e,r,i))&&(zc(t,e,i),sa(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var i=Dc(),r=ra(i);r.tag=1,r.payload=t,void 0!==n&&null!==n&&(r.callback=n),null!==(t=aa(e,r,i))&&(zc(t,e,i),sa(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Dc(),i=ra(n);i.tag=2,void 0!==t&&null!==t&&(i.callback=t),null!==(t=aa(e,i,n))&&(zc(t,e,n),sa(t,e,n))}};function mo(e,t,n,i,r,a,s){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(i,a,s):!t.prototype||!t.prototype.isPureReactComponent||(!Xn(n,i)||!Xn(r,a))}function go(e,t,n,i){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,i),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,i),t.state!==e&&po.enqueueReplaceState(t,t.state,null)}function bo(e,t){var n=t;if("ref"in t)for(var i in n={},t)"ref"!==i&&(n[i]=t[i]);if(e=e.defaultProps)for(var r in n===t&&(n=h({},n)),e)void 0===n[r]&&(n[r]=e[r]);return n}var yo="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function xo(e){yo(e)}function vo(e){console.error(e)}function wo(e){yo(e)}function ko(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout(function(){throw n})}}function _o(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(i){setTimeout(function(){throw i})}}function So(e,t,n){return(n=ra(n)).tag=3,n.payload={element:null},n.callback=function(){ko(e,t)},n}function No(e){return(e=ra(e)).tag=3,e}function Mo(e,t,n,i){var r=n.type.getDerivedStateFromError;if("function"===typeof r){var a=i.value;e.payload=function(){return r(a)},e.callback=function(){_o(t,n,i)}}var s=n.stateNode;null!==s&&"function"===typeof s.componentDidCatch&&(e.callback=function(){_o(t,n,i),"function"!==typeof r&&(null===Nc?Nc=new Set([this]):Nc.add(this));var e=i.stack;this.componentDidCatch(i.value,{componentStack:null!==e?e:""})})}var Co=Error(s(461)),Po=!1;function Eo(e,t,n,i){t.child=null===e?io(t,null,n,i):no(t,e.child,n,i)}function jo(e,t,n,i,r){n=n.render;var a=t.ref;if("ref"in i){var s={};for(var o in i)"ref"!==o&&(s[o]=i[o])}else s=i;return Nr(t),i=Ta(e,t,n,s,a,r),o=Da(),null===e||Po?(ar&&o&&tr(t),t.flags|=1,Eo(e,t,i,r),t.child):(Ra(e,t,r),Xo(e,t,r))}function To(e,t,n,i,r){if(null===e){var a=n.type;return"function"!==typeof a||zi(a)||void 0!==a.defaultProps||null!==n.compare?((e=Wi(n.type,null,i,t,t.mode,r)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=a,Lo(e,t,a,i,r))}if(a=e.child,!Go(e,r)){var s=a.memoizedProps;if((n=null!==(n=n.compare)?n:Xn)(s,i)&&e.ref===t.ref)return Xo(e,t,r)}return t.flags|=1,(e=Fi(a,i)).ref=t.ref,e.return=t,t.child=e}function Lo(e,t,n,i,r){if(null!==e){var a=e.memoizedProps;if(Xn(a,i)&&e.ref===t.ref){if(Po=!1,t.pendingProps=i=a,!Go(e,r))return t.lanes=e.lanes,Xo(e,t,r);0!==(131072&e.flags)&&(Po=!0)}}return Ro(e,t,n,i,r)}function Oo(e,t,n){var i=t.pendingProps,r=i.children,a=null!==e?e.memoizedState:null;if("hidden"===i.mode){if(0!==(128&t.flags)){if(i=null!==a?a.baseLanes|n:n,null!==e){for(r=t.child=e.child,a=0;null!==r;)a=a|r.lanes|r.childLanes,r=r.sibling;t.childLanes=a&~i}else t.childLanes=0,t.child=null;return Ao(e,t,i,n)}if(0===(536870912&n))return t.lanes=t.childLanes=536870912,Ao(e,t,null!==a?a.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&Vr(0,null!==a?a.cachePool:null),null!==a?ma(t,a):ga(),oo(t)}else null!==a?(Vr(0,a.cachePool),ma(t,a),lo(),t.memoizedState=null):(null!==e&&Vr(0,null),ga(),lo());return Eo(e,t,r,n),t.child}function Ao(e,t,n,i){var r=Hr();return r=null===r?null:{parent:Lr._currentValue,pool:r},t.memoizedState={baseLanes:n,cachePool:r},null!==e&&Vr(0,null),ga(),oo(t),null!==e&&_r(e,t,i,!0),null}function Do(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!==typeof n&&"object"!==typeof n)throw Error(s(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function Ro(e,t,n,i,r){return Nr(t),n=Ta(e,t,n,i,void 0,r),i=Da(),null===e||Po?(ar&&i&&tr(t),t.flags|=1,Eo(e,t,n,r),t.child):(Ra(e,t,r),Xo(e,t,r))}function zo(e,t,n,i,r,a){return Nr(t),t.updateQueue=null,n=Oa(t,i,n,r),La(e),i=Da(),null===e||Po?(ar&&i&&tr(t),t.flags|=1,Eo(e,t,n,a),t.child):(Ra(e,t,a),Xo(e,t,a))}function Fo(e,t,n,i,r){if(Nr(t),null===t.stateNode){var a=Ai,s=n.contextType;"object"===typeof s&&null!==s&&(a=Mr(s)),a=new n(i,a),t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,a.updater=po,t.stateNode=a,a._reactInternals=t,(a=t.stateNode).props=i,a.state=t.memoizedState,a.refs={},na(t),s=n.contextType,a.context="object"===typeof s&&null!==s?Mr(s):Ai,a.state=t.memoizedState,"function"===typeof(s=n.getDerivedStateFromProps)&&(fo(t,n,s,i),a.state=t.memoizedState),"function"===typeof n.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(s=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),s!==a.state&&po.enqueueReplaceState(a,a.state,null),ua(t,i,a,r),ca(),a.state=t.memoizedState),"function"===typeof a.componentDidMount&&(t.flags|=4194308),i=!0}else if(null===e){a=t.stateNode;var o=t.memoizedProps,l=bo(n,o);a.props=l;var c=a.context,u=n.contextType;s=Ai,"object"===typeof u&&null!==u&&(s=Mr(u));var d=n.getDerivedStateFromProps;u="function"===typeof d||"function"===typeof a.getSnapshotBeforeUpdate,o=t.pendingProps!==o,u||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(o||c!==s)&&go(t,a,i,s),ta=!1;var h=t.memoizedState;a.state=h,ua(t,i,a,r),ca(),c=t.memoizedState,o||h!==c||ta?("function"===typeof d&&(fo(t,n,d,i),c=t.memoizedState),(l=ta||mo(t,n,l,i,h,c,s))?(u||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||("function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount()),"function"===typeof a.componentDidMount&&(t.flags|=4194308)):("function"===typeof a.componentDidMount&&(t.flags|=4194308),t.memoizedProps=i,t.memoizedState=c),a.props=i,a.state=c,a.context=s,i=l):("function"===typeof a.componentDidMount&&(t.flags|=4194308),i=!1)}else{a=t.stateNode,ia(e,t),u=bo(n,s=t.memoizedProps),a.props=u,d=t.pendingProps,h=a.context,c=n.contextType,l=Ai,"object"===typeof c&&null!==c&&(l=Mr(c)),(c="function"===typeof(o=n.getDerivedStateFromProps)||"function"===typeof a.getSnapshotBeforeUpdate)||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(s!==d||h!==l)&&go(t,a,i,l),ta=!1,h=t.memoizedState,a.state=h,ua(t,i,a,r),ca();var f=t.memoizedState;s!==d||h!==f||ta||null!==e&&null!==e.dependencies&&Sr(e.dependencies)?("function"===typeof o&&(fo(t,n,o,i),f=t.memoizedState),(u=ta||mo(t,n,u,i,h,f,l)||null!==e&&null!==e.dependencies&&Sr(e.dependencies))?(c||"function"!==typeof a.UNSAFE_componentWillUpdate&&"function"!==typeof a.componentWillUpdate||("function"===typeof a.componentWillUpdate&&a.componentWillUpdate(i,f,l),"function"===typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(i,f,l)),"function"===typeof a.componentDidUpdate&&(t.flags|=4),"function"===typeof a.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof a.componentDidUpdate||s===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||s===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=i,t.memoizedState=f),a.props=i,a.state=f,a.context=l,i=u):("function"!==typeof a.componentDidUpdate||s===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||s===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),i=!1)}return a=i,Do(e,t),i=0!==(128&t.flags),a||i?(a=t.stateNode,n=i&&"function"!==typeof n.getDerivedStateFromError?null:a.render(),t.flags|=1,null!==e&&i?(t.child=no(t,e.child,null,r),t.child=no(t,null,n,r)):Eo(e,t,n,r),t.memoizedState=a.state,e=t.child):e=Xo(e,t,r),e}function Io(e,t,n,i){return fr(),t.flags|=256,Eo(e,t,n,i),t.child}var Wo={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Bo(e){return{baseLanes:e,cachePool:Ur()}}function Ho(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=bc),e}function Vo(e,t,n){var i,r=t.pendingProps,a=!1,o=0!==(128&t.flags);if((i=o)||(i=(null===e||null!==e.memoizedState)&&0!==(2&uo.current)),i&&(a=!0,t.flags&=-129),i=0!==(32&t.flags),t.flags&=-33,null===e){if(ar){if(a?so(t):lo(),ar){var l,c=rr;if(l=c){e:{for(l=c,c=or;8!==l.nodeType;){if(!c){c=null;break e}if(null===(l=yd(l.nextSibling))){c=null;break e}}c=l}null!==c?(t.memoizedState={dehydrated:c,treeContext:null!==Xi?{id:Gi,overflow:Ji}:null,retryLane:536870912,hydrationErrors:null},(l=Ri(18,null,null,0)).stateNode=c,l.return=t,t.child=l,ir=t,rr=null,l=!0):l=!1}l||cr(t)}if(null!==(c=t.memoizedState)&&null!==(c=c.dehydrated))return bd(c)?t.lanes=32:t.lanes=536870912,null;co(t)}return c=r.children,r=r.fallback,a?(lo(),c=$o({mode:"hidden",children:c},a=t.mode),r=Bi(r,a,n,null),c.return=t,r.return=t,c.sibling=r,t.child=c,(a=t.child).memoizedState=Bo(n),a.childLanes=Ho(e,i,n),t.memoizedState=Wo,r):(so(t),Uo(t,c))}if(null!==(l=e.memoizedState)&&null!==(c=l.dehydrated)){if(o)256&t.flags?(so(t),t.flags&=-257,t=Yo(e,t,n)):null!==t.memoizedState?(lo(),t.child=e.child,t.flags|=128,t=null):(lo(),a=r.fallback,c=t.mode,r=$o({mode:"visible",children:r.children},c),(a=Bi(a,c,n,null)).flags|=2,r.return=t,a.return=t,r.sibling=a,t.child=r,no(t,e.child,null,n),(r=t.child).memoizedState=Bo(n),r.childLanes=Ho(e,i,n),t.memoizedState=Wo,t=a);else if(so(t),bd(c)){if(i=c.nextSibling&&c.nextSibling.dataset)var u=i.dgst;i=u,(r=Error(s(419))).stack="",r.digest=i,mr({value:r,source:null,stack:null}),t=Yo(e,t,n)}else if(Po||_r(e,t,n,!1),i=0!==(n&e.childLanes),Po||i){if(null!==(i=rc)&&(0!==(r=0!==((r=0!==(42&(r=n&-n))?1:Pe(r))&(i.suspendedLanes|n))?0:r)&&r!==l.retryLane))throw l.retryLane=r,Ti(e,r),zc(i,e,r),Co;"$?"===c.data||Kc(),t=Yo(e,t,n)}else"$?"===c.data?(t.flags|=192,t.child=e.child,t=null):(e=l.treeContext,rr=yd(c.nextSibling),ir=t,ar=!0,sr=null,or=!1,null!==e&&(Ki[Qi++]=Gi,Ki[Qi++]=Ji,Ki[Qi++]=Xi,Gi=e.id,Ji=e.overflow,Xi=t),(t=Uo(t,r.children)).flags|=4096);return t}return a?(lo(),a=r.fallback,c=t.mode,u=(l=e.child).sibling,(r=Fi(l,{mode:"hidden",children:r.children})).subtreeFlags=65011712&l.subtreeFlags,null!==u?a=Fi(u,a):(a=Bi(a,c,n,null)).flags|=2,a.return=t,r.return=t,r.sibling=a,t.child=r,r=a,a=t.child,null===(c=e.child.memoizedState)?c=Bo(n):(null!==(l=c.cachePool)?(u=Lr._currentValue,l=l.parent!==u?{parent:u,pool:u}:l):l=Ur(),c={baseLanes:c.baseLanes|n,cachePool:l}),a.memoizedState=c,a.childLanes=Ho(e,i,n),t.memoizedState=Wo,r):(so(t),e=(n=e.child).sibling,(n=Fi(n,{mode:"visible",children:r.children})).return=t,n.sibling=null,null!==e&&(null===(i=t.deletions)?(t.deletions=[e],t.flags|=16):i.push(e)),t.child=n,t.memoizedState=null,n)}function Uo(e,t){return(t=$o({mode:"visible",children:t},e.mode)).return=e,e.child=t}function $o(e,t){return(e=Ri(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Yo(e,t,n){return no(t,e.child,null,n),(e=Uo(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function qo(e,t,n){e.lanes|=t;var i=e.alternate;null!==i&&(i.lanes|=t),wr(e.return,t,n)}function Ko(e,t,n,i,r){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:i,tail:n,tailMode:r}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=i,a.tail=n,a.tailMode=r)}function Qo(e,t,n){var i=t.pendingProps,r=i.revealOrder,a=i.tail;if(Eo(e,t,i.children,n),0!==(2&(i=uo.current)))i=1&i|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&qo(e,n,t);else if(19===e.tag)qo(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}i&=1}switch(B(uo,i),r){case"forwards":for(n=t.child,r=null;null!==n;)null!==(e=n.alternate)&&null===ho(e)&&(r=n),n=n.sibling;null===(n=r)?(r=t.child,t.child=null):(r=n.sibling,n.sibling=null),Ko(t,!1,r,n,a);break;case"backwards":for(n=null,r=t.child,t.child=null;null!==r;){if(null!==(e=r.alternate)&&null===ho(e)){t.child=r;break}e=r.sibling,r.sibling=n,n=r,r=e}Ko(t,!0,n,null,a);break;case"together":Ko(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Xo(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),pc|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(_r(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(s(153));if(null!==t.child){for(n=Fi(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Fi(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Go(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!Sr(e))}function Jo(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)Po=!0;else{if(!Go(e,n)&&0===(128&t.flags))return Po=!1,function(e,t,n){switch(t.tag){case 3:Y(t,t.stateNode.containerInfo),xr(0,Lr,e.memoizedState.cache),fr();break;case 27:case 5:K(t);break;case 4:Y(t,t.stateNode.containerInfo);break;case 10:xr(0,t.type,t.memoizedProps.value);break;case 13:var i=t.memoizedState;if(null!==i)return null!==i.dehydrated?(so(t),t.flags|=128,null):0!==(n&t.child.childLanes)?Vo(e,t,n):(so(t),null!==(e=Xo(e,t,n))?e.sibling:null);so(t);break;case 19:var r=0!==(128&e.flags);if((i=0!==(n&t.childLanes))||(_r(e,t,n,!1),i=0!==(n&t.childLanes)),r){if(i)return Qo(e,t,n);t.flags|=128}if(null!==(r=t.memoizedState)&&(r.rendering=null,r.tail=null,r.lastEffect=null),B(uo,uo.current),i)break;return null;case 22:case 23:return t.lanes=0,Oo(e,t,n);case 24:xr(0,Lr,e.memoizedState.cache)}return Xo(e,t,n)}(e,t,n);Po=0!==(131072&e.flags)}else Po=!1,ar&&0!==(1048576&t.flags)&&er(t,qi,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var i=t.elementType,r=i._init;if(i=r(i._payload),t.type=i,"function"!==typeof i){if(void 0!==i&&null!==i){if((r=i.$$typeof)===k){t.tag=11,t=jo(null,t,i,e,n);break e}if(r===N){t.tag=14,t=To(null,t,i,e,n);break e}}throw t=L(i)||i,Error(s(306,t,""))}zi(i)?(e=bo(i,e),t.tag=1,t=Fo(null,t,i,e,n)):(t.tag=0,t=Ro(null,t,i,e,n))}return t;case 0:return Ro(e,t,t.type,t.pendingProps,n);case 1:return Fo(e,t,i=t.type,r=bo(i,t.pendingProps),n);case 3:e:{if(Y(t,t.stateNode.containerInfo),null===e)throw Error(s(387));i=t.pendingProps;var a=t.memoizedState;r=a.element,ia(e,t),ua(t,i,null,n);var o=t.memoizedState;if(i=o.cache,xr(0,Lr,i),i!==a.cache&&kr(t,[Lr],n,!0),ca(),i=o.element,a.isDehydrated){if(a={element:i,isDehydrated:!1,cache:o.cache},t.updateQueue.baseState=a,t.memoizedState=a,256&t.flags){t=Io(e,t,i,n);break e}if(i!==r){mr(r=Si(Error(s(424)),t)),t=Io(e,t,i,n);break e}if(9===(e=t.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(rr=yd(e.firstChild),ir=t,ar=!0,sr=null,or=!0,n=io(t,null,i,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(fr(),i===r){t=Xo(e,t,n);break e}Eo(e,t,i,n)}t=t.child}return t;case 26:return Do(e,t),null===e?(n=Ed(t.type,null,t.pendingProps,null))?t.memoizedState=n:ar||(n=t.type,e=t.pendingProps,(i=rd(U.current).createElement(n))[Le]=t,i[Oe]=e,td(i,n,e),$e(i),t.stateNode=i):t.memoizedState=Ed(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return K(t),null===e&&ar&&(i=t.stateNode=wd(t.type,t.pendingProps,U.current),ir=t,or=!0,r=rr,pd(t.type)?(xd=r,rr=yd(i.firstChild)):rr=r),Eo(e,t,t.pendingProps.children,n),Do(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&ar&&((r=i=rr)&&(null!==(i=function(e,t,n,i){for(;1===e.nodeType;){var r=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!i&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(i){if(!e[Ie])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(a=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(a!==r.rel||e.getAttribute("href")!==(null==r.href||""===r.href?null:r.href)||e.getAttribute("crossorigin")!==(null==r.crossOrigin?null:r.crossOrigin)||e.getAttribute("title")!==(null==r.title?null:r.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((a=e.getAttribute("src"))!==(null==r.src?null:r.src)||e.getAttribute("type")!==(null==r.type?null:r.type)||e.getAttribute("crossorigin")!==(null==r.crossOrigin?null:r.crossOrigin))&&a&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var a=null==r.name?null:""+r.name;if("hidden"===r.type&&e.getAttribute("name")===a)return e}if(null===(e=yd(e.nextSibling)))break}return null}(i,t.type,t.pendingProps,or))?(t.stateNode=i,ir=t,rr=yd(i.firstChild),or=!1,r=!0):r=!1),r||cr(t)),K(t),r=t.type,a=t.pendingProps,o=null!==e?e.memoizedProps:null,i=a.children,od(r,a)?i=null:null!==o&&od(r,o)&&(t.flags|=32),null!==t.memoizedState&&(r=Ta(e,t,Aa,null,null,n),Qd._currentValue=r),Do(e,t),Eo(e,t,i,n),t.child;case 6:return null===e&&ar&&((e=n=rr)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=yd(e.nextSibling)))return null}return e}(n,t.pendingProps,or))?(t.stateNode=n,ir=t,rr=null,e=!0):e=!1),e||cr(t)),null;case 13:return Vo(e,t,n);case 4:return Y(t,t.stateNode.containerInfo),i=t.pendingProps,null===e?t.child=no(t,null,i,n):Eo(e,t,i,n),t.child;case 11:return jo(e,t,t.type,t.pendingProps,n);case 7:return Eo(e,t,t.pendingProps,n),t.child;case 8:case 12:return Eo(e,t,t.pendingProps.children,n),t.child;case 10:return i=t.pendingProps,xr(0,t.type,i.value),Eo(e,t,i.children,n),t.child;case 9:return r=t.type._context,i=t.pendingProps.children,Nr(t),i=i(r=Mr(r)),t.flags|=1,Eo(e,t,i,n),t.child;case 14:return To(e,t,t.type,t.pendingProps,n);case 15:return Lo(e,t,t.type,t.pendingProps,n);case 19:return Qo(e,t,n);case 31:return i=t.pendingProps,n=t.mode,i={mode:i.mode,children:i.children},null===e?((n=$o(i,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=Fi(e.child,i)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Oo(e,t,n);case 24:return Nr(t),i=Mr(Lr),null===e?(null===(r=Hr())&&(r=rc,a=Or(),r.pooledCache=a,a.refCount++,null!==a&&(r.pooledCacheLanes|=n),r=a),t.memoizedState={parent:i,cache:r},na(t),xr(0,Lr,r)):(0!==(e.lanes&n)&&(ia(e,t),ua(t,null,null,n),ca()),r=e.memoizedState,a=t.memoizedState,r.parent!==i?(r={parent:i,cache:i},t.memoizedState=r,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=r),xr(0,Lr,i)):(i=a.cache,xr(0,Lr,i),i!==r.cache&&kr(t,[Lr],n,!0))),Eo(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(s(156,t.tag))}function Zo(e){e.flags|=4}function el(e,t){if("stylesheet"!==t.type||0!==(4&t.state.loading))e.flags&=-16777217;else if(e.flags|=16777216,!Hd(t)){if(null!==(t=ro.current)&&((4194048&sc)===sc?null!==ao:(62914560&sc)!==sc&&0===(536870912&sc)||t!==ao))throw Jr=Kr,Yr;e.flags|=8192}}function tl(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?_e():536870912,e.lanes|=t,yc|=t)}function nl(e,t){if(!ar)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var i=null;null!==n;)null!==n.alternate&&(i=n),n=n.sibling;null===i?t||null===e.tail?e.tail=null:e.tail.sibling=null:i.sibling=null}}function il(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,i=0;if(t)for(var r=e.child;null!==r;)n|=r.lanes|r.childLanes,i|=65011712&r.subtreeFlags,i|=65011712&r.flags,r.return=e,r=r.sibling;else for(r=e.child;null!==r;)n|=r.lanes|r.childLanes,i|=r.subtreeFlags,i|=r.flags,r.return=e,r=r.sibling;return e.subtreeFlags|=i,e.childLanes=n,t}function rl(e,t,n){var i=t.pendingProps;switch(nr(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return il(t),null;case 3:return n=t.stateNode,i=null,null!==e&&(i=e.memoizedState.cache),t.memoizedState.cache!==i&&(t.flags|=2048),vr(Lr),q(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(hr(t)?Zo(t):null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,pr())),il(t),null;case 26:return n=t.memoizedState,null===e?(Zo(t),null!==n?(il(t),el(t,n)):(il(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Zo(t),il(t),el(t,n)):(il(t),t.flags&=-16777217):(e.memoizedProps!==i&&Zo(t),il(t),t.flags&=-16777217),null;case 27:Q(t),n=U.current;var r=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==i&&Zo(t);else{if(!i){if(null===t.stateNode)throw Error(s(166));return il(t),null}e=H.current,hr(t)?ur(t):(e=wd(r,i,n),t.stateNode=e,Zo(t))}return il(t),null;case 5:if(Q(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==i&&Zo(t);else{if(!i){if(null===t.stateNode)throw Error(s(166));return il(t),null}if(e=H.current,hr(t))ur(t);else{switch(r=rd(U.current),e){case 1:e=r.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=r.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=r.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=r.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=r.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"===typeof i.is?r.createElement("select",{is:i.is}):r.createElement("select"),i.multiple?e.multiple=!0:i.size&&(e.size=i.size);break;default:e="string"===typeof i.is?r.createElement(n,{is:i.is}):r.createElement(n)}}e[Le]=t,e[Oe]=i;e:for(r=t.child;null!==r;){if(5===r.tag||6===r.tag)e.appendChild(r.stateNode);else if(4!==r.tag&&27!==r.tag&&null!==r.child){r.child.return=r,r=r.child;continue}if(r===t)break e;for(;null===r.sibling;){if(null===r.return||r.return===t)break e;r=r.return}r.sibling.return=r.return,r=r.sibling}t.stateNode=e;e:switch(td(e,n,i),n){case"button":case"input":case"select":case"textarea":e=!!i.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Zo(t)}}return il(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==i&&Zo(t);else{if("string"!==typeof i&&null===t.stateNode)throw Error(s(166));if(e=U.current,hr(t)){if(e=t.stateNode,n=t.memoizedProps,i=null,null!==(r=ir))switch(r.tag){case 27:case 5:i=r.memoizedProps}e[Le]=t,(e=!!(e.nodeValue===n||null!==i&&!0===i.suppressHydrationWarning||Gu(e.nodeValue,n)))||cr(t)}else(e=rd(e).createTextNode(i))[Le]=t,t.stateNode=e}return il(t),null;case 13:if(i=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(r=hr(t),null!==i&&null!==i.dehydrated){if(null===e){if(!r)throw Error(s(318));if(!(r=null!==(r=t.memoizedState)?r.dehydrated:null))throw Error(s(317));r[Le]=t}else fr(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;il(t),r=!1}else r=pr(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=r),r=!0;if(!r)return 256&t.flags?(co(t),t):(co(t),null)}if(co(t),0!==(128&t.flags))return t.lanes=n,t;if(n=null!==i,e=null!==e&&null!==e.memoizedState,n){r=null,null!==(i=t.child).alternate&&null!==i.alternate.memoizedState&&null!==i.alternate.memoizedState.cachePool&&(r=i.alternate.memoizedState.cachePool.pool);var a=null;null!==i.memoizedState&&null!==i.memoizedState.cachePool&&(a=i.memoizedState.cachePool.pool),a!==r&&(i.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),tl(t,t.updateQueue),il(t),null;case 4:return q(),null===e&&Bu(t.stateNode.containerInfo),il(t),null;case 10:return vr(t.type),il(t),null;case 19:if(W(uo),null===(r=t.memoizedState))return il(t),null;if(i=0!==(128&t.flags),null===(a=r.rendering))if(i)nl(r,!1);else{if(0!==fc||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(a=ho(e))){for(t.flags|=128,nl(r,!1),e=a.updateQueue,t.updateQueue=e,tl(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)Ii(n,e),n=n.sibling;return B(uo,1&uo.current|2),t.child}e=e.sibling}null!==r.tail&&te()>_c&&(t.flags|=128,i=!0,nl(r,!1),t.lanes=4194304)}else{if(!i)if(null!==(e=ho(a))){if(t.flags|=128,i=!0,e=e.updateQueue,t.updateQueue=e,tl(t,e),nl(r,!0),null===r.tail&&"hidden"===r.tailMode&&!a.alternate&&!ar)return il(t),null}else 2*te()-r.renderingStartTime>_c&&536870912!==n&&(t.flags|=128,i=!0,nl(r,!1),t.lanes=4194304);r.isBackwards?(a.sibling=t.child,t.child=a):(null!==(e=r.last)?e.sibling=a:t.child=a,r.last=a)}return null!==r.tail?(t=r.tail,r.rendering=t,r.tail=t.sibling,r.renderingStartTime=te(),t.sibling=null,e=uo.current,B(uo,i?1&e|2:1&e),t):(il(t),null);case 22:case 23:return co(t),ba(),i=null!==t.memoizedState,null!==e?null!==e.memoizedState!==i&&(t.flags|=8192):i&&(t.flags|=8192),i?0!==(536870912&n)&&0===(128&t.flags)&&(il(t),6&t.subtreeFlags&&(t.flags|=8192)):il(t),null!==(n=t.updateQueue)&&tl(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),i=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(i=t.memoizedState.cachePool.pool),i!==n&&(t.flags|=2048),null!==e&&W(Br),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),vr(Lr),il(t),null;case 25:case 30:return null}throw Error(s(156,t.tag))}function al(e,t){switch(nr(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return vr(Lr),q(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return Q(t),null;case 13:if(co(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(s(340));fr()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return W(uo),null;case 4:return q(),null;case 10:return vr(t.type),null;case 22:case 23:return co(t),ba(),null!==e&&W(Br),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return vr(Lr),null;default:return null}}function sl(e,t){switch(nr(t),t.tag){case 3:vr(Lr),q();break;case 26:case 27:case 5:Q(t);break;case 4:q();break;case 13:co(t);break;case 19:W(uo);break;case 10:vr(t.type);break;case 22:case 23:co(t),ba(),null!==e&&W(Br);break;case 24:vr(Lr)}}function ol(e,t){try{var n=t.updateQueue,i=null!==n?n.lastEffect:null;if(null!==i){var r=i.next;n=r;do{if((n.tag&e)===e){i=void 0;var a=n.create,s=n.inst;i=a(),s.destroy=i}n=n.next}while(n!==r)}}catch(o){du(t,t.return,o)}}function ll(e,t,n){try{var i=t.updateQueue,r=null!==i?i.lastEffect:null;if(null!==r){var a=r.next;i=a;do{if((i.tag&e)===e){var s=i.inst,o=s.destroy;if(void 0!==o){s.destroy=void 0,r=t;var l=n,c=o;try{c()}catch(u){du(r,l,u)}}}i=i.next}while(i!==a)}}catch(u){du(t,t.return,u)}}function cl(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{ha(t,n)}catch(i){du(e,e.return,i)}}}function ul(e,t,n){n.props=bo(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(i){du(e,t,i)}}function dl(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var i=e.stateNode;break;default:i=e.stateNode}"function"===typeof n?e.refCleanup=n(i):n.current=i}}catch(r){du(e,t,r)}}function hl(e,t){var n=e.ref,i=e.refCleanup;if(null!==n)if("function"===typeof i)try{i()}catch(r){du(e,t,r)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"===typeof n)try{n(null)}catch(a){du(e,t,a)}else n.current=null}function fl(e){var t=e.type,n=e.memoizedProps,i=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&i.focus();break e;case"img":n.src?i.src=n.src:n.srcSet&&(i.srcset=n.srcSet)}}catch(r){du(e,e.return,r)}}function pl(e,t,n){try{var i=e.stateNode;!function(e,t,n,i){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var r=null,a=null,o=null,l=null,c=null,u=null,d=null;for(p in n){var h=n[p];if(n.hasOwnProperty(p)&&null!=h)switch(p){case"checked":case"value":break;case"defaultValue":c=h;default:i.hasOwnProperty(p)||Zu(e,t,p,null,i,h)}}for(var f in i){var p=i[f];if(h=n[f],i.hasOwnProperty(f)&&(null!=p||null!=h))switch(f){case"type":a=p;break;case"name":r=p;break;case"checked":u=p;break;case"defaultChecked":d=p;break;case"value":o=p;break;case"defaultValue":l=p;break;case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(s(137,t));break;default:p!==h&&Zu(e,t,f,p,i,h)}}return void gt(e,o,l,c,u,d,a,r);case"select":for(a in p=o=l=f=null,n)if(c=n[a],n.hasOwnProperty(a)&&null!=c)switch(a){case"value":break;case"multiple":p=c;default:i.hasOwnProperty(a)||Zu(e,t,a,null,i,c)}for(r in i)if(a=i[r],c=n[r],i.hasOwnProperty(r)&&(null!=a||null!=c))switch(r){case"value":f=a;break;case"defaultValue":l=a;break;case"multiple":o=a;default:a!==c&&Zu(e,t,r,a,i,c)}return t=l,n=o,i=p,void(null!=f?xt(e,!!n,f,!1):!!i!==!!n&&(null!=t?xt(e,!!n,t,!0):xt(e,!!n,n?[]:"",!1)));case"textarea":for(l in p=f=null,n)if(r=n[l],n.hasOwnProperty(l)&&null!=r&&!i.hasOwnProperty(l))switch(l){case"value":case"children":break;default:Zu(e,t,l,null,i,r)}for(o in i)if(r=i[o],a=n[o],i.hasOwnProperty(o)&&(null!=r||null!=a))switch(o){case"value":f=r;break;case"defaultValue":p=r;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=r)throw Error(s(91));break;default:r!==a&&Zu(e,t,o,r,i,a)}return void vt(e,f,p);case"option":for(var m in n)if(f=n[m],n.hasOwnProperty(m)&&null!=f&&!i.hasOwnProperty(m))if("selected"===m)e.selected=!1;else Zu(e,t,m,null,i,f);for(c in i)if(f=i[c],p=n[c],i.hasOwnProperty(c)&&f!==p&&(null!=f||null!=p))if("selected"===c)e.selected=f&&"function"!==typeof f&&"symbol"!==typeof f;else Zu(e,t,c,f,i,p);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)f=n[g],n.hasOwnProperty(g)&&null!=f&&!i.hasOwnProperty(g)&&Zu(e,t,g,null,i,f);for(u in i)if(f=i[u],p=n[u],i.hasOwnProperty(u)&&f!==p&&(null!=f||null!=p))switch(u){case"children":case"dangerouslySetInnerHTML":if(null!=f)throw Error(s(137,t));break;default:Zu(e,t,u,f,i,p)}return;default:if(Mt(t)){for(var b in n)f=n[b],n.hasOwnProperty(b)&&void 0!==f&&!i.hasOwnProperty(b)&&ed(e,t,b,void 0,i,f);for(d in i)f=i[d],p=n[d],!i.hasOwnProperty(d)||f===p||void 0===f&&void 0===p||ed(e,t,d,f,i,p);return}}for(var y in n)f=n[y],n.hasOwnProperty(y)&&null!=f&&!i.hasOwnProperty(y)&&Zu(e,t,y,null,i,f);for(h in i)f=i[h],p=n[h],!i.hasOwnProperty(h)||f===p||null==f&&null==p||Zu(e,t,h,f,i,p)}(i,e.type,n,t),i[Oe]=t}catch(r){du(e,e.return,r)}}function ml(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&pd(e.type)||4===e.tag}function gl(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||ml(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&pd(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function bl(e,t,n){var i=e.tag;if(5===i||6===i)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Ju));else if(4!==i&&(27===i&&pd(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(bl(e,t,n),e=e.sibling;null!==e;)bl(e,t,n),e=e.sibling}function yl(e,t,n){var i=e.tag;if(5===i||6===i)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==i&&(27===i&&pd(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(yl(e,t,n),e=e.sibling;null!==e;)yl(e,t,n),e=e.sibling}function xl(e){var t=e.stateNode,n=e.memoizedProps;try{for(var i=e.type,r=t.attributes;r.length;)t.removeAttributeNode(r[0]);td(t,i,n),t[Le]=e,t[Oe]=n}catch(a){du(e,e.return,a)}}var vl=!1,wl=!1,kl=!1,_l="function"===typeof WeakSet?WeakSet:Set,Sl=null;function Nl(e,t,n){var i=n.flags;switch(n.tag){case 0:case 11:case 15:Fl(e,n),4&i&&ol(5,n);break;case 1:if(Fl(e,n),4&i)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(s){du(n,n.return,s)}else{var r=bo(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(r,t,e.__reactInternalSnapshotBeforeUpdate)}catch(o){du(n,n.return,o)}}64&i&&cl(n),512&i&&dl(n,n.return);break;case 3:if(Fl(e,n),64&i&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{ha(e,t)}catch(s){du(n,n.return,s)}}break;case 27:null===t&&4&i&&xl(n);case 26:case 5:Fl(e,n),null===t&&4&i&&fl(n),512&i&&dl(n,n.return);break;case 12:Fl(e,n);break;case 13:Fl(e,n),4&i&&Tl(e,n),64&i&&(null!==(e=n.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var i=function(){t(),n.removeEventListener("DOMContentLoaded",i)};n.addEventListener("DOMContentLoaded",i),e._reactRetry=i}}(e,n=mu.bind(null,n))));break;case 22:if(!(i=null!==n.memoizedState||vl)){t=null!==t&&null!==t.memoizedState||wl,r=vl;var a=wl;vl=i,(wl=t)&&!a?Wl(e,n,0!==(8772&n.subtreeFlags)):Fl(e,n),vl=r,wl=a}break;case 30:break;default:Fl(e,n)}}function Ml(e){var t=e.alternate;null!==t&&(e.alternate=null,Ml(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&We(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Cl=null,Pl=!1;function El(e,t,n){for(n=n.child;null!==n;)jl(e,t,n),n=n.sibling}function jl(e,t,n){if(de&&"function"===typeof de.onCommitFiberUnmount)try{de.onCommitFiberUnmount(ue,n)}catch(a){}switch(n.tag){case 26:wl||hl(n,t),El(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:wl||hl(n,t);var i=Cl,r=Pl;pd(n.type)&&(Cl=n.stateNode,Pl=!1),El(e,t,n),kd(n.stateNode),Cl=i,Pl=r;break;case 5:wl||hl(n,t);case 6:if(i=Cl,r=Pl,Cl=null,El(e,t,n),Pl=r,null!==(Cl=i))if(Pl)try{(9===Cl.nodeType?Cl.body:"HTML"===Cl.nodeName?Cl.ownerDocument.body:Cl).removeChild(n.stateNode)}catch(s){du(n,t,s)}else try{Cl.removeChild(n.stateNode)}catch(s){du(n,t,s)}break;case 18:null!==Cl&&(Pl?(md(9===(e=Cl).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),Ph(e)):md(Cl,n.stateNode));break;case 4:i=Cl,r=Pl,Cl=n.stateNode.containerInfo,Pl=!0,El(e,t,n),Cl=i,Pl=r;break;case 0:case 11:case 14:case 15:wl||ll(2,n,t),wl||ll(4,n,t),El(e,t,n);break;case 1:wl||(hl(n,t),"function"===typeof(i=n.stateNode).componentWillUnmount&&ul(n,t,i)),El(e,t,n);break;case 21:El(e,t,n);break;case 22:wl=(i=wl)||null!==n.memoizedState,El(e,t,n),wl=i;break;default:El(e,t,n)}}function Tl(e,t){if(null===t.memoizedState&&(null!==(e=t.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{Ph(e)}catch(n){du(t,t.return,n)}}function Ll(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new _l),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new _l),t;default:throw Error(s(435,e.tag))}}(e);t.forEach(function(t){var i=gu.bind(null,e,t);n.has(t)||(n.add(t),t.then(i,i))})}function Ol(e,t){var n=t.deletions;if(null!==n)for(var i=0;i<n.length;i++){var r=n[i],a=e,o=t,l=o;e:for(;null!==l;){switch(l.tag){case 27:if(pd(l.type)){Cl=l.stateNode,Pl=!1;break e}break;case 5:Cl=l.stateNode,Pl=!1;break e;case 3:case 4:Cl=l.stateNode.containerInfo,Pl=!0;break e}l=l.return}if(null===Cl)throw Error(s(160));jl(a,o,r),Cl=null,Pl=!1,null!==(a=r.alternate)&&(a.return=null),r.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)Dl(t,e),t=t.sibling}var Al=null;function Dl(e,t){var n=e.alternate,i=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Ol(t,e),Rl(e),4&i&&(ll(3,e,e.return),ol(3,e),ll(5,e,e.return));break;case 1:Ol(t,e),Rl(e),512&i&&(wl||null===n||hl(n,n.return)),64&i&&vl&&(null!==(e=e.updateQueue)&&(null!==(i=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?i:n.concat(i))));break;case 26:var r=Al;if(Ol(t,e),Rl(e),512&i&&(wl||null===n||hl(n,n.return)),4&i){var a=null!==n?n.memoizedState:null;if(i=e.memoizedState,null===n)if(null===i)if(null===e.stateNode){e:{i=e.type,n=e.memoizedProps,r=r.ownerDocument||r;t:switch(i){case"title":(!(a=r.getElementsByTagName("title")[0])||a[Ie]||a[Le]||"http://www.w3.org/2000/svg"===a.namespaceURI||a.hasAttribute("itemprop"))&&(a=r.createElement(i),r.head.insertBefore(a,r.querySelector("head > title"))),td(a,i,n),a[Le]=e,$e(a),i=a;break e;case"link":var o=Wd("link","href",r).get(i+(n.href||""));if(o)for(var l=0;l<o.length;l++)if((a=o[l]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&a.getAttribute("rel")===(null==n.rel?null:n.rel)&&a.getAttribute("title")===(null==n.title?null:n.title)&&a.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){o.splice(l,1);break t}td(a=r.createElement(i),i,n),r.head.appendChild(a);break;case"meta":if(o=Wd("meta","content",r).get(i+(n.content||"")))for(l=0;l<o.length;l++)if((a=o[l]).getAttribute("content")===(null==n.content?null:""+n.content)&&a.getAttribute("name")===(null==n.name?null:n.name)&&a.getAttribute("property")===(null==n.property?null:n.property)&&a.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&a.getAttribute("charset")===(null==n.charSet?null:n.charSet)){o.splice(l,1);break t}td(a=r.createElement(i),i,n),r.head.appendChild(a);break;default:throw Error(s(468,i))}a[Le]=e,$e(a),i=a}e.stateNode=i}else Bd(r,e.type,e.stateNode);else e.stateNode=Dd(r,i,e.memoizedProps);else a!==i?(null===a?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):a.count--,null===i?Bd(r,e.type,e.stateNode):Dd(r,i,e.memoizedProps)):null===i&&null!==e.stateNode&&pl(e,e.memoizedProps,n.memoizedProps)}break;case 27:Ol(t,e),Rl(e),512&i&&(wl||null===n||hl(n,n.return)),null!==n&&4&i&&pl(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Ol(t,e),Rl(e),512&i&&(wl||null===n||hl(n,n.return)),32&e.flags){r=e.stateNode;try{kt(r,"")}catch(p){du(e,e.return,p)}}4&i&&null!=e.stateNode&&pl(e,r=e.memoizedProps,null!==n?n.memoizedProps:r),1024&i&&(kl=!0);break;case 6:if(Ol(t,e),Rl(e),4&i){if(null===e.stateNode)throw Error(s(162));i=e.memoizedProps,n=e.stateNode;try{n.nodeValue=i}catch(p){du(e,e.return,p)}}break;case 3:if(Id=null,r=Al,Al=Nd(t.containerInfo),Ol(t,e),Al=r,Rl(e),4&i&&null!==n&&n.memoizedState.isDehydrated)try{Ph(t.containerInfo)}catch(p){du(e,e.return,p)}kl&&(kl=!1,zl(e));break;case 4:i=Al,Al=Nd(e.stateNode.containerInfo),Ol(t,e),Rl(e),Al=i;break;case 12:default:Ol(t,e),Rl(e);break;case 13:Ol(t,e),Rl(e),8192&e.child.flags&&null!==e.memoizedState!==(null!==n&&null!==n.memoizedState)&&(kc=te()),4&i&&(null!==(i=e.updateQueue)&&(e.updateQueue=null,Ll(e,i)));break;case 22:r=null!==e.memoizedState;var c=null!==n&&null!==n.memoizedState,u=vl,d=wl;if(vl=u||r,wl=d||c,Ol(t,e),wl=d,vl=u,Rl(e),8192&i)e:for(t=e.stateNode,t._visibility=r?-2&t._visibility:1|t._visibility,r&&(null===n||c||vl||wl||Il(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){c=n=t;try{if(a=c.stateNode,r)"function"===typeof(o=a.style).setProperty?o.setProperty("display","none","important"):o.display="none";else{l=c.stateNode;var h=c.memoizedProps.style,f=void 0!==h&&null!==h&&h.hasOwnProperty("display")?h.display:null;l.style.display=null==f||"boolean"===typeof f?"":(""+f).trim()}}catch(p){du(c,c.return,p)}}}else if(6===t.tag){if(null===n){c=t;try{c.stateNode.nodeValue=r?"":c.memoizedProps}catch(p){du(c,c.return,p)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&i&&(null!==(i=e.updateQueue)&&(null!==(n=i.retryQueue)&&(i.retryQueue=null,Ll(e,n))));break;case 19:Ol(t,e),Rl(e),4&i&&(null!==(i=e.updateQueue)&&(e.updateQueue=null,Ll(e,i)));case 30:case 21:}}function Rl(e){var t=e.flags;if(2&t){try{for(var n,i=e.return;null!==i;){if(ml(i)){n=i;break}i=i.return}if(null==n)throw Error(s(160));switch(n.tag){case 27:var r=n.stateNode;yl(e,gl(e),r);break;case 5:var a=n.stateNode;32&n.flags&&(kt(a,""),n.flags&=-33),yl(e,gl(e),a);break;case 3:case 4:var o=n.stateNode.containerInfo;bl(e,gl(e),o);break;default:throw Error(s(161))}}catch(l){du(e,e.return,l)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function zl(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;zl(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function Fl(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)Nl(e,t.alternate,t),t=t.sibling}function Il(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:ll(4,t,t.return),Il(t);break;case 1:hl(t,t.return);var n=t.stateNode;"function"===typeof n.componentWillUnmount&&ul(t,t.return,n),Il(t);break;case 27:kd(t.stateNode);case 26:case 5:hl(t,t.return),Il(t);break;case 22:null===t.memoizedState&&Il(t);break;default:Il(t)}e=e.sibling}}function Wl(e,t,n){for(n=n&&0!==(8772&t.subtreeFlags),t=t.child;null!==t;){var i=t.alternate,r=e,a=t,s=a.flags;switch(a.tag){case 0:case 11:case 15:Wl(r,a,n),ol(4,a);break;case 1:if(Wl(r,a,n),"function"===typeof(r=(i=a).stateNode).componentDidMount)try{r.componentDidMount()}catch(c){du(i,i.return,c)}if(null!==(r=(i=a).updateQueue)){var o=i.stateNode;try{var l=r.shared.hiddenCallbacks;if(null!==l)for(r.shared.hiddenCallbacks=null,r=0;r<l.length;r++)da(l[r],o)}catch(c){du(i,i.return,c)}}n&&64&s&&cl(a),dl(a,a.return);break;case 27:xl(a);case 26:case 5:Wl(r,a,n),n&&null===i&&4&s&&fl(a),dl(a,a.return);break;case 12:Wl(r,a,n);break;case 13:Wl(r,a,n),n&&4&s&&Tl(r,a);break;case 22:null===a.memoizedState&&Wl(r,a,n),dl(a,a.return);break;case 30:break;default:Wl(r,a,n)}t=t.sibling}}function Bl(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&Ar(n))}function Hl(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Ar(e))}function Vl(e,t,n,i){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)Ul(e,t,n,i),t=t.sibling}function Ul(e,t,n,i){var r=t.flags;switch(t.tag){case 0:case 11:case 15:Vl(e,t,n,i),2048&r&&ol(9,t);break;case 1:case 13:default:Vl(e,t,n,i);break;case 3:Vl(e,t,n,i),2048&r&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Ar(e)));break;case 12:if(2048&r){Vl(e,t,n,i),e=t.stateNode;try{var a=t.memoizedProps,s=a.id,o=a.onPostCommit;"function"===typeof o&&o(s,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(l){du(t,t.return,l)}}else Vl(e,t,n,i);break;case 23:break;case 22:a=t.stateNode,s=t.alternate,null!==t.memoizedState?2&a._visibility?Vl(e,t,n,i):Yl(e,t):2&a._visibility?Vl(e,t,n,i):(a._visibility|=2,$l(e,t,n,i,0!==(10256&t.subtreeFlags))),2048&r&&Bl(s,t);break;case 24:Vl(e,t,n,i),2048&r&&Hl(t.alternate,t)}}function $l(e,t,n,i,r){for(r=r&&0!==(10256&t.subtreeFlags),t=t.child;null!==t;){var a=e,s=t,o=n,l=i,c=s.flags;switch(s.tag){case 0:case 11:case 15:$l(a,s,o,l,r),ol(8,s);break;case 23:break;case 22:var u=s.stateNode;null!==s.memoizedState?2&u._visibility?$l(a,s,o,l,r):Yl(a,s):(u._visibility|=2,$l(a,s,o,l,r)),r&&2048&c&&Bl(s.alternate,s);break;case 24:$l(a,s,o,l,r),r&&2048&c&&Hl(s.alternate,s);break;default:$l(a,s,o,l,r)}t=t.sibling}}function Yl(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,i=t,r=i.flags;switch(i.tag){case 22:Yl(n,i),2048&r&&Bl(i.alternate,i);break;case 24:Yl(n,i),2048&r&&Hl(i.alternate,i);break;default:Yl(n,i)}t=t.sibling}}var ql=8192;function Kl(e){if(e.subtreeFlags&ql)for(e=e.child;null!==e;)Ql(e),e=e.sibling}function Ql(e){switch(e.tag){case 26:Kl(e),e.flags&ql&&null!==e.memoizedState&&function(e,t,n){if(null===Vd)throw Error(s(475));var i=Vd;if("stylesheet"===t.type&&("string"!==typeof n.media||!1!==matchMedia(n.media).matches)&&0===(4&t.state.loading)){if(null===t.instance){var r=jd(n.href),a=e.querySelector(Td(r));if(a)return null!==(e=a._p)&&"object"===typeof e&&"function"===typeof e.then&&(i.count++,i=$d.bind(i),e.then(i,i)),t.state.loading|=4,t.instance=a,void $e(a);a=e.ownerDocument||e,n=Ld(n),(r=_d.get(r))&&zd(n,r),$e(a=a.createElement("link"));var o=a;o._p=new Promise(function(e,t){o.onload=e,o.onerror=t}),td(a,"link",n),t.instance=a}null===i.stylesheets&&(i.stylesheets=new Map),i.stylesheets.set(t,e),(e=t.state.preload)&&0===(3&t.state.loading)&&(i.count++,t=$d.bind(i),e.addEventListener("load",t),e.addEventListener("error",t))}}(Al,e.memoizedState,e.memoizedProps);break;case 5:default:Kl(e);break;case 3:case 4:var t=Al;Al=Nd(e.stateNode.containerInfo),Kl(e),Al=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=ql,ql=16777216,Kl(e),ql=t):Kl(e))}}function Xl(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Gl(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var i=t[n];Sl=i,ec(i,e)}Xl(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Jl(e),e=e.sibling}function Jl(e){switch(e.tag){case 0:case 11:case 15:Gl(e),2048&e.flags&&ll(9,e,e.return);break;case 3:case 12:default:Gl(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,Zl(e)):Gl(e)}}function Zl(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var i=t[n];Sl=i,ec(i,e)}Xl(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:ll(8,t,t.return),Zl(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,Zl(t));break;default:Zl(t)}e=e.sibling}}function ec(e,t){for(;null!==Sl;){var n=Sl;switch(n.tag){case 0:case 11:case 15:ll(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var i=n.memoizedState.cachePool.pool;null!=i&&i.refCount++}break;case 24:Ar(n.memoizedState.cache)}if(null!==(i=n.child))i.return=n,Sl=i;else e:for(n=e;null!==Sl;){var r=(i=Sl).sibling,a=i.return;if(Ml(i),i===n){Sl=null;break e}if(null!==r){r.return=a,Sl=r;break e}Sl=a}}}var tc={getCacheForType:function(e){var t=Mr(Lr),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},nc="function"===typeof WeakMap?WeakMap:Map,ic=0,rc=null,ac=null,sc=0,oc=0,lc=null,cc=!1,uc=!1,dc=!1,hc=0,fc=0,pc=0,mc=0,gc=0,bc=0,yc=0,xc=null,vc=null,wc=!1,kc=0,_c=1/0,Sc=null,Nc=null,Mc=0,Cc=null,Pc=null,Ec=0,jc=0,Tc=null,Lc=null,Oc=0,Ac=null;function Dc(){if(0!==(2&ic)&&0!==sc)return sc&-sc;if(null!==A.T){return 0!==zr?zr:ju()}return je()}function Rc(){0===bc&&(bc=0===(536870912&sc)||ar?ke():536870912);var e=ro.current;return null!==e&&(e.flags|=32),bc}function zc(e,t,n){(e!==rc||2!==oc&&9!==oc)&&null===e.cancelPendingCommit||(Uc(e,0),Bc(e,sc,bc,!1)),Ne(e,n),0!==(2&ic)&&e===rc||(e===rc&&(0===(2&ic)&&(mc|=n),4===fc&&Bc(e,sc,bc,!1)),_u(e))}function Fc(e,t,n){if(0!==(6&ic))throw Error(s(327));for(var i=!n&&0===(124&t)&&0===(t&e.expiredLanes)||ve(e,t),r=i?function(e,t){var n=ic;ic|=2;var i=Yc(),r=qc();rc!==e||sc!==t?(Sc=null,_c=te()+500,Uc(e,t)):uc=ve(e,t);e:for(;;)try{if(0!==oc&&null!==ac){t=ac;var a=lc;t:switch(oc){case 1:oc=0,lc=null,eu(e,t,a,1);break;case 2:case 9:if(Qr(a)){oc=0,lc=null,Zc(t);break}t=function(){2!==oc&&9!==oc||rc!==e||(oc=7),_u(e)},a.then(t,t);break e;case 3:oc=7;break e;case 4:oc=5;break e;case 7:Qr(a)?(oc=0,lc=null,Zc(t)):(oc=0,lc=null,eu(e,t,a,7));break;case 5:var o=null;switch(ac.tag){case 26:o=ac.memoizedState;case 5:case 27:var l=ac;if(!o||Hd(o)){oc=0,lc=null;var c=l.sibling;if(null!==c)ac=c;else{var u=l.return;null!==u?(ac=u,tu(u)):ac=null}break t}}oc=0,lc=null,eu(e,t,a,5);break;case 6:oc=0,lc=null,eu(e,t,a,6);break;case 8:Vc(),fc=6;break e;default:throw Error(s(462))}}Gc();break}catch(d){$c(e,d)}return yr=br=null,A.H=i,A.A=r,ic=n,null!==ac?0:(rc=null,sc=0,Pi(),fc)}(e,t):Qc(e,t,!0),a=i;;){if(0===r){uc&&!i&&Bc(e,t,0,!1);break}if(n=e.current.alternate,!a||Wc(n)){if(2===r){if(a=t,e.errorRecoveryDisabledLanes&a)var o=0;else o=0!==(o=-536870913&e.pendingLanes)?o:536870912&o?536870912:0;if(0!==o){t=o;e:{var l=e;r=xc;var c=l.current.memoizedState.isDehydrated;if(c&&(Uc(l,o).flags|=256),2!==(o=Qc(l,o,!1))){if(dc&&!c){l.errorRecoveryDisabledLanes|=a,mc|=a,r=4;break e}a=vc,vc=r,null!==a&&(null===vc?vc=a:vc.push.apply(vc,a))}r=o}if(a=!1,2!==r)continue}}if(1===r){Uc(e,0),Bc(e,t,0,!0);break}e:{switch(i=e,a=r){case 0:case 1:throw Error(s(345));case 4:if((4194048&t)!==t)break;case 6:Bc(i,t,bc,!cc);break e;case 2:vc=null;break;case 3:case 5:break;default:throw Error(s(329))}if((62914560&t)===t&&10<(r=kc+300-te())){if(Bc(i,t,bc,!cc),0!==xe(i,0,!0))break e;i.timeoutHandle=cd(Ic.bind(null,i,n,vc,Sc,wc,t,bc,mc,yc,cc,a,2,-0,0),r)}else Ic(i,n,vc,Sc,wc,t,bc,mc,yc,cc,a,0,-0,0)}break}r=Qc(e,t,!1),a=!1}_u(e)}function Ic(e,t,n,i,r,a,o,l,c,u,d,h,f,p){if(e.timeoutHandle=-1,(8192&(h=t.subtreeFlags)||16785408===(16785408&h))&&(Vd={stylesheets:null,count:0,unsuspend:Ud},Ql(t),null!==(h=function(){if(null===Vd)throw Error(s(475));var e=Vd;return e.stylesheets&&0===e.count&&qd(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&qd(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=h(iu.bind(null,e,t,a,n,i,r,o,l,c,d,1,f,p)),void Bc(e,a,o,!u);iu(e,t,a,n,i,r,o,l,c)}function Wc(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&(null!==(n=t.updateQueue)&&null!==(n=n.stores)))for(var i=0;i<n.length;i++){var r=n[i],a=r.getSnapshot;r=r.value;try{if(!Qn(a(),r))return!1}catch(s){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Bc(e,t,n,i){t&=~gc,t&=~mc,e.suspendedLanes|=t,e.pingedLanes&=~t,i&&(e.warmLanes|=t),i=e.expirationTimes;for(var r=t;0<r;){var a=31-fe(r),s=1<<a;i[a]=-1,r&=~s}0!==n&&Me(e,n,t)}function Hc(){return 0!==(6&ic)||(Su(0,!1),!1)}function Vc(){if(null!==ac){if(0===oc)var e=ac.return;else yr=br=null,za(e=ac),Qs=null,Xs=0,e=ac;for(;null!==e;)sl(e.alternate,e),e=e.return;ac=null}}function Uc(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,ud(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Vc(),rc=e,ac=n=Fi(e.current,null),sc=t,oc=0,lc=null,cc=!1,uc=ve(e,t),dc=!1,yc=bc=gc=mc=pc=fc=0,vc=xc=null,wc=!1,0!==(8&t)&&(t|=32&t);var i=e.entangledLanes;if(0!==i)for(e=e.entanglements,i&=t;0<i;){var r=31-fe(i),a=1<<r;t|=e[r],i&=~a}return hc=t,Pi(),n}function $c(e,t){xa=null,A.H=$s,t===$r||t===qr?(t=Zr(),oc=3):t===Yr?(t=Zr(),oc=4):oc=t===Co?8:null!==t&&"object"===typeof t&&"function"===typeof t.then?6:1,lc=t,null===ac&&(fc=1,ko(e,Si(t,e.current)))}function Yc(){var e=A.H;return A.H=$s,null===e?$s:e}function qc(){var e=A.A;return A.A=tc,e}function Kc(){fc=4,cc||(4194048&sc)!==sc&&null!==ro.current||(uc=!0),0===(134217727&pc)&&0===(134217727&mc)||null===rc||Bc(rc,sc,bc,!1)}function Qc(e,t,n){var i=ic;ic|=2;var r=Yc(),a=qc();rc===e&&sc===t||(Sc=null,Uc(e,t)),t=!1;var s=fc;e:for(;;)try{if(0!==oc&&null!==ac){var o=ac,l=lc;switch(oc){case 8:Vc(),s=6;break e;case 3:case 2:case 9:case 6:null===ro.current&&(t=!0);var c=oc;if(oc=0,lc=null,eu(e,o,l,c),n&&uc){s=0;break e}break;default:c=oc,oc=0,lc=null,eu(e,o,l,c)}}Xc(),s=fc;break}catch(u){$c(e,u)}return t&&e.shellSuspendCounter++,yr=br=null,ic=i,A.H=r,A.A=a,null===ac&&(rc=null,sc=0,Pi()),s}function Xc(){for(;null!==ac;)Jc(ac)}function Gc(){for(;null!==ac&&!Z();)Jc(ac)}function Jc(e){var t=Jo(e.alternate,e,hc);e.memoizedProps=e.pendingProps,null===t?tu(e):ac=t}function Zc(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=zo(n,t,t.pendingProps,t.type,void 0,sc);break;case 11:t=zo(n,t,t.pendingProps,t.type.render,t.ref,sc);break;case 5:za(t);default:sl(n,t),t=Jo(n,t=ac=Ii(t,hc),hc)}e.memoizedProps=e.pendingProps,null===t?tu(e):ac=t}function eu(e,t,n,i){yr=br=null,za(t),Qs=null,Xs=0;var r=t.return;try{if(function(e,t,n,i,r){if(n.flags|=32768,null!==i&&"object"===typeof i&&"function"===typeof i.then){if(null!==(t=n.alternate)&&_r(t,n,r,!0),null!==(n=ro.current)){switch(n.tag){case 13:return null===ao?Kc():null===n.alternate&&0===fc&&(fc=3),n.flags&=-257,n.flags|=65536,n.lanes=r,i===Kr?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([i]):t.add(i),hu(e,i,r)),!1;case 22:return n.flags|=65536,i===Kr?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([i])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([i]):n.add(i),hu(e,i,r)),!1}throw Error(s(435,n.tag))}return hu(e,i,r),Kc(),!1}if(ar)return null!==(t=ro.current)?(0===(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=r,i!==lr&&mr(Si(e=Error(s(422),{cause:i}),n))):(i!==lr&&mr(Si(t=Error(s(423),{cause:i}),n)),(e=e.current.alternate).flags|=65536,r&=-r,e.lanes|=r,i=Si(i,n),oa(e,r=So(e.stateNode,i,r)),4!==fc&&(fc=2)),!1;var a=Error(s(520),{cause:i});if(a=Si(a,n),null===xc?xc=[a]:xc.push(a),4!==fc&&(fc=2),null===t)return!0;i=Si(i,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=r&-r,n.lanes|=e,oa(n,e=So(n.stateNode,i,e)),!1;case 1:if(t=n.type,a=n.stateNode,0===(128&n.flags)&&("function"===typeof t.getDerivedStateFromError||null!==a&&"function"===typeof a.componentDidCatch&&(null===Nc||!Nc.has(a))))return n.flags|=65536,r&=-r,n.lanes|=r,Mo(r=No(r),e,n,i),oa(n,r),!1}n=n.return}while(null!==n);return!1}(e,r,t,n,sc))return fc=1,ko(e,Si(n,e.current)),void(ac=null)}catch(a){if(null!==r)throw ac=r,a;return fc=1,ko(e,Si(n,e.current)),void(ac=null)}32768&t.flags?(ar||1===i?e=!0:uc||0!==(536870912&sc)?e=!1:(cc=e=!0,(2===i||9===i||3===i||6===i)&&(null!==(i=ro.current)&&13===i.tag&&(i.flags|=16384))),nu(t,e)):tu(t)}function tu(e){var t=e;do{if(0!==(32768&t.flags))return void nu(t,cc);e=t.return;var n=rl(t.alternate,t,hc);if(null!==n)return void(ac=n);if(null!==(t=t.sibling))return void(ac=t);ac=t=e}while(null!==t);0===fc&&(fc=5)}function nu(e,t){do{var n=al(e.alternate,e);if(null!==n)return n.flags&=32767,void(ac=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(ac=e);ac=e=n}while(null!==e);fc=6,ac=null}function iu(e,t,n,i,r,a,o,l,c){e.cancelPendingCommit=null;do{lu()}while(0!==Mc);if(0!==(6&ic))throw Error(s(327));if(null!==t){if(t===e.current)throw Error(s(177));if(a=t.lanes|t.childLanes,function(e,t,n,i,r,a){var s=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var o=e.entanglements,l=e.expirationTimes,c=e.hiddenUpdates;for(n=s&~n;0<n;){var u=31-fe(n),d=1<<u;o[u]=0,l[u]=-1;var h=c[u];if(null!==h)for(c[u]=null,u=0;u<h.length;u++){var f=h[u];null!==f&&(f.lane&=-536870913)}n&=~d}0!==i&&Me(e,i,0),0!==a&&0===r&&0!==e.tag&&(e.suspendedLanes|=a&~(s&~t))}(e,n,a|=Ci,o,l,c),e===rc&&(ac=rc=null,sc=0),Pc=t,Cc=e,Ec=n,jc=a,Tc=r,Lc=i,0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?(e.callbackNode=null,e.callbackPriority=0,function(e,t){G(e,t)}(ae,function(){return cu(),null})):(e.callbackNode=null,e.callbackPriority=0),i=0!==(13878&t.flags),0!==(13878&t.subtreeFlags)||i){i=A.T,A.T=null,r=D.p,D.p=2,o=ic,ic|=4;try{!function(e,t){if(e=e.containerInfo,nd=ih,ti(e=ei(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var i=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(i&&0!==i.rangeCount){n=i.anchorNode;var r=i.anchorOffset,a=i.focusNode;i=i.focusOffset;try{n.nodeType,a.nodeType}catch(g){n=null;break e}var o=0,l=-1,c=-1,u=0,d=0,h=e,f=null;t:for(;;){for(var p;h!==n||0!==r&&3!==h.nodeType||(l=o+r),h!==a||0!==i&&3!==h.nodeType||(c=o+i),3===h.nodeType&&(o+=h.nodeValue.length),null!==(p=h.firstChild);)f=h,h=p;for(;;){if(h===e)break t;if(f===n&&++u===r&&(l=o),f===a&&++d===i&&(c=o),null!==(p=h.nextSibling))break;f=(h=f).parentNode}h=p}n=-1===l||-1===c?null:{start:l,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(id={focusedElem:e,selectionRange:n},ih=!1,Sl=t;null!==Sl;)if(e=(t=Sl).child,0!==(1024&t.subtreeFlags)&&null!==e)e.return=t,Sl=e;else for(;null!==Sl;){switch(a=(t=Sl).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(0!==(1024&e)&&null!==a){e=void 0,n=t,r=a.memoizedProps,a=a.memoizedState,i=n.stateNode;try{var m=bo(n.type,r,(n.elementType,n.type));e=i.getSnapshotBeforeUpdate(m,a),i.__reactInternalSnapshotBeforeUpdate=e}catch(b){du(n,n.return,b)}}break;case 3:if(0!==(1024&e))if(9===(n=(e=t.stateNode.containerInfo).nodeType))gd(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":gd(e);break;default:e.textContent=""}break;default:if(0!==(1024&e))throw Error(s(163))}if(null!==(e=t.sibling)){e.return=t.return,Sl=e;break}Sl=t.return}}(e,t)}finally{ic=o,D.p=r,A.T=i}}Mc=1,ru(),au(),su()}}function ru(){if(1===Mc){Mc=0;var e=Cc,t=Pc,n=0!==(13878&t.flags);if(0!==(13878&t.subtreeFlags)||n){n=A.T,A.T=null;var i=D.p;D.p=2;var r=ic;ic|=4;try{Dl(t,e);var a=id,s=ei(e.containerInfo),o=a.focusedElem,l=a.selectionRange;if(s!==o&&o&&o.ownerDocument&&Zn(o.ownerDocument.documentElement,o)){if(null!==l&&ti(o)){var c=l.start,u=l.end;if(void 0===u&&(u=c),"selectionStart"in o)o.selectionStart=c,o.selectionEnd=Math.min(u,o.value.length);else{var d=o.ownerDocument||document,h=d&&d.defaultView||window;if(h.getSelection){var f=h.getSelection(),p=o.textContent.length,m=Math.min(l.start,p),g=void 0===l.end?m:Math.min(l.end,p);!f.extend&&m>g&&(s=g,g=m,m=s);var b=Jn(o,m),y=Jn(o,g);if(b&&y&&(1!==f.rangeCount||f.anchorNode!==b.node||f.anchorOffset!==b.offset||f.focusNode!==y.node||f.focusOffset!==y.offset)){var x=d.createRange();x.setStart(b.node,b.offset),f.removeAllRanges(),m>g?(f.addRange(x),f.extend(y.node,y.offset)):(x.setEnd(y.node,y.offset),f.addRange(x))}}}}for(d=[],f=o;f=f.parentNode;)1===f.nodeType&&d.push({element:f,left:f.scrollLeft,top:f.scrollTop});for("function"===typeof o.focus&&o.focus(),o=0;o<d.length;o++){var v=d[o];v.element.scrollLeft=v.left,v.element.scrollTop=v.top}}ih=!!nd,id=nd=null}finally{ic=r,D.p=i,A.T=n}}e.current=t,Mc=2}}function au(){if(2===Mc){Mc=0;var e=Cc,t=Pc,n=0!==(8772&t.flags);if(0!==(8772&t.subtreeFlags)||n){n=A.T,A.T=null;var i=D.p;D.p=2;var r=ic;ic|=4;try{Nl(e,t.alternate,t)}finally{ic=r,D.p=i,A.T=n}}Mc=3}}function su(){if(4===Mc||3===Mc){Mc=0,ee();var e=Cc,t=Pc,n=Ec,i=Lc;0!==(10256&t.subtreeFlags)||0!==(10256&t.flags)?Mc=5:(Mc=0,Pc=Cc=null,ou(e,e.pendingLanes));var r=e.pendingLanes;if(0===r&&(Nc=null),Ee(n),t=t.stateNode,de&&"function"===typeof de.onCommitFiberRoot)try{de.onCommitFiberRoot(ue,t,void 0,128===(128&t.current.flags))}catch(l){}if(null!==i){t=A.T,r=D.p,D.p=2,A.T=null;try{for(var a=e.onRecoverableError,s=0;s<i.length;s++){var o=i[s];a(o.value,{componentStack:o.stack})}}finally{A.T=t,D.p=r}}0!==(3&Ec)&&lu(),_u(e),r=e.pendingLanes,0!==(4194090&n)&&0!==(42&r)?e===Ac?Oc++:(Oc=0,Ac=e):Oc=0,Su(0,!1)}}function ou(e,t){0===(e.pooledCacheLanes&=t)&&(null!=(t=e.pooledCache)&&(e.pooledCache=null,Ar(t)))}function lu(e){return ru(),au(),su(),cu()}function cu(){if(5!==Mc)return!1;var e=Cc,t=jc;jc=0;var n=Ee(Ec),i=A.T,r=D.p;try{D.p=32>n?32:n,A.T=null,n=Tc,Tc=null;var a=Cc,o=Ec;if(Mc=0,Pc=Cc=null,Ec=0,0!==(6&ic))throw Error(s(331));var l=ic;if(ic|=4,Jl(a.current),Ul(a,a.current,o,n),ic=l,Su(0,!1),de&&"function"===typeof de.onPostCommitFiberRoot)try{de.onPostCommitFiberRoot(ue,a)}catch(c){}return!0}finally{D.p=r,A.T=i,ou(e,t)}}function uu(e,t,n){t=Si(n,t),null!==(e=aa(e,t=So(e.stateNode,t,2),2))&&(Ne(e,2),_u(e))}function du(e,t,n){if(3===e.tag)uu(e,e,n);else for(;null!==t;){if(3===t.tag){uu(t,e,n);break}if(1===t.tag){var i=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof i.componentDidCatch&&(null===Nc||!Nc.has(i))){e=Si(n,e),null!==(i=aa(t,n=No(2),2))&&(Mo(n,i,t,e),Ne(i,2),_u(i));break}}t=t.return}}function hu(e,t,n){var i=e.pingCache;if(null===i){i=e.pingCache=new nc;var r=new Set;i.set(t,r)}else void 0===(r=i.get(t))&&(r=new Set,i.set(t,r));r.has(n)||(dc=!0,r.add(n),e=fu.bind(null,e,t,n),t.then(e,e))}function fu(e,t,n){var i=e.pingCache;null!==i&&i.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,rc===e&&(sc&n)===n&&(4===fc||3===fc&&(62914560&sc)===sc&&300>te()-kc?0===(2&ic)&&Uc(e,0):gc|=n,yc===sc&&(yc=0)),_u(e)}function pu(e,t){0===t&&(t=_e()),null!==(e=Ti(e,t))&&(Ne(e,t),_u(e))}function mu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),pu(e,n)}function gu(e,t){var n=0;switch(e.tag){case 13:var i=e.stateNode,r=e.memoizedState;null!==r&&(n=r.retryLane);break;case 19:i=e.stateNode;break;case 22:i=e.stateNode._retryCache;break;default:throw Error(s(314))}null!==i&&i.delete(t),pu(e,n)}var bu=null,yu=null,xu=!1,vu=!1,wu=!1,ku=0;function _u(e){e!==yu&&null===e.next&&(null===yu?bu=yu=e:yu=yu.next=e),vu=!0,xu||(xu=!0,hd(function(){0!==(6&ic)?G(ie,Nu):Mu()}))}function Su(e,t){if(!wu&&vu){wu=!0;do{for(var n=!1,i=bu;null!==i;){if(!t)if(0!==e){var r=i.pendingLanes;if(0===r)var a=0;else{var s=i.suspendedLanes,o=i.pingedLanes;a=(1<<31-fe(42|e)+1)-1,a=201326741&(a&=r&~(s&~o))?201326741&a|1:a?2|a:0}0!==a&&(n=!0,Eu(i,a))}else a=sc,0===(3&(a=xe(i,i===rc?a:0,null!==i.cancelPendingCommit||-1!==i.timeoutHandle)))||ve(i,a)||(n=!0,Eu(i,a));i=i.next}}while(n);wu=!1}}function Nu(){Mu()}function Mu(){vu=xu=!1;var e=0;0!==ku&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==ld&&(ld=e,!0);return ld=null,!1}()&&(e=ku),ku=0);for(var t=te(),n=null,i=bu;null!==i;){var r=i.next,a=Cu(i,t);0===a?(i.next=null,null===n?bu=r:n.next=r,null===r&&(yu=n)):(n=i,(0!==e||0!==(3&a))&&(vu=!0)),i=r}Su(e,!1)}function Cu(e,t){for(var n=e.suspendedLanes,i=e.pingedLanes,r=e.expirationTimes,a=-62914561&e.pendingLanes;0<a;){var s=31-fe(a),o=1<<s,l=r[s];-1===l?0!==(o&n)&&0===(o&i)||(r[s]=we(o,t)):l<=t&&(e.expiredLanes|=o),a&=~o}if(n=sc,n=xe(e,e===(t=rc)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),i=e.callbackNode,0===n||e===t&&(2===oc||9===oc)||null!==e.cancelPendingCommit)return null!==i&&null!==i&&J(i),e.callbackNode=null,e.callbackPriority=0;if(0===(3&n)||ve(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==i&&J(i),Ee(n)){case 2:case 8:n=re;break;case 32:default:n=ae;break;case 268435456:n=oe}return i=Pu.bind(null,e),n=G(n,i),e.callbackPriority=t,e.callbackNode=n,t}return null!==i&&null!==i&&J(i),e.callbackPriority=2,e.callbackNode=null,2}function Pu(e,t){if(0!==Mc&&5!==Mc)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(lu()&&e.callbackNode!==n)return null;var i=sc;return 0===(i=xe(e,e===rc?i:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Fc(e,i,t),Cu(e,te()),null!=e.callbackNode&&e.callbackNode===n?Pu.bind(null,e):null)}function Eu(e,t){if(lu())return null;Fc(e,t,!0)}function ju(){return 0===ku&&(ku=ke()),ku}function Tu(e){return null==e||"symbol"===typeof e||"boolean"===typeof e?null:"function"===typeof e?e:Et(""+e)}function Lu(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var Ou=0;Ou<wi.length;Ou++){var Au=wi[Ou];ki(Au.toLowerCase(),"on"+(Au[0].toUpperCase()+Au.slice(1)))}ki(fi,"onAnimationEnd"),ki(pi,"onAnimationIteration"),ki(mi,"onAnimationStart"),ki("dblclick","onDoubleClick"),ki("focusin","onFocus"),ki("focusout","onBlur"),ki(gi,"onTransitionRun"),ki(bi,"onTransitionStart"),ki(yi,"onTransitionCancel"),ki(xi,"onTransitionEnd"),Qe("onMouseEnter",["mouseout","mouseover"]),Qe("onMouseLeave",["mouseout","mouseover"]),Qe("onPointerEnter",["pointerout","pointerover"]),Qe("onPointerLeave",["pointerout","pointerover"]),Ke("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ke("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ke("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ke("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ke("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ke("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Du="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ru=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Du));function zu(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var i=e[n],r=i.event;i=i.listeners;e:{var a=void 0;if(t)for(var s=i.length-1;0<=s;s--){var o=i[s],l=o.instance,c=o.currentTarget;if(o=o.listener,l!==a&&r.isPropagationStopped())break e;a=o,r.currentTarget=c;try{a(r)}catch(u){yo(u)}r.currentTarget=null,a=l}else for(s=0;s<i.length;s++){if(l=(o=i[s]).instance,c=o.currentTarget,o=o.listener,l!==a&&r.isPropagationStopped())break e;a=o,r.currentTarget=c;try{a(r)}catch(u){yo(u)}r.currentTarget=null,a=l}}}}function Fu(e,t){var n=t[De];void 0===n&&(n=t[De]=new Set);var i=e+"__bubble";n.has(i)||(Hu(t,e,2,!1),n.add(i))}function Iu(e,t,n){var i=0;t&&(i|=4),Hu(n,e,i,t)}var Wu="_reactListening"+Math.random().toString(36).slice(2);function Bu(e){if(!e[Wu]){e[Wu]=!0,Ye.forEach(function(t){"selectionchange"!==t&&(Ru.has(t)||Iu(t,!1,e),Iu(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Wu]||(t[Wu]=!0,Iu("selectionchange",!1,t))}}function Hu(e,t,n,i){switch(uh(t)){case 2:var r=rh;break;case 8:r=ah;break;default:r=sh}n=r.bind(null,t,n,e),r=void 0,!It||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(r=!0),i?void 0!==r?e.addEventListener(t,n,{capture:!0,passive:r}):e.addEventListener(t,n,!0):void 0!==r?e.addEventListener(t,n,{passive:r}):e.addEventListener(t,n,!1)}function Vu(e,t,n,i,r){var a=i;if(0===(1&t)&&0===(2&t)&&null!==i)e:for(;;){if(null===i)return;var s=i.tag;if(3===s||4===s){var o=i.stateNode.containerInfo;if(o===r)break;if(4===s)for(s=i.return;null!==s;){var c=s.tag;if((3===c||4===c)&&s.stateNode.containerInfo===r)return;s=s.return}for(;null!==o;){if(null===(s=Be(o)))return;if(5===(c=s.tag)||6===c||26===c||27===c){i=a=s;continue e}o=o.parentNode}}i=i.return}Rt(function(){var i=a,r=Tt(n),s=[];e:{var o=vi.get(e);if(void 0!==o){var c=Zt,u=e;switch(e){case"keypress":if(0===$t(n))break e;case"keydown":case"keyup":c=mn;break;case"focusin":u="focus",c=sn;break;case"focusout":u="blur",c=sn;break;case"beforeblur":case"afterblur":c=sn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":c=rn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":c=an;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":c=bn;break;case fi:case pi:case mi:c=on;break;case xi:c=yn;break;case"scroll":case"scrollend":c=tn;break;case"wheel":c=xn;break;case"copy":case"cut":case"paste":c=ln;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":c=gn;break;case"toggle":case"beforetoggle":c=vn}var d=0!==(4&t),h=!d&&("scroll"===e||"scrollend"===e),f=d?null!==o?o+"Capture":null:o;d=[];for(var p,m=i;null!==m;){var g=m;if(p=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===p||null===f||null!=(g=zt(m,f))&&d.push(Uu(m,g,p)),h)break;m=m.return}0<d.length&&(o=new c(o,u,null,n,r),s.push({event:o,listeners:d}))}}if(0===(7&t)){if(c="mouseout"===e||"pointerout"===e,(!(o="mouseover"===e||"pointerover"===e)||n===jt||!(u=n.relatedTarget||n.fromElement)||!Be(u)&&!u[Ae])&&(c||o)&&(o=r.window===r?r:(o=r.ownerDocument)?o.defaultView||o.parentWindow:window,c?(c=i,null!==(u=(u=n.relatedTarget||n.toElement)?Be(u):null)&&(h=l(u),d=u.tag,u!==h||5!==d&&27!==d&&6!==d)&&(u=null)):(c=null,u=i),c!==u)){if(d=rn,g="onMouseLeave",f="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(d=gn,g="onPointerLeave",f="onPointerEnter",m="pointer"),h=null==c?o:Ve(c),p=null==u?o:Ve(u),(o=new d(g,m+"leave",c,n,r)).target=h,o.relatedTarget=p,g=null,Be(r)===i&&((d=new d(f,m+"enter",u,n,r)).target=p,d.relatedTarget=h,g=d),h=g,c&&u)e:{for(f=u,m=0,p=d=c;p;p=Yu(p))m++;for(p=0,g=f;g;g=Yu(g))p++;for(;0<m-p;)d=Yu(d),m--;for(;0<p-m;)f=Yu(f),p--;for(;m--;){if(d===f||null!==f&&d===f.alternate)break e;d=Yu(d),f=Yu(f)}d=null}else d=null;null!==c&&qu(s,o,c,d,!1),null!==u&&null!==h&&qu(s,h,u,d,!0)}if("select"===(c=(o=i?Ve(i):window).nodeName&&o.nodeName.toLowerCase())||"input"===c&&"file"===o.type)var b=Fn;else if(Ln(o))if(In)b=Kn;else{b=Yn;var y=$n}else!(c=o.nodeName)||"input"!==c.toLowerCase()||"checkbox"!==o.type&&"radio"!==o.type?i&&Mt(i.elementType)&&(b=Fn):b=qn;switch(b&&(b=b(e,i))?On(s,b,n,r):(y&&y(e,o,i),"focusout"===e&&i&&"number"===o.type&&null!=i.memoizedProps.value&&yt(o,"number",o.value)),y=i?Ve(i):window,e){case"focusin":(Ln(y)||"true"===y.contentEditable)&&(ii=y,ri=i,ai=null);break;case"focusout":ai=ri=ii=null;break;case"mousedown":si=!0;break;case"contextmenu":case"mouseup":case"dragend":si=!1,oi(s,n,r);break;case"selectionchange":if(ni)break;case"keydown":case"keyup":oi(s,n,r)}var x;if(kn)e:{switch(e){case"compositionstart":var v="onCompositionStart";break e;case"compositionend":v="onCompositionEnd";break e;case"compositionupdate":v="onCompositionUpdate";break e}v=void 0}else jn?Pn(e,n)&&(v="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(v="onCompositionStart");v&&(Nn&&"ko"!==n.locale&&(jn||"onCompositionStart"!==v?"onCompositionEnd"===v&&jn&&(x=Ut()):(Ht="value"in(Bt=r)?Bt.value:Bt.textContent,jn=!0)),0<(y=$u(i,v)).length&&(v=new cn(v,e,null,n,r),s.push({event:v,listeners:y}),x?v.data=x:null!==(x=En(n))&&(v.data=x))),(x=Sn?function(e,t){switch(e){case"compositionend":return En(t);case"keypress":return 32!==t.which?null:(Cn=!0,Mn);case"textInput":return(e=t.data)===Mn&&Cn?null:e;default:return null}}(e,n):function(e,t){if(jn)return"compositionend"===e||!kn&&Pn(e,t)?(e=Ut(),Vt=Ht=Bt=null,jn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Nn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(v=$u(i,"onBeforeInput")).length&&(y=new cn("onBeforeInput","beforeinput",null,n,r),s.push({event:y,listeners:v}),y.data=x)),function(e,t,n,i,r){if("submit"===t&&n&&n.stateNode===r){var a=Tu((r[Oe]||null).action),s=i.submitter;s&&null!==(t=(t=s[Oe]||null)?Tu(t.formAction):s.getAttribute("formAction"))&&(a=t,s=null);var o=new Zt("action","action",null,i,r);e.push({event:o,listeners:[{instance:null,listener:function(){if(i.defaultPrevented){if(0!==ku){var e=s?Lu(r,s):new FormData(r);Ts(n,{pending:!0,data:e,method:r.method,action:a},null,e)}}else"function"===typeof a&&(o.preventDefault(),e=s?Lu(r,s):new FormData(r),Ts(n,{pending:!0,data:e,method:r.method,action:a},a,e))},currentTarget:r}]})}}(s,e,i,n,r)}zu(s,t)})}function Uu(e,t,n){return{instance:e,listener:t,currentTarget:n}}function $u(e,t){for(var n=t+"Capture",i=[];null!==e;){var r=e,a=r.stateNode;if(5!==(r=r.tag)&&26!==r&&27!==r||null===a||(null!=(r=zt(e,n))&&i.unshift(Uu(e,r,a)),null!=(r=zt(e,t))&&i.push(Uu(e,r,a))),3===e.tag)return i;e=e.return}return[]}function Yu(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function qu(e,t,n,i,r){for(var a=t._reactName,s=[];null!==n&&n!==i;){var o=n,l=o.alternate,c=o.stateNode;if(o=o.tag,null!==l&&l===i)break;5!==o&&26!==o&&27!==o||null===c||(l=c,r?null!=(c=zt(n,a))&&s.unshift(Uu(n,c,l)):r||null!=(c=zt(n,a))&&s.push(Uu(n,c,l))),n=n.return}0!==s.length&&e.push({event:t,listeners:s})}var Ku=/\r\n?/g,Qu=/\u0000|\uFFFD/g;function Xu(e){return("string"===typeof e?e:""+e).replace(Ku,"\n").replace(Qu,"")}function Gu(e,t){return t=Xu(t),Xu(e)===t}function Ju(){}function Zu(e,t,n,i,r,a){switch(n){case"children":"string"===typeof i?"body"===t||"textarea"===t&&""===i||kt(e,i):("number"===typeof i||"bigint"===typeof i)&&"body"!==t&&kt(e,""+i);break;case"className":nt(e,"class",i);break;case"tabIndex":nt(e,"tabindex",i);break;case"dir":case"role":case"viewBox":case"width":case"height":nt(e,n,i);break;case"style":Nt(e,i,a);break;case"data":if("object"!==t){nt(e,"data",i);break}case"src":case"href":if(""===i&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==i||"function"===typeof i||"symbol"===typeof i||"boolean"===typeof i){e.removeAttribute(n);break}i=Et(""+i),e.setAttribute(n,i);break;case"action":case"formAction":if("function"===typeof i){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"===typeof a&&("formAction"===n?("input"!==t&&Zu(e,t,"name",r.name,r,null),Zu(e,t,"formEncType",r.formEncType,r,null),Zu(e,t,"formMethod",r.formMethod,r,null),Zu(e,t,"formTarget",r.formTarget,r,null)):(Zu(e,t,"encType",r.encType,r,null),Zu(e,t,"method",r.method,r,null),Zu(e,t,"target",r.target,r,null))),null==i||"symbol"===typeof i||"boolean"===typeof i){e.removeAttribute(n);break}i=Et(""+i),e.setAttribute(n,i);break;case"onClick":null!=i&&(e.onclick=Ju);break;case"onScroll":null!=i&&Fu("scroll",e);break;case"onScrollEnd":null!=i&&Fu("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=i){if("object"!==typeof i||!("__html"in i))throw Error(s(61));if(null!=(n=i.__html)){if(null!=r.children)throw Error(s(60));e.innerHTML=n}}break;case"multiple":e.multiple=i&&"function"!==typeof i&&"symbol"!==typeof i;break;case"muted":e.muted=i&&"function"!==typeof i&&"symbol"!==typeof i;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==i||"function"===typeof i||"boolean"===typeof i||"symbol"===typeof i){e.removeAttribute("xlink:href");break}n=Et(""+i),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=i&&"function"!==typeof i&&"symbol"!==typeof i?e.setAttribute(n,""+i):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":i&&"function"!==typeof i&&"symbol"!==typeof i?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===i?e.setAttribute(n,""):!1!==i&&null!=i&&"function"!==typeof i&&"symbol"!==typeof i?e.setAttribute(n,i):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&!isNaN(i)&&1<=i?e.setAttribute(n,i):e.removeAttribute(n);break;case"rowSpan":case"start":null==i||"function"===typeof i||"symbol"===typeof i||isNaN(i)?e.removeAttribute(n):e.setAttribute(n,i);break;case"popover":Fu("beforetoggle",e),Fu("toggle",e),tt(e,"popover",i);break;case"xlinkActuate":it(e,"http://www.w3.org/1999/xlink","xlink:actuate",i);break;case"xlinkArcrole":it(e,"http://www.w3.org/1999/xlink","xlink:arcrole",i);break;case"xlinkRole":it(e,"http://www.w3.org/1999/xlink","xlink:role",i);break;case"xlinkShow":it(e,"http://www.w3.org/1999/xlink","xlink:show",i);break;case"xlinkTitle":it(e,"http://www.w3.org/1999/xlink","xlink:title",i);break;case"xlinkType":it(e,"http://www.w3.org/1999/xlink","xlink:type",i);break;case"xmlBase":it(e,"http://www.w3.org/XML/1998/namespace","xml:base",i);break;case"xmlLang":it(e,"http://www.w3.org/XML/1998/namespace","xml:lang",i);break;case"xmlSpace":it(e,"http://www.w3.org/XML/1998/namespace","xml:space",i);break;case"is":tt(e,"is",i);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&tt(e,n=Ct.get(n)||n,i)}}function ed(e,t,n,i,r,a){switch(n){case"style":Nt(e,i,a);break;case"dangerouslySetInnerHTML":if(null!=i){if("object"!==typeof i||!("__html"in i))throw Error(s(61));if(null!=(n=i.__html)){if(null!=r.children)throw Error(s(60));e.innerHTML=n}}break;case"children":"string"===typeof i?kt(e,i):("number"===typeof i||"bigint"===typeof i)&&kt(e,""+i);break;case"onScroll":null!=i&&Fu("scroll",e);break;case"onScrollEnd":null!=i&&Fu("scrollend",e);break;case"onClick":null!=i&&(e.onclick=Ju);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:qe.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(r=n.endsWith("Capture"),t=n.slice(2,r?n.length-7:void 0),"function"===typeof(a=null!=(a=e[Oe]||null)?a[n]:null)&&e.removeEventListener(t,a,r),"function"!==typeof i)?n in e?e[n]=i:!0===i?e.setAttribute(n,""):tt(e,n,i):("function"!==typeof a&&null!==a&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,i,r)))}}function td(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Fu("error",e),Fu("load",e);var i,r=!1,a=!1;for(i in n)if(n.hasOwnProperty(i)){var o=n[i];if(null!=o)switch(i){case"src":r=!0;break;case"srcSet":a=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:Zu(e,t,i,o,n,null)}}return a&&Zu(e,t,"srcSet",n.srcSet,n,null),void(r&&Zu(e,t,"src",n.src,n,null));case"input":Fu("invalid",e);var l=i=o=a=null,c=null,u=null;for(r in n)if(n.hasOwnProperty(r)){var d=n[r];if(null!=d)switch(r){case"name":a=d;break;case"type":o=d;break;case"checked":c=d;break;case"defaultChecked":u=d;break;case"value":i=d;break;case"defaultValue":l=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(s(137,t));break;default:Zu(e,t,r,d,n,null)}}return bt(e,i,l,c,u,o,a,!1),void dt(e);case"select":for(a in Fu("invalid",e),r=o=i=null,n)if(n.hasOwnProperty(a)&&null!=(l=n[a]))switch(a){case"value":i=l;break;case"defaultValue":o=l;break;case"multiple":r=l;default:Zu(e,t,a,l,n,null)}return t=i,n=o,e.multiple=!!r,void(null!=t?xt(e,!!r,t,!1):null!=n&&xt(e,!!r,n,!0));case"textarea":for(o in Fu("invalid",e),i=a=r=null,n)if(n.hasOwnProperty(o)&&null!=(l=n[o]))switch(o){case"value":r=l;break;case"defaultValue":a=l;break;case"children":i=l;break;case"dangerouslySetInnerHTML":if(null!=l)throw Error(s(91));break;default:Zu(e,t,o,l,n,null)}return wt(e,r,a,i),void dt(e);case"option":for(c in n)if(n.hasOwnProperty(c)&&null!=(r=n[c]))if("selected"===c)e.selected=r&&"function"!==typeof r&&"symbol"!==typeof r;else Zu(e,t,c,r,n,null);return;case"dialog":Fu("beforetoggle",e),Fu("toggle",e),Fu("cancel",e),Fu("close",e);break;case"iframe":case"object":Fu("load",e);break;case"video":case"audio":for(r=0;r<Du.length;r++)Fu(Du[r],e);break;case"image":Fu("error",e),Fu("load",e);break;case"details":Fu("toggle",e);break;case"embed":case"source":case"link":Fu("error",e),Fu("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(u in n)if(n.hasOwnProperty(u)&&null!=(r=n[u]))switch(u){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,t));default:Zu(e,t,u,r,n,null)}return;default:if(Mt(t)){for(d in n)n.hasOwnProperty(d)&&(void 0!==(r=n[d])&&ed(e,t,d,r,n,void 0));return}}for(l in n)n.hasOwnProperty(l)&&(null!=(r=n[l])&&Zu(e,t,l,r,n,null))}var nd=null,id=null;function rd(e){return 9===e.nodeType?e:e.ownerDocument}function ad(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function sd(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function od(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"bigint"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ld=null;var cd="function"===typeof setTimeout?setTimeout:void 0,ud="function"===typeof clearTimeout?clearTimeout:void 0,dd="function"===typeof Promise?Promise:void 0,hd="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof dd?function(e){return dd.resolve(null).then(e).catch(fd)}:cd;function fd(e){setTimeout(function(){throw e})}function pd(e){return"head"===e}function md(e,t){var n=t,i=0,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0<i&&8>i){n=i;var s=e.ownerDocument;if(1&n&&kd(s.documentElement),2&n&&kd(s.body),4&n)for(kd(n=s.head),s=n.firstChild;s;){var o=s.nextSibling,l=s.nodeName;s[Ie]||"SCRIPT"===l||"STYLE"===l||"LINK"===l&&"stylesheet"===s.rel.toLowerCase()||n.removeChild(s),s=o}}if(0===r)return e.removeChild(a),void Ph(t);r--}else"$"===n||"$?"===n||"$!"===n?r++:i=n.charCodeAt(0)-48;else i=0;n=a}while(n);Ph(t)}function gd(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":gd(n),We(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function bd(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function yd(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var xd=null;function vd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function wd(e,t,n){switch(t=rd(n),e){case"html":if(!(e=t.documentElement))throw Error(s(452));return e;case"head":if(!(e=t.head))throw Error(s(453));return e;case"body":if(!(e=t.body))throw Error(s(454));return e;default:throw Error(s(451))}}function kd(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);We(e)}var _d=new Map,Sd=new Set;function Nd(e){return"function"===typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Md=D.d;D.d={f:function(){var e=Md.f(),t=Hc();return e||t},r:function(e){var t=He(e);null!==t&&5===t.tag&&"form"===t.type?Os(t):Md.r(e)},D:function(e){Md.D(e),Pd("dns-prefetch",e,null)},C:function(e,t){Md.C(e,t),Pd("preconnect",e,t)},L:function(e,t,n){Md.L(e,t,n);var i=Cd;if(i&&e&&t){var r='link[rel="preload"][as="'+mt(t)+'"]';"image"===t&&n&&n.imageSrcSet?(r+='[imagesrcset="'+mt(n.imageSrcSet)+'"]',"string"===typeof n.imageSizes&&(r+='[imagesizes="'+mt(n.imageSizes)+'"]')):r+='[href="'+mt(e)+'"]';var a=r;switch(t){case"style":a=jd(e);break;case"script":a=Od(e)}_d.has(a)||(e=h({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),_d.set(a,e),null!==i.querySelector(r)||"style"===t&&i.querySelector(Td(a))||"script"===t&&i.querySelector(Ad(a))||(td(t=i.createElement("link"),"link",e),$e(t),i.head.appendChild(t)))}},m:function(e,t){Md.m(e,t);var n=Cd;if(n&&e){var i=t&&"string"===typeof t.as?t.as:"script",r='link[rel="modulepreload"][as="'+mt(i)+'"][href="'+mt(e)+'"]',a=r;switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":a=Od(e)}if(!_d.has(a)&&(e=h({rel:"modulepreload",href:e},t),_d.set(a,e),null===n.querySelector(r))){switch(i){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Ad(a)))return}td(i=n.createElement("link"),"link",e),$e(i),n.head.appendChild(i)}}},X:function(e,t){Md.X(e,t);var n=Cd;if(n&&e){var i=Ue(n).hoistableScripts,r=Od(e),a=i.get(r);a||((a=n.querySelector(Ad(r)))||(e=h({src:e,async:!0},t),(t=_d.get(r))&&Fd(e,t),$e(a=n.createElement("script")),td(a,"link",e),n.head.appendChild(a)),a={type:"script",instance:a,count:1,state:null},i.set(r,a))}},S:function(e,t,n){Md.S(e,t,n);var i=Cd;if(i&&e){var r=Ue(i).hoistableStyles,a=jd(e);t=t||"default";var s=r.get(a);if(!s){var o={loading:0,preload:null};if(s=i.querySelector(Td(a)))o.loading=5;else{e=h({rel:"stylesheet",href:e,"data-precedence":t},n),(n=_d.get(a))&&zd(e,n);var l=s=i.createElement("link");$e(l),td(l,"link",e),l._p=new Promise(function(e,t){l.onload=e,l.onerror=t}),l.addEventListener("load",function(){o.loading|=1}),l.addEventListener("error",function(){o.loading|=2}),o.loading|=4,Rd(s,t,i)}s={type:"stylesheet",instance:s,count:1,state:o},r.set(a,s)}}},M:function(e,t){Md.M(e,t);var n=Cd;if(n&&e){var i=Ue(n).hoistableScripts,r=Od(e),a=i.get(r);a||((a=n.querySelector(Ad(r)))||(e=h({src:e,async:!0,type:"module"},t),(t=_d.get(r))&&Fd(e,t),$e(a=n.createElement("script")),td(a,"link",e),n.head.appendChild(a)),a={type:"script",instance:a,count:1,state:null},i.set(r,a))}}};var Cd="undefined"===typeof document?null:document;function Pd(e,t,n){var i=Cd;if(i&&"string"===typeof t&&t){var r=mt(t);r='link[rel="'+e+'"][href="'+r+'"]',"string"===typeof n&&(r+='[crossorigin="'+n+'"]'),Sd.has(r)||(Sd.add(r),e={rel:e,crossOrigin:n,href:t},null===i.querySelector(r)&&(td(t=i.createElement("link"),"link",e),$e(t),i.head.appendChild(t)))}}function Ed(e,t,n,i){var r,a,o,l,c=(c=U.current)?Nd(c):null;if(!c)throw Error(s(446));switch(e){case"meta":case"title":return null;case"style":return"string"===typeof n.precedence&&"string"===typeof n.href?(t=jd(n.href),(i=(n=Ue(c).hoistableStyles).get(t))||(i={type:"style",instance:null,count:0,state:null},n.set(t,i)),i):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"===typeof n.href&&"string"===typeof n.precedence){e=jd(n.href);var u=Ue(c).hoistableStyles,d=u.get(e);if(d||(c=c.ownerDocument||c,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,d),(u=c.querySelector(Td(e)))&&!u._p&&(d.instance=u,d.state.loading=5),_d.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},_d.set(e,n),u||(r=c,a=e,o=n,l=d.state,r.querySelector('link[rel="preload"][as="style"]['+a+"]")?l.loading=1:(a=r.createElement("link"),l.preload=a,a.addEventListener("load",function(){return l.loading|=1}),a.addEventListener("error",function(){return l.loading|=2}),td(a,"link",o),$e(a),r.head.appendChild(a))))),t&&null===i)throw Error(s(528,""));return d}if(t&&null!==i)throw Error(s(529,""));return null;case"script":return t=n.async,"string"===typeof(n=n.src)&&t&&"function"!==typeof t&&"symbol"!==typeof t?(t=Od(n),(i=(n=Ue(c).hoistableScripts).get(t))||(i={type:"script",instance:null,count:0,state:null},n.set(t,i)),i):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,e))}}function jd(e){return'href="'+mt(e)+'"'}function Td(e){return'link[rel="stylesheet"]['+e+"]"}function Ld(e){return h({},e,{"data-precedence":e.precedence,precedence:null})}function Od(e){return'[src="'+mt(e)+'"]'}function Ad(e){return"script[async]"+e}function Dd(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var i=e.querySelector('style[data-href~="'+mt(n.href)+'"]');if(i)return t.instance=i,$e(i),i;var r=h({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return $e(i=(e.ownerDocument||e).createElement("style")),td(i,"style",r),Rd(i,n.precedence,e),t.instance=i;case"stylesheet":r=jd(n.href);var a=e.querySelector(Td(r));if(a)return t.state.loading|=4,t.instance=a,$e(a),a;i=Ld(n),(r=_d.get(r))&&zd(i,r),$e(a=(e.ownerDocument||e).createElement("link"));var o=a;return o._p=new Promise(function(e,t){o.onload=e,o.onerror=t}),td(a,"link",i),t.state.loading|=4,Rd(a,n.precedence,e),t.instance=a;case"script":return a=Od(n.src),(r=e.querySelector(Ad(a)))?(t.instance=r,$e(r),r):(i=n,(r=_d.get(a))&&Fd(i=h({},n),r),$e(r=(e=e.ownerDocument||e).createElement("script")),td(r,"link",i),e.head.appendChild(r),t.instance=r);case"void":return null;default:throw Error(s(443,t.type))}else"stylesheet"===t.type&&0===(4&t.state.loading)&&(i=t.instance,t.state.loading|=4,Rd(i,n.precedence,e));return t.instance}function Rd(e,t,n){for(var i=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),r=i.length?i[i.length-1]:null,a=r,s=0;s<i.length;s++){var o=i[s];if(o.dataset.precedence===t)a=o;else if(a!==r)break}a?a.parentNode.insertBefore(e,a.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function zd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function Fd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var Id=null;function Wd(e,t,n){if(null===Id){var i=new Map,r=Id=new Map;r.set(n,i)}else(i=(r=Id).get(n))||(i=new Map,r.set(n,i));if(i.has(e))return i;for(i.set(e,null),n=n.getElementsByTagName(e),r=0;r<n.length;r++){var a=n[r];if(!(a[Ie]||a[Le]||"link"===e&&"stylesheet"===a.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==a.namespaceURI){var s=a.getAttribute(t)||"";s=e+s;var o=i.get(s);o?o.push(a):i.set(s,[a])}}return i}function Bd(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function Hd(e){return"stylesheet"!==e.type||0!==(3&e.state.loading)}var Vd=null;function Ud(){}function $d(){if(this.count--,0===this.count)if(this.stylesheets)qd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var Yd=null;function qd(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,Yd=new Map,t.forEach(Kd,e),Yd=null,$d.call(e))}function Kd(e,t){if(!(4&t.state.loading)){var n=Yd.get(e);if(n)var i=n.get(null);else{n=new Map,Yd.set(e,n);for(var r=e.querySelectorAll("link[data-precedence],style[data-precedence]"),a=0;a<r.length;a++){var s=r[a];"LINK"!==s.nodeName&&"not all"===s.getAttribute("media")||(n.set(s.dataset.precedence,s),i=s)}i&&n.set(null,i)}s=(r=t.instance).getAttribute("data-precedence"),(a=n.get(s)||i)===i&&n.set(null,r),n.set(s,r),this.count++,i=$d.bind(this),r.addEventListener("load",i),r.addEventListener("error",i),a?a.parentNode.insertBefore(r,a.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(r,e.firstChild),t.state.loading|=4}}var Qd={$$typeof:w,Provider:null,Consumer:null,_currentValue:R,_currentValue2:R,_threadCount:0};function Xd(e,t,n,i,r,a,s,o){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Se(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Se(0),this.hiddenUpdates=Se(null),this.identifierPrefix=i,this.onUncaughtError=r,this.onCaughtError=a,this.onRecoverableError=s,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=o,this.incompleteTransitions=new Map}function Gd(e,t,n,i,r,a,s,o,l,c,u,d){return e=new Xd(e,t,n,s,o,l,c,d),t=1,!0===a&&(t|=24),a=Ri(3,null,null,t),e.current=a,a.stateNode=e,(t=Or()).refCount++,e.pooledCache=t,t.refCount++,a.memoizedState={element:i,isDehydrated:n,cache:t},na(a),e}function Jd(e){return e?e=Ai:Ai}function Zd(e,t,n,i,r,a){r=Jd(r),null===i.context?i.context=r:i.pendingContext=r,(i=ra(t)).payload={element:n},null!==(a=void 0===a?null:a)&&(i.callback=a),null!==(n=aa(e,i,t))&&(zc(n,0,t),sa(n,e,t))}function eh(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function th(e,t){eh(e,t),(e=e.alternate)&&eh(e,t)}function nh(e){if(13===e.tag){var t=Ti(e,67108864);null!==t&&zc(t,0,67108864),th(e,67108864)}}var ih=!0;function rh(e,t,n,i){var r=A.T;A.T=null;var a=D.p;try{D.p=2,sh(e,t,n,i)}finally{D.p=a,A.T=r}}function ah(e,t,n,i){var r=A.T;A.T=null;var a=D.p;try{D.p=8,sh(e,t,n,i)}finally{D.p=a,A.T=r}}function sh(e,t,n,i){if(ih){var r=oh(i);if(null===r)Vu(e,t,i,lh,n),xh(e,i);else if(function(e,t,n,i,r){switch(t){case"focusin":return hh=vh(hh,e,t,n,i,r),!0;case"dragenter":return fh=vh(fh,e,t,n,i,r),!0;case"mouseover":return ph=vh(ph,e,t,n,i,r),!0;case"pointerover":var a=r.pointerId;return mh.set(a,vh(mh.get(a)||null,e,t,n,i,r)),!0;case"gotpointercapture":return a=r.pointerId,gh.set(a,vh(gh.get(a)||null,e,t,n,i,r)),!0}return!1}(r,e,t,n,i))i.stopPropagation();else if(xh(e,i),4&t&&-1<yh.indexOf(e)){for(;null!==r;){var a=He(r);if(null!==a)switch(a.tag){case 3:if((a=a.stateNode).current.memoizedState.isDehydrated){var s=ye(a.pendingLanes);if(0!==s){var o=a;for(o.pendingLanes|=2,o.entangledLanes|=2;s;){var l=1<<31-fe(s);o.entanglements[1]|=l,s&=~l}_u(a),0===(6&ic)&&(_c=te()+500,Su(0,!1))}}break;case 13:null!==(o=Ti(a,2))&&zc(o,0,2),Hc(),th(a,2)}if(null===(a=oh(i))&&Vu(e,t,i,lh,n),a===r)break;r=a}null!==r&&i.stopPropagation()}else Vu(e,t,i,null,n)}}function oh(e){return ch(e=Tt(e))}var lh=null;function ch(e){if(lh=null,null!==(e=Be(e))){var t=l(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=c(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return lh=e,null}function uh(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ne()){case ie:return 2;case re:return 8;case ae:case se:return 32;case oe:return 268435456;default:return 32}default:return 32}}var dh=!1,hh=null,fh=null,ph=null,mh=new Map,gh=new Map,bh=[],yh="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function xh(e,t){switch(e){case"focusin":case"focusout":hh=null;break;case"dragenter":case"dragleave":fh=null;break;case"mouseover":case"mouseout":ph=null;break;case"pointerover":case"pointerout":mh.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":gh.delete(t.pointerId)}}function vh(e,t,n,i,r,a){return null===e||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:i,nativeEvent:a,targetContainers:[r]},null!==t&&(null!==(t=He(t))&&nh(t)),e):(e.eventSystemFlags|=i,t=e.targetContainers,null!==r&&-1===t.indexOf(r)&&t.push(r),e)}function wh(e){var t=Be(e.target);if(null!==t){var n=l(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=c(n)))return e.blockedOn=t,void function(e,t){var n=D.p;try{return D.p=e,t()}finally{D.p=n}}(e.priority,function(){if(13===n.tag){var e=Dc();e=Pe(e);var t=Ti(n,e);null!==t&&zc(t,0,e),th(n,e)}})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function kh(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=oh(e.nativeEvent);if(null!==n)return null!==(t=He(n))&&nh(t),e.blockedOn=n,!1;var i=new(n=e.nativeEvent).constructor(n.type,n);jt=i,n.target.dispatchEvent(i),jt=null,t.shift()}return!0}function _h(e,t,n){kh(e)&&n.delete(t)}function Sh(){dh=!1,null!==hh&&kh(hh)&&(hh=null),null!==fh&&kh(fh)&&(fh=null),null!==ph&&kh(ph)&&(ph=null),mh.forEach(_h),gh.forEach(_h)}function Nh(e,t){e.blockedOn===t&&(e.blockedOn=null,dh||(dh=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,Sh)))}var Mh=null;function Ch(e){Mh!==e&&(Mh=e,i.unstable_scheduleCallback(i.unstable_NormalPriority,function(){Mh===e&&(Mh=null);for(var t=0;t<e.length;t+=3){var n=e[t],i=e[t+1],r=e[t+2];if("function"!==typeof i){if(null===ch(i||n))continue;break}var a=He(n);null!==a&&(e.splice(t,3),t-=3,Ts(a,{pending:!0,data:r,method:n.method,action:i},i,r))}}))}function Ph(e){function t(t){return Nh(t,e)}null!==hh&&Nh(hh,e),null!==fh&&Nh(fh,e),null!==ph&&Nh(ph,e),mh.forEach(t),gh.forEach(t);for(var n=0;n<bh.length;n++){var i=bh[n];i.blockedOn===e&&(i.blockedOn=null)}for(;0<bh.length&&null===(n=bh[0]).blockedOn;)wh(n),null===n.blockedOn&&bh.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(i=0;i<n.length;i+=3){var r=n[i],a=n[i+1],s=r[Oe]||null;if("function"===typeof a)s||Ch(n);else if(s){var o=null;if(a&&a.hasAttribute("formAction")){if(r=a,s=a[Oe]||null)o=s.formAction;else if(null!==ch(r))continue}else o=s.action;"function"===typeof o?n[i+1]=o:(n.splice(i,3),i-=3),Ch(n)}}}function Eh(e){this._internalRoot=e}function jh(e){this._internalRoot=e}jh.prototype.render=Eh.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(s(409));Zd(t.current,Dc(),e,t,null,null)},jh.prototype.unmount=Eh.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;Zd(e.current,2,null,e,null,null),Hc(),t[Ae]=null}},jh.prototype.unstable_scheduleHydration=function(e){if(e){var t=je();e={blockedOn:null,target:e,priority:t};for(var n=0;n<bh.length&&0!==t&&t<bh[n].priority;n++);bh.splice(n,0,e),0===n&&wh(e)}};var Th=r.version;if("19.1.1"!==Th)throw Error(s(527,Th,"19.1.1"));D.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(s(188));throw e=Object.keys(e).join(","),Error(s(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=l(e)))throw Error(s(188));return t!==e?null:e}for(var n=e,i=t;;){var r=n.return;if(null===r)break;var a=r.alternate;if(null===a){if(null!==(i=r.return)){n=i;continue}break}if(r.child===a.child){for(a=r.child;a;){if(a===n)return u(r),e;if(a===i)return u(r),t;a=a.sibling}throw Error(s(188))}if(n.return!==i.return)n=r,i=a;else{for(var o=!1,c=r.child;c;){if(c===n){o=!0,n=r,i=a;break}if(c===i){o=!0,i=r,n=a;break}c=c.sibling}if(!o){for(c=a.child;c;){if(c===n){o=!0,n=a,i=r;break}if(c===i){o=!0,i=a,n=r;break}c=c.sibling}if(!o)throw Error(s(189))}}if(n.alternate!==i)throw Error(s(190))}if(3!==n.tag)throw Error(s(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?d(e):null)?null:e.stateNode};var Lh={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:A,reconcilerVersion:"19.1.1"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Oh=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Oh.isDisabled&&Oh.supportsFiber)try{ue=Oh.inject(Lh),de=Oh}catch(Dh){}}t.createRoot=function(e,t){if(!o(e))throw Error(s(299));var n=!1,i="",r=xo,a=vo,l=wo;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(i=t.identifierPrefix),void 0!==t.onUncaughtError&&(r=t.onUncaughtError),void 0!==t.onCaughtError&&(a=t.onCaughtError),void 0!==t.onRecoverableError&&(l=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=Gd(e,1,!1,null,0,n,i,r,a,l,0,null),e[Ae]=t.current,Bu(e),new Eh(t)},t.hydrateRoot=function(e,t,n){if(!o(e))throw Error(s(299));var i=!1,r="",a=xo,l=vo,c=wo,u=null;return null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(i=!0),void 0!==n.identifierPrefix&&(r=n.identifierPrefix),void 0!==n.onUncaughtError&&(a=n.onUncaughtError),void 0!==n.onCaughtError&&(l=n.onCaughtError),void 0!==n.onRecoverableError&&(c=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(u=n.formState)),(t=Gd(e,1,!0,t,0,i,r,a,l,c,0,u)).context=Jd(null),n=t.current,(r=ra(i=Pe(i=Dc()))).callback=null,aa(n,r,i),n=i,t.current.lanes=n,Ne(t,n),_u(t),e[Ae]=t.current,Bu(e),new jh(t)},t.version="19.1.1"},643:(e,t,n)=>{e.exports=n(152)},752:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var i=n-1>>>1,r=e[i];if(!(0<a(r,t)))break e;e[i]=t,e[n]=r,n=i}}function i(e){return 0===e.length?null:e[0]}function r(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var i=0,r=e.length,s=r>>>1;i<s;){var o=2*(i+1)-1,l=e[o],c=o+1,u=e[c];if(0>a(l,n))c<r&&0>a(u,l)?(e[i]=u,e[c]=n,i=c):(e[i]=l,e[o]=n,i=o);else{if(!(c<r&&0>a(u,n)))break e;e[i]=u,e[c]=n,i=c}}}return t}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"===typeof performance&&"function"===typeof performance.now){var s=performance;t.unstable_now=function(){return s.now()}}else{var o=Date,l=o.now();t.unstable_now=function(){return o.now()-l}}var c=[],u=[],d=1,h=null,f=3,p=!1,m=!1,g=!1,b=!1,y="function"===typeof setTimeout?setTimeout:null,x="function"===typeof clearTimeout?clearTimeout:null,v="undefined"!==typeof setImmediate?setImmediate:null;function w(e){for(var t=i(u);null!==t;){if(null===t.callback)r(u);else{if(!(t.startTime<=e))break;r(u),t.sortIndex=t.expirationTime,n(c,t)}t=i(u)}}function k(e){if(g=!1,w(e),!m)if(null!==i(c))m=!0,S||(S=!0,_());else{var t=i(u);null!==t&&L(k,t.startTime-e)}}var _,S=!1,N=-1,M=5,C=-1;function P(){return!!b||!(t.unstable_now()-C<M)}function E(){if(b=!1,S){var e=t.unstable_now();C=e;var n=!0;try{e:{m=!1,g&&(g=!1,x(N),N=-1),p=!0;var a=f;try{t:{for(w(e),h=i(c);null!==h&&!(h.expirationTime>e&&P());){var s=h.callback;if("function"===typeof s){h.callback=null,f=h.priorityLevel;var o=s(h.expirationTime<=e);if(e=t.unstable_now(),"function"===typeof o){h.callback=o,w(e),n=!0;break t}h===i(c)&&r(c),w(e)}else r(c);h=i(c)}if(null!==h)n=!0;else{var l=i(u);null!==l&&L(k,l.startTime-e),n=!1}}break e}finally{h=null,f=a,p=!1}n=void 0}}finally{n?_():S=!1}}}if("function"===typeof v)_=function(){v(E)};else if("undefined"!==typeof MessageChannel){var j=new MessageChannel,T=j.port2;j.port1.onmessage=E,_=function(){T.postMessage(null)}}else _=function(){y(E,0)};function L(e,n){N=y(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):M=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return f},t.unstable_next=function(e){switch(f){case 1:case 2:case 3:var t=3;break;default:t=f}var n=f;f=t;try{return e()}finally{f=n}},t.unstable_requestPaint=function(){b=!0},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=f;f=e;try{return t()}finally{f=n}},t.unstable_scheduleCallback=function(e,r,a){var s=t.unstable_now();switch("object"===typeof a&&null!==a?a="number"===typeof(a=a.delay)&&0<a?s+a:s:a=s,e){case 1:var o=-1;break;case 2:o=250;break;case 5:o=1073741823;break;case 4:o=1e4;break;default:o=5e3}return e={id:d++,callback:r,priorityLevel:e,startTime:a,expirationTime:o=a+o,sortIndex:-1},a>s?(e.sortIndex=a,n(u,e),null===i(c)&&e===i(u)&&(g?(x(N),N=-1):g=!0,L(k,a-s))):(e.sortIndex=o,n(c,e),m||p||(m=!0,S||(S=!0,_()))),e},t.unstable_shouldYield=P,t.unstable_wrapCallback=function(e){var t=f;return function(){var n=f;f=t;try{return e.apply(this,arguments)}finally{f=n}}}},766:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(432)},935:(e,t)=>{var n=Symbol.for("react.transitional.element");function i(e,t,i){var r=null;if(void 0!==i&&(r=""+i),void 0!==t.key&&(r=""+t.key),"key"in t)for(var a in i={},t)"key"!==a&&(i[a]=t[a]);else i=t;return t=i.ref,{$$typeof:n,type:e,key:r,ref:void 0!==t?t:null,props:i}}Symbol.for("react.fragment"),t.jsx=i,t.jsxs=i}},t={};function n(i){var r=t[i];if(void 0!==r)return r.exports;var a=t[i]={exports:{}};return e[i](a,a.exports,n),a.exports}n.m=e,n.d=(e,t)=>{for(var i in t)n.o(t,i)&&!n.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce((t,i)=>(n.f[i](e,t),t),[])),n.u=e=>"static/js/"+e+".bb3c95e5.chunk.js",n.miniCssF=e=>{},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="frontend:";n.l=(i,r,a,s)=>{if(e[i])e[i].push(r);else{var o,l;if(void 0!==a)for(var c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==i||d.getAttribute("data-webpack")==t+a){o=d;break}}o||(l=!0,(o=document.createElement("script")).charset="utf-8",o.timeout=120,n.nc&&o.setAttribute("nonce",n.nc),o.setAttribute("data-webpack",t+a),o.src=i),e[i]=[r];var h=(t,n)=>{o.onerror=o.onload=null,clearTimeout(f);var r=e[i];if(delete e[i],o.parentNode&&o.parentNode.removeChild(o),r&&r.forEach(e=>e(n)),t)return t(n)},f=setTimeout(h.bind(null,void 0,{type:"timeout",target:o}),12e4);o.onerror=h.bind(null,o.onerror),o.onload=h.bind(null,o.onload),l&&document.head.appendChild(o)}}})(),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="/",(()=>{var e={792:0};n.f.j=(t,i)=>{var r=n.o(e,t)?e[t]:void 0;if(0!==r)if(r)i.push(r[2]);else{var a=new Promise((n,i)=>r=e[t]=[n,i]);i.push(r[2]=a);var s=n.p+n.u(t),o=new Error;n.l(s,i=>{if(n.o(e,t)&&(0!==(r=e[t])&&(e[t]=void 0),r)){var a=i&&("load"===i.type?"missing":i.type),s=i&&i.target&&i.target.src;o.message="Loading chunk "+t+" failed.\n("+a+": "+s+")",o.name="ChunkLoadError",o.type=a,o.request=s,r[1](o)}},"chunk-"+t,t)}};var t=(t,i)=>{var r,a,s=i[0],o=i[1],l=i[2],c=0;if(s.some(t=>0!==e[t])){for(r in o)n.o(o,r)&&(n.m[r]=o[r]);if(l)l(n)}for(t&&t(i);c<s.length;c++)a=s[c],n.o(e,a)&&e[a]&&e[a][0](),e[a]=0},i=self.webpackChunkfrontend=self.webpackChunkfrontend||[];i.forEach(t.bind(null,0)),i.push=t.bind(null,i.push.bind(i))})();var i=n(643),r=n(367);function a(e,t){if(null==e)return{};var n,i,r=function(e,t){if(null==e)return{};var n={};for(var i in e)if({}.hasOwnProperty.call(e,i)){if(-1!==t.indexOf(i))continue;n[i]=e[i]}return n}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(i=0;i<a.length;i++)n=a[i],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(r[n]=e[n])}return r}function s(e){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},s(e)}function o(e){var t=function(e,t){if("object"!=s(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var i=n.call(e,t||"default");if("object"!=s(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==s(t)?t:t+""}function l(e,t,n){return(t=o(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,i)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach(function(t){l(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}const d=["sri"],h=["page"],f=["page","matches"],p=["onClick","discover","prefetch","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],m=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],g=["discover","fetcherKey","navigate","reloadDocument","replace","state","method","action","onSubmit","relative","preventScrollReset","viewTransition"];var b="popstate";function y(){return N(function(e,t){let{pathname:n,search:i,hash:r}=e.location;return k("",{pathname:n,search:i,hash:r},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"===typeof t?t:_(t)},null,arguments.length>0&&void 0!==arguments[0]?arguments[0]:{})}function x(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function v(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function w(e,t){return{usr:e.state,key:e.key,idx:t}}function k(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=arguments.length>3?arguments[3]:void 0;return u(u({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?S(t):t),{},{state:n,key:t&&t.key||i||Math.random().toString(36).substring(2,10)})}function _(e){let{pathname:t="/",search:n="",hash:i=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),i&&"#"!==i&&(t+="#"===i.charAt(0)?i:"#"+i),t}function S(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let i=e.indexOf("?");i>=0&&(t.search=e.substring(i),e=e.substring(0,i)),e&&(t.pathname=e)}return t}function N(e,t,n){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},{window:r=document.defaultView,v5Compat:a=!1}=i,s=r.history,o="POP",l=null,c=d();function d(){return(s.state||{idx:null}).idx}function h(){o="POP";let e=d(),t=null==e?null:e-c;c=e,l&&l({action:o,location:p.location,delta:t})}function f(e){return M(e)}null==c&&(c=0,s.replaceState(u(u({},s.state),{},{idx:c}),""));let p={get action(){return o},get location(){return e(r,s)},listen(e){if(l)throw new Error("A history only accepts one active listener");return r.addEventListener(b,h),l=e,()=>{r.removeEventListener(b,h),l=null}},createHref:e=>t(r,e),createURL:f,encodeLocation(e){let t=f(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){o="PUSH";let i=k(p.location,e,t);n&&n(i,e),c=d()+1;let u=w(i,c),h=p.createHref(i);try{s.pushState(u,"",h)}catch(f){if(f instanceof DOMException&&"DataCloneError"===f.name)throw f;r.location.assign(h)}a&&l&&l({action:o,location:p.location,delta:1})},replace:function(e,t){o="REPLACE";let i=k(p.location,e,t);n&&n(i,e),c=d();let r=w(i,c),u=p.createHref(i);s.replaceState(r,"",u),a&&l&&l({action:o,location:p.location,delta:0})},go:e=>s.go(e)};return p}function M(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n="http://localhost";"undefined"!==typeof window&&(n="null"!==window.location.origin?window.location.origin:window.location.href),x(n,"No window.location.(origin|href) available to create URL");let i="string"===typeof e?e:_(e);return i=i.replace(/ $/,"%20"),!t&&i.startsWith("//")&&(i=n+i),new URL(i,n)}new WeakMap;function C(e,t){return P(e,t,arguments.length>2&&void 0!==arguments[2]?arguments[2]:"/",!1)}function P(e,t,n,i){let r=V(("string"===typeof t?S(t):t).pathname||"/",n);if(null==r)return null;let a=E(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(a);let s=null;for(let o=0;null==s&&o<a.length;++o){let e=H(r);s=I(a[o],e,i)}return s}function E(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",r=arguments.length>4&&void 0!==arguments[4]&&arguments[4],a=function(e,a){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:r,o=arguments.length>3?arguments[3]:void 0,l={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};if(l.relativePath.startsWith("/")){if(!l.relativePath.startsWith(i)&&s)return;x(l.relativePath.startsWith(i),'Absolute route path "'.concat(l.relativePath,'" nested under path "').concat(i,'" is not valid. An absolute child route path must start with the combined path of all its parent routes.')),l.relativePath=l.relativePath.slice(i.length)}let c=K([i,l.relativePath]),u=n.concat(l);e.children&&e.children.length>0&&(x(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'.concat(c,'".')),E(e.children,t,u,c,s)),(null!=e.path||e.index)&&t.push({path:c,score:F(c,e.index),routesMeta:u})};return e.forEach((e,t)=>{var n;if(""!==e.path&&null!==(n=e.path)&&void 0!==n&&n.includes("?"))for(let i of j(e.path))a(e,t,!0,i);else a(e,t)}),t}function j(e){let t=e.split("/");if(0===t.length)return[];let[n,...i]=t,r=n.endsWith("?"),a=n.replace(/\?$/,"");if(0===i.length)return r?[a,""]:[a];let s=j(i.join("/")),o=[];return o.push(...s.map(e=>""===e?a:[a,e].join("/"))),r&&o.push(...s),o.map(t=>e.startsWith("/")&&""===t?"/":t)}var T=/^:[\w-]+$/,L=3,O=2,A=1,D=10,R=-2,z=e=>"*"===e;function F(e,t){let n=e.split("/"),i=n.length;return n.some(z)&&(i+=R),t&&(i+=O),n.filter(e=>!z(e)).reduce((e,t)=>e+(T.test(t)?L:""===t?A:D),i)}function I(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],{routesMeta:i}=e,r={},a="/",s=[];for(let o=0;o<i.length;++o){let e=i[o],l=o===i.length-1,c="/"===a?t:t.slice(a.length)||"/",u=W({path:e.relativePath,caseSensitive:e.caseSensitive,end:l},c),d=e.route;if(!u&&l&&n&&!i[i.length-1].route.index&&(u=W({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},c)),!u)return null;Object.assign(r,u.params),s.push({params:r,pathname:K([a,u.pathname]),pathnameBase:Q(K([a,u.pathnameBase])),route:d}),"/"!==u.pathnameBase&&(a=K([a,u.pathnameBase]))}return s}function W(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,i]=B(e.path,e.caseSensitive,e.end),r=t.match(n);if(!r)return null;let a=r[0],s=a.replace(/(.)\/+$/,"$1"),o=r.slice(1),l=i.reduce((e,t,n)=>{let{paramName:i,isOptional:r}=t;if("*"===i){let e=o[n]||"";s=a.slice(0,a.length-e.length).replace(/(.)\/+$/,"$1")}const l=o[n];return e[i]=r&&!l?void 0:(l||"").replace(/%2F/g,"/"),e},{});return{params:l,pathname:a,pathnameBase:s,pattern:e}}function B(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];v("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'.concat(e,'" will be treated as if it were "').concat(e.replace(/\*$/,"/*"),'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "').concat(e.replace(/\*$/,"/*"),'".'));let i=[],r="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(i.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")).replace(/\/([\w-]+)\?(\/|$)/g,"(/$1)?$2");return e.endsWith("*")?(i.push({paramName:"*"}),r+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?r+="\\/*$":""!==e&&"/"!==e&&(r+="(?:(?=\\/|$))"),[new RegExp(r,t?void 0:"i"),i]}function H(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return v(!1,'The URL path "'.concat(e,'" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (').concat(t,").")),e}}function V(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,i=e.charAt(n);return i&&"/"!==i?null:e.slice(n)||"/"}function U(e,t,n,i){return"Cannot include a '".concat(e,"' character in a manually specified `to.").concat(t,"` field [").concat(JSON.stringify(i),"].  Please separate it out to the `to.").concat(n,'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.')}function $(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}function Y(e){let t=$(e);return t.map((e,n)=>n===t.length-1?e.pathname:e.pathnameBase)}function q(e,t,n){let i,r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];"string"===typeof e?i=S(e):(i=u({},e),x(!i.pathname||!i.pathname.includes("?"),U("?","pathname","search",i)),x(!i.pathname||!i.pathname.includes("#"),U("#","pathname","hash",i)),x(!i.search||!i.search.includes("#"),U("#","search","hash",i)));let a,s=""===e||""===i.pathname,o=s?"/":i.pathname;if(null==o)a=n;else{let e=t.length-1;if(!r&&o.startsWith("..")){let t=o.split("/");for(;".."===t[0];)t.shift(),e-=1;i.pathname=t.join("/")}a=e>=0?t[e]:"/"}let l=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/",{pathname:n,search:i="",hash:r=""}="string"===typeof e?S(e):e,a=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:a,search:X(i),hash:G(r)}}(i,a),c=o&&"/"!==o&&o.endsWith("/"),d=(s||"."===o)&&n.endsWith("/");return l.pathname.endsWith("/")||!c&&!d||(l.pathname+="/"),l}var K=e=>e.join("/").replace(/\/\/+/g,"/"),Q=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),X=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",G=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";function J(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}var Z=["POST","PUT","PATCH","DELETE"],ee=(new Set(Z),["GET",...Z]);new Set(ee),Symbol("ResetLoaderData");var te=i.createContext(null);te.displayName="DataRouter";var ne=i.createContext(null);ne.displayName="DataRouterState";var ie=i.createContext(!1);var re=i.createContext({isTransitioning:!1});re.displayName="ViewTransition";var ae=i.createContext(new Map);ae.displayName="Fetchers";var se=i.createContext(null);se.displayName="Await";var oe=i.createContext(null);oe.displayName="Navigation";var le=i.createContext(null);le.displayName="Location";var ce=i.createContext({outlet:null,matches:[],isDataRoute:!1});ce.displayName="Route";var ue=i.createContext(null);ue.displayName="RouteError";function de(){return null!=i.useContext(le)}function he(){return x(de(),"useLocation() may be used only in the context of a <Router> component."),i.useContext(le).location}var fe="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function pe(e){i.useContext(oe).static||i.useLayoutEffect(e)}function me(){let{isDataRoute:e}=i.useContext(ce);return e?function(){let{router:e}=Se("useNavigate"),t=Me("useNavigate"),n=i.useRef(!1);pe(()=>{n.current=!0});let r=i.useCallback(async function(i){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};v(n.current,fe),n.current&&("number"===typeof i?e.navigate(i):await e.navigate(i,u({fromRouteId:t},r)))},[e,t]);return r}():function(){x(de(),"useNavigate() may be used only in the context of a <Router> component.");let e=i.useContext(te),{basename:t,navigator:n}=i.useContext(oe),{matches:r}=i.useContext(ce),{pathname:a}=he(),s=JSON.stringify(Y(r)),o=i.useRef(!1);pe(()=>{o.current=!0});let l=i.useCallback(function(i){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(v(o.current,fe),!o.current)return;if("number"===typeof i)return void n.go(i);let l=q(i,JSON.parse(s),a,"path"===r.relative);null==e&&"/"!==t&&(l.pathname="/"===l.pathname?t:K([t,l.pathname])),(r.replace?n.replace:n.push)(l,r.state,r)},[t,n,s,a,e]);return l}()}i.createContext(null);function ge(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{matches:n}=i.useContext(ce),{pathname:r}=he(),a=JSON.stringify(Y(n));return i.useMemo(()=>q(e,JSON.parse(a),r,"path"===t),[e,a,r,t])}function be(e,t,n,r,a){x(de(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:s}=i.useContext(oe),{matches:o}=i.useContext(ce),l=o[o.length-1],c=l?l.params:{},d=l?l.pathname:"/",h=l?l.pathnameBase:"/",f=l&&l.route;{let e=f&&f.path||"";Ee(d,!f||e.endsWith("*")||e.endsWith("*?"),'You rendered descendant <Routes> (or called `useRoutes()`) at "'.concat(d,'" (under <Route path="').concat(e,'">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won\'t match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="').concat(e,'"> to <Route path="').concat("/"===e?"*":"".concat(e,"/*"),'">.'))}let p,m=he();if(t){var g;let e="string"===typeof t?S(t):t;x("/"===h||(null===(g=e.pathname)||void 0===g?void 0:g.startsWith(h)),'When overriding the location using `<Routes location>` or `useRoutes(routes, location)`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "'.concat(h,'" but pathname "').concat(e.pathname,'" was given in the `location` prop.')),p=e}else p=m;let b=p.pathname||"/",y=b;if("/"!==h){let e=h.replace(/^\//,"").split("/");y="/"+b.replace(/^\//,"").split("/").slice(e.length).join("/")}let w=C(e,{pathname:y});v(f||null!=w,'No routes matched location "'.concat(p.pathname).concat(p.search).concat(p.hash,'" ')),v(null==w||void 0!==w[w.length-1].route.element||void 0!==w[w.length-1].route.Component||void 0!==w[w.length-1].route.lazy,'Matched leaf route at location "'.concat(p.pathname).concat(p.search).concat(p.hash,'" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.'));let k=ke(w&&w.map(e=>Object.assign({},e,{params:Object.assign({},c,e.params),pathname:K([h,s.encodeLocation?s.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?h:K([h,s.encodeLocation?s.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),o,n,r,a);return t&&k?i.createElement(le.Provider,{value:{location:u({pathname:"/",search:"",hash:"",state:null,key:"default"},p),navigationType:"POP"}},k):k}function ye(){let e=Ce(),t=J(e)?"".concat(e.status," ").concat(e.statusText):e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:r},s={padding:"2px 4px",backgroundColor:r},o=null;return console.error("Error handled by React Router default ErrorBoundary:",e),o=i.createElement(i.Fragment,null,i.createElement("p",null,"\ud83d\udcbf Hey developer \ud83d\udc4b"),i.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",i.createElement("code",{style:s},"ErrorBoundary")," or"," ",i.createElement("code",{style:s},"errorElement")," prop on your route.")),i.createElement(i.Fragment,null,i.createElement("h2",null,"Unexpected Application Error!"),i.createElement("h3",{style:{fontStyle:"italic"}},t),n?i.createElement("pre",{style:a},n):null,o)}var xe=i.createElement(ye,null),ve=class extends i.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){this.props.unstable_onError?this.props.unstable_onError(e,t):console.error("React Router caught the following error during render",e)}render(){return void 0!==this.state.error?i.createElement(ce.Provider,{value:this.props.routeContext},i.createElement(ue.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function we(e){let{routeContext:t,match:n,children:r}=e,a=i.useContext(te);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),i.createElement(ce.Provider,{value:t},r)}function ke(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;if(null==e){if(!n)return null;if(n.errors)e=n.matches;else{if(0!==t.length||n.initialized||!(n.matches.length>0))return null;e=n.matches}}let a=e,s=null===n||void 0===n?void 0:n.errors;if(null!=s){let e=a.findIndex(e=>e.route.id&&void 0!==(null===s||void 0===s?void 0:s[e.route.id]));x(e>=0,"Could not find a matching route for errors on route IDs: ".concat(Object.keys(s).join(","))),a=a.slice(0,Math.min(a.length,e+1))}let o=!1,l=-1;if(n)for(let i=0;i<a.length;i++){let e=a[i];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(l=i),e.route.id){let{loaderData:t,errors:i}=n,r=e.route.loader&&!t.hasOwnProperty(e.route.id)&&(!i||void 0===i[e.route.id]);if(e.route.lazy||r){o=!0,a=l>=0?a.slice(0,l+1):[a[0]];break}}}return a.reduceRight((e,c,u)=>{let d,h=!1,f=null,p=null;n&&(d=s&&c.route.id?s[c.route.id]:void 0,f=c.route.errorElement||xe,o&&(l<0&&0===u?(Ee("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),h=!0,p=null):l===u&&(h=!0,p=c.route.hydrateFallbackElement||null)));let m=t.concat(a.slice(0,u+1)),g=()=>{let t;return t=d?f:h?p:c.route.Component?i.createElement(c.route.Component,null):c.route.element?c.route.element:e,i.createElement(we,{match:c,routeContext:{outlet:e,matches:m,isDataRoute:null!=n},children:t})};return n&&(c.route.ErrorBoundary||c.route.errorElement||0===u)?i.createElement(ve,{location:n.location,revalidation:n.revalidation,component:f,error:d,children:g(),routeContext:{outlet:null,matches:m,isDataRoute:!0},unstable_onError:r}):g()},null)}function _e(e){return"".concat(e," must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.")}function Se(e){let t=i.useContext(te);return x(t,_e(e)),t}function Ne(e){let t=i.useContext(ne);return x(t,_e(e)),t}function Me(e){let t=function(e){let t=i.useContext(ce);return x(t,_e(e)),t}(e),n=t.matches[t.matches.length-1];return x(n.route.id,"".concat(e,' can only be used on routes that contain a unique "id"')),n.route.id}function Ce(){var e;let t=i.useContext(ue),n=Ne("useRouteError"),r=Me("useRouteError");return void 0!==t?t:null===(e=n.errors)||void 0===e?void 0:e[r]}var Pe={};function Ee(e,t,n){t||Pe[e]||(Pe[e]=!0,v(!1,n))}var je={};function Te(e,t){e||je[t]||(je[t]=!0,console.warn(t))}i.memo(function(e){let{routes:t,future:n,state:i,unstable_onError:r}=e;return be(t,void 0,i,r,n)});function Le(e){x(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function Oe(e){let{basename:t="/",children:n=null,location:r,navigationType:a="POP",navigator:s,static:o=!1}=e;x(!de(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let l=t.replace(/^\/*/,"/"),c=i.useMemo(()=>({basename:l,navigator:s,static:o,future:{}}),[l,s,o]);"string"===typeof r&&(r=S(r));let{pathname:u="/",search:d="",hash:h="",state:f=null,key:p="default"}=r,m=i.useMemo(()=>{let e=V(u,l);return null==e?null:{location:{pathname:e,search:d,hash:h,state:f,key:p},navigationType:a}},[l,u,d,h,f,p,a]);return v(null!=m,'<Router basename="'.concat(l,'"> is not able to match the URL "').concat(u).concat(d).concat(h,"\" because it does not start with the basename, so the <Router> won't render anything.")),null==m?null:i.createElement(oe.Provider,{value:c},i.createElement(le.Provider,{children:n,value:m}))}function Ae(e){let{children:t,location:n}=e;return be(De(t),n)}i.Component;function De(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[];return i.Children.forEach(e,(e,r)=>{if(!i.isValidElement(e))return;let a=[...t,r];if(e.type===i.Fragment)return void n.push.apply(n,De(e.props.children,a));x(e.type===Le,"[".concat("string"===typeof e.type?e.type:e.type.name,"] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>")),x(!e.props.index||!e.props.children,"An index route cannot have child routes.");let s={id:e.props.id||a.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,hydrateFallbackElement:e.props.hydrateFallbackElement,HydrateFallback:e.props.HydrateFallback,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:!0===e.props.hasErrorBoundary||null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(s.children=De(e.props.children,a)),n.push(s)}),n}var Re="get",ze="application/x-www-form-urlencoded";function Fe(e){return null!=e&&"string"===typeof e.tagName}var Ie=null;var We=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Be(e){return null==e||We.has(e)?e:(v(!1,'"'.concat(e,'" is not a valid `encType` for `<Form>`/`<fetcher.Form>` and will default to "').concat(ze,'"')),null)}function He(e,t){let n,i,r,a,s;if(Fe(o=e)&&"form"===o.tagName.toLowerCase()){let s=e.getAttribute("action");i=s?V(s,t):null,n=e.getAttribute("method")||Re,r=Be(e.getAttribute("enctype"))||ze,a=new FormData(e)}else if(function(e){return Fe(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return Fe(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let s=e.form;if(null==s)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let o=e.getAttribute("formaction")||s.getAttribute("action");if(i=o?V(o,t):null,n=e.getAttribute("formmethod")||s.getAttribute("method")||Re,r=Be(e.getAttribute("formenctype"))||Be(s.getAttribute("enctype"))||ze,a=new FormData(s,e),!function(){if(null===Ie)try{new FormData(document.createElement("form"),0),Ie=!1}catch(e){Ie=!0}return Ie}()){let{name:t,type:n,value:i}=e;if("image"===n){let e=t?"".concat(t,"."):"";a.append("".concat(e,"x"),"0"),a.append("".concat(e,"y"),"0")}else t&&a.append(t,i)}}else{if(Fe(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=Re,i=null,r=ze,s=e}var o;return a&&"text/plain"===r&&(s=a,a=void 0),{action:i,method:n.toLowerCase(),encType:r,formData:a,body:s}}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");"undefined"!==typeof window?window:"undefined"!==typeof globalThis&&globalThis;function Ve(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}Symbol("SingleFetchRedirect");function Ue(e,t,n){let i="string"===typeof e?new URL(e,"undefined"===typeof window?"server://singlefetch/":window.location.origin):e;return"/"===i.pathname?i.pathname="_root.".concat(n):t&&"/"===V(i.pathname,t)?i.pathname="".concat(t.replace(/\/$/,""),"/_root.").concat(n):i.pathname="".concat(i.pathname.replace(/\/$/,""),".").concat(n),i}async function $e(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error("Error loading route module `".concat(e.module,"`, reloading page...")),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}function Ye(e){return null!=e&&"string"===typeof e.page}function qe(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"===typeof e.imageSrcSet&&"string"===typeof e.imageSizes:"string"===typeof e.rel&&"string"===typeof e.href)}function Ke(e,t,n,i,r,a){let s=(e,t)=>!n[t]||e.route.id!==n[t].route.id,o=(e,t)=>{var i;return n[t].pathname!==e.pathname||(null===(i=n[t].route.path)||void 0===i?void 0:i.endsWith("*"))&&n[t].params["*"]!==e.params["*"]};return"assets"===a?t.filter((e,t)=>s(e,t)||o(e,t)):"data"===a?t.filter((t,a)=>{let l=i.routes[t.route.id];if(!l||!l.hasLoader)return!1;if(s(t,a)||o(t,a))return!0;if(t.route.shouldRevalidate){var c;let i=t.route.shouldRevalidate({currentUrl:new URL(r.pathname+r.search+r.hash,window.origin),currentParams:(null===(c=n[0])||void 0===c?void 0:c.params)||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"===typeof i)return i}return!0}):[]}function Qe(e,t){let{includeHydrateFallback:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return i=e.map(e=>{let i=t.routes[e.route.id];if(!i)return[];let r=[i.module];return i.clientActionModule&&(r=r.concat(i.clientActionModule)),i.clientLoaderModule&&(r=r.concat(i.clientLoaderModule)),n&&i.hydrateFallbackModule&&(r=r.concat(i.hydrateFallbackModule)),i.imports&&(r=r.concat(i.imports)),r}).flat(1),[...new Set(i)];var i}function Xe(e,t){let n=new Set,i=new Set(t);return e.reduce((e,r)=>{if(t&&!Ye(r)&&"script"===r.as&&r.href&&i.has(r.href))return e;let a=JSON.stringify(function(e){let t={},n=Object.keys(e).sort();for(let i of n)t[i]=e[i];return t}(r));return n.has(a)||(n.add(a),e.push({key:a,link:r})),e},[])}function Ge(e,t){return"lazy"===e.mode&&!0===t}function Je(){let e=i.useContext(te);return Ve(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function Ze(){let e=i.useContext(ne);return Ve(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var et=i.createContext(void 0);function tt(){let e=i.useContext(et);return Ve(e,"You must render this element inside a <HydratedRouter> element"),e}function nt(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function it(e,t,n){if(n&&!ot)return[e[0]];if(t){let n=e.findIndex(e=>void 0!==t[e.route.id]);return e.slice(0,n+1)}return e}et.displayName="FrameworkContext";function rt(e){let{page:t}=e,n=a(e,h),{router:r}=Je(),s=i.useMemo(()=>C(r.routes,t,r.basename),[r.routes,t,r.basename]);return s?i.createElement(st,u({page:t,matches:s},n)):null}function at(e){let{manifest:t,routeModules:n}=tt(),[r,a]=i.useState([]);return i.useEffect(()=>{let i=!1;return async function(e,t,n){return Xe((await Promise.all(e.map(async e=>{let i=t.routes[e.route.id];if(i){let e=await $e(i,n);return e.links?e.links():[]}return[]}))).flat(1).filter(qe).filter(e=>"stylesheet"===e.rel||"preload"===e.rel).map(e=>"stylesheet"===e.rel?u(u({},e),{},{rel:"prefetch",as:"style"}):u(u({},e),{},{rel:"prefetch"})))}(e,t,n).then(e=>{i||a(e)}),()=>{i=!0}},[e,t,n]),r}function st(e){let{page:t,matches:n}=e,r=a(e,f),s=he(),{manifest:o,routeModules:l}=tt(),{basename:c}=Je(),{loaderData:d,matches:h}=Ze(),p=i.useMemo(()=>Ke(t,n,h,o,s,"data"),[t,n,h,o,s]),m=i.useMemo(()=>Ke(t,n,h,o,s,"assets"),[t,n,h,o,s]),g=i.useMemo(()=>{if(t===s.pathname+s.search+s.hash)return[];let e=new Set,i=!1;if(n.forEach(t=>{var n;let r=o.routes[t.route.id];r&&r.hasLoader&&(!p.some(e=>e.route.id===t.route.id)&&t.route.id in d&&null!==(n=l[t.route.id])&&void 0!==n&&n.shouldRevalidate||r.hasClientLoader?i=!0:e.add(t.route.id))}),0===e.size)return[];let r=Ue(t,c,"data");return i&&e.size>0&&r.searchParams.set("_routes",n.filter(t=>e.has(t.route.id)).map(e=>e.route.id).join(",")),[r.pathname+r.search]},[c,d,s,o,p,n,t,l]),b=i.useMemo(()=>Qe(m,o),[m,o]),y=at(m);return i.createElement(i.Fragment,null,g.map(e=>i.createElement("link",u({key:e,rel:"prefetch",as:"fetch",href:e},r))),b.map(e=>i.createElement("link",u({key:e,rel:"modulepreload",href:e},r))),y.map(e=>{let{key:t,link:n}=e;return i.createElement("link",u({key:t,nonce:r.nonce},n))}))}var ot=!1;function lt(e){let{manifest:t,serverHandoffString:n,isSpaMode:r,renderMeta:s,routeDiscovery:o,ssr:l}=tt(),{router:c,static:h,staticContext:f}=Je(),{matches:p}=Ze(),m=i.useContext(ie),g=Ge(o,l);s&&(s.didRenderScripts=!0);let b=it(p,null,r);i.useEffect(()=>{ot=!0},[]);let y=i.useMemo(()=>{var r;if(m)return null;let s=f?"window.__reactRouterContext = ".concat(n,";").concat("window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());"):" ",o=h?"".concat(null!==(r=t.hmr)&&void 0!==r&&r.runtime?"import ".concat(JSON.stringify(t.hmr.runtime),";"):"").concat(g?"":"import ".concat(JSON.stringify(t.url)),";\n").concat(b.map((e,n)=>{let i="route".concat(n),r=t.routes[e.route.id];Ve(r,"Route ".concat(e.route.id," not found in manifest"));let{clientActionModule:a,clientLoaderModule:s,clientMiddlewareModule:o,hydrateFallbackModule:l,module:c}=r,u=[...a?[{module:a,varName:"".concat(i,"_clientAction")}]:[],...s?[{module:s,varName:"".concat(i,"_clientLoader")}]:[],...o?[{module:o,varName:"".concat(i,"_clientMiddleware")}]:[],...l?[{module:l,varName:"".concat(i,"_HydrateFallback")}]:[],{module:c,varName:"".concat(i,"_main")}];return 1===u.length?"import * as ".concat(i," from ").concat(JSON.stringify(c),";"):[u.map(e=>"import * as ".concat(e.varName,' from "').concat(e.module,'";')).join("\n"),"const ".concat(i," = {").concat(u.map(e=>"...".concat(e.varName)).join(","),"};")].join("\n")}).join("\n"),"\n  ").concat(g?"window.__reactRouterManifest = ".concat(JSON.stringify(function(e,t){let{sri:n}=e,i=a(e,d),r=new Set(t.state.matches.map(e=>e.route.id)),s=t.state.location.pathname.split("/").filter(Boolean),o=["/"];for(s.pop();s.length>0;)o.push("/".concat(s.join("/"))),s.pop();o.forEach(e=>{let n=C(t.routes,e,t.basename);n&&n.forEach(e=>r.add(e.route.id))});let l=[...r].reduce((e,t)=>Object.assign(e,{[t]:i.routes[t]}),{});return u(u({},i),{},{routes:l,sri:!!n||void 0})}(t,c),null,2),";"):"","\n  window.__reactRouterRouteModules = {").concat(b.map((e,t)=>"".concat(JSON.stringify(e.route.id),":route").concat(t)).join(","),"};\n\nimport(").concat(JSON.stringify(t.entry.module),");"):" ";return i.createElement(i.Fragment,null,i.createElement("script",u(u({},e),{},{suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:s},type:void 0})),i.createElement("script",u(u({},e),{},{suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:o},type:"module",async:!0})))},[]),x=ot||m?[]:(v=t.entry.imports.concat(Qe(b,t,{includeHydrateFallback:!0})),[...new Set(v)]);var v;let w="object"===typeof t.sri?t.sri:{};return Te(!m,"The <Scripts /> element is a no-op when using RSC and can be safely removed."),ot||m?null:i.createElement(i.Fragment,null,"object"===typeof t.sri?i.createElement("script",{"rr-importmap":"",type:"importmap",suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:JSON.stringify({integrity:w})}}):null,g?null:i.createElement("link",{rel:"modulepreload",href:t.url,crossOrigin:e.crossOrigin,integrity:w[t.url],suppressHydrationWarning:!0}),i.createElement("link",{rel:"modulepreload",href:t.entry.module,crossOrigin:e.crossOrigin,integrity:w[t.entry.module],suppressHydrationWarning:!0}),x.map(t=>i.createElement("link",{key:t,rel:"modulepreload",href:t,crossOrigin:e.crossOrigin,integrity:w[t],suppressHydrationWarning:!0})),y)}function ct(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>{t.forEach(t=>{"function"===typeof t?t(e):null!=t&&(t.current=e)})}}i.Component;function ut(e){let{error:t,isOutsideRemixApp:n}=e;console.error(t);let r,a=i.createElement("script",{dangerouslySetInnerHTML:{__html:'\n        console.log(\n          "\ud83d\udcbf Hey developer \ud83d\udc4b. You can provide a way better UX than this when your app throws errors. Check out https://reactrouter.com/how-to/error-boundary for more information."\n        );\n      '}});if(J(t))return i.createElement(dt,{title:"Unhandled Thrown Response!"},i.createElement("h1",{style:{fontSize:"24px"}},t.status," ",t.statusText),a);if(t instanceof Error)r=t;else{let e=null==t?"Unknown Error":"object"===typeof t&&"toString"in t?t.toString():JSON.stringify(t);r=new Error(e)}return i.createElement(dt,{title:"Application Error!",isOutsideRemixApp:n},i.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),i.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},r.stack),a)}function dt(e){var t;let{title:n,renderScripts:r,isOutsideRemixApp:a,children:s}=e,{routeModules:o}=tt();return null!==(t=o.root)&&void 0!==t&&t.Layout&&!a?s:i.createElement("html",{lang:"en"},i.createElement("head",null,i.createElement("meta",{charSet:"utf-8"}),i.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),i.createElement("title",null,n)),i.createElement("body",null,i.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},s,r?i.createElement(lt,null):null)))}var ht="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement;try{ht&&(window.__reactRouterVersion="7.9.0")}catch(Ec){}function ft(e){let{basename:t,children:n,window:r}=e,a=i.useRef();null==a.current&&(a.current=y({window:r,v5Compat:!0}));let s=a.current,[o,l]=i.useState({action:s.action,location:s.location}),c=i.useCallback(e=>{i.startTransition(()=>l(e))},[l]);return i.useLayoutEffect(()=>s.listen(c),[s,c]),i.createElement(Oe,{basename:t,children:n,location:o.location,navigationType:o.action,navigator:s})}var pt=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,mt=i.forwardRef(function(e,t){let n,{onClick:r,discover:s="render",prefetch:o="none",relative:l,reloadDocument:c,replace:d,state:h,target:f,to:m,preventScrollReset:g,viewTransition:b}=e,y=a(e,p),{basename:w}=i.useContext(oe),k="string"===typeof m&&pt.test(m),S=!1;if("string"===typeof m&&k&&(n=m,ht))try{let e=new URL(window.location.href),t=m.startsWith("//")?new URL(e.protocol+m):new URL(m),n=V(t.pathname,w);t.origin===e.origin&&null!=n?m=n+t.search+t.hash:S=!0}catch(Ec){v(!1,'<Link to="'.concat(m,'"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.'))}let N=function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};x(de(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:r}=i.useContext(oe),{hash:a,pathname:s,search:o}=ge(e,{relative:t}),l=s;return"/"!==n&&(l="/"===s?n:K([n,s])),r.createHref({pathname:l,search:o,hash:a})}(m,{relative:l}),[M,C,P]=function(e,t){let n=i.useContext(et),[r,a]=i.useState(!1),[s,o]=i.useState(!1),{onFocus:l,onBlur:c,onMouseEnter:u,onMouseLeave:d,onTouchStart:h}=t,f=i.useRef(null);i.useEffect(()=>{if("render"===e&&o(!0),"viewport"===e){let e=new IntersectionObserver(e=>{e.forEach(e=>{o(e.isIntersecting)})},{threshold:.5});return f.current&&e.observe(f.current),()=>{e.disconnect()}}},[e]),i.useEffect(()=>{if(r){let e=setTimeout(()=>{o(!0)},100);return()=>{clearTimeout(e)}}},[r]);let p=()=>{a(!0)},m=()=>{a(!1),o(!1)};return n?"intent"!==e?[s,f,{}]:[s,f,{onFocus:nt(l,p),onBlur:nt(c,m),onMouseEnter:nt(u,p),onMouseLeave:nt(d,m),onTouchStart:nt(h,p)}]:[!1,f,{}]}(o,y),E=function(e){let{target:t,replace:n,state:r,preventScrollReset:a,relative:s,viewTransition:o}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},l=me(),c=he(),u=ge(e,{relative:s});return i.useCallback(i=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(i,t)){i.preventDefault();let t=void 0!==n?n:_(c)===_(u);l(e,{replace:t,state:r,preventScrollReset:a,relative:s,viewTransition:o})}},[c,l,u,n,r,t,e,a,s,o])}(m,{replace:d,state:h,target:f,preventScrollReset:g,relative:l,viewTransition:b});let j=i.createElement("a",u(u(u({},y),P),{},{href:n||N,onClick:S||c?r:function(e){r&&r(e),e.defaultPrevented||E(e)},ref:ct(t,C),target:f,"data-discover":k||"render"!==s?void 0:"true"}));return M&&!k?i.createElement(i.Fragment,null,j,i.createElement(rt,{page:N})):j});mt.displayName="Link";var gt=i.forwardRef(function(e,t){let{"aria-current":n="page",caseSensitive:r=!1,className:s="",end:o=!1,style:l,to:c,viewTransition:d,children:h}=e,f=a(e,m),p=ge(c,{relative:f.relative}),g=he(),b=i.useContext(ne),{navigator:y,basename:v}=i.useContext(oe),w=null!=b&&function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=i.useContext(re);x(null!=n,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=xt("useViewTransitionState"),a=ge(e,{relative:t});if(!n.isTransitioning)return!1;let s=V(n.currentLocation.pathname,r)||n.currentLocation.pathname,o=V(n.nextLocation.pathname,r)||n.nextLocation.pathname;return null!=W(a.pathname,o)||null!=W(a.pathname,s)}(p)&&!0===d,k=y.encodeLocation?y.encodeLocation(p).pathname:p.pathname,_=g.pathname,S=b&&b.navigation&&b.navigation.location?b.navigation.location.pathname:null;r||(_=_.toLowerCase(),S=S?S.toLowerCase():null,k=k.toLowerCase()),S&&v&&(S=V(S,v)||S);const N="/"!==k&&k.endsWith("/")?k.length-1:k.length;let M,C=_===k||!o&&_.startsWith(k)&&"/"===_.charAt(N),P=null!=S&&(S===k||!o&&S.startsWith(k)&&"/"===S.charAt(k.length)),E={isActive:C,isPending:P,isTransitioning:w},j=C?n:void 0;M="function"===typeof s?s(E):[s,C?"active":null,P?"pending":null,w?"transitioning":null].filter(Boolean).join(" ");let T="function"===typeof l?l(E):l;return i.createElement(mt,u(u({},f),{},{"aria-current":j,className:M,ref:t,style:T,to:c,viewTransition:d}),"function"===typeof h?h(E):h)});gt.displayName="NavLink";var bt=i.forwardRef((e,t)=>{let{discover:n="render",fetcherKey:r,navigate:s,reloadDocument:o,replace:l,state:c,method:d=Re,action:h,onSubmit:f,relative:p,preventScrollReset:m,viewTransition:b}=e,y=a(e,g),v=kt(),w=function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{basename:n}=i.useContext(oe),r=i.useContext(ce);x(r,"useFormAction must be used inside a RouteContext");let[a]=r.matches.slice(-1),s=u({},ge(e||".",{relative:t})),o=he();if(null==e){s.search=o.search;let e=new URLSearchParams(s.search),t=e.getAll("index");if(t.some(e=>""===e)){e.delete("index"),t.filter(e=>e).forEach(t=>e.append("index",t));let n=e.toString();s.search=n?"?".concat(n):""}}e&&"."!==e||!a.route.index||(s.search=s.search?s.search.replace(/^\?/,"?index&"):"?index");"/"!==n&&(s.pathname="/"===s.pathname?n:K([n,s.pathname]));return _(s)}(h,{relative:p}),k="get"===d.toLowerCase()?"get":"post",S="string"===typeof h&&pt.test(h);return i.createElement("form",u(u({ref:t,method:k,action:w,onSubmit:o?f:e=>{if(f&&f(e),e.defaultPrevented)return;e.preventDefault();let t=e.nativeEvent.submitter,n=(null===t||void 0===t?void 0:t.getAttribute("formmethod"))||d;v(t||e.currentTarget,{fetcherKey:r,method:n,navigate:s,replace:l,state:c,relative:p,preventScrollReset:m,viewTransition:b})}},y),{},{"data-discover":S||"render"!==n?void 0:"true"}))});function yt(e){return"".concat(e," must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.")}function xt(e){let t=i.useContext(te);return x(t,yt(e)),t}bt.displayName="Form";var vt=0,wt=()=>"__".concat(String(++vt),"__");function kt(){let{router:e}=xt("useSubmit"),{basename:t}=i.useContext(oe),n=Me("useRouteId");return i.useCallback(async function(i){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{action:a,method:s,encType:o,formData:l,body:c}=He(i,t);if(!1===r.navigate){let t=r.fetcherKey||wt();await e.fetch(t,n,r.action||a,{preventScrollReset:r.preventScrollReset,formData:l,body:c,formMethod:r.method||s,formEncType:r.encType||o,flushSync:r.flushSync})}else await e.navigate(r.action||a,{preventScrollReset:r.preventScrollReset,formData:l,body:c,formMethod:r.method||s,formEncType:r.encType||o,replace:r.replace,state:r.state,fromRouteId:n,flushSync:r.flushSync,viewTransition:r.viewTransition})},[e,t,n])}var _t=n(507);const St=()=>(0,_t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-reedsoft-light to-white",children:[(0,_t.jsx)("section",{className:"pt-20 pb-16 px-4",children:(0,_t.jsxs)("div",{className:"max-w-6xl mx-auto text-center",children:[(0,_t.jsx)("h1",{className:"text-5xl md:text-7xl font-black text-reedsoft-primary mb-6 tracking-tight",children:"REEDSOFT"}),(0,_t.jsx)("p",{className:"text-xl md:text-2xl text-reedsoft-secondary mb-8 max-w-3xl mx-auto",children:"Building innovative solutions for personal and professional development"}),(0,_t.jsx)("p",{className:"text-lg text-gray-600 mb-12 max-w-2xl mx-auto",children:"We create comprehensive digital experiences that transform ambitious goals into structured, achievable plans."}),(0,_t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,_t.jsx)(mt,{to:"/japan",className:"bg-reedsoft-primary text-white px-8 py-4 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors shadow-lg",children:"Explore Japan Project"}),(0,_t.jsx)("button",{className:"bg-white text-reedsoft-primary px-8 py-4 rounded-lg font-semibold border-2 border-reedsoft-primary hover:bg-reedsoft-primary hover:text-white transition-colors",children:"Learn More"})]})]})}),(0,_t.jsx)("section",{className:"py-16 px-4 bg-white",children:(0,_t.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,_t.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-center text-reedsoft-primary mb-12",children:"Featured Project"}),(0,_t.jsx)("div",{className:"bg-gradient-to-r from-reedsoft-primary to-reedsoft-secondary rounded-2xl p-8 md:p-12 text-white",children:(0,_t.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 items-center",children:[(0,_t.jsxs)("div",{children:[(0,_t.jsx)("h3",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Japan Work Preparation Plan"}),(0,_t.jsx)("p",{className:"text-lg mb-6 opacity-90",children:"A comprehensive 4-month training program featuring four core pillars: Physical Fitness, Japanese Language, Practical Skills, and Cognitive Fitness."}),(0,_t.jsxs)("ul",{className:"space-y-2 mb-8",children:[(0,_t.jsxs)("li",{className:"flex items-center",children:[(0,_t.jsx)("span",{className:"w-2 h-2 bg-reedsoft-light rounded-full mr-3"}),"Interactive progress tracking"]}),(0,_t.jsxs)("li",{className:"flex items-center",children:[(0,_t.jsx)("span",{className:"w-2 h-2 bg-reedsoft-light rounded-full mr-3"}),"Data visualizations with Chart.js"]}),(0,_t.jsxs)("li",{className:"flex items-center",children:[(0,_t.jsx)("span",{className:"w-2 h-2 bg-reedsoft-light rounded-full mr-3"}),"Structured daily schedules"]}),(0,_t.jsxs)("li",{className:"flex items-center",children:[(0,_t.jsx)("span",{className:"w-2 h-2 bg-reedsoft-light rounded-full mr-3"}),"Responsive design for all devices"]})]}),(0,_t.jsx)(mt,{to:"/japan",className:"inline-block bg-white text-reedsoft-primary px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-light transition-colors",children:"View Project \u2192"})]}),(0,_t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,_t.jsxs)("div",{className:"bg-white bg-opacity-10 rounded-lg p-4 text-center",children:[(0,_t.jsx)("div",{className:"text-2xl font-bold mb-1",children:"4"}),(0,_t.jsx)("div",{className:"text-sm opacity-80",children:"Core Pillars"})]}),(0,_t.jsxs)("div",{className:"bg-white bg-opacity-10 rounded-lg p-4 text-center",children:[(0,_t.jsx)("div",{className:"text-2xl font-bold mb-1",children:"16"}),(0,_t.jsx)("div",{className:"text-sm opacity-80",children:"Weeks Program"})]}),(0,_t.jsxs)("div",{className:"bg-white bg-opacity-10 rounded-lg p-4 text-center",children:[(0,_t.jsx)("div",{className:"text-2xl font-bold mb-1",children:"45+"}),(0,_t.jsx)("div",{className:"text-sm opacity-80",children:"Hours/Week"})]}),(0,_t.jsxs)("div",{className:"bg-white bg-opacity-10 rounded-lg p-4 text-center",children:[(0,_t.jsx)("div",{className:"text-2xl font-bold mb-1",children:"100%"}),(0,_t.jsx)("div",{className:"text-sm opacity-80",children:"Commitment"})]})]})]})})]})}),(0,_t.jsx)("section",{className:"py-16 px-4 bg-gray-50",children:(0,_t.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,_t.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-center text-reedsoft-primary mb-12",children:"Built with Modern Technology"}),(0,_t.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,_t.jsxs)("div",{className:"text-center",children:[(0,_t.jsx)("div",{className:"w-16 h-16 bg-reedsoft-primary rounded-full flex items-center justify-center mx-auto mb-4",children:(0,_t.jsx)("span",{className:"text-white font-bold text-xl",children:"R"})}),(0,_t.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"React & TypeScript"}),(0,_t.jsx)("p",{className:"text-gray-600",children:"Modern frontend with type safety and component-based architecture"})]}),(0,_t.jsxs)("div",{className:"text-center",children:[(0,_t.jsx)("div",{className:"w-16 h-16 bg-reedsoft-secondary rounded-full flex items-center justify-center mx-auto mb-4",children:(0,_t.jsx)("span",{className:"text-white font-bold text-xl",children:"T"})}),(0,_t.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Tailwind CSS"}),(0,_t.jsx)("p",{className:"text-gray-600",children:"Utility-first CSS framework for rapid UI development"})]}),(0,_t.jsxs)("div",{className:"text-center",children:[(0,_t.jsx)("div",{className:"w-16 h-16 bg-reedsoft-accent rounded-full flex items-center justify-center mx-auto mb-4",children:(0,_t.jsx)("span",{className:"text-white font-bold text-xl",children:"C"})}),(0,_t.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Chart.js"}),(0,_t.jsx)("p",{className:"text-gray-600",children:"Interactive data visualizations and progress tracking"})]})]})]})}),(0,_t.jsx)("footer",{className:"bg-reedsoft-primary text-white py-12 px-4",children:(0,_t.jsxs)("div",{className:"max-w-6xl mx-auto text-center",children:[(0,_t.jsx)("h3",{className:"text-2xl font-bold mb-4",children:"Ready to Transform Your Goals?"}),(0,_t.jsx)("p",{className:"text-reedsoft-light mb-8 max-w-2xl mx-auto",children:"Join the journey of structured personal development with data-driven insights and comprehensive planning."}),(0,_t.jsx)(mt,{to:"/japan",className:"inline-block bg-white text-reedsoft-primary px-8 py-4 rounded-lg font-semibold hover:bg-reedsoft-light transition-colors",children:"Start Your Journey"}),(0,_t.jsx)("div",{className:"mt-12 pt-8 border-t border-reedsoft-secondary",children:(0,_t.jsx)("p",{className:"text-reedsoft-light",children:"\xa9 2025 Reedsoft. Building innovative solutions for personal and professional development."})})]})})]});function Nt(e){return e+.5|0}const Mt=(e,t,n)=>Math.max(Math.min(e,n),t);function Ct(e){return Mt(Nt(2.55*e),0,255)}function Pt(e){return Mt(Nt(255*e),0,255)}function Et(e){return Mt(Nt(e/2.55)/100,0,1)}function jt(e){return Mt(Nt(100*e),0,100)}const Tt={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},Lt=[..."0123456789ABCDEF"],Ot=e=>Lt[15&e],At=e=>Lt[(240&e)>>4]+Lt[15&e],Dt=e=>(240&e)>>4===(15&e);function Rt(e){var t=(e=>Dt(e.r)&&Dt(e.g)&&Dt(e.b)&&Dt(e.a))(e)?Ot:At;return e?"#"+t(e.r)+t(e.g)+t(e.b)+((e,t)=>e<255?t(e):"")(e.a,t):void 0}const zt=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function Ft(e,t,n){const i=t*Math.min(n,1-n),r=function(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(t+e/30)%12;return n-i*Math.max(Math.min(r-3,9-r,1),-1)};return[r(0),r(8),r(4)]}function It(e,t,n){const i=function(i){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(i+e/60)%6;return n-n*t*Math.max(Math.min(r,4-r,1),0)};return[i(5),i(3),i(1)]}function Wt(e,t,n){const i=Ft(e,1,.5);let r;for(t+n>1&&(r=1/(t+n),t*=r,n*=r),r=0;r<3;r++)i[r]*=1-t-n,i[r]+=t;return i}function Bt(e){const t=e.r/255,n=e.g/255,i=e.b/255,r=Math.max(t,n,i),a=Math.min(t,n,i),s=(r+a)/2;let o,l,c;return r!==a&&(c=r-a,l=s>.5?c/(2-r-a):c/(r+a),o=function(e,t,n,i,r){return e===r?(t-n)/i+(t<n?6:0):t===r?(n-e)/i+2:(e-t)/i+4}(t,n,i,c,r),o=60*o+.5),[0|o,l||0,s]}function Ht(e,t,n,i){return(Array.isArray(t)?e(t[0],t[1],t[2]):e(t,n,i)).map(Pt)}function Vt(e,t,n){return Ht(Ft,e,t,n)}function Ut(e){return(e%360+360)%360}function $t(e){const t=zt.exec(e);let n,i=255;if(!t)return;t[5]!==n&&(i=t[6]?Ct(+t[5]):Pt(+t[5]));const r=Ut(+t[2]),a=+t[3]/100,s=+t[4]/100;return n="hwb"===t[1]?function(e,t,n){return Ht(Wt,e,t,n)}(r,a,s):"hsv"===t[1]?function(e,t,n){return Ht(It,e,t,n)}(r,a,s):Vt(r,a,s),{r:n[0],g:n[1],b:n[2],a:i}}const Yt={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},qt={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};let Kt;function Qt(e){Kt||(Kt=function(){const e={},t=Object.keys(qt),n=Object.keys(Yt);let i,r,a,s,o;for(i=0;i<t.length;i++){for(s=o=t[i],r=0;r<n.length;r++)a=n[r],o=o.replace(a,Yt[a]);a=parseInt(qt[s],16),e[o]=[a>>16&255,a>>8&255,255&a]}return e}(),Kt.transparent=[0,0,0,0]);const t=Kt[e.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:4===t.length?t[3]:255}}const Xt=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;const Gt=e=>e<=.0031308?12.92*e:1.055*Math.pow(e,1/2.4)-.055,Jt=e=>e<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4);function Zt(e,t,n){if(e){let i=Bt(e);i[t]=Math.max(0,Math.min(i[t]+i[t]*n,0===t?360:1)),i=Vt(i),e.r=i[0],e.g=i[1],e.b=i[2]}}function en(e,t){return e?Object.assign(t||{},e):e}function tn(e){var t={r:0,g:0,b:0,a:255};return Array.isArray(e)?e.length>=3&&(t={r:e[0],g:e[1],b:e[2],a:255},e.length>3&&(t.a=Pt(e[3]))):(t=en(e,{r:0,g:0,b:0,a:1})).a=Pt(t.a),t}function nn(e){return"r"===e.charAt(0)?function(e){const t=Xt.exec(e);let n,i,r,a=255;if(t){if(t[7]!==n){const e=+t[7];a=t[8]?Ct(e):Mt(255*e,0,255)}return n=+t[1],i=+t[3],r=+t[5],n=255&(t[2]?Ct(n):Mt(n,0,255)),i=255&(t[4]?Ct(i):Mt(i,0,255)),r=255&(t[6]?Ct(r):Mt(r,0,255)),{r:n,g:i,b:r,a:a}}}(e):$t(e)}class rn{constructor(e){if(e instanceof rn)return e;const t=typeof e;let n;"object"===t?n=tn(e):"string"===t&&(n=function(e){var t,n=e.length;return"#"===e[0]&&(4===n||5===n?t={r:255&17*Tt[e[1]],g:255&17*Tt[e[2]],b:255&17*Tt[e[3]],a:5===n?17*Tt[e[4]]:255}:7!==n&&9!==n||(t={r:Tt[e[1]]<<4|Tt[e[2]],g:Tt[e[3]]<<4|Tt[e[4]],b:Tt[e[5]]<<4|Tt[e[6]],a:9===n?Tt[e[7]]<<4|Tt[e[8]]:255})),t}(e)||Qt(e)||nn(e)),this._rgb=n,this._valid=!!n}get valid(){return this._valid}get rgb(){var e=en(this._rgb);return e&&(e.a=Et(e.a)),e}set rgb(e){this._rgb=tn(e)}rgbString(){return this._valid?(e=this._rgb)&&(e.a<255?"rgba(".concat(e.r,", ").concat(e.g,", ").concat(e.b,", ").concat(Et(e.a),")"):"rgb(".concat(e.r,", ").concat(e.g,", ").concat(e.b,")")):void 0;var e}hexString(){return this._valid?Rt(this._rgb):void 0}hslString(){return this._valid?function(e){if(!e)return;const t=Bt(e),n=t[0],i=jt(t[1]),r=jt(t[2]);return e.a<255?"hsla(".concat(n,", ").concat(i,"%, ").concat(r,"%, ").concat(Et(e.a),")"):"hsl(".concat(n,", ").concat(i,"%, ").concat(r,"%)")}(this._rgb):void 0}mix(e,t){if(e){const n=this.rgb,i=e.rgb;let r;const a=t===r?.5:t,s=2*a-1,o=n.a-i.a,l=((s*o===-1?s:(s+o)/(1+s*o))+1)/2;r=1-l,n.r=255&l*n.r+r*i.r+.5,n.g=255&l*n.g+r*i.g+.5,n.b=255&l*n.b+r*i.b+.5,n.a=a*n.a+(1-a)*i.a,this.rgb=n}return this}interpolate(e,t){return e&&(this._rgb=function(e,t,n){const i=Jt(Et(e.r)),r=Jt(Et(e.g)),a=Jt(Et(e.b));return{r:Pt(Gt(i+n*(Jt(Et(t.r))-i))),g:Pt(Gt(r+n*(Jt(Et(t.g))-r))),b:Pt(Gt(a+n*(Jt(Et(t.b))-a))),a:e.a+n*(t.a-e.a)}}(this._rgb,e._rgb,t)),this}clone(){return new rn(this.rgb)}alpha(e){return this._rgb.a=Pt(e),this}clearer(e){return this._rgb.a*=1-e,this}greyscale(){const e=this._rgb,t=Nt(.3*e.r+.59*e.g+.11*e.b);return e.r=e.g=e.b=t,this}opaquer(e){return this._rgb.a*=1+e,this}negate(){const e=this._rgb;return e.r=255-e.r,e.g=255-e.g,e.b=255-e.b,this}lighten(e){return Zt(this._rgb,2,e),this}darken(e){return Zt(this._rgb,2,-e),this}saturate(e){return Zt(this._rgb,1,e),this}desaturate(e){return Zt(this._rgb,1,-e),this}rotate(e){return function(e,t){var n=Bt(e);n[0]=Ut(n[0]+t),n=Vt(n),e.r=n[0],e.g=n[1],e.b=n[2]}(this._rgb,e),this}}function an(){}const sn=(()=>{let e=0;return()=>e++})();function on(e){return null===e||void 0===e}function ln(e){if(Array.isArray&&Array.isArray(e))return!0;const t=Object.prototype.toString.call(e);return"[object"===t.slice(0,7)&&"Array]"===t.slice(-6)}function cn(e){return null!==e&&"[object Object]"===Object.prototype.toString.call(e)}function un(e){return("number"===typeof e||e instanceof Number)&&isFinite(+e)}function dn(e,t){return un(e)?e:t}function hn(e,t){return"undefined"===typeof e?t:e}const fn=(e,t)=>"string"===typeof e&&e.endsWith("%")?parseFloat(e)/100*t:+e;function pn(e,t,n){if(e&&"function"===typeof e.call)return e.apply(n,t)}function mn(e,t,n,i){let r,a,s;if(ln(e))if(a=e.length,i)for(r=a-1;r>=0;r--)t.call(n,e[r],r);else for(r=0;r<a;r++)t.call(n,e[r],r);else if(cn(e))for(s=Object.keys(e),a=s.length,r=0;r<a;r++)t.call(n,e[s[r]],s[r])}function gn(e,t){let n,i,r,a;if(!e||!t||e.length!==t.length)return!1;for(n=0,i=e.length;n<i;++n)if(r=e[n],a=t[n],r.datasetIndex!==a.datasetIndex||r.index!==a.index)return!1;return!0}function bn(e){if(ln(e))return e.map(bn);if(cn(e)){const t=Object.create(null),n=Object.keys(e),i=n.length;let r=0;for(;r<i;++r)t[n[r]]=bn(e[n[r]]);return t}return e}function yn(e){return-1===["__proto__","prototype","constructor"].indexOf(e)}function xn(e,t,n,i){if(!yn(e))return;const r=t[e],a=n[e];cn(r)&&cn(a)?vn(r,a,i):t[e]=bn(a)}function vn(e,t,n){const i=ln(t)?t:[t],r=i.length;if(!cn(e))return e;const a=(n=n||{}).merger||xn;let s;for(let o=0;o<r;++o){if(s=i[o],!cn(s))continue;const t=Object.keys(s);for(let i=0,r=t.length;i<r;++i)a(t[i],e,s,n)}return e}function wn(e,t){return vn(e,t,{merger:kn})}function kn(e,t,n){if(!yn(e))return;const i=t[e],r=n[e];cn(i)&&cn(r)?wn(i,r):Object.prototype.hasOwnProperty.call(t,e)||(t[e]=bn(r))}const _n={"":e=>e,x:e=>e.x,y:e=>e.y};function Sn(e,t){const n=_n[t]||(_n[t]=function(e){const t=function(e){const t=e.split("."),n=[];let i="";for(const r of t)i+=r,i.endsWith("\\")?i=i.slice(0,-1)+".":(n.push(i),i="");return n}(e);return e=>{for(const n of t){if(""===n)break;e=e&&e[n]}return e}}(t));return n(e)}function Nn(e){return e.charAt(0).toUpperCase()+e.slice(1)}const Mn=e=>"undefined"!==typeof e,Cn=e=>"function"===typeof e,Pn=(e,t)=>{if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0};const En=Math.PI,jn=2*En,Tn=jn+En,Ln=Number.POSITIVE_INFINITY,On=En/180,An=En/2,Dn=En/4,Rn=2*En/3,zn=Math.log10,Fn=Math.sign;function In(e,t,n){return Math.abs(e-t)<n}function Wn(e){const t=Math.round(e);e=In(e,t,e/1e3)?t:e;const n=Math.pow(10,Math.floor(zn(e))),i=e/n;return(i<=1?1:i<=2?2:i<=5?5:10)*n}function Bn(e){return!function(e){return"symbol"===typeof e||"object"===typeof e&&null!==e&&!(Symbol.toPrimitive in e||"toString"in e||"valueOf"in e)}(e)&&!isNaN(parseFloat(e))&&isFinite(e)}function Hn(e,t,n){let i,r,a;for(i=0,r=e.length;i<r;i++)a=e[i][n],isNaN(a)||(t.min=Math.min(t.min,a),t.max=Math.max(t.max,a))}function Vn(e){return e*(En/180)}function Un(e){return e*(180/En)}function $n(e){if(!un(e))return;let t=1,n=0;for(;Math.round(e*t)/t!==e;)t*=10,n++;return n}function Yn(e,t){const n=t.x-e.x,i=t.y-e.y,r=Math.sqrt(n*n+i*i);let a=Math.atan2(i,n);return a<-.5*En&&(a+=jn),{angle:a,distance:r}}function qn(e,t){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))}function Kn(e,t){return(e-t+Tn)%jn-En}function Qn(e){return(e%jn+jn)%jn}function Xn(e,t,n,i){const r=Qn(e),a=Qn(t),s=Qn(n),o=Qn(a-r),l=Qn(s-r),c=Qn(r-a),u=Qn(r-s);return r===a||r===s||i&&a===s||o>l&&c<u}function Gn(e,t,n){return Math.max(t,Math.min(n,e))}function Jn(e,t,n){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1e-6;return e>=Math.min(t,n)-i&&e<=Math.max(t,n)+i}function Zn(e,t,n){n=n||(n=>e[n]<t);let i,r=e.length-1,a=0;for(;r-a>1;)i=a+r>>1,n(i)?a=i:r=i;return{lo:a,hi:r}}const ei=(e,t,n,i)=>Zn(e,n,i?i=>{const r=e[i][t];return r<n||r===n&&e[i+1][t]===n}:i=>e[i][t]<n),ti=(e,t,n)=>Zn(e,n,i=>e[i][t]>=n);const ni=["push","pop","shift","splice","unshift"];function ii(e,t){const n=e._chartjs;if(!n)return;const i=n.listeners,r=i.indexOf(t);-1!==r&&i.splice(r,1),i.length>0||(ni.forEach(t=>{delete e[t]}),delete e._chartjs)}function ri(e){const t=new Set(e);return t.size===e.length?e:Array.from(t)}const ai="undefined"===typeof window?function(e){return e()}:window.requestAnimationFrame;function si(e,t){let n=[],i=!1;return function(){for(var r=arguments.length,a=new Array(r),s=0;s<r;s++)a[s]=arguments[s];n=a,i||(i=!0,ai.call(window,()=>{i=!1,e.apply(t,n)}))}}const oi=e=>"start"===e?"left":"end"===e?"right":"center",li=(e,t,n)=>"start"===e?t:"end"===e?n:(t+n)/2;function ci(e,t,n){const i=t.length;let r=0,a=i;if(e._sorted){const{iScale:s,vScale:o,_parsed:l}=e,c=e.dataset&&e.dataset.options?e.dataset.options.spanGaps:null,u=s.axis,{min:d,max:h,minDefined:f,maxDefined:p}=s.getUserBounds();if(f){if(r=Math.min(ei(l,u,d).lo,n?i:ei(t,u,s.getPixelForValue(d)).lo),c){const e=l.slice(0,r+1).reverse().findIndex(e=>!on(e[o.axis]));r-=Math.max(0,e)}r=Gn(r,0,i-1)}if(p){let e=Math.max(ei(l,s.axis,h,!0).hi+1,n?0:ei(t,u,s.getPixelForValue(h),!0).hi+1);if(c){const t=l.slice(e-1).findIndex(e=>!on(e[o.axis]));e+=Math.max(0,t)}a=Gn(e,r,i)-r}else a=i-r}return{start:r,count:a}}function ui(e){const{xScale:t,yScale:n,_scaleRanges:i}=e,r={xmin:t.min,xmax:t.max,ymin:n.min,ymax:n.max};if(!i)return e._scaleRanges=r,!0;const a=i.xmin!==t.min||i.xmax!==t.max||i.ymin!==n.min||i.ymax!==n.max;return Object.assign(i,r),a}const di=e=>0===e||1===e,hi=(e,t,n)=>-Math.pow(2,10*(e-=1))*Math.sin((e-t)*jn/n),fi=(e,t,n)=>Math.pow(2,-10*e)*Math.sin((e-t)*jn/n)+1,pi={linear:e=>e,easeInQuad:e=>e*e,easeOutQuad:e=>-e*(e-2),easeInOutQuad:e=>(e/=.5)<1?.5*e*e:-.5*(--e*(e-2)-1),easeInCubic:e=>e*e*e,easeOutCubic:e=>(e-=1)*e*e+1,easeInOutCubic:e=>(e/=.5)<1?.5*e*e*e:.5*((e-=2)*e*e+2),easeInQuart:e=>e*e*e*e,easeOutQuart:e=>-((e-=1)*e*e*e-1),easeInOutQuart:e=>(e/=.5)<1?.5*e*e*e*e:-.5*((e-=2)*e*e*e-2),easeInQuint:e=>e*e*e*e*e,easeOutQuint:e=>(e-=1)*e*e*e*e+1,easeInOutQuint:e=>(e/=.5)<1?.5*e*e*e*e*e:.5*((e-=2)*e*e*e*e+2),easeInSine:e=>1-Math.cos(e*An),easeOutSine:e=>Math.sin(e*An),easeInOutSine:e=>-.5*(Math.cos(En*e)-1),easeInExpo:e=>0===e?0:Math.pow(2,10*(e-1)),easeOutExpo:e=>1===e?1:1-Math.pow(2,-10*e),easeInOutExpo:e=>di(e)?e:e<.5?.5*Math.pow(2,10*(2*e-1)):.5*(2-Math.pow(2,-10*(2*e-1))),easeInCirc:e=>e>=1?e:-(Math.sqrt(1-e*e)-1),easeOutCirc:e=>Math.sqrt(1-(e-=1)*e),easeInOutCirc:e=>(e/=.5)<1?-.5*(Math.sqrt(1-e*e)-1):.5*(Math.sqrt(1-(e-=2)*e)+1),easeInElastic:e=>di(e)?e:hi(e,.075,.3),easeOutElastic:e=>di(e)?e:fi(e,.075,.3),easeInOutElastic(e){const t=.1125;return di(e)?e:e<.5?.5*hi(2*e,t,.45):.5+.5*fi(2*e-1,t,.45)},easeInBack(e){const t=1.70158;return e*e*((t+1)*e-t)},easeOutBack(e){const t=1.70158;return(e-=1)*e*((t+1)*e+t)+1},easeInOutBack(e){let t=1.70158;return(e/=.5)<1?e*e*((1+(t*=1.525))*e-t)*.5:.5*((e-=2)*e*((1+(t*=1.525))*e+t)+2)},easeInBounce:e=>1-pi.easeOutBounce(1-e),easeOutBounce(e){const t=7.5625,n=2.75;return e<1/n?t*e*e:e<2/n?t*(e-=1.5/n)*e+.75:e<2.5/n?t*(e-=2.25/n)*e+.9375:t*(e-=2.625/n)*e+.984375},easeInOutBounce:e=>e<.5?.5*pi.easeInBounce(2*e):.5*pi.easeOutBounce(2*e-1)+.5};function mi(e){if(e&&"object"===typeof e){const t=e.toString();return"[object CanvasPattern]"===t||"[object CanvasGradient]"===t}return!1}function gi(e){return mi(e)?e:new rn(e)}function bi(e){return mi(e)?e:new rn(e).saturate(.5).darken(.1).hexString()}const yi=["x","y","borderWidth","radius","tension"],xi=["color","borderColor","backgroundColor"];const vi=new Map;function wi(e,t,n){return function(e,t){t=t||{};const n=e+JSON.stringify(t);let i=vi.get(n);return i||(i=new Intl.NumberFormat(e,t),vi.set(n,i)),i}(t,n).format(e)}const ki={values:e=>ln(e)?e:""+e,numeric(e,t,n){if(0===e)return"0";const i=this.chart.options.locale;let r,a=e;if(n.length>1){const t=Math.max(Math.abs(n[0].value),Math.abs(n[n.length-1].value));(t<1e-4||t>1e15)&&(r="scientific"),a=function(e,t){let n=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;Math.abs(n)>=1&&e!==Math.floor(e)&&(n=e-Math.floor(e));return n}(e,n)}const s=zn(Math.abs(a)),o=isNaN(s)?1:Math.max(Math.min(-1*Math.floor(s),20),0),l={notation:r,minimumFractionDigits:o,maximumFractionDigits:o};return Object.assign(l,this.options.ticks.format),wi(e,i,l)},logarithmic(e,t,n){if(0===e)return"0";const i=n[t].significand||e/Math.pow(10,Math.floor(zn(e)));return[1,2,3,5,10,15].includes(i)||t>.8*n.length?ki.numeric.call(this,e,t,n):""}};var _i={formatters:ki};const Si=Object.create(null),Ni=Object.create(null);function Mi(e,t){if(!t)return e;const n=t.split(".");for(let i=0,r=n.length;i<r;++i){const t=n[i];e=e[t]||(e[t]=Object.create(null))}return e}function Ci(e,t,n){return"string"===typeof t?vn(Mi(e,t),n):vn(Mi(e,""),t)}class Pi{constructor(e,t){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=e=>e.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(e,t)=>bi(t.backgroundColor),this.hoverBorderColor=(e,t)=>bi(t.borderColor),this.hoverColor=(e,t)=>bi(t.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(e),this.apply(t)}set(e,t){return Ci(this,e,t)}get(e){return Mi(this,e)}describe(e,t){return Ci(Ni,e,t)}override(e,t){return Ci(Si,e,t)}route(e,t,n,i){const r=Mi(this,e),a=Mi(this,n),s="_"+t;Object.defineProperties(r,{[s]:{value:r[t],writable:!0},[t]:{enumerable:!0,get(){const e=this[s],t=a[i];return cn(e)?Object.assign({},t,e):hn(e,t)},set(e){this[s]=e}}})}apply(e){e.forEach(e=>e(this))}}var Ei=new Pi({_scriptable:e=>!e.startsWith("on"),_indexable:e=>"events"!==e,hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[function(e){e.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),e.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:e=>"onProgress"!==e&&"onComplete"!==e&&"fn"!==e}),e.set("animations",{colors:{type:"color",properties:xi},numbers:{type:"number",properties:yi}}),e.describe("animations",{_fallback:"animation"}),e.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:e=>0|e}}}})},function(e){e.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})},function(e){e.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(e,t)=>t.lineWidth,tickColor:(e,t)=>t.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:_i.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),e.route("scale.ticks","color","","color"),e.route("scale.grid","color","","borderColor"),e.route("scale.border","color","","borderColor"),e.route("scale.title","color","","color"),e.describe("scale",{_fallback:!1,_scriptable:e=>!e.startsWith("before")&&!e.startsWith("after")&&"callback"!==e&&"parser"!==e,_indexable:e=>"borderDash"!==e&&"tickBorderDash"!==e&&"dash"!==e}),e.describe("scales",{_fallback:"scale"}),e.describe("scale.ticks",{_scriptable:e=>"backdropPadding"!==e&&"callback"!==e,_indexable:e=>"backdropPadding"!==e})}]);function ji(e,t,n,i,r){let a=t[r];return a||(a=t[r]=e.measureText(r).width,n.push(r)),a>i&&(i=a),i}function Ti(e,t,n,i){let r=(i=i||{}).data=i.data||{},a=i.garbageCollect=i.garbageCollect||[];i.font!==t&&(r=i.data={},a=i.garbageCollect=[],i.font=t),e.save(),e.font=t;let s=0;const o=n.length;let l,c,u,d,h;for(l=0;l<o;l++)if(d=n[l],void 0===d||null===d||ln(d)){if(ln(d))for(c=0,u=d.length;c<u;c++)h=d[c],void 0===h||null===h||ln(h)||(s=ji(e,r,a,s,h))}else s=ji(e,r,a,s,d);e.restore();const f=a.length/2;if(f>n.length){for(l=0;l<f;l++)delete r[a[l]];a.splice(0,f)}return s}function Li(e,t,n){const i=e.currentDevicePixelRatio,r=0!==n?Math.max(n/2,.5):0;return Math.round((t-r)*i)/i+r}function Oi(e,t){(t||e)&&((t=t||e.getContext("2d")).save(),t.resetTransform(),t.clearRect(0,0,e.width,e.height),t.restore())}function Ai(e,t,n,i){Di(e,t,n,i,null)}function Di(e,t,n,i,r){let a,s,o,l,c,u,d,h;const f=t.pointStyle,p=t.rotation,m=t.radius;let g=(p||0)*On;if(f&&"object"===typeof f&&(a=f.toString(),"[object HTMLImageElement]"===a||"[object HTMLCanvasElement]"===a))return e.save(),e.translate(n,i),e.rotate(g),e.drawImage(f,-f.width/2,-f.height/2,f.width,f.height),void e.restore();if(!(isNaN(m)||m<=0)){switch(e.beginPath(),f){default:r?e.ellipse(n,i,r/2,m,0,0,jn):e.arc(n,i,m,0,jn),e.closePath();break;case"triangle":u=r?r/2:m,e.moveTo(n+Math.sin(g)*u,i-Math.cos(g)*m),g+=Rn,e.lineTo(n+Math.sin(g)*u,i-Math.cos(g)*m),g+=Rn,e.lineTo(n+Math.sin(g)*u,i-Math.cos(g)*m),e.closePath();break;case"rectRounded":c=.516*m,l=m-c,s=Math.cos(g+Dn)*l,d=Math.cos(g+Dn)*(r?r/2-c:l),o=Math.sin(g+Dn)*l,h=Math.sin(g+Dn)*(r?r/2-c:l),e.arc(n-d,i-o,c,g-En,g-An),e.arc(n+h,i-s,c,g-An,g),e.arc(n+d,i+o,c,g,g+An),e.arc(n-h,i+s,c,g+An,g+En),e.closePath();break;case"rect":if(!p){l=Math.SQRT1_2*m,u=r?r/2:l,e.rect(n-u,i-l,2*u,2*l);break}g+=Dn;case"rectRot":d=Math.cos(g)*(r?r/2:m),s=Math.cos(g)*m,o=Math.sin(g)*m,h=Math.sin(g)*(r?r/2:m),e.moveTo(n-d,i-o),e.lineTo(n+h,i-s),e.lineTo(n+d,i+o),e.lineTo(n-h,i+s),e.closePath();break;case"crossRot":g+=Dn;case"cross":d=Math.cos(g)*(r?r/2:m),s=Math.cos(g)*m,o=Math.sin(g)*m,h=Math.sin(g)*(r?r/2:m),e.moveTo(n-d,i-o),e.lineTo(n+d,i+o),e.moveTo(n+h,i-s),e.lineTo(n-h,i+s);break;case"star":d=Math.cos(g)*(r?r/2:m),s=Math.cos(g)*m,o=Math.sin(g)*m,h=Math.sin(g)*(r?r/2:m),e.moveTo(n-d,i-o),e.lineTo(n+d,i+o),e.moveTo(n+h,i-s),e.lineTo(n-h,i+s),g+=Dn,d=Math.cos(g)*(r?r/2:m),s=Math.cos(g)*m,o=Math.sin(g)*m,h=Math.sin(g)*(r?r/2:m),e.moveTo(n-d,i-o),e.lineTo(n+d,i+o),e.moveTo(n+h,i-s),e.lineTo(n-h,i+s);break;case"line":s=r?r/2:Math.cos(g)*m,o=Math.sin(g)*m,e.moveTo(n-s,i-o),e.lineTo(n+s,i+o);break;case"dash":e.moveTo(n,i),e.lineTo(n+Math.cos(g)*(r?r/2:m),i+Math.sin(g)*m);break;case!1:e.closePath()}e.fill(),t.borderWidth>0&&e.stroke()}}function Ri(e,t,n){return n=n||.5,!t||e&&e.x>t.left-n&&e.x<t.right+n&&e.y>t.top-n&&e.y<t.bottom+n}function zi(e,t){e.save(),e.beginPath(),e.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),e.clip()}function Fi(e){e.restore()}function Ii(e,t,n,i,r){if(!t)return e.lineTo(n.x,n.y);if("middle"===r){const i=(t.x+n.x)/2;e.lineTo(i,t.y),e.lineTo(i,n.y)}else"after"===r!==!!i?e.lineTo(t.x,n.y):e.lineTo(n.x,t.y);e.lineTo(n.x,n.y)}function Wi(e,t,n,i){if(!t)return e.lineTo(n.x,n.y);e.bezierCurveTo(i?t.cp1x:t.cp2x,i?t.cp1y:t.cp2y,i?n.cp2x:n.cp1x,i?n.cp2y:n.cp1y,n.x,n.y)}function Bi(e,t,n,i,r){if(r.strikethrough||r.underline){const a=e.measureText(i),s=t-a.actualBoundingBoxLeft,o=t+a.actualBoundingBoxRight,l=n-a.actualBoundingBoxAscent,c=n+a.actualBoundingBoxDescent,u=r.strikethrough?(l+c)/2:c;e.strokeStyle=e.fillStyle,e.beginPath(),e.lineWidth=r.decorationWidth||2,e.moveTo(s,u),e.lineTo(o,u),e.stroke()}}function Hi(e,t){const n=e.fillStyle;e.fillStyle=t.color,e.fillRect(t.left,t.top,t.width,t.height),e.fillStyle=n}function Vi(e,t,n,i,r){let a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{};const s=ln(t)?t:[t],o=a.strokeWidth>0&&""!==a.strokeColor;let l,c;for(e.save(),e.font=r.string,function(e,t){t.translation&&e.translate(t.translation[0],t.translation[1]),on(t.rotation)||e.rotate(t.rotation),t.color&&(e.fillStyle=t.color),t.textAlign&&(e.textAlign=t.textAlign),t.textBaseline&&(e.textBaseline=t.textBaseline)}(e,a),l=0;l<s.length;++l)c=s[l],a.backdrop&&Hi(e,a.backdrop),o&&(a.strokeColor&&(e.strokeStyle=a.strokeColor),on(a.strokeWidth)||(e.lineWidth=a.strokeWidth),e.strokeText(c,n,i,a.maxWidth)),e.fillText(c,n,i,a.maxWidth),Bi(e,n,i,c,a),i+=Number(r.lineHeight);e.restore()}function Ui(e,t){const{x:n,y:i,w:r,h:a,radius:s}=t;e.arc(n+s.topLeft,i+s.topLeft,s.topLeft,1.5*En,En,!0),e.lineTo(n,i+a-s.bottomLeft),e.arc(n+s.bottomLeft,i+a-s.bottomLeft,s.bottomLeft,En,An,!0),e.lineTo(n+r-s.bottomRight,i+a),e.arc(n+r-s.bottomRight,i+a-s.bottomRight,s.bottomRight,An,0,!0),e.lineTo(n+r,i+s.topRight),e.arc(n+r-s.topRight,i+s.topRight,s.topRight,0,-An,!0),e.lineTo(n+s.topLeft,i)}const $i=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Yi=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function qi(e,t){const n=(""+e).match($i);if(!n||"normal"===n[1])return 1.2*t;switch(e=+n[2],n[3]){case"px":return e;case"%":e/=100}return t*e}const Ki=e=>+e||0;function Qi(e,t){const n={},i=cn(t),r=i?Object.keys(t):t,a=cn(e)?i?n=>hn(e[n],e[t[n]]):t=>e[t]:()=>e;for(const s of r)n[s]=Ki(a(s));return n}function Xi(e){return Qi(e,{top:"y",right:"x",bottom:"y",left:"x"})}function Gi(e){return Qi(e,["topLeft","topRight","bottomLeft","bottomRight"])}function Ji(e){const t=Xi(e);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function Zi(e,t){e=e||{},t=t||Ei.font;let n=hn(e.size,t.size);"string"===typeof n&&(n=parseInt(n,10));let i=hn(e.style,t.style);i&&!(""+i).match(Yi)&&(console.warn('Invalid font style specified: "'+i+'"'),i=void 0);const r={family:hn(e.family,t.family),lineHeight:qi(hn(e.lineHeight,t.lineHeight),n),size:n,style:i,weight:hn(e.weight,t.weight),string:""};return r.string=function(e){return!e||on(e.size)||on(e.family)?null:(e.style?e.style+" ":"")+(e.weight?e.weight+" ":"")+e.size+"px "+e.family}(r),r}function er(e,t,n,i){let r,a,s,o=!0;for(r=0,a=e.length;r<a;++r)if(s=e[r],void 0!==s&&(void 0!==t&&"function"===typeof s&&(s=s(t),o=!1),void 0!==n&&ln(s)&&(s=s[n%s.length],o=!1),void 0!==s))return i&&!o&&(i.cacheable=!1),s}function tr(e,t){return Object.assign(Object.create(e),t)}function nr(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[""],n=arguments.length>3?arguments[3]:void 0,i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:()=>e[0];const r=(arguments.length>2?arguments[2]:void 0)||e;"undefined"===typeof n&&(n=fr("_fallback",e));const a={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:e,_rootScopes:r,_fallback:n,_getTarget:i,override:i=>nr([i,...e],t,r,n)};return new Proxy(a,{deleteProperty:(t,n)=>(delete t[n],delete t._keys,delete e[0][n],!0),get:(n,i)=>or(n,i,()=>function(e,t,n,i){let r;for(const a of t)if(r=fr(ar(a,e),n),"undefined"!==typeof r)return sr(e,r)?dr(n,i,e,r):r}(i,t,e,n)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e._scopes[0],t),getPrototypeOf:()=>Reflect.getPrototypeOf(e[0]),has:(e,t)=>pr(e).includes(t),ownKeys:e=>pr(e),set(e,t,n){const r=e._storage||(e._storage=i());return e[t]=r[t]=n,delete e._keys,!0}})}function ir(e,t,n,i){const r={_cacheable:!1,_proxy:e,_context:t,_subProxy:n,_stack:new Set,_descriptors:rr(e,i),setContext:t=>ir(e,t,n,i),override:r=>ir(e.override(r),t,n,i)};return new Proxy(r,{deleteProperty:(t,n)=>(delete t[n],delete e[n],!0),get:(e,t,n)=>or(e,t,()=>function(e,t,n){const{_proxy:i,_context:r,_subProxy:a,_descriptors:s}=e;let o=i[t];Cn(o)&&s.isScriptable(t)&&(o=function(e,t,n,i){const{_proxy:r,_context:a,_subProxy:s,_stack:o}=n;if(o.has(e))throw new Error("Recursion detected: "+Array.from(o).join("->")+"->"+e);o.add(e);let l=t(a,s||i);o.delete(e),sr(e,l)&&(l=dr(r._scopes,r,e,l));return l}(t,o,e,n));ln(o)&&o.length&&(o=function(e,t,n,i){const{_proxy:r,_context:a,_subProxy:s,_descriptors:o}=n;if("undefined"!==typeof a.index&&i(e))return t[a.index%t.length];if(cn(t[0])){const n=t,i=r._scopes.filter(e=>e!==n);t=[];for(const l of n){const n=dr(i,r,e,l);t.push(ir(n,a,s&&s[e],o))}}return t}(t,o,e,s.isIndexable));sr(t,o)&&(o=ir(o,r,a&&a[t],s));return o}(e,t,n)),getOwnPropertyDescriptor:(t,n)=>t._descriptors.allKeys?Reflect.has(e,n)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(e,n),getPrototypeOf:()=>Reflect.getPrototypeOf(e),has:(t,n)=>Reflect.has(e,n),ownKeys:()=>Reflect.ownKeys(e),set:(t,n,i)=>(e[n]=i,delete t[n],!0)})}function rr(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{scriptable:!0,indexable:!0};const{_scriptable:n=t.scriptable,_indexable:i=t.indexable,_allKeys:r=t.allKeys}=e;return{allKeys:r,scriptable:n,indexable:i,isScriptable:Cn(n)?n:()=>n,isIndexable:Cn(i)?i:()=>i}}const ar=(e,t)=>e?e+Nn(t):t,sr=(e,t)=>cn(t)&&"adapters"!==e&&(null===Object.getPrototypeOf(t)||t.constructor===Object);function or(e,t,n){if(Object.prototype.hasOwnProperty.call(e,t)||"constructor"===t)return e[t];const i=n();return e[t]=i,i}function lr(e,t,n){return Cn(e)?e(t,n):e}const cr=(e,t)=>!0===e?t:"string"===typeof e?Sn(t,e):void 0;function ur(e,t,n,i,r){for(const a of t){const t=cr(n,a);if(t){e.add(t);const a=lr(t._fallback,n,r);if("undefined"!==typeof a&&a!==n&&a!==i)return a}else if(!1===t&&"undefined"!==typeof i&&n!==i)return null}return!1}function dr(e,t,n,i){const r=t._rootScopes,a=lr(t._fallback,n,i),s=[...e,...r],o=new Set;o.add(i);let l=hr(o,s,n,a||n,i);return null!==l&&(("undefined"===typeof a||a===n||(l=hr(o,s,a,l,i),null!==l))&&nr(Array.from(o),[""],r,a,()=>function(e,t,n){const i=e._getTarget();t in i||(i[t]={});const r=i[t];if(ln(r)&&cn(n))return n;return r||{}}(t,n,i)))}function hr(e,t,n,i,r){for(;n;)n=ur(e,t,n,i,r);return n}function fr(e,t){for(const n of t){if(!n)continue;const t=n[e];if("undefined"!==typeof t)return t}}function pr(e){let t=e._keys;return t||(t=e._keys=function(e){const t=new Set;for(const n of e)for(const e of Object.keys(n).filter(e=>!e.startsWith("_")))t.add(e);return Array.from(t)}(e._scopes)),t}function mr(e,t,n,i){const{iScale:r}=e,{key:a="r"}=this._parsing,s=new Array(i);let o,l,c,u;for(o=0,l=i;o<l;++o)c=o+n,u=t[c],s[o]={r:r.parse(Sn(u,a),c)};return s}const gr=Number.EPSILON||1e-14,br=(e,t)=>t<e.length&&!e[t].skip&&e[t],yr=e=>"x"===e?"y":"x";function xr(e,t,n,i){const r=e.skip?t:e,a=t,s=n.skip?t:n,o=qn(a,r),l=qn(s,a);let c=o/(o+l),u=l/(o+l);c=isNaN(c)?0:c,u=isNaN(u)?0:u;const d=i*c,h=i*u;return{previous:{x:a.x-d*(s.x-r.x),y:a.y-d*(s.y-r.y)},next:{x:a.x+h*(s.x-r.x),y:a.y+h*(s.y-r.y)}}}function vr(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"x";const n=yr(t),i=e.length,r=Array(i).fill(0),a=Array(i);let s,o,l,c=br(e,0);for(s=0;s<i;++s)if(o=l,l=c,c=br(e,s+1),l){if(c){const e=c[t]-l[t];r[s]=0!==e?(c[n]-l[n])/e:0}a[s]=o?c?Fn(r[s-1])!==Fn(r[s])?0:(r[s-1]+r[s])/2:r[s-1]:r[s]}!function(e,t,n){const i=e.length;let r,a,s,o,l,c=br(e,0);for(let u=0;u<i-1;++u)l=c,c=br(e,u+1),l&&c&&(In(t[u],0,gr)?n[u]=n[u+1]=0:(r=n[u]/t[u],a=n[u+1]/t[u],o=Math.pow(r,2)+Math.pow(a,2),o<=9||(s=3/Math.sqrt(o),n[u]=r*s*t[u],n[u+1]=a*s*t[u])))}(e,r,a),function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"x";const i=yr(n),r=e.length;let a,s,o,l=br(e,0);for(let c=0;c<r;++c){if(s=o,o=l,l=br(e,c+1),!o)continue;const r=o[n],u=o[i];s&&(a=(r-s[n])/3,o["cp1".concat(n)]=r-a,o["cp1".concat(i)]=u-a*t[c]),l&&(a=(l[n]-r)/3,o["cp2".concat(n)]=r+a,o["cp2".concat(i)]=u+a*t[c])}}(e,a,t)}function wr(e,t,n){return Math.max(Math.min(e,n),t)}function kr(e,t,n,i,r){let a,s,o,l;if(t.spanGaps&&(e=e.filter(e=>!e.skip)),"monotone"===t.cubicInterpolationMode)vr(e,r);else{let n=i?e[e.length-1]:e[0];for(a=0,s=e.length;a<s;++a)o=e[a],l=xr(n,o,e[Math.min(a+1,s-(i?0:1))%s],t.tension),o.cp1x=l.previous.x,o.cp1y=l.previous.y,o.cp2x=l.next.x,o.cp2y=l.next.y,n=o}t.capBezierPoints&&function(e,t){let n,i,r,a,s,o=Ri(e[0],t);for(n=0,i=e.length;n<i;++n)s=a,a=o,o=n<i-1&&Ri(e[n+1],t),a&&(r=e[n],s&&(r.cp1x=wr(r.cp1x,t.left,t.right),r.cp1y=wr(r.cp1y,t.top,t.bottom)),o&&(r.cp2x=wr(r.cp2x,t.left,t.right),r.cp2y=wr(r.cp2y,t.top,t.bottom)))}(e,n)}function _r(){return"undefined"!==typeof window&&"undefined"!==typeof document}function Sr(e){let t=e.parentNode;return t&&"[object ShadowRoot]"===t.toString()&&(t=t.host),t}function Nr(e,t,n){let i;return"string"===typeof e?(i=parseInt(e,10),-1!==e.indexOf("%")&&(i=i/100*t.parentNode[n])):i=e,i}const Mr=e=>e.ownerDocument.defaultView.getComputedStyle(e,null);const Cr=["top","right","bottom","left"];function Pr(e,t,n){const i={};n=n?"-"+n:"";for(let r=0;r<4;r++){const a=Cr[r];i[a]=parseFloat(e[t+"-"+a+n])||0}return i.width=i.left+i.right,i.height=i.top+i.bottom,i}function Er(e,t){if("native"in e)return e;const{canvas:n,currentDevicePixelRatio:i}=t,r=Mr(n),a="border-box"===r.boxSizing,s=Pr(r,"padding"),o=Pr(r,"border","width"),{x:l,y:c,box:u}=function(e,t){const n=e.touches,i=n&&n.length?n[0]:e,{offsetX:r,offsetY:a}=i;let s,o,l=!1;if(((e,t,n)=>(e>0||t>0)&&(!n||!n.shadowRoot))(r,a,e.target))s=r,o=a;else{const e=t.getBoundingClientRect();s=i.clientX-e.left,o=i.clientY-e.top,l=!0}return{x:s,y:o,box:l}}(e,n),d=s.left+(u&&o.left),h=s.top+(u&&o.top);let{width:f,height:p}=t;return a&&(f-=s.width+o.width,p-=s.height+o.height),{x:Math.round((l-d)/f*n.width/i),y:Math.round((c-h)/p*n.height/i)}}const jr=e=>Math.round(10*e)/10;function Tr(e,t,n,i){const r=Mr(e),a=Pr(r,"margin"),s=Nr(r.maxWidth,e,"clientWidth")||Ln,o=Nr(r.maxHeight,e,"clientHeight")||Ln,l=function(e,t,n){let i,r;if(void 0===t||void 0===n){const a=e&&Sr(e);if(a){const e=a.getBoundingClientRect(),s=Mr(a),o=Pr(s,"border","width"),l=Pr(s,"padding");t=e.width-l.width-o.width,n=e.height-l.height-o.height,i=Nr(s.maxWidth,a,"clientWidth"),r=Nr(s.maxHeight,a,"clientHeight")}else t=e.clientWidth,n=e.clientHeight}return{width:t,height:n,maxWidth:i||Ln,maxHeight:r||Ln}}(e,t,n);let{width:c,height:u}=l;if("content-box"===r.boxSizing){const e=Pr(r,"border","width"),t=Pr(r,"padding");c-=t.width+e.width,u-=t.height+e.height}c=Math.max(0,c-a.width),u=Math.max(0,i?c/i:u-a.height),c=jr(Math.min(c,s,l.maxWidth)),u=jr(Math.min(u,o,l.maxHeight)),c&&!u&&(u=jr(c/2));return(void 0!==t||void 0!==n)&&i&&l.height&&u>l.height&&(u=l.height,c=jr(Math.floor(u*i))),{width:c,height:u}}function Lr(e,t,n){const i=t||1,r=Math.floor(e.height*i),a=Math.floor(e.width*i);e.height=Math.floor(e.height),e.width=Math.floor(e.width);const s=e.canvas;return s.style&&(n||!s.style.height&&!s.style.width)&&(s.style.height="".concat(e.height,"px"),s.style.width="".concat(e.width,"px")),(e.currentDevicePixelRatio!==i||s.height!==r||s.width!==a)&&(e.currentDevicePixelRatio=i,s.height=r,s.width=a,e.ctx.setTransform(i,0,0,i,0,0),!0)}const Or=function(){let e=!1;try{const t={get passive(){return e=!0,!1}};_r()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch(Ec){}return e}();function Ar(e,t){const n=function(e,t){return Mr(e).getPropertyValue(t)}(e,t),i=n&&n.match(/^(\d+)(\.\d+)?px$/);return i?+i[1]:void 0}function Dr(e,t,n,i){return{x:e.x+n*(t.x-e.x),y:e.y+n*(t.y-e.y)}}function Rr(e,t,n,i){return{x:e.x+n*(t.x-e.x),y:"middle"===i?n<.5?e.y:t.y:"after"===i?n<1?e.y:t.y:n>0?t.y:e.y}}function zr(e,t,n,i){const r={x:e.cp2x,y:e.cp2y},a={x:t.cp1x,y:t.cp1y},s=Dr(e,r,n),o=Dr(r,a,n),l=Dr(a,t,n),c=Dr(s,o,n),u=Dr(o,l,n);return Dr(c,u,n)}function Fr(e,t,n){return e?function(e,t){return{x:n=>e+e+t-n,setWidth(e){t=e},textAlign:e=>"center"===e?e:"right"===e?"left":"right",xPlus:(e,t)=>e-t,leftForLtr:(e,t)=>e-t}}(t,n):{x:e=>e,setWidth(e){},textAlign:e=>e,xPlus:(e,t)=>e+t,leftForLtr:(e,t)=>e}}function Ir(e,t){let n,i;"ltr"!==t&&"rtl"!==t||(n=e.canvas.style,i=[n.getPropertyValue("direction"),n.getPropertyPriority("direction")],n.setProperty("direction",t,"important"),e.prevTextDirection=i)}function Wr(e,t){void 0!==t&&(delete e.prevTextDirection,e.canvas.style.setProperty("direction",t[0],t[1]))}function Br(e){return"angle"===e?{between:Xn,compare:Kn,normalize:Qn}:{between:Jn,compare:(e,t)=>e-t,normalize:e=>e}}function Hr(e){let{start:t,end:n,count:i,loop:r,style:a}=e;return{start:t%i,end:n%i,loop:r&&(n-t+1)%i===0,style:a}}function Vr(e,t,n){if(!n)return[e];const{property:i,start:r,end:a}=n,s=t.length,{compare:o,between:l,normalize:c}=Br(i),{start:u,end:d,loop:h,style:f}=function(e,t,n){const{property:i,start:r,end:a}=n,{between:s,normalize:o}=Br(i),l=t.length;let c,u,{start:d,end:h,loop:f}=e;if(f){for(d+=l,h+=l,c=0,u=l;c<u&&s(o(t[d%l][i]),r,a);++c)d--,h--;d%=l,h%=l}return h<d&&(h+=l),{start:d,end:h,loop:f,style:e.style}}(e,t,n),p=[];let m,g,b,y=!1,x=null;const v=()=>y||l(r,b,m)&&0!==o(r,b),w=()=>!y||0===o(a,m)||l(a,b,m);for(let k=u,_=u;k<=d;++k)g=t[k%s],g.skip||(m=c(g[i]),m!==b&&(y=l(m,r,a),null===x&&v()&&(x=0===o(m,r)?k:_),null!==x&&w()&&(p.push(Hr({start:x,end:k,loop:h,count:s,style:f})),x=null),_=k,b=m));return null!==x&&p.push(Hr({start:x,end:d,loop:h,count:s,style:f})),p}function Ur(e,t){const n=[],i=e.segments;for(let r=0;r<i.length;r++){const a=Vr(i[r],e.points,t);a.length&&n.push(...a)}return n}function $r(e,t,n,i){return i&&i.setContext&&n?function(e,t,n,i){const r=e._chart.getContext(),a=Yr(e.options),{_datasetIndex:s,options:{spanGaps:o}}=e,l=n.length,c=[];let u=a,d=t[0].start,h=d;function f(e,t,i,r){const a=o?-1:1;if(e!==t){for(e+=l;n[e%l].skip;)e-=a;for(;n[t%l].skip;)t+=a;e%l!==t%l&&(c.push({start:e%l,end:t%l,loop:i,style:r}),u=r,d=t%l)}}for(const p of t){d=o?d:p.start;let e,t=n[d%l];for(h=d+1;h<=p.end;h++){const a=n[h%l];e=Yr(i.setContext(tr(r,{type:"segment",p0:t,p1:a,p0DataIndex:(h-1)%l,p1DataIndex:h%l,datasetIndex:s}))),qr(e,u)&&f(d,h-1,p.loop,u),t=a,u=e}d<h-1&&f(d,h-1,p.loop,u)}return c}(e,t,n,i):t}function Yr(e){return{backgroundColor:e.backgroundColor,borderCapStyle:e.borderCapStyle,borderDash:e.borderDash,borderDashOffset:e.borderDashOffset,borderJoinStyle:e.borderJoinStyle,borderWidth:e.borderWidth,borderColor:e.borderColor}}function qr(e,t){if(!t)return!1;const n=[],i=function(e,t){return mi(t)?(n.includes(t)||n.push(t),n.indexOf(t)):t};return JSON.stringify(e,i)!==JSON.stringify(t,i)}function Kr(e,t,n){return e.options.clip?e[n]:t[n]}function Qr(e,t){const n=t._clip;if(n.disabled)return!1;const i=function(e,t){const{xScale:n,yScale:i}=e;return n&&i?{left:Kr(n,t,"left"),right:Kr(n,t,"right"),top:Kr(i,t,"top"),bottom:Kr(i,t,"bottom")}:t}(t,e.chartArea);return{left:!1===n.left?0:i.left-(!0===n.left?0:n.left),right:!1===n.right?e.width:i.right+(!0===n.right?0:n.right),top:!1===n.top?0:i.top-(!0===n.top?0:n.top),bottom:!1===n.bottom?e.height:i.bottom+(!0===n.bottom?0:n.bottom)}}class Xr{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(e,t,n,i){const r=t.listeners[i],a=t.duration;r.forEach(i=>i({chart:e,initial:t.initial,numSteps:a,currentStep:Math.min(n-t.start,a)}))}_refresh(){this._request||(this._running=!0,this._request=ai.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Date.now(),t=0;this._charts.forEach((n,i)=>{if(!n.running||!n.items.length)return;const r=n.items;let a,s=r.length-1,o=!1;for(;s>=0;--s)a=r[s],a._active?(a._total>n.duration&&(n.duration=a._total),a.tick(e),o=!0):(r[s]=r[r.length-1],r.pop());o&&(i.draw(),this._notify(i,n,e,"progress")),r.length||(n.running=!1,this._notify(i,n,e,"complete"),n.initial=!1),t+=r.length}),this._lastDate=e,0===t&&(this._running=!1)}_getAnims(e){const t=this._charts;let n=t.get(e);return n||(n={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},t.set(e,n)),n}listen(e,t,n){this._getAnims(e).listeners[t].push(n)}add(e,t){t&&t.length&&this._getAnims(e).items.push(...t)}has(e){return this._getAnims(e).items.length>0}start(e){const t=this._charts.get(e);t&&(t.running=!0,t.start=Date.now(),t.duration=t.items.reduce((e,t)=>Math.max(e,t._duration),0),this._refresh())}running(e){if(!this._running)return!1;const t=this._charts.get(e);return!!(t&&t.running&&t.items.length)}stop(e){const t=this._charts.get(e);if(!t||!t.items.length)return;const n=t.items;let i=n.length-1;for(;i>=0;--i)n[i].cancel();t.items=[],this._notify(e,t,Date.now(),"complete")}remove(e){return this._charts.delete(e)}}var Gr=new Xr;const Jr="transparent",Zr={boolean:(e,t,n)=>n>.5?t:e,color(e,t,n){const i=gi(e||Jr),r=i.valid&&gi(t||Jr);return r&&r.valid?r.mix(i,n).hexString():t},number:(e,t,n)=>e+(t-e)*n};class ea{constructor(e,t,n,i){const r=t[n];i=er([e.to,i,r,e.from]);const a=er([e.from,r,i]);this._active=!0,this._fn=e.fn||Zr[e.type||typeof a],this._easing=pi[e.easing]||pi.linear,this._start=Math.floor(Date.now()+(e.delay||0)),this._duration=this._total=Math.floor(e.duration),this._loop=!!e.loop,this._target=t,this._prop=n,this._from=a,this._to=i,this._promises=void 0}active(){return this._active}update(e,t,n){if(this._active){this._notify(!1);const i=this._target[this._prop],r=n-this._start,a=this._duration-r;this._start=n,this._duration=Math.floor(Math.max(a,e.duration)),this._total+=r,this._loop=!!e.loop,this._to=er([e.to,t,i,e.from]),this._from=er([e.from,i,t])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(e){const t=e-this._start,n=this._duration,i=this._prop,r=this._from,a=this._loop,s=this._to;let o;if(this._active=r!==s&&(a||t<n),!this._active)return this._target[i]=s,void this._notify(!0);t<0?this._target[i]=r:(o=t/n%2,o=a&&o>1?2-o:o,o=this._easing(Math.min(1,Math.max(0,o))),this._target[i]=this._fn(r,s,o))}wait(){const e=this._promises||(this._promises=[]);return new Promise((t,n)=>{e.push({res:t,rej:n})})}_notify(e){const t=e?"res":"rej",n=this._promises||[];for(let i=0;i<n.length;i++)n[i][t]()}}class ta{constructor(e,t){this._chart=e,this._properties=new Map,this.configure(t)}configure(e){if(!cn(e))return;const t=Object.keys(Ei.animation),n=this._properties;Object.getOwnPropertyNames(e).forEach(i=>{const r=e[i];if(!cn(r))return;const a={};for(const e of t)a[e]=r[e];(ln(r.properties)&&r.properties||[i]).forEach(e=>{e!==i&&n.has(e)||n.set(e,a)})})}_animateOptions(e,t){const n=t.options,i=function(e,t){if(!t)return;let n=e.options;if(!n)return void(e.options=t);n.$shared&&(e.options=n=Object.assign({},n,{$shared:!1,$animations:{}}));return n}(e,n);if(!i)return[];const r=this._createAnimations(i,n);return n.$shared&&function(e,t){const n=[],i=Object.keys(t);for(let r=0;r<i.length;r++){const t=e[i[r]];t&&t.active()&&n.push(t.wait())}return Promise.all(n)}(e.options.$animations,n).then(()=>{e.options=n},()=>{}),r}_createAnimations(e,t){const n=this._properties,i=[],r=e.$animations||(e.$animations={}),a=Object.keys(t),s=Date.now();let o;for(o=a.length-1;o>=0;--o){const l=a[o];if("$"===l.charAt(0))continue;if("options"===l){i.push(...this._animateOptions(e,t));continue}const c=t[l];let u=r[l];const d=n.get(l);if(u){if(d&&u.active()){u.update(d,c,s);continue}u.cancel()}d&&d.duration?(r[l]=u=new ea(d,e,l,c),i.push(u)):e[l]=c}return i}update(e,t){if(0===this._properties.size)return void Object.assign(e,t);const n=this._createAnimations(e,t);return n.length?(Gr.add(this._chart,n),!0):void 0}}function na(e,t){const n=e&&e.options||{},i=n.reverse,r=void 0===n.min?t:0,a=void 0===n.max?t:0;return{start:i?a:r,end:i?r:a}}function ia(e,t){const n=[],i=e._getSortedDatasetMetas(t);let r,a;for(r=0,a=i.length;r<a;++r)n.push(i[r].index);return n}function ra(e,t,n){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const r=e.keys,a="single"===i.mode;let s,o,l,c;if(null===t)return;let u=!1;for(s=0,o=r.length;s<o;++s){if(l=+r[s],l===n){if(u=!0,i.all)continue;break}c=e.values[l],un(c)&&(a||0===t||Fn(t)===Fn(c))&&(t+=c)}return u||i.all?t:0}function aa(e,t){const n=e&&e.options.stacked;return n||void 0===n&&void 0!==t.stack}function sa(e,t,n){const i=e[t]||(e[t]={});return i[n]||(i[n]={})}function oa(e,t,n,i){for(const r of t.getMatchingVisibleMetas(i).reverse()){const t=e[r.index];if(n&&t>0||!n&&t<0)return r.index}return null}function la(e,t){const{chart:n,_cachedMeta:i}=e,r=n._stacks||(n._stacks={}),{iScale:a,vScale:s,index:o}=i,l=a.axis,c=s.axis,u=function(e,t,n){return"".concat(e.id,".").concat(t.id,".").concat(n.stack||n.type)}(a,s,i),d=t.length;let h;for(let f=0;f<d;++f){const e=t[f],{[l]:n,[c]:a}=e;h=(e._stacks||(e._stacks={}))[c]=sa(r,u,n),h[o]=a,h._top=oa(h,s,!0,i.type),h._bottom=oa(h,s,!1,i.type);(h._visualValues||(h._visualValues={}))[o]=a}}function ca(e,t){const n=e.scales;return Object.keys(n).filter(e=>n[e].axis===t).shift()}function ua(e,t){const n=e.controller.index,i=e.vScale&&e.vScale.axis;if(i){t=t||e._parsed;for(const e of t){const t=e._stacks;if(!t||void 0===t[i]||void 0===t[i][n])return;delete t[i][n],void 0!==t[i]._visualValues&&void 0!==t[i]._visualValues[n]&&delete t[i]._visualValues[n]}}}const da=e=>"reset"===e||"none"===e,ha=(e,t)=>t?e:Object.assign({},e);class fa{constructor(e,t){this.chart=e,this._ctx=e.ctx,this.index=t,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const e=this._cachedMeta;this.configure(),this.linkScales(),e._stacked=aa(e.vScale,e),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(e){this.index!==e&&ua(this._cachedMeta),this.index=e}linkScales(){const e=this.chart,t=this._cachedMeta,n=this.getDataset(),i=(e,t,n,i)=>"x"===e?t:"r"===e?i:n,r=t.xAxisID=hn(n.xAxisID,ca(e,"x")),a=t.yAxisID=hn(n.yAxisID,ca(e,"y")),s=t.rAxisID=hn(n.rAxisID,ca(e,"r")),o=t.indexAxis,l=t.iAxisID=i(o,r,a,s),c=t.vAxisID=i(o,a,r,s);t.xScale=this.getScaleForId(r),t.yScale=this.getScaleForId(a),t.rScale=this.getScaleForId(s),t.iScale=this.getScaleForId(l),t.vScale=this.getScaleForId(c)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(e){return this.chart.scales[e]}_getOtherScale(e){const t=this._cachedMeta;return e===t.iScale?t.vScale:t.iScale}reset(){this._update("reset")}_destroy(){const e=this._cachedMeta;this._data&&ii(this._data,this),e._stacked&&ua(e)}_dataCheck(){const e=this.getDataset(),t=e.data||(e.data=[]),n=this._data;if(cn(t)){const e=this._cachedMeta;this._data=function(e,t){const{iScale:n,vScale:i}=t,r="x"===n.axis?"x":"y",a="x"===i.axis?"x":"y",s=Object.keys(e),o=new Array(s.length);let l,c,u;for(l=0,c=s.length;l<c;++l)u=s[l],o[l]={[r]:u,[a]:e[u]};return o}(t,e)}else if(n!==t){if(n){ii(n,this);const e=this._cachedMeta;ua(e),e._parsed=[]}t&&Object.isExtensible(t)&&(r=this,(i=t)._chartjs?i._chartjs.listeners.push(r):(Object.defineProperty(i,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[r]}}),ni.forEach(e=>{const t="_onData"+Nn(e),n=i[e];Object.defineProperty(i,e,{configurable:!0,enumerable:!1,value(){for(var e=arguments.length,r=new Array(e),a=0;a<e;a++)r[a]=arguments[a];const s=n.apply(this,r);return i._chartjs.listeners.forEach(e=>{"function"===typeof e[t]&&e[t](...r)}),s}})}))),this._syncList=[],this._data=t}var i,r}addElements(){const e=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(e.dataset=new this.datasetElementType)}buildOrUpdateElements(e){const t=this._cachedMeta,n=this.getDataset();let i=!1;this._dataCheck();const r=t._stacked;t._stacked=aa(t.vScale,t),t.stack!==n.stack&&(i=!0,ua(t),t.stack=n.stack),this._resyncElements(e),(i||r!==t._stacked)&&(la(this,t._parsed),t._stacked=aa(t.vScale,t))}configure(){const e=this.chart.config,t=e.datasetScopeKeys(this._type),n=e.getOptionScopes(this.getDataset(),t,!0);this.options=e.createResolver(n,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(e,t){const{_cachedMeta:n,_data:i}=this,{iScale:r,_stacked:a}=n,s=r.axis;let o,l,c,u=0===e&&t===i.length||n._sorted,d=e>0&&n._parsed[e-1];if(!1===this._parsing)n._parsed=i,n._sorted=!0,c=i;else{c=ln(i[e])?this.parseArrayData(n,i,e,t):cn(i[e])?this.parseObjectData(n,i,e,t):this.parsePrimitiveData(n,i,e,t);const r=()=>null===l[s]||d&&l[s]<d[s];for(o=0;o<t;++o)n._parsed[o+e]=l=c[o],u&&(r()&&(u=!1),d=l);n._sorted=u}a&&la(this,c)}parsePrimitiveData(e,t,n,i){const{iScale:r,vScale:a}=e,s=r.axis,o=a.axis,l=r.getLabels(),c=r===a,u=new Array(i);let d,h,f;for(d=0,h=i;d<h;++d)f=d+n,u[d]={[s]:c||r.parse(l[f],f),[o]:a.parse(t[f],f)};return u}parseArrayData(e,t,n,i){const{xScale:r,yScale:a}=e,s=new Array(i);let o,l,c,u;for(o=0,l=i;o<l;++o)c=o+n,u=t[c],s[o]={x:r.parse(u[0],c),y:a.parse(u[1],c)};return s}parseObjectData(e,t,n,i){const{xScale:r,yScale:a}=e,{xAxisKey:s="x",yAxisKey:o="y"}=this._parsing,l=new Array(i);let c,u,d,h;for(c=0,u=i;c<u;++c)d=c+n,h=t[d],l[c]={x:r.parse(Sn(h,s),d),y:a.parse(Sn(h,o),d)};return l}getParsed(e){return this._cachedMeta._parsed[e]}getDataElement(e){return this._cachedMeta.data[e]}applyStack(e,t,n){const i=this.chart,r=this._cachedMeta,a=t[e.axis];return ra({keys:ia(i,!0),values:t._stacks[e.axis]._visualValues},a,r.index,{mode:n})}updateRangeFromParsed(e,t,n,i){const r=n[t.axis];let a=null===r?NaN:r;const s=i&&n._stacks[t.axis];i&&s&&(i.values=s,a=ra(i,r,this._cachedMeta.index)),e.min=Math.min(e.min,a),e.max=Math.max(e.max,a)}getMinMax(e,t){const n=this._cachedMeta,i=n._parsed,r=n._sorted&&e===n.iScale,a=i.length,s=this._getOtherScale(e),o=((e,t,n)=>e&&!t.hidden&&t._stacked&&{keys:ia(n,!0),values:null})(t,n,this.chart),l={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:c,max:u}=function(e){const{min:t,max:n,minDefined:i,maxDefined:r}=e.getUserBounds();return{min:i?t:Number.NEGATIVE_INFINITY,max:r?n:Number.POSITIVE_INFINITY}}(s);let d,h;function f(){h=i[d];const t=h[s.axis];return!un(h[e.axis])||c>t||u<t}for(d=0;d<a&&(f()||(this.updateRangeFromParsed(l,e,h,o),!r));++d);if(r)for(d=a-1;d>=0;--d)if(!f()){this.updateRangeFromParsed(l,e,h,o);break}return l}getAllParsedValues(e){const t=this._cachedMeta._parsed,n=[];let i,r,a;for(i=0,r=t.length;i<r;++i)a=t[i][e.axis],un(a)&&n.push(a);return n}getMaxOverflow(){return!1}getLabelAndValue(e){const t=this._cachedMeta,n=t.iScale,i=t.vScale,r=this.getParsed(e);return{label:n?""+n.getLabelForValue(r[n.axis]):"",value:i?""+i.getLabelForValue(r[i.axis]):""}}_update(e){const t=this._cachedMeta;this.update(e||"default"),t._clip=function(e){let t,n,i,r;return cn(e)?(t=e.top,n=e.right,i=e.bottom,r=e.left):t=n=i=r=e,{top:t,right:n,bottom:i,left:r,disabled:!1===e}}(hn(this.options.clip,function(e,t,n){if(!1===n)return!1;const i=na(e,n),r=na(t,n);return{top:r.end,right:i.end,bottom:r.start,left:i.start}}(t.xScale,t.yScale,this.getMaxOverflow())))}update(e){}draw(){const e=this._ctx,t=this.chart,n=this._cachedMeta,i=n.data||[],r=t.chartArea,a=[],s=this._drawStart||0,o=this._drawCount||i.length-s,l=this.options.drawActiveElementsOnTop;let c;for(n.dataset&&n.dataset.draw(e,r,s,o),c=s;c<s+o;++c){const t=i[c];t.hidden||(t.active&&l?a.push(t):t.draw(e,r))}for(c=0;c<a.length;++c)a[c].draw(e,r)}getStyle(e,t){const n=t?"active":"default";return void 0===e&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(n):this.resolveDataElementOptions(e||0,n)}getContext(e,t,n){const i=this.getDataset();let r;if(e>=0&&e<this._cachedMeta.data.length){const t=this._cachedMeta.data[e];r=t.$context||(t.$context=function(e,t,n){return tr(e,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:n,index:t,mode:"default",type:"data"})}(this.getContext(),e,t)),r.parsed=this.getParsed(e),r.raw=i.data[e],r.index=r.dataIndex=e}else r=this.$context||(this.$context=function(e,t){return tr(e,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}(this.chart.getContext(),this.index)),r.dataset=i,r.index=r.datasetIndex=this.index;return r.active=!!t,r.mode=n,r}resolveDatasetElementOptions(e){return this._resolveElementOptions(this.datasetElementType.id,e)}resolveDataElementOptions(e,t){return this._resolveElementOptions(this.dataElementType.id,t,e)}_resolveElementOptions(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default",n=arguments.length>2?arguments[2]:void 0;const i="active"===t,r=this._cachedDataOpts,a=e+"-"+t,s=r[a],o=this.enableOptionSharing&&Mn(n);if(s)return ha(s,o);const l=this.chart.config,c=l.datasetElementScopeKeys(this._type,e),u=i?["".concat(e,"Hover"),"hover",e,""]:[e,""],d=l.getOptionScopes(this.getDataset(),c),h=Object.keys(Ei.elements[e]),f=l.resolveNamedOptions(d,h,()=>this.getContext(n,i,t),u);return f.$shared&&(f.$shared=o,r[a]=Object.freeze(ha(f,o))),f}_resolveAnimations(e,t,n){const i=this.chart,r=this._cachedDataOpts,a="animation-".concat(t),s=r[a];if(s)return s;let o;if(!1!==i.options.animation){const i=this.chart.config,r=i.datasetAnimationScopeKeys(this._type,t),a=i.getOptionScopes(this.getDataset(),r);o=i.createResolver(a,this.getContext(e,n,t))}const l=new ta(i,o&&o.animations);return o&&o._cacheable&&(r[a]=Object.freeze(l)),l}getSharedOptions(e){if(e.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},e))}includeOptions(e,t){return!t||da(e)||this.chart._animationsDisabled}_getSharedOptions(e,t){const n=this.resolveDataElementOptions(e,t),i=this._sharedOptions,r=this.getSharedOptions(n),a=this.includeOptions(t,r)||r!==i;return this.updateSharedOptions(r,t,n),{sharedOptions:r,includeOptions:a}}updateElement(e,t,n,i){da(i)?Object.assign(e,n):this._resolveAnimations(t,i).update(e,n)}updateSharedOptions(e,t,n){e&&!da(t)&&this._resolveAnimations(void 0,t).update(e,n)}_setStyle(e,t,n,i){e.active=i;const r=this.getStyle(t,i);this._resolveAnimations(t,n,i).update(e,{options:!i&&this.getSharedOptions(r)||r})}removeHoverStyle(e,t,n){this._setStyle(e,n,"active",!1)}setHoverStyle(e,t,n){this._setStyle(e,n,"active",!0)}_removeDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!1)}_setDatasetHoverStyle(){const e=this._cachedMeta.dataset;e&&this._setStyle(e,void 0,"active",!0)}_resyncElements(e){const t=this._data,n=this._cachedMeta.data;for(const[s,o,l]of this._syncList)this[s](o,l);this._syncList=[];const i=n.length,r=t.length,a=Math.min(r,i);a&&this.parse(0,a),r>i?this._insertElements(i,r-i,e):r<i&&this._removeElements(r,i-r)}_insertElements(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];const i=this._cachedMeta,r=i.data,a=e+t;let s;const o=e=>{for(e.length+=t,s=e.length-1;s>=a;s--)e[s]=e[s-t]};for(o(r),s=e;s<a;++s)r[s]=new this.dataElementType;this._parsing&&o(i._parsed),this.parse(e,t),n&&this.updateElements(r,e,t,"reset")}updateElements(e,t,n,i){}_removeElements(e,t){const n=this._cachedMeta;if(this._parsing){const i=n._parsed.splice(e,t);n._stacked&&ua(n,i)}n.data.splice(e,t)}_sync(e){if(this._parsing)this._syncList.push(e);else{const[t,n,i]=e;this[t](n,i)}this.chart._dataChanges.push([this.index,...e])}_onDataPush(){const e=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-e,e])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(e,t){t&&this._sync(["_removeElements",e,t]);const n=arguments.length-2;n&&this._sync(["_insertElements",e,n])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}function pa(e){const t=e.iScale,n=function(e,t){if(!e._cache.$bar){const n=e.getMatchingVisibleMetas(t);let i=[];for(let t=0,r=n.length;t<r;t++)i=i.concat(n[t].controller.getAllParsedValues(e));e._cache.$bar=ri(i.sort((e,t)=>e-t))}return e._cache.$bar}(t,e.type);let i,r,a,s,o=t._length;const l=()=>{32767!==a&&-32768!==a&&(Mn(s)&&(o=Math.min(o,Math.abs(a-s)||o)),s=a)};for(i=0,r=n.length;i<r;++i)a=t.getPixelForValue(n[i]),l();for(s=void 0,i=0,r=t.ticks.length;i<r;++i)a=t.getPixelForTick(i),l();return o}function ma(e,t,n,i){return ln(e)?function(e,t,n,i){const r=n.parse(e[0],i),a=n.parse(e[1],i),s=Math.min(r,a),o=Math.max(r,a);let l=s,c=o;Math.abs(s)>Math.abs(o)&&(l=o,c=s),t[n.axis]=c,t._custom={barStart:l,barEnd:c,start:r,end:a,min:s,max:o}}(e,t,n,i):t[n.axis]=n.parse(e,i),t}function ga(e,t,n,i){const r=e.iScale,a=e.vScale,s=r.getLabels(),o=r===a,l=[];let c,u,d,h;for(c=n,u=n+i;c<u;++c)h=t[c],d={},d[r.axis]=o||r.parse(s[c],c),l.push(ma(h,d,a,c));return l}function ba(e){return e&&void 0!==e.barStart&&void 0!==e.barEnd}function ya(e,t,n,i){let r=t.borderSkipped;const a={};if(!r)return void(e.borderSkipped=a);if(!0===r)return void(e.borderSkipped={top:!0,right:!0,bottom:!0,left:!0});const{start:s,end:o,reverse:l,top:c,bottom:u}=function(e){let t,n,i,r,a;return e.horizontal?(t=e.base>e.x,n="left",i="right"):(t=e.base<e.y,n="bottom",i="top"),t?(r="end",a="start"):(r="start",a="end"),{start:n,end:i,reverse:t,top:r,bottom:a}}(e);"middle"===r&&n&&(e.enableBorderRadius=!0,(n._top||0)===i?r=c:(n._bottom||0)===i?r=u:(a[xa(u,s,o,l)]=!0,r=c)),a[xa(r,s,o,l)]=!0,e.borderSkipped=a}function xa(e,t,n,i){var r,a,s;return i?(s=n,e=va(e=(r=e)===(a=t)?s:r===s?a:r,n,t)):e=va(e,t,n),e}function va(e,t,n){return"start"===e?t:"end"===e?n:e}function wa(e,t,n){let{inflateAmount:i}=t;e.inflateAmount="auto"===i?1===n?.33:0:i}l(fa,"defaults",{}),l(fa,"datasetElementType",null),l(fa,"dataElementType",null);class ka extends fa{parsePrimitiveData(e,t,n,i){return ga(e,t,n,i)}parseArrayData(e,t,n,i){return ga(e,t,n,i)}parseObjectData(e,t,n,i){const{iScale:r,vScale:a}=e,{xAxisKey:s="x",yAxisKey:o="y"}=this._parsing,l="x"===r.axis?s:o,c="x"===a.axis?s:o,u=[];let d,h,f,p;for(d=n,h=n+i;d<h;++d)p=t[d],f={},f[r.axis]=r.parse(Sn(p,l),d),u.push(ma(Sn(p,c),f,a,d));return u}updateRangeFromParsed(e,t,n,i){super.updateRangeFromParsed(e,t,n,i);const r=n._custom;r&&t===this._cachedMeta.vScale&&(e.min=Math.min(e.min,r.min),e.max=Math.max(e.max,r.max))}getMaxOverflow(){return 0}getLabelAndValue(e){const t=this._cachedMeta,{iScale:n,vScale:i}=t,r=this.getParsed(e),a=r._custom,s=ba(a)?"["+a.start+", "+a.end+"]":""+i.getLabelForValue(r[i.axis]);return{label:""+n.getLabelForValue(r[n.axis]),value:s}}initialize(){this.enableOptionSharing=!0,super.initialize();this._cachedMeta.stack=this.getDataset().stack}update(e){const t=this._cachedMeta;this.updateElements(t.data,0,t.data.length,e)}updateElements(e,t,n,i){const r="reset"===i,{index:a,_cachedMeta:{vScale:s}}=this,o=s.getBasePixel(),l=s.isHorizontal(),c=this._getRuler(),{sharedOptions:u,includeOptions:d}=this._getSharedOptions(t,i);for(let h=t;h<t+n;h++){const t=this.getParsed(h),n=r||on(t[s.axis])?{base:o,head:o}:this._calculateBarValuePixels(h),f=this._calculateBarIndexPixels(h,c),p=(t._stacks||{})[s.axis],m={horizontal:l,base:n.base,enableBorderRadius:!p||ba(t._custom)||a===p._top||a===p._bottom,x:l?n.head:f.center,y:l?f.center:n.head,height:l?f.size:Math.abs(n.size),width:l?Math.abs(n.size):f.size};d&&(m.options=u||this.resolveDataElementOptions(h,e[h].active?"active":i));const g=m.options||e[h].options;ya(m,g,p,a),wa(m,g,c.ratio),this.updateElement(e[h],h,m,i)}}_getStacks(e,t){const{iScale:n}=this._cachedMeta,i=n.getMatchingVisibleMetas(this._type).filter(e=>e.controller.options.grouped),r=n.options.stacked,a=[],s=this._cachedMeta.controller.getParsed(t),o=s&&s[n.axis],l=e=>{const t=e._parsed.find(e=>e[n.axis]===o),i=t&&t[e.vScale.axis];if(on(i)||isNaN(i))return!0};for(const c of i)if((void 0===t||!l(c))&&((!1===r||-1===a.indexOf(c.stack)||void 0===r&&void 0===c.stack)&&a.push(c.stack),c.index===e))break;return a.length||a.push(void 0),a}_getStackCount(e){return this._getStacks(void 0,e).length}_getAxisCount(){return this._getAxis().length}getFirstScaleIdForIndexAxis(){const e=this.chart.scales,t=this.chart.options.indexAxis;return Object.keys(e).filter(n=>e[n].axis===t).shift()}_getAxis(){const e={},t=this.getFirstScaleIdForIndexAxis();for(const n of this.chart.data.datasets)e[hn("x"===this.chart.options.indexAxis?n.xAxisID:n.yAxisID,t)]=!0;return Object.keys(e)}_getStackIndex(e,t,n){const i=this._getStacks(e,n),r=void 0!==t?i.indexOf(t):-1;return-1===r?i.length-1:r}_getRuler(){const e=this.options,t=this._cachedMeta,n=t.iScale,i=[];let r,a;for(r=0,a=t.data.length;r<a;++r)i.push(n.getPixelForValue(this.getParsed(r)[n.axis],r));const s=e.barThickness;return{min:s||pa(t),pixels:i,start:n._startPixel,end:n._endPixel,stackCount:this._getStackCount(),scale:n,grouped:e.grouped,ratio:s?1:e.categoryPercentage*e.barPercentage}}_calculateBarValuePixels(e){const{_cachedMeta:{vScale:t,_stacked:n,index:i},options:{base:r,minBarLength:a}}=this,s=r||0,o=this.getParsed(e),l=o._custom,c=ba(l);let u,d,h=o[t.axis],f=0,p=n?this.applyStack(t,o,n):h;p!==h&&(f=p-h,p=h),c&&(h=l.barStart,p=l.barEnd-l.barStart,0!==h&&Fn(h)!==Fn(l.barEnd)&&(f=0),f+=h);const m=on(r)||c?f:r;let g=t.getPixelForValue(m);if(u=this.chart.getDataVisibility(e)?t.getPixelForValue(f+p):g,d=u-g,Math.abs(d)<a){d=function(e,t,n){return 0!==e?Fn(e):(t.isHorizontal()?1:-1)*(t.min>=n?1:-1)}(d,t,s)*a,h===s&&(g-=d/2);const e=t.getPixelForDecimal(0),r=t.getPixelForDecimal(1),l=Math.min(e,r),f=Math.max(e,r);g=Math.max(Math.min(g,f),l),u=g+d,n&&!c&&(o._stacks[t.axis]._visualValues[i]=t.getValueForPixel(u)-t.getValueForPixel(g))}if(g===t.getPixelForValue(s)){const e=Fn(d)*t.getLineWidthForValue(s)/2;g+=e,d-=e}return{size:d,base:g,head:u,center:u+d/2}}_calculateBarIndexPixels(e,t){const n=t.scale,i=this.options,r=i.skipNull,a=hn(i.maxBarThickness,1/0);let s,o;const l=this._getAxisCount();if(t.grouped){const n=r?this._getStackCount(e):t.stackCount,c="flex"===i.barThickness?function(e,t,n,i){const r=t.pixels,a=r[e];let s=e>0?r[e-1]:null,o=e<r.length-1?r[e+1]:null;const l=n.categoryPercentage;null===s&&(s=a-(null===o?t.end-t.start:o-a)),null===o&&(o=a+a-s);const c=a-(a-Math.min(s,o))/2*l;return{chunk:Math.abs(o-s)/2*l/i,ratio:n.barPercentage,start:c}}(e,t,i,n*l):function(e,t,n,i){const r=n.barThickness;let a,s;return on(r)?(a=t.min*n.categoryPercentage,s=n.barPercentage):(a=r*i,s=1),{chunk:a/i,ratio:s,start:t.pixels[e]-a/2}}(e,t,i,n*l),u="x"===this.chart.options.indexAxis?this.getDataset().xAxisID:this.getDataset().yAxisID,d=this._getAxis().indexOf(hn(u,this.getFirstScaleIdForIndexAxis())),h=this._getStackIndex(this.index,this._cachedMeta.stack,r?e:void 0)+d;s=c.start+c.chunk*h+c.chunk/2,o=Math.min(a,c.chunk*c.ratio)}else s=n.getPixelForValue(this.getParsed(e)[n.axis],e),o=Math.min(a,t.min*t.ratio);return{base:s-o/2,head:s+o/2,center:s,size:o}}draw(){const e=this._cachedMeta,t=e.vScale,n=e.data,i=n.length;let r=0;for(;r<i;++r)null===this.getParsed(r)[t.axis]||n[r].hidden||n[r].draw(this._ctx)}}l(ka,"id","bar"),l(ka,"defaults",{datasetElementType:!1,dataElementType:"bar",categoryPercentage:.8,barPercentage:.9,grouped:!0,animations:{numbers:{type:"number",properties:["x","y","base","width","height"]}}}),l(ka,"overrides",{scales:{_index_:{type:"category",offset:!0,grid:{offset:!0}},_value_:{type:"linear",beginAtZero:!0}}});class _a extends fa{initialize(){this.enableOptionSharing=!0,super.initialize()}parsePrimitiveData(e,t,n,i){const r=super.parsePrimitiveData(e,t,n,i);for(let a=0;a<r.length;a++)r[a]._custom=this.resolveDataElementOptions(a+n).radius;return r}parseArrayData(e,t,n,i){const r=super.parseArrayData(e,t,n,i);for(let a=0;a<r.length;a++){const e=t[n+a];r[a]._custom=hn(e[2],this.resolveDataElementOptions(a+n).radius)}return r}parseObjectData(e,t,n,i){const r=super.parseObjectData(e,t,n,i);for(let a=0;a<r.length;a++){const e=t[n+a];r[a]._custom=hn(e&&e.r&&+e.r,this.resolveDataElementOptions(a+n).radius)}return r}getMaxOverflow(){const e=this._cachedMeta.data;let t=0;for(let n=e.length-1;n>=0;--n)t=Math.max(t,e[n].size(this.resolveDataElementOptions(n))/2);return t>0&&t}getLabelAndValue(e){const t=this._cachedMeta,n=this.chart.data.labels||[],{xScale:i,yScale:r}=t,a=this.getParsed(e),s=i.getLabelForValue(a.x),o=r.getLabelForValue(a.y),l=a._custom;return{label:n[e]||"",value:"("+s+", "+o+(l?", "+l:"")+")"}}update(e){const t=this._cachedMeta.data;this.updateElements(t,0,t.length,e)}updateElements(e,t,n,i){const r="reset"===i,{iScale:a,vScale:s}=this._cachedMeta,{sharedOptions:o,includeOptions:l}=this._getSharedOptions(t,i),c=a.axis,u=s.axis;for(let d=t;d<t+n;d++){const t=e[d],n=!r&&this.getParsed(d),h={},f=h[c]=r?a.getPixelForDecimal(.5):a.getPixelForValue(n[c]),p=h[u]=r?s.getBasePixel():s.getPixelForValue(n[u]);h.skip=isNaN(f)||isNaN(p),l&&(h.options=o||this.resolveDataElementOptions(d,t.active?"active":i),r&&(h.options.radius=0)),this.updateElement(t,d,h,i)}}resolveDataElementOptions(e,t){const n=this.getParsed(e);let i=super.resolveDataElementOptions(e,t);i.$shared&&(i=Object.assign({},i,{$shared:!1}));const r=i.radius;return"active"!==t&&(i.radius=0),i.radius+=hn(n&&n._custom,r),i}}l(_a,"id","bubble"),l(_a,"defaults",{datasetElementType:!1,dataElementType:"point",animations:{numbers:{type:"number",properties:["x","y","borderWidth","radius"]}}}),l(_a,"overrides",{scales:{x:{type:"linear"},y:{type:"linear"}}});class Sa extends fa{constructor(e,t){super(e,t),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(e,t){const n=this.getDataset().data,i=this._cachedMeta;if(!1===this._parsing)i._parsed=n;else{let r,a,s=e=>+n[e];if(cn(n[e])){const{key:e="value"}=this._parsing;s=t=>+Sn(n[t],e)}for(r=e,a=e+t;r<a;++r)i._parsed[r]=s(r)}}_getRotation(){return Vn(this.options.rotation-90)}_getCircumference(){return Vn(this.options.circumference)}_getRotationExtents(){let e=jn,t=-jn;for(let n=0;n<this.chart.data.datasets.length;++n)if(this.chart.isDatasetVisible(n)&&this.chart.getDatasetMeta(n).type===this._type){const i=this.chart.getDatasetMeta(n).controller,r=i._getRotation(),a=i._getCircumference();e=Math.min(e,r),t=Math.max(t,r+a)}return{rotation:e,circumference:t-e}}update(e){const t=this.chart,{chartArea:n}=t,i=this._cachedMeta,r=i.data,a=this.getMaxBorderWidth()+this.getMaxOffset(r)+this.options.spacing,s=Math.max((Math.min(n.width,n.height)-a)/2,0),o=Math.min((l=this.options.cutout,c=s,"string"===typeof l&&l.endsWith("%")?parseFloat(l)/100:+l/c),1);var l,c;const u=this._getRingWeight(this.index),{circumference:d,rotation:h}=this._getRotationExtents(),{ratioX:f,ratioY:p,offsetX:m,offsetY:g}=function(e,t,n){let i=1,r=1,a=0,s=0;if(t<jn){const o=e,l=o+t,c=Math.cos(o),u=Math.sin(o),d=Math.cos(l),h=Math.sin(l),f=(e,t,i)=>Xn(e,o,l,!0)?1:Math.max(t,t*n,i,i*n),p=(e,t,i)=>Xn(e,o,l,!0)?-1:Math.min(t,t*n,i,i*n),m=f(0,c,d),g=f(An,u,h),b=p(En,c,d),y=p(En+An,u,h);i=(m-b)/2,r=(g-y)/2,a=-(m+b)/2,s=-(g+y)/2}return{ratioX:i,ratioY:r,offsetX:a,offsetY:s}}(h,d,o),b=(n.width-a)/f,y=(n.height-a)/p,x=Math.max(Math.min(b,y)/2,0),v=fn(this.options.radius,x),w=(v-Math.max(v*o,0))/this._getVisibleDatasetWeightTotal();this.offsetX=m*v,this.offsetY=g*v,i.total=this.calculateTotal(),this.outerRadius=v-w*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-w*u,0),this.updateElements(r,0,r.length,e)}_circumference(e,t){const n=this.options,i=this._cachedMeta,r=this._getCircumference();return t&&n.animation.animateRotate||!this.chart.getDataVisibility(e)||null===i._parsed[e]||i.data[e].hidden?0:this.calculateCircumference(i._parsed[e]*r/jn)}updateElements(e,t,n,i){const r="reset"===i,a=this.chart,s=a.chartArea,o=a.options.animation,l=(s.left+s.right)/2,c=(s.top+s.bottom)/2,u=r&&o.animateScale,d=u?0:this.innerRadius,h=u?0:this.outerRadius,{sharedOptions:f,includeOptions:p}=this._getSharedOptions(t,i);let m,g=this._getRotation();for(m=0;m<t;++m)g+=this._circumference(m,r);for(m=t;m<t+n;++m){const t=this._circumference(m,r),n=e[m],a={x:l+this.offsetX,y:c+this.offsetY,startAngle:g,endAngle:g+t,circumference:t,outerRadius:h,innerRadius:d};p&&(a.options=f||this.resolveDataElementOptions(m,n.active?"active":i)),g+=t,this.updateElement(n,m,a,i)}}calculateTotal(){const e=this._cachedMeta,t=e.data;let n,i=0;for(n=0;n<t.length;n++){const r=e._parsed[n];null===r||isNaN(r)||!this.chart.getDataVisibility(n)||t[n].hidden||(i+=Math.abs(r))}return i}calculateCircumference(e){const t=this._cachedMeta.total;return t>0&&!isNaN(e)?jn*(Math.abs(e)/t):0}getLabelAndValue(e){const t=this._cachedMeta,n=this.chart,i=n.data.labels||[],r=wi(t._parsed[e],n.options.locale);return{label:i[e]||"",value:r}}getMaxBorderWidth(e){let t=0;const n=this.chart;let i,r,a,s,o;if(!e)for(i=0,r=n.data.datasets.length;i<r;++i)if(n.isDatasetVisible(i)){a=n.getDatasetMeta(i),e=a.data,s=a.controller;break}if(!e)return 0;for(i=0,r=e.length;i<r;++i)o=s.resolveDataElementOptions(i),"inner"!==o.borderAlign&&(t=Math.max(t,o.borderWidth||0,o.hoverBorderWidth||0));return t}getMaxOffset(e){let t=0;for(let n=0,i=e.length;n<i;++n){const e=this.resolveDataElementOptions(n);t=Math.max(t,e.offset||0,e.hoverOffset||0)}return t}_getRingWeightOffset(e){let t=0;for(let n=0;n<e;++n)this.chart.isDatasetVisible(n)&&(t+=this._getRingWeight(n));return t}_getRingWeight(e){return Math.max(hn(this.chart.data.datasets[e].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}l(Sa,"id","doughnut"),l(Sa,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),l(Sa,"descriptors",{_scriptable:e=>"spacing"!==e,_indexable:e=>"spacing"!==e&&!e.startsWith("borderDash")&&!e.startsWith("hoverBorderDash")}),l(Sa,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(e){const t=e.data;if(t.labels.length&&t.datasets.length){const{labels:{pointStyle:n,color:i}}=e.legend.options;return t.labels.map((t,r)=>{const a=e.getDatasetMeta(0).controller.getStyle(r);return{text:t,fillStyle:a.backgroundColor,strokeStyle:a.borderColor,fontColor:i,lineWidth:a.borderWidth,pointStyle:n,hidden:!e.getDataVisibility(r),index:r}})}return[]}},onClick(e,t,n){n.chart.toggleDataVisibility(t.index),n.chart.update()}}}});class Na extends fa{initialize(){this.enableOptionSharing=!0,this.supportsDecimation=!0,super.initialize()}update(e){const t=this._cachedMeta,{dataset:n,data:i=[],_dataset:r}=t,a=this.chart._animationsDisabled;let{start:s,count:o}=ci(t,i,a);this._drawStart=s,this._drawCount=o,ui(t)&&(s=0,o=i.length),n._chart=this.chart,n._datasetIndex=this.index,n._decimated=!!r._decimated,n.points=i;const l=this.resolveDatasetElementOptions(e);this.options.showLine||(l.borderWidth=0),l.segment=this.options.segment,this.updateElement(n,void 0,{animated:!a,options:l},e),this.updateElements(i,s,o,e)}updateElements(e,t,n,i){const r="reset"===i,{iScale:a,vScale:s,_stacked:o,_dataset:l}=this._cachedMeta,{sharedOptions:c,includeOptions:u}=this._getSharedOptions(t,i),d=a.axis,h=s.axis,{spanGaps:f,segment:p}=this.options,m=Bn(f)?f:Number.POSITIVE_INFINITY,g=this.chart._animationsDisabled||r||"none"===i,b=t+n,y=e.length;let x=t>0&&this.getParsed(t-1);for(let v=0;v<y;++v){const n=e[v],f=g?n:{};if(v<t||v>=b){f.skip=!0;continue}const y=this.getParsed(v),w=on(y[h]),k=f[d]=a.getPixelForValue(y[d],v),_=f[h]=r||w?s.getBasePixel():s.getPixelForValue(o?this.applyStack(s,y,o):y[h],v);f.skip=isNaN(k)||isNaN(_)||w,f.stop=v>0&&Math.abs(y[d]-x[d])>m,p&&(f.parsed=y,f.raw=l.data[v]),u&&(f.options=c||this.resolveDataElementOptions(v,n.active?"active":i)),g||this.updateElement(n,v,f,i),x=y}}getMaxOverflow(){const e=this._cachedMeta,t=e.dataset,n=t.options&&t.options.borderWidth||0,i=e.data||[];if(!i.length)return n;const r=i[0].size(this.resolveDataElementOptions(0)),a=i[i.length-1].size(this.resolveDataElementOptions(i.length-1));return Math.max(n,r,a)/2}draw(){const e=this._cachedMeta;e.dataset.updateControlPoints(this.chart.chartArea,e.iScale.axis),super.draw()}}l(Na,"id","line"),l(Na,"defaults",{datasetElementType:"line",dataElementType:"point",showLine:!0,spanGaps:!1}),l(Na,"overrides",{scales:{_index_:{type:"category"},_value_:{type:"linear"}}});class Ma extends fa{constructor(e,t){super(e,t),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(e){const t=this._cachedMeta,n=this.chart,i=n.data.labels||[],r=wi(t._parsed[e].r,n.options.locale);return{label:i[e]||"",value:r}}parseObjectData(e,t,n,i){return mr.bind(this)(e,t,n,i)}update(e){const t=this._cachedMeta.data;this._updateRadius(),this.updateElements(t,0,t.length,e)}getMinMax(){const e=this._cachedMeta,t={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return e.data.forEach((e,n)=>{const i=this.getParsed(n).r;!isNaN(i)&&this.chart.getDataVisibility(n)&&(i<t.min&&(t.min=i),i>t.max&&(t.max=i))}),t}_updateRadius(){const e=this.chart,t=e.chartArea,n=e.options,i=Math.min(t.right-t.left,t.bottom-t.top),r=Math.max(i/2,0),a=(r-Math.max(n.cutoutPercentage?r/100*n.cutoutPercentage:1,0))/e.getVisibleDatasetCount();this.outerRadius=r-a*this.index,this.innerRadius=this.outerRadius-a}updateElements(e,t,n,i){const r="reset"===i,a=this.chart,s=a.options.animation,o=this._cachedMeta.rScale,l=o.xCenter,c=o.yCenter,u=o.getIndexAngle(0)-.5*En;let d,h=u;const f=360/this.countVisibleElements();for(d=0;d<t;++d)h+=this._computeAngle(d,i,f);for(d=t;d<t+n;d++){const t=e[d];let n=h,p=h+this._computeAngle(d,i,f),m=a.getDataVisibility(d)?o.getDistanceFromCenterForValue(this.getParsed(d).r):0;h=p,r&&(s.animateScale&&(m=0),s.animateRotate&&(n=p=u));const g={x:l,y:c,innerRadius:0,outerRadius:m,startAngle:n,endAngle:p,options:this.resolveDataElementOptions(d,t.active?"active":i)};this.updateElement(t,d,g,i)}}countVisibleElements(){const e=this._cachedMeta;let t=0;return e.data.forEach((e,n)=>{!isNaN(this.getParsed(n).r)&&this.chart.getDataVisibility(n)&&t++}),t}_computeAngle(e,t,n){return this.chart.getDataVisibility(e)?Vn(this.resolveDataElementOptions(e,t).angle||n):0}}l(Ma,"id","polarArea"),l(Ma,"defaults",{dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0}),l(Ma,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(e){const t=e.data;if(t.labels.length&&t.datasets.length){const{labels:{pointStyle:n,color:i}}=e.legend.options;return t.labels.map((t,r)=>{const a=e.getDatasetMeta(0).controller.getStyle(r);return{text:t,fillStyle:a.backgroundColor,strokeStyle:a.borderColor,fontColor:i,lineWidth:a.borderWidth,pointStyle:n,hidden:!e.getDataVisibility(r),index:r}})}return[]}},onClick(e,t,n){n.chart.toggleDataVisibility(t.index),n.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}});class Ca extends Sa{}l(Ca,"id","pie"),l(Ca,"defaults",{cutout:0,rotation:0,circumference:360,radius:"100%"});class Pa extends fa{getLabelAndValue(e){const t=this._cachedMeta.vScale,n=this.getParsed(e);return{label:t.getLabels()[e],value:""+t.getLabelForValue(n[t.axis])}}parseObjectData(e,t,n,i){return mr.bind(this)(e,t,n,i)}update(e){const t=this._cachedMeta,n=t.dataset,i=t.data||[],r=t.iScale.getLabels();if(n.points=i,"resize"!==e){const t=this.resolveDatasetElementOptions(e);this.options.showLine||(t.borderWidth=0);const a={_loop:!0,_fullLoop:r.length===i.length,options:t};this.updateElement(n,void 0,a,e)}this.updateElements(i,0,i.length,e)}updateElements(e,t,n,i){const r=this._cachedMeta.rScale,a="reset"===i;for(let s=t;s<t+n;s++){const t=e[s],n=this.resolveDataElementOptions(s,t.active?"active":i),o=r.getPointPositionForValue(s,this.getParsed(s).r),l=a?r.xCenter:o.x,c=a?r.yCenter:o.y,u={x:l,y:c,angle:o.angle,skip:isNaN(l)||isNaN(c),options:n};this.updateElement(t,s,u,i)}}}l(Pa,"id","radar"),l(Pa,"defaults",{datasetElementType:"line",dataElementType:"point",indexAxis:"r",showLine:!0,elements:{line:{fill:"start"}}}),l(Pa,"overrides",{aspectRatio:1,scales:{r:{type:"radialLinear"}}});class Ea extends fa{getLabelAndValue(e){const t=this._cachedMeta,n=this.chart.data.labels||[],{xScale:i,yScale:r}=t,a=this.getParsed(e),s=i.getLabelForValue(a.x),o=r.getLabelForValue(a.y);return{label:n[e]||"",value:"("+s+", "+o+")"}}update(e){const t=this._cachedMeta,{data:n=[]}=t,i=this.chart._animationsDisabled;let{start:r,count:a}=ci(t,n,i);if(this._drawStart=r,this._drawCount=a,ui(t)&&(r=0,a=n.length),this.options.showLine){this.datasetElementType||this.addElements();const{dataset:r,_dataset:a}=t;r._chart=this.chart,r._datasetIndex=this.index,r._decimated=!!a._decimated,r.points=n;const s=this.resolveDatasetElementOptions(e);s.segment=this.options.segment,this.updateElement(r,void 0,{animated:!i,options:s},e)}else this.datasetElementType&&(delete t.dataset,this.datasetElementType=!1);this.updateElements(n,r,a,e)}addElements(){const{showLine:e}=this.options;!this.datasetElementType&&e&&(this.datasetElementType=this.chart.registry.getElement("line")),super.addElements()}updateElements(e,t,n,i){const r="reset"===i,{iScale:a,vScale:s,_stacked:o,_dataset:l}=this._cachedMeta,c=this.resolveDataElementOptions(t,i),u=this.getSharedOptions(c),d=this.includeOptions(i,u),h=a.axis,f=s.axis,{spanGaps:p,segment:m}=this.options,g=Bn(p)?p:Number.POSITIVE_INFINITY,b=this.chart._animationsDisabled||r||"none"===i;let y=t>0&&this.getParsed(t-1);for(let x=t;x<t+n;++x){const t=e[x],n=this.getParsed(x),c=b?t:{},p=on(n[f]),v=c[h]=a.getPixelForValue(n[h],x),w=c[f]=r||p?s.getBasePixel():s.getPixelForValue(o?this.applyStack(s,n,o):n[f],x);c.skip=isNaN(v)||isNaN(w)||p,c.stop=x>0&&Math.abs(n[h]-y[h])>g,m&&(c.parsed=n,c.raw=l.data[x]),d&&(c.options=u||this.resolveDataElementOptions(x,t.active?"active":i)),b||this.updateElement(t,x,c,i),y=n}this.updateSharedOptions(u,i,c)}getMaxOverflow(){const e=this._cachedMeta,t=e.data||[];if(!this.options.showLine){let e=0;for(let n=t.length-1;n>=0;--n)e=Math.max(e,t[n].size(this.resolveDataElementOptions(n))/2);return e>0&&e}const n=e.dataset,i=n.options&&n.options.borderWidth||0;if(!t.length)return i;const r=t[0].size(this.resolveDataElementOptions(0)),a=t[t.length-1].size(this.resolveDataElementOptions(t.length-1));return Math.max(i,r,a)/2}}l(Ea,"id","scatter"),l(Ea,"defaults",{datasetElementType:!1,dataElementType:"point",showLine:!1,fill:!1}),l(Ea,"overrides",{interaction:{mode:"point"},scales:{x:{type:"linear"},y:{type:"linear"}}});function ja(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class Ta{static override(e){Object.assign(Ta.prototype,e)}constructor(e){l(this,"options",void 0),this.options=e||{}}init(){}formats(){return ja()}parse(){return ja()}format(){return ja()}add(){return ja()}diff(){return ja()}startOf(){return ja()}endOf(){return ja()}}var La=Ta;function Oa(e,t,n,i){const{controller:r,data:a,_sorted:s}=e,o=r._cachedMeta.iScale,l=e.dataset&&e.dataset.options?e.dataset.options.spanGaps:null;if(o&&t===o.axis&&"r"!==t&&s&&a.length){const s=o._reversePixels?ti:ei;if(!i){const i=s(a,t,n);if(l){const{vScale:t}=r._cachedMeta,{_parsed:n}=e,a=n.slice(0,i.lo+1).reverse().findIndex(e=>!on(e[t.axis]));i.lo-=Math.max(0,a);const s=n.slice(i.hi).findIndex(e=>!on(e[t.axis]));i.hi+=Math.max(0,s)}return i}if(r._sharedOptions){const e=a[0],i="function"===typeof e.getRange&&e.getRange(t);if(i){const e=s(a,t,n-i),r=s(a,t,n+i);return{lo:e.lo,hi:r.hi}}}}return{lo:0,hi:a.length-1}}function Aa(e,t,n,i,r){const a=e.getSortedVisibleDatasetMetas(),s=n[t];for(let o=0,l=a.length;o<l;++o){const{index:e,data:n}=a[o],{lo:l,hi:c}=Oa(a[o],t,s,r);for(let t=l;t<=c;++t){const r=n[t];r.skip||i(r,e,t)}}}function Da(e,t,n,i,r){const a=[];if(!r&&!e.isPointInArea(t))return a;return Aa(e,n,t,function(n,s,o){(r||Ri(n,e.chartArea,0))&&n.inRange(t.x,t.y,i)&&a.push({element:n,datasetIndex:s,index:o})},!0),a}function Ra(e,t,n,i,r,a){let s=[];const o=function(e){const t=-1!==e.indexOf("x"),n=-1!==e.indexOf("y");return function(e,i){const r=t?Math.abs(e.x-i.x):0,a=n?Math.abs(e.y-i.y):0;return Math.sqrt(Math.pow(r,2)+Math.pow(a,2))}}(n);let l=Number.POSITIVE_INFINITY;return Aa(e,n,t,function(n,c,u){const d=n.inRange(t.x,t.y,r);if(i&&!d)return;const h=n.getCenterPoint(r);if(!(!!a||e.isPointInArea(h))&&!d)return;const f=o(t,h);f<l?(s=[{element:n,datasetIndex:c,index:u}],l=f):f===l&&s.push({element:n,datasetIndex:c,index:u})}),s}function za(e,t,n,i,r,a){return a||e.isPointInArea(t)?"r"!==n||i?Ra(e,t,n,i,r,a):function(e,t,n,i){let r=[];return Aa(e,n,t,function(e,n,a){const{startAngle:s,endAngle:o}=e.getProps(["startAngle","endAngle"],i),{angle:l}=Yn(e,{x:t.x,y:t.y});Xn(l,s,o)&&r.push({element:e,datasetIndex:n,index:a})}),r}(e,t,n,r):[]}function Fa(e,t,n,i,r){const a=[],s="x"===n?"inXRange":"inYRange";let o=!1;return Aa(e,n,t,(e,i,l)=>{e[s]&&e[s](t[n],r)&&(a.push({element:e,datasetIndex:i,index:l}),o=o||e.inRange(t.x,t.y,r))}),i&&!o?[]:a}var Ia={evaluateInteractionItems:Aa,modes:{index(e,t,n,i){const r=Er(t,e),a=n.axis||"x",s=n.includeInvisible||!1,o=n.intersect?Da(e,r,a,i,s):za(e,r,a,!1,i,s),l=[];return o.length?(e.getSortedVisibleDatasetMetas().forEach(e=>{const t=o[0].index,n=e.data[t];n&&!n.skip&&l.push({element:n,datasetIndex:e.index,index:t})}),l):[]},dataset(e,t,n,i){const r=Er(t,e),a=n.axis||"xy",s=n.includeInvisible||!1;let o=n.intersect?Da(e,r,a,i,s):za(e,r,a,!1,i,s);if(o.length>0){const t=o[0].datasetIndex,n=e.getDatasetMeta(t).data;o=[];for(let e=0;e<n.length;++e)o.push({element:n[e],datasetIndex:t,index:e})}return o},point:(e,t,n,i)=>Da(e,Er(t,e),n.axis||"xy",i,n.includeInvisible||!1),nearest(e,t,n,i){const r=Er(t,e),a=n.axis||"xy",s=n.includeInvisible||!1;return za(e,r,a,n.intersect,i,s)},x:(e,t,n,i)=>Fa(e,Er(t,e),"x",n.intersect,i),y:(e,t,n,i)=>Fa(e,Er(t,e),"y",n.intersect,i)}};const Wa=["left","top","right","bottom"];function Ba(e,t){return e.filter(e=>e.pos===t)}function Ha(e,t){return e.filter(e=>-1===Wa.indexOf(e.pos)&&e.box.axis===t)}function Va(e,t){return e.sort((e,n)=>{const i=t?n:e,r=t?e:n;return i.weight===r.weight?i.index-r.index:i.weight-r.weight})}function Ua(e,t){const n=function(e){const t={};for(const n of e){const{stack:e,pos:i,stackWeight:r}=n;if(!e||!Wa.includes(i))continue;const a=t[e]||(t[e]={count:0,placed:0,weight:0,size:0});a.count++,a.weight+=r}return t}(e),{vBoxMaxWidth:i,hBoxMaxHeight:r}=t;let a,s,o;for(a=0,s=e.length;a<s;++a){o=e[a];const{fullSize:s}=o.box,l=n[o.stack],c=l&&o.stackWeight/l.weight;o.horizontal?(o.width=c?c*i:s&&t.availableWidth,o.height=r):(o.width=i,o.height=c?c*r:s&&t.availableHeight)}return n}function $a(e,t,n,i){return Math.max(e[n],t[n])+Math.max(e[i],t[i])}function Ya(e,t){e.top=Math.max(e.top,t.top),e.left=Math.max(e.left,t.left),e.bottom=Math.max(e.bottom,t.bottom),e.right=Math.max(e.right,t.right)}function qa(e,t,n,i){const{pos:r,box:a}=n,s=e.maxPadding;if(!cn(r)){n.size&&(e[r]-=n.size);const t=i[n.stack]||{size:0,count:1};t.size=Math.max(t.size,n.horizontal?a.height:a.width),n.size=t.size/t.count,e[r]+=n.size}a.getPadding&&Ya(s,a.getPadding());const o=Math.max(0,t.outerWidth-$a(s,e,"left","right")),l=Math.max(0,t.outerHeight-$a(s,e,"top","bottom")),c=o!==e.w,u=l!==e.h;return e.w=o,e.h=l,n.horizontal?{same:c,other:u}:{same:u,other:c}}function Ka(e,t){const n=t.maxPadding;function i(e){const i={left:0,top:0,right:0,bottom:0};return e.forEach(e=>{i[e]=Math.max(t[e],n[e])}),i}return i(e?["left","right"]:["top","bottom"])}function Qa(e,t,n,i){const r=[];let a,s,o,l,c,u;for(a=0,s=e.length,c=0;a<s;++a){o=e[a],l=o.box,l.update(o.width||t.w,o.height||t.h,Ka(o.horizontal,t));const{same:s,other:d}=qa(t,n,o,i);c|=s&&r.length,u=u||d,l.fullSize||r.push(o)}return c&&Qa(r,t,n,i)||u}function Xa(e,t,n,i,r){e.top=n,e.left=t,e.right=t+i,e.bottom=n+r,e.width=i,e.height=r}function Ga(e,t,n,i){const r=n.padding;let{x:a,y:s}=t;for(const o of e){const e=o.box,l=i[o.stack]||{count:1,placed:0,weight:1},c=o.stackWeight/l.weight||1;if(o.horizontal){const i=t.w*c,a=l.size||e.height;Mn(l.start)&&(s=l.start),e.fullSize?Xa(e,r.left,s,n.outerWidth-r.right-r.left,a):Xa(e,t.left+l.placed,s,i,a),l.start=s,l.placed+=i,s=e.bottom}else{const i=t.h*c,s=l.size||e.width;Mn(l.start)&&(a=l.start),e.fullSize?Xa(e,a,r.top,s,n.outerHeight-r.bottom-r.top):Xa(e,a,t.top+l.placed,s,i),l.start=a,l.placed+=i,a=e.right}}t.x=a,t.y=s}var Ja={addBox(e,t){e.boxes||(e.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(e){t.draw(e)}}]},e.boxes.push(t)},removeBox(e,t){const n=e.boxes?e.boxes.indexOf(t):-1;-1!==n&&e.boxes.splice(n,1)},configure(e,t,n){t.fullSize=n.fullSize,t.position=n.position,t.weight=n.weight},update(e,t,n,i){if(!e)return;const r=Ji(e.options.layout.padding),a=Math.max(t-r.width,0),s=Math.max(n-r.height,0),o=function(e){const t=function(e){const t=[];let n,i,r,a,s,o;for(n=0,i=(e||[]).length;n<i;++n)r=e[n],({position:a,options:{stack:s,stackWeight:o=1}}=r),t.push({index:n,box:r,pos:a,horizontal:r.isHorizontal(),weight:r.weight,stack:s&&a+s,stackWeight:o});return t}(e),n=Va(t.filter(e=>e.box.fullSize),!0),i=Va(Ba(t,"left"),!0),r=Va(Ba(t,"right")),a=Va(Ba(t,"top"),!0),s=Va(Ba(t,"bottom")),o=Ha(t,"x"),l=Ha(t,"y");return{fullSize:n,leftAndTop:i.concat(a),rightAndBottom:r.concat(l).concat(s).concat(o),chartArea:Ba(t,"chartArea"),vertical:i.concat(r).concat(l),horizontal:a.concat(s).concat(o)}}(e.boxes),l=o.vertical,c=o.horizontal;mn(e.boxes,e=>{"function"===typeof e.beforeLayout&&e.beforeLayout()});const u=l.reduce((e,t)=>t.box.options&&!1===t.box.options.display?e:e+1,0)||1,d=Object.freeze({outerWidth:t,outerHeight:n,padding:r,availableWidth:a,availableHeight:s,vBoxMaxWidth:a/2/u,hBoxMaxHeight:s/2}),h=Object.assign({},r);Ya(h,Ji(i));const f=Object.assign({maxPadding:h,w:a,h:s,x:r.left,y:r.top},r),p=Ua(l.concat(c),d);Qa(o.fullSize,f,d,p),Qa(l,f,d,p),Qa(c,f,d,p)&&Qa(l,f,d,p),function(e){const t=e.maxPadding;function n(n){const i=Math.max(t[n]-e[n],0);return e[n]+=i,i}e.y+=n("top"),e.x+=n("left"),n("right"),n("bottom")}(f),Ga(o.leftAndTop,f,d,p),f.x+=f.w,f.y+=f.h,Ga(o.rightAndBottom,f,d,p),e.chartArea={left:f.left,top:f.top,right:f.left+f.w,bottom:f.top+f.h,height:f.h,width:f.w},mn(o.chartArea,t=>{const n=t.box;Object.assign(n,e.chartArea),n.update(f.w,f.h,{left:0,top:0,right:0,bottom:0})})}};class Za{acquireContext(e,t){}releaseContext(e){return!1}addEventListener(e,t,n){}removeEventListener(e,t,n){}getDevicePixelRatio(){return 1}getMaximumSize(e,t,n,i){return t=Math.max(0,t||e.width),n=n||e.height,{width:t,height:Math.max(0,i?Math.floor(t/i):n)}}isAttached(e){return!0}updateConfig(e){}}class es extends Za{acquireContext(e){return e&&e.getContext&&e.getContext("2d")||null}updateConfig(e){e.options.animation=!1}}const ts="$chartjs",ns={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},is=e=>null===e||""===e;const rs=!!Or&&{passive:!0};function as(e,t,n){e&&e.canvas&&e.canvas.removeEventListener(t,n,rs)}function ss(e,t){for(const n of e)if(n===t||n.contains(t))return!0}function os(e,t,n){const i=e.canvas,r=new MutationObserver(e=>{let t=!1;for(const n of e)t=t||ss(n.addedNodes,i),t=t&&!ss(n.removedNodes,i);t&&n()});return r.observe(document,{childList:!0,subtree:!0}),r}function ls(e,t,n){const i=e.canvas,r=new MutationObserver(e=>{let t=!1;for(const n of e)t=t||ss(n.removedNodes,i),t=t&&!ss(n.addedNodes,i);t&&n()});return r.observe(document,{childList:!0,subtree:!0}),r}const cs=new Map;let us=0;function ds(){const e=window.devicePixelRatio;e!==us&&(us=e,cs.forEach((t,n)=>{n.currentDevicePixelRatio!==e&&t()}))}function hs(e,t,n){const i=e.canvas,r=i&&Sr(i);if(!r)return;const a=si((e,t)=>{const i=r.clientWidth;n(e,t),i<r.clientWidth&&n()},window),s=new ResizeObserver(e=>{const t=e[0],n=t.contentRect.width,i=t.contentRect.height;0===n&&0===i||a(n,i)});return s.observe(r),function(e,t){cs.size||window.addEventListener("resize",ds),cs.set(e,t)}(e,a),s}function fs(e,t,n){n&&n.disconnect(),"resize"===t&&function(e){cs.delete(e),cs.size||window.removeEventListener("resize",ds)}(e)}function ps(e,t,n){const i=e.canvas,r=si(t=>{null!==e.ctx&&n(function(e,t){const n=ns[e.type]||e.type,{x:i,y:r}=Er(e,t);return{type:n,chart:t,native:e,x:void 0!==i?i:null,y:void 0!==r?r:null}}(t,e))},e);return function(e,t,n){e&&e.addEventListener(t,n,rs)}(i,t,r),r}class ms extends Za{acquireContext(e,t){const n=e&&e.getContext&&e.getContext("2d");return n&&n.canvas===e?(function(e,t){const n=e.style,i=e.getAttribute("height"),r=e.getAttribute("width");if(e[ts]={initial:{height:i,width:r,style:{display:n.display,height:n.height,width:n.width}}},n.display=n.display||"block",n.boxSizing=n.boxSizing||"border-box",is(r)){const t=Ar(e,"width");void 0!==t&&(e.width=t)}if(is(i))if(""===e.style.height)e.height=e.width/(t||2);else{const t=Ar(e,"height");void 0!==t&&(e.height=t)}}(e,t),n):null}releaseContext(e){const t=e.canvas;if(!t[ts])return!1;const n=t[ts].initial;["height","width"].forEach(e=>{const i=n[e];on(i)?t.removeAttribute(e):t.setAttribute(e,i)});const i=n.style||{};return Object.keys(i).forEach(e=>{t.style[e]=i[e]}),t.width=t.width,delete t[ts],!0}addEventListener(e,t,n){this.removeEventListener(e,t);const i=e.$proxies||(e.$proxies={}),r={attach:os,detach:ls,resize:hs}[t]||ps;i[t]=r(e,t,n)}removeEventListener(e,t){const n=e.$proxies||(e.$proxies={}),i=n[t];if(!i)return;({attach:fs,detach:fs,resize:fs}[t]||as)(e,t,i),n[t]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(e,t,n,i){return Tr(e,t,n,i)}isAttached(e){const t=e&&Sr(e);return!(!t||!t.isConnected)}}class gs{constructor(){l(this,"x",void 0),l(this,"y",void 0),l(this,"active",!1),l(this,"options",void 0),l(this,"$animations",void 0)}tooltipPosition(e){const{x:t,y:n}=this.getProps(["x","y"],e);return{x:t,y:n}}hasValue(){return Bn(this.x)&&Bn(this.y)}getProps(e,t){const n=this.$animations;if(!t||!n)return this;const i={};return e.forEach(e=>{i[e]=n[e]&&n[e].active()?n[e]._to:this[e]}),i}}function bs(e,t){const n=e.options.ticks,i=function(e){const t=e.options.offset,n=e._tickSize(),i=e._length/n+(t?0:1),r=e._maxLength/n;return Math.floor(Math.min(i,r))}(e),r=Math.min(n.maxTicksLimit||i,i),a=n.major.enabled?function(e){const t=[];let n,i;for(n=0,i=e.length;n<i;n++)e[n].major&&t.push(n);return t}(t):[],s=a.length,o=a[0],l=a[s-1],c=[];if(s>r)return function(e,t,n,i){let r,a=0,s=n[0];for(i=Math.ceil(i),r=0;r<e.length;r++)r===s&&(t.push(e[r]),a++,s=n[a*i])}(t,c,a,s/r),c;const u=function(e,t,n){const i=function(e){const t=e.length;let n,i;if(t<2)return!1;for(i=e[0],n=1;n<t;++n)if(e[n]-e[n-1]!==i)return!1;return i}(e),r=t.length/n;if(!i)return Math.max(r,1);const a=function(e){const t=[],n=Math.sqrt(e);let i;for(i=1;i<n;i++)e%i===0&&(t.push(i),t.push(e/i));return n===(0|n)&&t.push(n),t.sort((e,t)=>e-t).pop(),t}(i);for(let s=0,o=a.length-1;s<o;s++){const e=a[s];if(e>r)return e}return Math.max(r,1)}(a,t,r);if(s>0){let e,n;const i=s>1?Math.round((l-o)/(s-1)):null;for(ys(t,c,u,on(i)?0:o-i,o),e=0,n=s-1;e<n;e++)ys(t,c,u,a[e],a[e+1]);return ys(t,c,u,l,on(i)?t.length:l+i),c}return ys(t,c,u),c}function ys(e,t,n,i,r){const a=hn(i,0),s=Math.min(hn(r,e.length),e.length);let o,l,c,u=0;for(n=Math.ceil(n),r&&(o=r-i,n=o/Math.floor(o/n)),c=a;c<0;)u++,c=Math.round(a+u*n);for(l=Math.max(a,0);l<s;l++)l===c&&(t.push(e[l]),u++,c=Math.round(a+u*n))}l(gs,"defaults",{}),l(gs,"defaultRoutes",void 0);const xs=(e,t,n)=>"top"===t||"left"===t?e[t]+n:e[t]-n,vs=(e,t)=>Math.min(t||e,e);function ws(e,t){const n=[],i=e.length/t,r=e.length;let a=0;for(;a<r;a+=i)n.push(e[Math.floor(a)]);return n}function ks(e,t,n){const i=e.ticks.length,r=Math.min(t,i-1),a=e._startPixel,s=e._endPixel,o=1e-6;let l,c=e.getPixelForTick(r);if(!(n&&(l=1===i?Math.max(c-a,s-c):0===t?(e.getPixelForTick(1)-c)/2:(c-e.getPixelForTick(r-1))/2,c+=r<t?l:-l,c<a-o||c>s+o)))return c}function _s(e){return e.drawTicks?e.tickLength:0}function Ss(e,t){if(!e.display)return 0;const n=Zi(e.font,t),i=Ji(e.padding);return(ln(e.text)?e.text.length:1)*n.lineHeight+i.height}function Ns(e,t,n){let i=oi(e);return(n&&"right"!==t||!n&&"right"===t)&&(i=(e=>"left"===e?"right":"right"===e?"left":e)(i)),i}class Ms extends gs{constructor(e){super(),this.id=e.id,this.type=e.type,this.options=void 0,this.ctx=e.ctx,this.chart=e.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(e){this.options=e.setContext(this.getContext()),this.axis=e.axis,this._userMin=this.parse(e.min),this._userMax=this.parse(e.max),this._suggestedMin=this.parse(e.suggestedMin),this._suggestedMax=this.parse(e.suggestedMax)}parse(e,t){return e}getUserBounds(){let{_userMin:e,_userMax:t,_suggestedMin:n,_suggestedMax:i}=this;return e=dn(e,Number.POSITIVE_INFINITY),t=dn(t,Number.NEGATIVE_INFINITY),n=dn(n,Number.POSITIVE_INFINITY),i=dn(i,Number.NEGATIVE_INFINITY),{min:dn(e,n),max:dn(t,i),minDefined:un(e),maxDefined:un(t)}}getMinMax(e){let t,{min:n,max:i,minDefined:r,maxDefined:a}=this.getUserBounds();if(r&&a)return{min:n,max:i};const s=this.getMatchingVisibleMetas();for(let o=0,l=s.length;o<l;++o)t=s[o].controller.getMinMax(this,e),r||(n=Math.min(n,t.min)),a||(i=Math.max(i,t.max));return n=a&&n>i?i:n,i=r&&n>i?n:i,{min:dn(n,dn(i,n)),max:dn(i,dn(n,i))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const e=this.chart.data;return this.options.labels||(this.isHorizontal()?e.xLabels:e.yLabels)||e.labels||[]}getLabelItems(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.chart.chartArea;return this._labelItems||(this._labelItems=this._computeLabelItems(e))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){pn(this.options.beforeUpdate,[this])}update(e,t,n){const{beginAtZero:i,grace:r,ticks:a}=this.options,s=a.sampleSize;this.beforeUpdate(),this.maxWidth=e,this.maxHeight=t,this._margins=n=Object.assign({left:0,right:0,top:0,bottom:0},n),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+n.left+n.right:this.height+n.top+n.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=function(e,t,n){const{min:i,max:r}=e,a=fn(t,(r-i)/2),s=(e,t)=>n&&0===e?0:e+t;return{min:s(i,-Math.abs(a)),max:s(r,a)}}(this,r,i),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const o=s<this.ticks.length;this._convertTicksToLabels(o?ws(this.ticks,s):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),a.display&&(a.autoSkip||"auto"===a.source)&&(this.ticks=bs(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),o&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let e,t,n=this.options.reverse;this.isHorizontal()?(e=this.left,t=this.right):(e=this.top,t=this.bottom,n=!n),this._startPixel=e,this._endPixel=t,this._reversePixels=n,this._length=t-e,this._alignToPixels=this.options.alignToPixels}afterUpdate(){pn(this.options.afterUpdate,[this])}beforeSetDimensions(){pn(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){pn(this.options.afterSetDimensions,[this])}_callHooks(e){this.chart.notifyPlugins(e,this.getContext()),pn(this.options[e],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){pn(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(e){const t=this.options.ticks;let n,i,r;for(n=0,i=e.length;n<i;n++)r=e[n],r.label=pn(t.callback,[r.value,n,e],this)}afterTickToLabelConversion(){pn(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){pn(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const e=this.options,t=e.ticks,n=vs(this.ticks.length,e.ticks.maxTicksLimit),i=t.minRotation||0,r=t.maxRotation;let a,s,o,l=i;if(!this._isVisible()||!t.display||i>=r||n<=1||!this.isHorizontal())return void(this.labelRotation=i);const c=this._getLabelSizes(),u=c.widest.width,d=c.highest.height,h=Gn(this.chart.width-u,0,this.maxWidth);a=e.offset?this.maxWidth/n:h/(n-1),u+6>a&&(a=h/(n-(e.offset?.5:1)),s=this.maxHeight-_s(e.grid)-t.padding-Ss(e.title,this.chart.options.font),o=Math.sqrt(u*u+d*d),l=Un(Math.min(Math.asin(Gn((c.highest.height+6)/a,-1,1)),Math.asin(Gn(s/o,-1,1))-Math.asin(Gn(d/o,-1,1)))),l=Math.max(i,Math.min(r,l))),this.labelRotation=l}afterCalculateLabelRotation(){pn(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){pn(this.options.beforeFit,[this])}fit(){const e={width:0,height:0},{chart:t,options:{ticks:n,title:i,grid:r}}=this,a=this._isVisible(),s=this.isHorizontal();if(a){const a=Ss(i,t.options.font);if(s?(e.width=this.maxWidth,e.height=_s(r)+a):(e.height=this.maxHeight,e.width=_s(r)+a),n.display&&this.ticks.length){const{first:t,last:i,widest:r,highest:a}=this._getLabelSizes(),o=2*n.padding,l=Vn(this.labelRotation),c=Math.cos(l),u=Math.sin(l);if(s){const t=n.mirror?0:u*r.width+c*a.height;e.height=Math.min(this.maxHeight,e.height+t+o)}else{const t=n.mirror?0:c*r.width+u*a.height;e.width=Math.min(this.maxWidth,e.width+t+o)}this._calculatePadding(t,i,u,c)}}this._handleMargins(),s?(this.width=this._length=t.width-this._margins.left-this._margins.right,this.height=e.height):(this.width=e.width,this.height=this._length=t.height-this._margins.top-this._margins.bottom)}_calculatePadding(e,t,n,i){const{ticks:{align:r,padding:a},position:s}=this.options,o=0!==this.labelRotation,l="top"!==s&&"x"===this.axis;if(this.isHorizontal()){const s=this.getPixelForTick(0)-this.left,c=this.right-this.getPixelForTick(this.ticks.length-1);let u=0,d=0;o?l?(u=i*e.width,d=n*t.height):(u=n*e.height,d=i*t.width):"start"===r?d=t.width:"end"===r?u=e.width:"inner"!==r&&(u=e.width/2,d=t.width/2),this.paddingLeft=Math.max((u-s+a)*this.width/(this.width-s),0),this.paddingRight=Math.max((d-c+a)*this.width/(this.width-c),0)}else{let n=t.height/2,i=e.height/2;"start"===r?(n=0,i=e.height):"end"===r&&(n=t.height,i=0),this.paddingTop=n+a,this.paddingBottom=i+a}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){pn(this.options.afterFit,[this])}isHorizontal(){const{axis:e,position:t}=this.options;return"top"===t||"bottom"===t||"x"===e}isFullSize(){return this.options.fullSize}_convertTicksToLabels(e){let t,n;for(this.beforeTickToLabelConversion(),this.generateTickLabels(e),t=0,n=e.length;t<n;t++)on(e[t].label)&&(e.splice(t,1),n--,t--);this.afterTickToLabelConversion()}_getLabelSizes(){let e=this._labelSizes;if(!e){const t=this.options.ticks.sampleSize;let n=this.ticks;t<n.length&&(n=ws(n,t)),this._labelSizes=e=this._computeLabelSizes(n,n.length,this.options.ticks.maxTicksLimit)}return e}_computeLabelSizes(e,t,n){const{ctx:i,_longestTextCache:r}=this,a=[],s=[],o=Math.floor(t/vs(t,n));let l,c,u,d,h,f,p,m,g,b,y,x=0,v=0;for(l=0;l<t;l+=o){if(d=e[l].label,h=this._resolveTickFontOptions(l),i.font=f=h.string,p=r[f]=r[f]||{data:{},gc:[]},m=h.lineHeight,g=b=0,on(d)||ln(d)){if(ln(d))for(c=0,u=d.length;c<u;++c)y=d[c],on(y)||ln(y)||(g=ji(i,p.data,p.gc,g,y),b+=m)}else g=ji(i,p.data,p.gc,g,d),b=m;a.push(g),s.push(b),x=Math.max(g,x),v=Math.max(b,v)}!function(e,t){mn(e,e=>{const n=e.gc,i=n.length/2;let r;if(i>t){for(r=0;r<i;++r)delete e.data[n[r]];n.splice(0,i)}})}(r,t);const w=a.indexOf(x),k=s.indexOf(v),_=e=>({width:a[e]||0,height:s[e]||0});return{first:_(0),last:_(t-1),widest:_(w),highest:_(k),widths:a,heights:s}}getLabelForValue(e){return e}getPixelForValue(e,t){return NaN}getValueForPixel(e){}getPixelForTick(e){const t=this.ticks;return e<0||e>t.length-1?null:this.getPixelForValue(t[e].value)}getPixelForDecimal(e){this._reversePixels&&(e=1-e);const t=this._startPixel+e*this._length;return Gn(this._alignToPixels?Li(this.chart,t,0):t,-32768,32767)}getDecimalForPixel(e){const t=(e-this._startPixel)/this._length;return this._reversePixels?1-t:t}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:e,max:t}=this;return e<0&&t<0?t:e>0&&t>0?e:0}getContext(e){const t=this.ticks||[];if(e>=0&&e<t.length){const n=t[e];return n.$context||(n.$context=function(e,t,n){return tr(e,{tick:n,index:t,type:"tick"})}(this.getContext(),e,n))}return this.$context||(this.$context=tr(this.chart.getContext(),{scale:this,type:"scale"}))}_tickSize(){const e=this.options.ticks,t=Vn(this.labelRotation),n=Math.abs(Math.cos(t)),i=Math.abs(Math.sin(t)),r=this._getLabelSizes(),a=e.autoSkipPadding||0,s=r?r.widest.width+a:0,o=r?r.highest.height+a:0;return this.isHorizontal()?o*n>s*i?s/n:o/i:o*i<s*n?o/n:s/i}_isVisible(){const e=this.options.display;return"auto"!==e?!!e:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(e){const t=this.axis,n=this.chart,i=this.options,{grid:r,position:a,border:s}=i,o=r.offset,l=this.isHorizontal(),c=this.ticks.length+(o?1:0),u=_s(r),d=[],h=s.setContext(this.getContext()),f=h.display?h.width:0,p=f/2,m=function(e){return Li(n,e,f)};let g,b,y,x,v,w,k,_,S,N,M,C;if("top"===a)g=m(this.bottom),w=this.bottom-u,_=g-p,N=m(e.top)+p,C=e.bottom;else if("bottom"===a)g=m(this.top),N=e.top,C=m(e.bottom)-p,w=g+p,_=this.top+u;else if("left"===a)g=m(this.right),v=this.right-u,k=g-p,S=m(e.left)+p,M=e.right;else if("right"===a)g=m(this.left),S=e.left,M=m(e.right)-p,v=g+p,k=this.left+u;else if("x"===t){if("center"===a)g=m((e.top+e.bottom)/2+.5);else if(cn(a)){const e=Object.keys(a)[0],t=a[e];g=m(this.chart.scales[e].getPixelForValue(t))}N=e.top,C=e.bottom,w=g+p,_=w+u}else if("y"===t){if("center"===a)g=m((e.left+e.right)/2);else if(cn(a)){const e=Object.keys(a)[0],t=a[e];g=m(this.chart.scales[e].getPixelForValue(t))}v=g-p,k=v-u,S=e.left,M=e.right}const P=hn(i.ticks.maxTicksLimit,c),E=Math.max(1,Math.ceil(c/P));for(b=0;b<c;b+=E){const e=this.getContext(b),t=r.setContext(e),i=s.setContext(e),a=t.lineWidth,c=t.color,u=i.dash||[],h=i.dashOffset,f=t.tickWidth,p=t.tickColor,m=t.tickBorderDash||[],g=t.tickBorderDashOffset;y=ks(this,b,o),void 0!==y&&(x=Li(n,y,a),l?v=k=S=M=x:w=_=N=C=x,d.push({tx1:v,ty1:w,tx2:k,ty2:_,x1:S,y1:N,x2:M,y2:C,width:a,color:c,borderDash:u,borderDashOffset:h,tickWidth:f,tickColor:p,tickBorderDash:m,tickBorderDashOffset:g}))}return this._ticksLength=c,this._borderValue=g,d}_computeLabelItems(e){const t=this.axis,n=this.options,{position:i,ticks:r}=n,a=this.isHorizontal(),s=this.ticks,{align:o,crossAlign:l,padding:c,mirror:u}=r,d=_s(n.grid),h=d+c,f=u?-c:h,p=-Vn(this.labelRotation),m=[];let g,b,y,x,v,w,k,_,S,N,M,C,P="middle";if("top"===i)w=this.bottom-f,k=this._getXAxisLabelAlignment();else if("bottom"===i)w=this.top+f,k=this._getXAxisLabelAlignment();else if("left"===i){const e=this._getYAxisLabelAlignment(d);k=e.textAlign,v=e.x}else if("right"===i){const e=this._getYAxisLabelAlignment(d);k=e.textAlign,v=e.x}else if("x"===t){if("center"===i)w=(e.top+e.bottom)/2+h;else if(cn(i)){const e=Object.keys(i)[0],t=i[e];w=this.chart.scales[e].getPixelForValue(t)+h}k=this._getXAxisLabelAlignment()}else if("y"===t){if("center"===i)v=(e.left+e.right)/2-h;else if(cn(i)){const e=Object.keys(i)[0],t=i[e];v=this.chart.scales[e].getPixelForValue(t)}k=this._getYAxisLabelAlignment(d).textAlign}"y"===t&&("start"===o?P="top":"end"===o&&(P="bottom"));const E=this._getLabelSizes();for(g=0,b=s.length;g<b;++g){y=s[g],x=y.label;const e=r.setContext(this.getContext(g));_=this.getPixelForTick(g)+r.labelOffset,S=this._resolveTickFontOptions(g),N=S.lineHeight,M=ln(x)?x.length:1;const t=M/2,n=e.color,o=e.textStrokeColor,c=e.textStrokeWidth;let d,h=k;if(a?(v=_,"inner"===k&&(h=g===b-1?this.options.reverse?"left":"right":0===g?this.options.reverse?"right":"left":"center"),C="top"===i?"near"===l||0!==p?-M*N+N/2:"center"===l?-E.highest.height/2-t*N+N:-E.highest.height+N/2:"near"===l||0!==p?N/2:"center"===l?E.highest.height/2-t*N:E.highest.height-M*N,u&&(C*=-1),0===p||e.showLabelBackdrop||(v+=N/2*Math.sin(p))):(w=_,C=(1-M)*N/2),e.showLabelBackdrop){const t=Ji(e.backdropPadding),n=E.heights[g],i=E.widths[g];let r=C-t.top,a=0-t.left;switch(P){case"middle":r-=n/2;break;case"bottom":r-=n}switch(k){case"center":a-=i/2;break;case"right":a-=i;break;case"inner":g===b-1?a-=i:g>0&&(a-=i/2)}d={left:a,top:r,width:i+t.width,height:n+t.height,color:e.backdropColor}}m.push({label:x,font:S,textOffset:C,options:{rotation:p,color:n,strokeColor:o,strokeWidth:c,textAlign:h,textBaseline:P,translation:[v,w],backdrop:d}})}return m}_getXAxisLabelAlignment(){const{position:e,ticks:t}=this.options;if(-Vn(this.labelRotation))return"top"===e?"left":"right";let n="center";return"start"===t.align?n="left":"end"===t.align?n="right":"inner"===t.align&&(n="inner"),n}_getYAxisLabelAlignment(e){const{position:t,ticks:{crossAlign:n,mirror:i,padding:r}}=this.options,a=e+r,s=this._getLabelSizes().widest.width;let o,l;return"left"===t?i?(l=this.right+r,"near"===n?o="left":"center"===n?(o="center",l+=s/2):(o="right",l+=s)):(l=this.right-a,"near"===n?o="right":"center"===n?(o="center",l-=s/2):(o="left",l=this.left)):"right"===t?i?(l=this.left+r,"near"===n?o="right":"center"===n?(o="center",l-=s/2):(o="left",l-=s)):(l=this.left+a,"near"===n?o="left":"center"===n?(o="center",l+=s/2):(o="right",l=this.right)):o="right",{textAlign:o,x:l}}_computeLabelArea(){if(this.options.ticks.mirror)return;const e=this.chart,t=this.options.position;return"left"===t||"right"===t?{top:0,left:this.left,bottom:e.height,right:this.right}:"top"===t||"bottom"===t?{top:this.top,left:0,bottom:this.bottom,right:e.width}:void 0}drawBackground(){const{ctx:e,options:{backgroundColor:t},left:n,top:i,width:r,height:a}=this;t&&(e.save(),e.fillStyle=t,e.fillRect(n,i,r,a),e.restore())}getLineWidthForValue(e){const t=this.options.grid;if(!this._isVisible()||!t.display)return 0;const n=this.ticks.findIndex(t=>t.value===e);if(n>=0){return t.setContext(this.getContext(n)).lineWidth}return 0}drawGrid(e){const t=this.options.grid,n=this.ctx,i=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(e));let r,a;const s=(e,t,i)=>{i.width&&i.color&&(n.save(),n.lineWidth=i.width,n.strokeStyle=i.color,n.setLineDash(i.borderDash||[]),n.lineDashOffset=i.borderDashOffset,n.beginPath(),n.moveTo(e.x,e.y),n.lineTo(t.x,t.y),n.stroke(),n.restore())};if(t.display)for(r=0,a=i.length;r<a;++r){const e=i[r];t.drawOnChartArea&&s({x:e.x1,y:e.y1},{x:e.x2,y:e.y2},e),t.drawTicks&&s({x:e.tx1,y:e.ty1},{x:e.tx2,y:e.ty2},{color:e.tickColor,width:e.tickWidth,borderDash:e.tickBorderDash,borderDashOffset:e.tickBorderDashOffset})}}drawBorder(){const{chart:e,ctx:t,options:{border:n,grid:i}}=this,r=n.setContext(this.getContext()),a=n.display?r.width:0;if(!a)return;const s=i.setContext(this.getContext(0)).lineWidth,o=this._borderValue;let l,c,u,d;this.isHorizontal()?(l=Li(e,this.left,a)-a/2,c=Li(e,this.right,s)+s/2,u=d=o):(u=Li(e,this.top,a)-a/2,d=Li(e,this.bottom,s)+s/2,l=c=o),t.save(),t.lineWidth=r.width,t.strokeStyle=r.color,t.beginPath(),t.moveTo(l,u),t.lineTo(c,d),t.stroke(),t.restore()}drawLabels(e){if(!this.options.ticks.display)return;const t=this.ctx,n=this._computeLabelArea();n&&zi(t,n);const i=this.getLabelItems(e);for(const r of i){const e=r.options,n=r.font;Vi(t,r.label,0,r.textOffset,n,e)}n&&Fi(t)}drawTitle(){const{ctx:e,options:{position:t,title:n,reverse:i}}=this;if(!n.display)return;const r=Zi(n.font),a=Ji(n.padding),s=n.align;let o=r.lineHeight/2;"bottom"===t||"center"===t||cn(t)?(o+=a.bottom,ln(n.text)&&(o+=r.lineHeight*(n.text.length-1))):o+=a.top;const{titleX:l,titleY:c,maxWidth:u,rotation:d}=function(e,t,n,i){const{top:r,left:a,bottom:s,right:o,chart:l}=e,{chartArea:c,scales:u}=l;let d,h,f,p=0;const m=s-r,g=o-a;if(e.isHorizontal()){if(h=li(i,a,o),cn(n)){const e=Object.keys(n)[0],i=n[e];f=u[e].getPixelForValue(i)+m-t}else f="center"===n?(c.bottom+c.top)/2+m-t:xs(e,n,t);d=o-a}else{if(cn(n)){const e=Object.keys(n)[0],i=n[e];h=u[e].getPixelForValue(i)-g+t}else h="center"===n?(c.left+c.right)/2-g+t:xs(e,n,t);f=li(i,s,r),p="left"===n?-An:An}return{titleX:h,titleY:f,maxWidth:d,rotation:p}}(this,o,t,s);Vi(e,n.text,0,0,r,{color:n.color,maxWidth:u,rotation:d,textAlign:Ns(s,t,i),textBaseline:"middle",translation:[l,c]})}draw(e){this._isVisible()&&(this.drawBackground(),this.drawGrid(e),this.drawBorder(),this.drawTitle(),this.drawLabels(e))}_layers(){const e=this.options,t=e.ticks&&e.ticks.z||0,n=hn(e.grid&&e.grid.z,-1),i=hn(e.border&&e.border.z,0);return this._isVisible()&&this.draw===Ms.prototype.draw?[{z:n,draw:e=>{this.drawBackground(),this.drawGrid(e),this.drawTitle()}},{z:i,draw:()=>{this.drawBorder()}},{z:t,draw:e=>{this.drawLabels(e)}}]:[{z:t,draw:e=>{this.draw(e)}}]}getMatchingVisibleMetas(e){const t=this.chart.getSortedVisibleDatasetMetas(),n=this.axis+"AxisID",i=[];let r,a;for(r=0,a=t.length;r<a;++r){const a=t[r];a[n]!==this.id||e&&a.type!==e||i.push(a)}return i}_resolveTickFontOptions(e){return Zi(this.options.ticks.setContext(this.getContext(e)).font)}_maxDigits(){const e=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/e}}class Cs{constructor(e,t,n){this.type=e,this.scope=t,this.override=n,this.items=Object.create(null)}isForType(e){return Object.prototype.isPrototypeOf.call(this.type.prototype,e.prototype)}register(e){const t=Object.getPrototypeOf(e);let n;(function(e){return"id"in e&&"defaults"in e})(t)&&(n=this.register(t));const i=this.items,r=e.id,a=this.scope+"."+r;if(!r)throw new Error("class does not have id: "+e);return r in i||(i[r]=e,function(e,t,n){const i=vn(Object.create(null),[n?Ei.get(n):{},Ei.get(t),e.defaults]);Ei.set(t,i),e.defaultRoutes&&function(e,t){Object.keys(t).forEach(n=>{const i=n.split("."),r=i.pop(),a=[e].concat(i).join("."),s=t[n].split("."),o=s.pop(),l=s.join(".");Ei.route(a,r,l,o)})}(t,e.defaultRoutes);e.descriptors&&Ei.describe(t,e.descriptors)}(e,a,n),this.override&&Ei.override(e.id,e.overrides)),a}get(e){return this.items[e]}unregister(e){const t=this.items,n=e.id,i=this.scope;n in t&&delete t[n],i&&n in Ei[i]&&(delete Ei[i][n],this.override&&delete Si[n])}}class Ps{constructor(){this.controllers=new Cs(fa,"datasets",!0),this.elements=new Cs(gs,"elements"),this.plugins=new Cs(Object,"plugins"),this.scales=new Cs(Ms,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this._each("register",t)}remove(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this._each("unregister",t)}addControllers(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this._each("register",t,this.controllers)}addElements(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this._each("register",t,this.elements)}addPlugins(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this._each("register",t,this.plugins)}addScales(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this._each("register",t,this.scales)}getController(e){return this._get(e,this.controllers,"controller")}getElement(e){return this._get(e,this.elements,"element")}getPlugin(e){return this._get(e,this.plugins,"plugin")}getScale(e){return this._get(e,this.scales,"scale")}removeControllers(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this._each("unregister",t,this.controllers)}removeElements(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this._each("unregister",t,this.elements)}removePlugins(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this._each("unregister",t,this.plugins)}removeScales(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];this._each("unregister",t,this.scales)}_each(e,t,n){[...t].forEach(t=>{const i=n||this._getRegistryForType(t);n||i.isForType(t)||i===this.plugins&&t.id?this._exec(e,i,t):mn(t,t=>{const i=n||this._getRegistryForType(t);this._exec(e,i,t)})})}_exec(e,t,n){const i=Nn(e);pn(n["before"+i],[],n),t[e](n),pn(n["after"+i],[],n)}_getRegistryForType(e){for(let t=0;t<this._typedRegistries.length;t++){const n=this._typedRegistries[t];if(n.isForType(e))return n}return this.plugins}_get(e,t,n){const i=t.get(e);if(void 0===i)throw new Error('"'+e+'" is not a registered '+n+".");return i}}var Es=new Ps;class js{constructor(){this._init=[]}notify(e,t,n,i){"beforeInit"===t&&(this._init=this._createDescriptors(e,!0),this._notify(this._init,e,"install"));const r=i?this._descriptors(e).filter(i):this._descriptors(e),a=this._notify(r,e,t,n);return"afterDestroy"===t&&(this._notify(r,e,"stop"),this._notify(this._init,e,"uninstall")),a}_notify(e,t,n,i){i=i||{};for(const r of e){const e=r.plugin;if(!1===pn(e[n],[t,i,r.options],e)&&i.cancelable)return!1}return!0}invalidate(){on(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(e){if(this._cache)return this._cache;const t=this._cache=this._createDescriptors(e);return this._notifyStateChanges(e),t}_createDescriptors(e,t){const n=e&&e.config,i=hn(n.options&&n.options.plugins,{}),r=function(e){const t={},n=[],i=Object.keys(Es.plugins.items);for(let a=0;a<i.length;a++)n.push(Es.getPlugin(i[a]));const r=e.plugins||[];for(let a=0;a<r.length;a++){const e=r[a];-1===n.indexOf(e)&&(n.push(e),t[e.id]=!0)}return{plugins:n,localIds:t}}(n);return!1!==i||t?function(e,t,n,i){let{plugins:r,localIds:a}=t;const s=[],o=e.getContext();for(const l of r){const t=l.id,r=Ts(n[t],i);null!==r&&s.push({plugin:l,options:Ls(e.config,{plugin:l,local:a[t]},r,o)})}return s}(e,r,i,t):[]}_notifyStateChanges(e){const t=this._oldCache||[],n=this._cache,i=(e,t)=>e.filter(e=>!t.some(t=>e.plugin.id===t.plugin.id));this._notify(i(t,n),e,"stop"),this._notify(i(n,t),e,"start")}}function Ts(e,t){return t||!1!==e?!0===e?{}:e:null}function Ls(e,t,n,i){let{plugin:r,local:a}=t;const s=e.pluginScopeKeys(r),o=e.getOptionScopes(n,s);return a&&r.defaults&&o.push(r.defaults),e.createResolver(o,i,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Os(e,t){const n=Ei.datasets[e]||{};return((t.datasets||{})[e]||{}).indexAxis||t.indexAxis||n.indexAxis||"x"}function As(e){if("x"===e||"y"===e||"r"===e)return e}function Ds(e){return"top"===e||"bottom"===e?"x":"left"===e||"right"===e?"y":void 0}function Rs(e){if(As(e))return e;for(var t=arguments.length,n=new Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];for(const r of n){const t=r.axis||Ds(r.position)||e.length>1&&As(e[0].toLowerCase());if(t)return t}throw new Error("Cannot determine type of '".concat(e,"' axis. Please provide 'axis' or 'position' option."))}function zs(e,t,n){if(n[t+"AxisID"]===e)return{axis:t}}function Fs(e,t){const n=Si[e.type]||{scales:{}},i=t.scales||{},r=Os(e.type,t),a=Object.create(null);return Object.keys(i).forEach(t=>{const s=i[t];if(!cn(s))return console.error("Invalid scale configuration for scale: ".concat(t));if(s._proxy)return console.warn("Ignoring resolver passed as options for scale: ".concat(t));const o=Rs(t,s,function(e,t){if(t.data&&t.data.datasets){const n=t.data.datasets.filter(t=>t.xAxisID===e||t.yAxisID===e);if(n.length)return zs(e,"x",n[0])||zs(e,"y",n[0])}return{}}(t,e),Ei.scales[s.type]),l=function(e,t){return e===t?"_index_":"_value_"}(o,r),c=n.scales||{};a[t]=wn(Object.create(null),[{axis:o},s,c[o],c[l]])}),e.data.datasets.forEach(n=>{const r=n.type||e.type,s=n.indexAxis||Os(r,t),o=(Si[r]||{}).scales||{};Object.keys(o).forEach(e=>{const t=function(e,t){let n=e;return"_index_"===e?n=t:"_value_"===e&&(n="x"===t?"y":"x"),n}(e,s),r=n[t+"AxisID"]||t;a[r]=a[r]||Object.create(null),wn(a[r],[{axis:t},i[r],o[e]])})}),Object.keys(a).forEach(e=>{const t=a[e];wn(t,[Ei.scales[t.type],Ei.scale])}),a}function Is(e){const t=e.options||(e.options={});t.plugins=hn(t.plugins,{}),t.scales=Fs(e,t)}function Ws(e){return(e=e||{}).datasets=e.datasets||[],e.labels=e.labels||[],e}const Bs=new Map,Hs=new Set;function Vs(e,t){let n=Bs.get(e);return n||(n=t(),Bs.set(e,n),Hs.add(n)),n}const Us=(e,t,n)=>{const i=Sn(t,n);void 0!==i&&e.add(i)};class $s{constructor(e){this._config=function(e){return(e=e||{}).data=Ws(e.data),Is(e),e}(e),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(e){this._config.type=e}get data(){return this._config.data}set data(e){this._config.data=Ws(e)}get options(){return this._config.options}set options(e){this._config.options=e}get plugins(){return this._config.plugins}update(){const e=this._config;this.clearCache(),Is(e)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(e){return Vs(e,()=>[["datasets.".concat(e),""]])}datasetAnimationScopeKeys(e,t){return Vs("".concat(e,".transition.").concat(t),()=>[["datasets.".concat(e,".transitions.").concat(t),"transitions.".concat(t)],["datasets.".concat(e),""]])}datasetElementScopeKeys(e,t){return Vs("".concat(e,"-").concat(t),()=>[["datasets.".concat(e,".elements.").concat(t),"datasets.".concat(e),"elements.".concat(t),""]])}pluginScopeKeys(e){const t=e.id,n=this.type;return Vs("".concat(n,"-plugin-").concat(t),()=>[["plugins.".concat(t),...e.additionalOptionScopes||[]]])}_cachedScopes(e,t){const n=this._scopeCache;let i=n.get(e);return i&&!t||(i=new Map,n.set(e,i)),i}getOptionScopes(e,t,n){const{options:i,type:r}=this,a=this._cachedScopes(e,n),s=a.get(t);if(s)return s;const o=new Set;t.forEach(t=>{e&&(o.add(e),t.forEach(t=>Us(o,e,t))),t.forEach(e=>Us(o,i,e)),t.forEach(e=>Us(o,Si[r]||{},e)),t.forEach(e=>Us(o,Ei,e)),t.forEach(e=>Us(o,Ni,e))});const l=Array.from(o);return 0===l.length&&l.push(Object.create(null)),Hs.has(t)&&a.set(t,l),l}chartOptionScopes(){const{options:e,type:t}=this;return[e,Si[t]||{},Ei.datasets[t]||{},{type:t},Ei,Ni]}resolveNamedOptions(e,t,n){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[""];const r={$shared:!0},{resolver:a,subPrefixes:s}=Ys(this._resolverCache,e,i);let o=a;if(function(e,t){const{isScriptable:n,isIndexable:i}=rr(e);for(const r of t){const t=n(r),a=i(r),s=(a||t)&&e[r];if(t&&(Cn(s)||qs(s))||a&&ln(s))return!0}return!1}(a,t)){r.$shared=!1;o=ir(a,n=Cn(n)?n():n,this.createResolver(e,n,s))}for(const l of t)r[l]=o[l];return r}createResolver(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[""],i=arguments.length>3?arguments[3]:void 0;const{resolver:r}=Ys(this._resolverCache,e,n);return cn(t)?ir(r,t,void 0,i):r}}function Ys(e,t,n){let i=e.get(t);i||(i=new Map,e.set(t,i));const r=n.join();let a=i.get(r);if(!a){a={resolver:nr(t,n),subPrefixes:n.filter(e=>!e.toLowerCase().includes("hover"))},i.set(r,a)}return a}const qs=e=>cn(e)&&Object.getOwnPropertyNames(e).some(t=>Cn(e[t]));const Ks=["top","bottom","left","right","chartArea"];function Qs(e,t){return"top"===e||"bottom"===e||-1===Ks.indexOf(e)&&"x"===t}function Xs(e,t){return function(n,i){return n[e]===i[e]?n[t]-i[t]:n[e]-i[e]}}function Gs(e){const t=e.chart,n=t.options.animation;t.notifyPlugins("afterRender"),pn(n&&n.onComplete,[e],t)}function Js(e){const t=e.chart,n=t.options.animation;pn(n&&n.onProgress,[e],t)}function Zs(e){return _r()&&"string"===typeof e?e=document.getElementById(e):e&&e.length&&(e=e[0]),e&&e.canvas&&(e=e.canvas),e}const eo={},to=e=>{const t=Zs(e);return Object.values(eo).filter(e=>e.canvas===t).pop()};function no(e,t,n){const i=Object.keys(e);for(const r of i){const i=+r;if(i>=t){const a=e[r];delete e[r],(n>0||i>t)&&(e[i+n]=a)}}}class io{static register(){Es.add(...arguments),ro()}static unregister(){Es.remove(...arguments),ro()}constructor(e,t){const n=this.config=new $s(t),i=Zs(e),r=to(i);if(r)throw new Error("Canvas is already in use. Chart with ID '"+r.id+"' must be destroyed before the canvas with ID '"+r.canvas.id+"' can be reused.");const a=n.createResolver(n.chartOptionScopes(),this.getContext());this.platform=new(n.platform||function(e){return!_r()||"undefined"!==typeof OffscreenCanvas&&e instanceof OffscreenCanvas?es:ms}(i)),this.platform.updateConfig(n);const s=this.platform.acquireContext(i,a.aspectRatio),o=s&&s.canvas,l=o&&o.height,c=o&&o.width;this.id=sn(),this.ctx=s,this.canvas=o,this.width=c,this.height=l,this._options=a,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new js,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=function(e,t){let n;return function(){for(var i=arguments.length,r=new Array(i),a=0;a<i;a++)r[a]=arguments[a];return t?(clearTimeout(n),n=setTimeout(e,t,r)):e.apply(this,r),t}}(e=>this.update(e),a.resizeDelay||0),this._dataChanges=[],eo[this.id]=this,s&&o?(Gr.listen(this,"complete",Gs),Gr.listen(this,"progress",Js),this._initialize(),this.attached&&this.update()):console.error("Failed to create chart: can't acquire context from the given item")}get aspectRatio(){const{options:{aspectRatio:e,maintainAspectRatio:t},width:n,height:i,_aspectRatio:r}=this;return on(e)?t&&r?r:i?n/i:null:e}get data(){return this.config.data}set data(e){this.config.data=e}get options(){return this._options}set options(e){this.config.options=e}get registry(){return Es}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():Lr(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return Oi(this.canvas,this.ctx),this}stop(){return Gr.stop(this),this}resize(e,t){Gr.running(this)?this._resizeBeforeDraw={width:e,height:t}:this._resize(e,t)}_resize(e,t){const n=this.options,i=this.canvas,r=n.maintainAspectRatio&&this.aspectRatio,a=this.platform.getMaximumSize(i,e,t,r),s=n.devicePixelRatio||this.platform.getDevicePixelRatio(),o=this.width?"resize":"attach";this.width=a.width,this.height=a.height,this._aspectRatio=this.aspectRatio,Lr(this,s,!0)&&(this.notifyPlugins("resize",{size:a}),pn(n.onResize,[this,a],this),this.attached&&this._doResize(o)&&this.render())}ensureScalesHaveIDs(){mn(this.options.scales||{},(e,t)=>{e.id=t})}buildOrUpdateScales(){const e=this.options,t=e.scales,n=this.scales,i=Object.keys(n).reduce((e,t)=>(e[t]=!1,e),{});let r=[];t&&(r=r.concat(Object.keys(t).map(e=>{const n=t[e],i=Rs(e,n),r="r"===i,a="x"===i;return{options:n,dposition:r?"chartArea":a?"bottom":"left",dtype:r?"radialLinear":a?"category":"linear"}}))),mn(r,t=>{const r=t.options,a=r.id,s=Rs(a,r),o=hn(r.type,t.dtype);void 0!==r.position&&Qs(r.position,s)===Qs(t.dposition)||(r.position=t.dposition),i[a]=!0;let l=null;if(a in n&&n[a].type===o)l=n[a];else{l=new(Es.getScale(o))({id:a,type:o,ctx:this.ctx,chart:this}),n[l.id]=l}l.init(r,e)}),mn(i,(e,t)=>{e||delete n[t]}),mn(n,e=>{Ja.configure(this,e,e.options),Ja.addBox(this,e)})}_updateMetasets(){const e=this._metasets,t=this.data.datasets.length,n=e.length;if(e.sort((e,t)=>e.index-t.index),n>t){for(let e=t;e<n;++e)this._destroyDatasetMeta(e);e.splice(t,n-t)}this._sortedMetasets=e.slice(0).sort(Xs("order","index"))}_removeUnreferencedMetasets(){const{_metasets:e,data:{datasets:t}}=this;e.length>t.length&&delete this._stacks,e.forEach((e,n)=>{0===t.filter(t=>t===e._dataset).length&&this._destroyDatasetMeta(n)})}buildOrUpdateControllers(){const e=[],t=this.data.datasets;let n,i;for(this._removeUnreferencedMetasets(),n=0,i=t.length;n<i;n++){const i=t[n];let r=this.getDatasetMeta(n);const a=i.type||this.config.type;if(r.type&&r.type!==a&&(this._destroyDatasetMeta(n),r=this.getDatasetMeta(n)),r.type=a,r.indexAxis=i.indexAxis||Os(a,this.options),r.order=i.order||0,r.index=n,r.label=""+i.label,r.visible=this.isDatasetVisible(n),r.controller)r.controller.updateIndex(n),r.controller.linkScales();else{const t=Es.getController(a),{datasetElementType:i,dataElementType:s}=Ei.datasets[a];Object.assign(t,{dataElementType:Es.getElement(s),datasetElementType:i&&Es.getElement(i)}),r.controller=new t(this,n),e.push(r.controller)}}return this._updateMetasets(),e}_resetElements(){mn(this.data.datasets,(e,t)=>{this.getDatasetMeta(t).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(e){const t=this.config;t.update();const n=this._options=t.createResolver(t.chartOptionScopes(),this.getContext()),i=this._animationsDisabled=!n.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),!1===this.notifyPlugins("beforeUpdate",{mode:e,cancelable:!0}))return;const r=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let a=0;for(let l=0,c=this.data.datasets.length;l<c;l++){const{controller:e}=this.getDatasetMeta(l),t=!i&&-1===r.indexOf(e);e.buildOrUpdateElements(t),a=Math.max(+e.getMaxOverflow(),a)}a=this._minPadding=n.layout.autoPadding?a:0,this._updateLayout(a),i||mn(r,e=>{e.reset()}),this._updateDatasets(e),this.notifyPlugins("afterUpdate",{mode:e}),this._layers.sort(Xs("z","_idx"));const{_active:s,_lastEvent:o}=this;o?this._eventHandler(o,!0):s.length&&this._updateHoverStyles(s,s,!0),this.render()}_updateScales(){mn(this.scales,e=>{Ja.removeBox(this,e)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const e=this.options,t=new Set(Object.keys(this._listeners)),n=new Set(e.events);Pn(t,n)&&!!this._responsiveListeners===e.responsive||(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:e}=this,t=this._getUniformDataChanges()||[];for(const{method:n,start:i,count:r}of t){no(e,i,"_removeElements"===n?-r:r)}}_getUniformDataChanges(){const e=this._dataChanges;if(!e||!e.length)return;this._dataChanges=[];const t=this.data.datasets.length,n=t=>new Set(e.filter(e=>e[0]===t).map((e,t)=>t+","+e.splice(1).join(","))),i=n(0);for(let r=1;r<t;r++)if(!Pn(i,n(r)))return;return Array.from(i).map(e=>e.split(",")).map(e=>({method:e[1],start:+e[2],count:+e[3]}))}_updateLayout(e){if(!1===this.notifyPlugins("beforeLayout",{cancelable:!0}))return;Ja.update(this,this.width,this.height,e);const t=this.chartArea,n=t.width<=0||t.height<=0;this._layers=[],mn(this.boxes,e=>{n&&"chartArea"===e.position||(e.configure&&e.configure(),this._layers.push(...e._layers()))},this),this._layers.forEach((e,t)=>{e._idx=t}),this.notifyPlugins("afterLayout")}_updateDatasets(e){if(!1!==this.notifyPlugins("beforeDatasetsUpdate",{mode:e,cancelable:!0})){for(let e=0,t=this.data.datasets.length;e<t;++e)this.getDatasetMeta(e).controller.configure();for(let t=0,n=this.data.datasets.length;t<n;++t)this._updateDataset(t,Cn(e)?e({datasetIndex:t}):e);this.notifyPlugins("afterDatasetsUpdate",{mode:e})}}_updateDataset(e,t){const n=this.getDatasetMeta(e),i={meta:n,index:e,mode:t,cancelable:!0};!1!==this.notifyPlugins("beforeDatasetUpdate",i)&&(n.controller._update(t),i.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",i))}render(){!1!==this.notifyPlugins("beforeRender",{cancelable:!0})&&(Gr.has(this)?this.attached&&!Gr.running(this)&&Gr.start(this):(this.draw(),Gs({chart:this})))}draw(){let e;if(this._resizeBeforeDraw){const{width:e,height:t}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(e,t)}if(this.clear(),this.width<=0||this.height<=0)return;if(!1===this.notifyPlugins("beforeDraw",{cancelable:!0}))return;const t=this._layers;for(e=0;e<t.length&&t[e].z<=0;++e)t[e].draw(this.chartArea);for(this._drawDatasets();e<t.length;++e)t[e].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(e){const t=this._sortedMetasets,n=[];let i,r;for(i=0,r=t.length;i<r;++i){const r=t[i];e&&!r.visible||n.push(r)}return n}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(!1===this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0}))return;const e=this.getSortedVisibleDatasetMetas();for(let t=e.length-1;t>=0;--t)this._drawDataset(e[t]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(e){const t=this.ctx,n={meta:e,index:e.index,cancelable:!0},i=Qr(this,e);!1!==this.notifyPlugins("beforeDatasetDraw",n)&&(i&&zi(t,i),e.controller.draw(),i&&Fi(t),n.cancelable=!1,this.notifyPlugins("afterDatasetDraw",n))}isPointInArea(e){return Ri(e,this.chartArea,this._minPadding)}getElementsAtEventForMode(e,t,n,i){const r=Ia.modes[t];return"function"===typeof r?r(this,e,n,i):[]}getDatasetMeta(e){const t=this.data.datasets[e],n=this._metasets;let i=n.filter(e=>e&&e._dataset===t).pop();return i||(i={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:t&&t.order||0,index:e,_dataset:t,_parsed:[],_sorted:!1},n.push(i)),i}getContext(){return this.$context||(this.$context=tr(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(e){const t=this.data.datasets[e];if(!t)return!1;const n=this.getDatasetMeta(e);return"boolean"===typeof n.hidden?!n.hidden:!t.hidden}setDatasetVisibility(e,t){this.getDatasetMeta(e).hidden=!t}toggleDataVisibility(e){this._hiddenIndices[e]=!this._hiddenIndices[e]}getDataVisibility(e){return!this._hiddenIndices[e]}_updateVisibility(e,t,n){const i=n?"show":"hide",r=this.getDatasetMeta(e),a=r.controller._resolveAnimations(void 0,i);Mn(t)?(r.data[t].hidden=!n,this.update()):(this.setDatasetVisibility(e,n),a.update(r,{visible:n}),this.update(t=>t.datasetIndex===e?i:void 0))}hide(e,t){this._updateVisibility(e,t,!1)}show(e,t){this._updateVisibility(e,t,!0)}_destroyDatasetMeta(e){const t=this._metasets[e];t&&t.controller&&t.controller._destroy(),delete this._metasets[e]}_stop(){let e,t;for(this.stop(),Gr.remove(this),e=0,t=this.data.datasets.length;e<t;++e)this._destroyDatasetMeta(e)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:e,ctx:t}=this;this._stop(),this.config.clearCache(),e&&(this.unbindEvents(),Oi(e,t),this.platform.releaseContext(t),this.canvas=null,this.ctx=null),delete eo[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(){return this.canvas.toDataURL(...arguments)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const e=this._listeners,t=this.platform,n=(n,i)=>{t.addEventListener(this,n,i),e[n]=i},i=(e,t,n)=>{e.offsetX=t,e.offsetY=n,this._eventHandler(e)};mn(this.options.events,e=>n(e,i))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const e=this._responsiveListeners,t=this.platform,n=(n,i)=>{t.addEventListener(this,n,i),e[n]=i},i=(n,i)=>{e[n]&&(t.removeEventListener(this,n,i),delete e[n])},r=(e,t)=>{this.canvas&&this.resize(e,t)};let a;const s=()=>{i("attach",s),this.attached=!0,this.resize(),n("resize",r),n("detach",a)};a=()=>{this.attached=!1,i("resize",r),this._stop(),this._resize(0,0),n("attach",s)},t.isAttached(this.canvas)?s():a()}unbindEvents(){mn(this._listeners,(e,t)=>{this.platform.removeEventListener(this,t,e)}),this._listeners={},mn(this._responsiveListeners,(e,t)=>{this.platform.removeEventListener(this,t,e)}),this._responsiveListeners=void 0}updateHoverStyle(e,t,n){const i=n?"set":"remove";let r,a,s,o;for("dataset"===t&&(r=this.getDatasetMeta(e[0].datasetIndex),r.controller["_"+i+"DatasetHoverStyle"]()),s=0,o=e.length;s<o;++s){a=e[s];const t=a&&this.getDatasetMeta(a.datasetIndex).controller;t&&t[i+"HoverStyle"](a.element,a.datasetIndex,a.index)}}getActiveElements(){return this._active||[]}setActiveElements(e){const t=this._active||[],n=e.map(e=>{let{datasetIndex:t,index:n}=e;const i=this.getDatasetMeta(t);if(!i)throw new Error("No dataset found at index "+t);return{datasetIndex:t,element:i.data[n],index:n}});!gn(n,t)&&(this._active=n,this._lastEvent=null,this._updateHoverStyles(n,t))}notifyPlugins(e,t,n){return this._plugins.notify(this,e,t,n)}isPluginEnabled(e){return 1===this._plugins._cache.filter(t=>t.plugin.id===e).length}_updateHoverStyles(e,t,n){const i=this.options.hover,r=(e,t)=>e.filter(e=>!t.some(t=>e.datasetIndex===t.datasetIndex&&e.index===t.index)),a=r(t,e),s=n?e:r(e,t);a.length&&this.updateHoverStyle(a,i.mode,!1),s.length&&i.mode&&this.updateHoverStyle(s,i.mode,!0)}_eventHandler(e,t){const n={event:e,replay:t,cancelable:!0,inChartArea:this.isPointInArea(e)},i=t=>(t.options.events||this.options.events).includes(e.native.type);if(!1===this.notifyPlugins("beforeEvent",n,i))return;const r=this._handleEvent(e,t,n.inChartArea);return n.cancelable=!1,this.notifyPlugins("afterEvent",n,i),(r||n.changed)&&this.render(),this}_handleEvent(e,t,n){const{_active:i=[],options:r}=this,a=t,s=this._getActiveElements(e,i,n,a),o=function(e){return"mouseup"===e.type||"click"===e.type||"contextmenu"===e.type}(e),l=function(e,t,n,i){return n&&"mouseout"!==e.type?i?t:e:null}(e,this._lastEvent,n,o);n&&(this._lastEvent=null,pn(r.onHover,[e,s,this],this),o&&pn(r.onClick,[e,s,this],this));const c=!gn(s,i);return(c||t)&&(this._active=s,this._updateHoverStyles(s,i,t)),this._lastEvent=l,c}_getActiveElements(e,t,n,i){if("mouseout"===e.type)return[];if(!n)return t;const r=this.options.hover;return this.getElementsAtEventForMode(e,r.mode,r,i)}}function ro(){return mn(io.instances,e=>e._plugins.invalidate())}function ao(e,t,n,i){const r=Qi(e.options.borderRadius,["outerStart","outerEnd","innerStart","innerEnd"]);const a=(n-t)/2,s=Math.min(a,i*t/2),o=e=>{const t=(n-Math.min(a,e))*i/2;return Gn(e,0,Math.min(a,t))};return{outerStart:o(r.outerStart),outerEnd:o(r.outerEnd),innerStart:Gn(r.innerStart,0,s),innerEnd:Gn(r.innerEnd,0,s)}}function so(e,t,n,i){return{x:n+e*Math.cos(t),y:i+e*Math.sin(t)}}function oo(e,t,n,i,r,a){const{x:s,y:o,startAngle:l,pixelMargin:c,innerRadius:u}=t,d=Math.max(t.outerRadius+i+n-c,0),h=u>0?u+i+n+c:0;let f=0;const p=r-l;if(i){const e=((u>0?u-i:0)+(d>0?d-i:0))/2;f=(p-(0!==e?p*e/(e+i):p))/2}const m=(p-Math.max(.001,p*d-n/En)/d)/2,g=l+m+f,b=r-m-f,{outerStart:y,outerEnd:x,innerStart:v,innerEnd:w}=ao(t,h,d,b-g),k=d-y,_=d-x,S=g+y/k,N=b-x/_,M=h+v,C=h+w,P=g+v/M,E=b-w/C;if(e.beginPath(),a){const t=(S+N)/2;if(e.arc(s,o,d,S,t),e.arc(s,o,d,t,N),x>0){const t=so(_,N,s,o);e.arc(t.x,t.y,x,N,b+An)}const n=so(C,b,s,o);if(e.lineTo(n.x,n.y),w>0){const t=so(C,E,s,o);e.arc(t.x,t.y,w,b+An,E+Math.PI)}const i=(b-w/h+(g+v/h))/2;if(e.arc(s,o,h,b-w/h,i,!0),e.arc(s,o,h,i,g+v/h,!0),v>0){const t=so(M,P,s,o);e.arc(t.x,t.y,v,P+Math.PI,g-An)}const r=so(k,g,s,o);if(e.lineTo(r.x,r.y),y>0){const t=so(k,S,s,o);e.arc(t.x,t.y,y,g-An,S)}}else{e.moveTo(s,o);const t=Math.cos(S)*d+s,n=Math.sin(S)*d+o;e.lineTo(t,n);const i=Math.cos(N)*d+s,r=Math.sin(N)*d+o;e.lineTo(i,r)}e.closePath()}function lo(e,t,n,i,r){const{fullCircles:a,startAngle:s,circumference:o,options:l}=t,{borderWidth:c,borderJoinStyle:u,borderDash:d,borderDashOffset:h,borderRadius:f}=l,p="inner"===l.borderAlign;if(!c)return;e.setLineDash(d||[]),e.lineDashOffset=h,p?(e.lineWidth=2*c,e.lineJoin=u||"round"):(e.lineWidth=c,e.lineJoin=u||"bevel");let m=t.endAngle;if(a){oo(e,t,n,i,m,r);for(let t=0;t<a;++t)e.stroke();isNaN(o)||(m=s+(o%jn||jn))}p&&function(e,t,n){const{startAngle:i,pixelMargin:r,x:a,y:s,outerRadius:o,innerRadius:l}=t;let c=r/o;e.beginPath(),e.arc(a,s,o,i-c,n+c),l>r?(c=r/l,e.arc(a,s,l,n+c,i-c,!0)):e.arc(a,s,r,n+An,i-An),e.closePath(),e.clip()}(e,t,m),l.selfJoin&&m-s>=En&&0===f&&"miter"!==u&&function(e,t,n){const{startAngle:i,x:r,y:a,outerRadius:s,innerRadius:o,options:l}=t,{borderWidth:c,borderJoinStyle:u}=l,d=Math.min(c/s,Qn(i-n));if(e.beginPath(),e.arc(r,a,s-c/2,i+d/2,n-d/2),o>0){const t=Math.min(c/o,Qn(i-n));e.arc(r,a,o+c/2,n-t/2,i+t/2,!0)}else{const t=Math.min(c/2,s*Qn(i-n));if("round"===u)e.arc(r,a,t,n-En/2,i+En/2,!0);else if("bevel"===u){const s=2*t*t,o=-s*Math.cos(n+En/2)+r,l=-s*Math.sin(n+En/2)+a,c=s*Math.cos(i+En/2)+r,u=s*Math.sin(i+En/2)+a;e.lineTo(o,l),e.lineTo(c,u)}}e.closePath(),e.moveTo(0,0),e.rect(0,0,e.canvas.width,e.canvas.height),e.clip("evenodd")}(e,t,m),a||(oo(e,t,n,i,m,r),e.stroke())}l(io,"defaults",Ei),l(io,"instances",eo),l(io,"overrides",Si),l(io,"registry",Es),l(io,"version","4.5.0"),l(io,"getChart",to);class co extends gs{constructor(e){super(),l(this,"circumference",void 0),l(this,"endAngle",void 0),l(this,"fullCircles",void 0),l(this,"innerRadius",void 0),l(this,"outerRadius",void 0),l(this,"pixelMargin",void 0),l(this,"startAngle",void 0),this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,e&&Object.assign(this,e)}inRange(e,t,n){const i=this.getProps(["x","y"],n),{angle:r,distance:a}=Yn(i,{x:e,y:t}),{startAngle:s,endAngle:o,innerRadius:l,outerRadius:c,circumference:u}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],n),d=(this.options.spacing+this.options.borderWidth)/2,h=hn(u,o-s),f=Xn(r,s,o)&&s!==o,p=h>=jn||f,m=Jn(a,l+d,c+d);return p&&m}getCenterPoint(e){const{x:t,y:n,startAngle:i,endAngle:r,innerRadius:a,outerRadius:s}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],e),{offset:o,spacing:l}=this.options,c=(i+r)/2,u=(a+s+l+o)/2;return{x:t+Math.cos(c)*u,y:n+Math.sin(c)*u}}tooltipPosition(e){return this.getCenterPoint(e)}draw(e){const{options:t,circumference:n}=this,i=(t.offset||0)/4,r=(t.spacing||0)/2,a=t.circular;if(this.pixelMargin="inner"===t.borderAlign?.33:0,this.fullCircles=n>jn?Math.floor(n/jn):0,0===n||this.innerRadius<0||this.outerRadius<0)return;e.save();const s=(this.startAngle+this.endAngle)/2;e.translate(Math.cos(s)*i,Math.sin(s)*i);const o=i*(1-Math.sin(Math.min(En,n||0)));e.fillStyle=t.backgroundColor,e.strokeStyle=t.borderColor,function(e,t,n,i,r){const{fullCircles:a,startAngle:s,circumference:o}=t;let l=t.endAngle;if(a){oo(e,t,n,i,l,r);for(let t=0;t<a;++t)e.fill();isNaN(o)||(l=s+(o%jn||jn))}oo(e,t,n,i,l,r),e.fill()}(e,this,o,r,a),lo(e,this,o,r,a),e.restore()}}function uo(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t;e.lineCap=hn(n.borderCapStyle,t.borderCapStyle),e.setLineDash(hn(n.borderDash,t.borderDash)),e.lineDashOffset=hn(n.borderDashOffset,t.borderDashOffset),e.lineJoin=hn(n.borderJoinStyle,t.borderJoinStyle),e.lineWidth=hn(n.borderWidth,t.borderWidth),e.strokeStyle=hn(n.borderColor,t.borderColor)}function ho(e,t,n){e.lineTo(n.x,n.y)}function fo(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=e.length,{start:r=0,end:a=i-1}=n,{start:s,end:o}=t,l=Math.max(r,s),c=Math.min(a,o),u=r<s&&a<s||r>o&&a>o;return{count:i,start:l,loop:t.loop,ilen:c<l&&!u?i+c-l:c-l}}function po(e,t,n,i){const{points:r,options:a}=t,{count:s,start:o,loop:l,ilen:c}=fo(r,n,i),u=function(e){return e.stepped?Ii:e.tension||"monotone"===e.cubicInterpolationMode?Wi:ho}(a);let d,h,f,{move:p=!0,reverse:m}=i||{};for(d=0;d<=c;++d)h=r[(o+(m?c-d:d))%s],h.skip||(p?(e.moveTo(h.x,h.y),p=!1):u(e,f,h,m,a.stepped),f=h);return l&&(h=r[(o+(m?c:0))%s],u(e,f,h,m,a.stepped)),!!l}function mo(e,t,n,i){const r=t.points,{count:a,start:s,ilen:o}=fo(r,n,i),{move:l=!0,reverse:c}=i||{};let u,d,h,f,p,m,g=0,b=0;const y=e=>(s+(c?o-e:e))%a,x=()=>{f!==p&&(e.lineTo(g,p),e.lineTo(g,f),e.lineTo(g,m))};for(l&&(d=r[y(0)],e.moveTo(d.x,d.y)),u=0;u<=o;++u){if(d=r[y(u)],d.skip)continue;const t=d.x,n=d.y,i=0|t;i===h?(n<f?f=n:n>p&&(p=n),g=(b*g+t)/++b):(x(),e.lineTo(t,n),h=i,b=0,f=p=n),m=n}x()}function go(e){const t=e.options,n=t.borderDash&&t.borderDash.length;return!e._decimated&&!e._loop&&!t.tension&&"monotone"!==t.cubicInterpolationMode&&!t.stepped&&!n?mo:po}l(co,"id","arc"),l(co,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0,selfJoin:!1}),l(co,"defaultRoutes",{backgroundColor:"backgroundColor"}),l(co,"descriptors",{_scriptable:!0,_indexable:e=>"borderDash"!==e});const bo="function"===typeof Path2D;function yo(e,t,n,i){bo&&!t.options.segment?function(e,t,n,i){let r=t._path;r||(r=t._path=new Path2D,t.path(r,n,i)&&r.closePath()),uo(e,t.options),e.stroke(r)}(e,t,n,i):function(e,t,n,i){const{segments:r,options:a}=t,s=go(t);for(const o of r)uo(e,a,o.style),e.beginPath(),s(e,t,o,{start:n,end:n+i-1})&&e.closePath(),e.stroke()}(e,t,n,i)}class xo extends gs{constructor(e){super(),this.animated=!0,this.options=void 0,this._chart=void 0,this._loop=void 0,this._fullLoop=void 0,this._path=void 0,this._points=void 0,this._segments=void 0,this._decimated=!1,this._pointsUpdated=!1,this._datasetIndex=void 0,e&&Object.assign(this,e)}updateControlPoints(e,t){const n=this.options;if((n.tension||"monotone"===n.cubicInterpolationMode)&&!n.stepped&&!this._pointsUpdated){const i=n.spanGaps?this._loop:this._fullLoop;kr(this._points,n,e,i,t),this._pointsUpdated=!0}}set points(e){this._points=e,delete this._segments,delete this._path,this._pointsUpdated=!1}get points(){return this._points}get segments(){return this._segments||(this._segments=function(e,t){const n=e.points,i=e.options.spanGaps,r=n.length;if(!r)return[];const a=!!e._loop,{start:s,end:o}=function(e,t,n,i){let r=0,a=t-1;if(n&&!i)for(;r<t&&!e[r].skip;)r++;for(;r<t&&e[r].skip;)r++;for(r%=t,n&&(a+=r);a>r&&e[a%t].skip;)a--;return a%=t,{start:r,end:a}}(n,r,a,i);return $r(e,!0===i?[{start:s,end:o,loop:a}]:function(e,t,n,i){const r=e.length,a=[];let s,o=t,l=e[t];for(s=t+1;s<=n;++s){const n=e[s%r];n.skip||n.stop?l.skip||(i=!1,a.push({start:t%r,end:(s-1)%r,loop:i}),t=o=n.stop?s:null):(o=s,l.skip&&(t=s)),l=n}return null!==o&&a.push({start:t%r,end:o%r,loop:i}),a}(n,s,o<s?o+r:o,!!e._fullLoop&&0===s&&o===r-1),n,t)}(this,this.options.segment))}first(){const e=this.segments,t=this.points;return e.length&&t[e[0].start]}last(){const e=this.segments,t=this.points,n=e.length;return n&&t[e[n-1].end]}interpolate(e,t){const n=this.options,i=e[t],r=this.points,a=Ur(this,{property:t,start:i,end:i});if(!a.length)return;const s=[],o=function(e){return e.stepped?Rr:e.tension||"monotone"===e.cubicInterpolationMode?zr:Dr}(n);let l,c;for(l=0,c=a.length;l<c;++l){const{start:c,end:u}=a[l],d=r[c],h=r[u];if(d===h){s.push(d);continue}const f=o(d,h,Math.abs((i-d[t])/(h[t]-d[t])),n.stepped);f[t]=e[t],s.push(f)}return 1===s.length?s[0]:s}pathSegment(e,t,n){return go(this)(e,this,t,n)}path(e,t,n){const i=this.segments,r=go(this);let a=this._loop;t=t||0,n=n||this.points.length-t;for(const s of i)a&=r(e,this,s,{start:t,end:t+n-1});return!!a}draw(e,t,n,i){const r=this.options||{};(this.points||[]).length&&r.borderWidth&&(e.save(),yo(e,this,n,i),e.restore()),this.animated&&(this._pointsUpdated=!1,this._path=void 0)}}function vo(e,t,n,i){const r=e.options,{[n]:a}=e.getProps([n],i);return Math.abs(t-a)<r.radius+r.hitRadius}l(xo,"id","line"),l(xo,"defaults",{borderCapStyle:"butt",borderDash:[],borderDashOffset:0,borderJoinStyle:"miter",borderWidth:3,capBezierPoints:!0,cubicInterpolationMode:"default",fill:!1,spanGaps:!1,stepped:!1,tension:0}),l(xo,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"}),l(xo,"descriptors",{_scriptable:!0,_indexable:e=>"borderDash"!==e&&"fill"!==e});class wo extends gs{constructor(e){super(),l(this,"parsed",void 0),l(this,"skip",void 0),l(this,"stop",void 0),this.options=void 0,this.parsed=void 0,this.skip=void 0,this.stop=void 0,e&&Object.assign(this,e)}inRange(e,t,n){const i=this.options,{x:r,y:a}=this.getProps(["x","y"],n);return Math.pow(e-r,2)+Math.pow(t-a,2)<Math.pow(i.hitRadius+i.radius,2)}inXRange(e,t){return vo(this,e,"x",t)}inYRange(e,t){return vo(this,e,"y",t)}getCenterPoint(e){const{x:t,y:n}=this.getProps(["x","y"],e);return{x:t,y:n}}size(e){let t=(e=e||this.options||{}).radius||0;t=Math.max(t,t&&e.hoverRadius||0);return 2*(t+(t&&e.borderWidth||0))}draw(e,t){const n=this.options;this.skip||n.radius<.1||!Ri(this,t,this.size(n)/2)||(e.strokeStyle=n.borderColor,e.lineWidth=n.borderWidth,e.fillStyle=n.backgroundColor,Ai(e,n,this.x,this.y))}getRange(){const e=this.options||{};return e.radius+e.hitRadius}}function ko(e,t){const{x:n,y:i,base:r,width:a,height:s}=e.getProps(["x","y","base","width","height"],t);let o,l,c,u,d;return e.horizontal?(d=s/2,o=Math.min(n,r),l=Math.max(n,r),c=i-d,u=i+d):(d=a/2,o=n-d,l=n+d,c=Math.min(i,r),u=Math.max(i,r)),{left:o,top:c,right:l,bottom:u}}function _o(e,t,n,i){return e?0:Gn(t,n,i)}function So(e){const t=ko(e),n=t.right-t.left,i=t.bottom-t.top,r=function(e,t,n){const i=e.options.borderWidth,r=e.borderSkipped,a=Xi(i);return{t:_o(r.top,a.top,0,n),r:_o(r.right,a.right,0,t),b:_o(r.bottom,a.bottom,0,n),l:_o(r.left,a.left,0,t)}}(e,n/2,i/2),a=function(e,t,n){const{enableBorderRadius:i}=e.getProps(["enableBorderRadius"]),r=e.options.borderRadius,a=Gi(r),s=Math.min(t,n),o=e.borderSkipped,l=i||cn(r);return{topLeft:_o(!l||o.top||o.left,a.topLeft,0,s),topRight:_o(!l||o.top||o.right,a.topRight,0,s),bottomLeft:_o(!l||o.bottom||o.left,a.bottomLeft,0,s),bottomRight:_o(!l||o.bottom||o.right,a.bottomRight,0,s)}}(e,n/2,i/2);return{outer:{x:t.left,y:t.top,w:n,h:i,radius:a},inner:{x:t.left+r.l,y:t.top+r.t,w:n-r.l-r.r,h:i-r.t-r.b,radius:{topLeft:Math.max(0,a.topLeft-Math.max(r.t,r.l)),topRight:Math.max(0,a.topRight-Math.max(r.t,r.r)),bottomLeft:Math.max(0,a.bottomLeft-Math.max(r.b,r.l)),bottomRight:Math.max(0,a.bottomRight-Math.max(r.b,r.r))}}}}function No(e,t,n,i){const r=null===t,a=null===n,s=e&&!(r&&a)&&ko(e,i);return s&&(r||Jn(t,s.left,s.right))&&(a||Jn(n,s.top,s.bottom))}function Mo(e,t){e.rect(t.x,t.y,t.w,t.h)}function Co(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=e.x!==n.x?-t:0,r=e.y!==n.y?-t:0,a=(e.x+e.w!==n.x+n.w?t:0)-i,s=(e.y+e.h!==n.y+n.h?t:0)-r;return{x:e.x+i,y:e.y+r,w:e.w+a,h:e.h+s,radius:e.radius}}l(wo,"id","point"),l(wo,"defaults",{borderWidth:1,hitRadius:1,hoverBorderWidth:1,hoverRadius:4,pointStyle:"circle",radius:3,rotation:0}),l(wo,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});class Po extends gs{constructor(e){super(),this.options=void 0,this.horizontal=void 0,this.base=void 0,this.width=void 0,this.height=void 0,this.inflateAmount=void 0,e&&Object.assign(this,e)}draw(e){const{inflateAmount:t,options:{borderColor:n,backgroundColor:i}}=this,{inner:r,outer:a}=So(this),s=(o=a.radius).topLeft||o.topRight||o.bottomLeft||o.bottomRight?Ui:Mo;var o;e.save(),a.w===r.w&&a.h===r.h||(e.beginPath(),s(e,Co(a,t,r)),e.clip(),s(e,Co(r,-t,a)),e.fillStyle=n,e.fill("evenodd")),e.beginPath(),s(e,Co(r,t)),e.fillStyle=i,e.fill(),e.restore()}inRange(e,t,n){return No(this,e,t,n)}inXRange(e,t){return No(this,e,null,t)}inYRange(e,t){return No(this,null,e,t)}getCenterPoint(e){const{x:t,y:n,base:i,horizontal:r}=this.getProps(["x","y","base","horizontal"],e);return{x:r?(t+i)/2:t,y:r?n:(n+i)/2}}getRange(e){return"x"===e?this.width/2:this.height/2}}l(Po,"id","bar"),l(Po,"defaults",{borderSkipped:"start",borderWidth:0,borderRadius:0,inflateAmount:"auto",pointStyle:void 0}),l(Po,"defaultRoutes",{backgroundColor:"backgroundColor",borderColor:"borderColor"});function Eo(e,t,n,i){if(i)return;let r=t[e],a=n[e];return"angle"===e&&(r=Qn(r),a=Qn(a)),{property:e,start:r,end:a}}function jo(e,t,n){for(;t>e;t--){const e=n[t];if(!isNaN(e.x)&&!isNaN(e.y))break}return t}function To(e,t,n,i){return e&&t?i(e[n],t[n]):e?e[n]:t?t[n]:0}function Lo(e,t){let n=[],i=!1;return ln(e)?(i=!0,n=e):n=function(e,t){const{x:n=null,y:i=null}=e||{},r=t.points,a=[];return t.segments.forEach(e=>{let{start:t,end:s}=e;s=jo(t,s,r);const o=r[t],l=r[s];null!==i?(a.push({x:o.x,y:i}),a.push({x:l.x,y:i})):null!==n&&(a.push({x:n,y:o.y}),a.push({x:n,y:l.y}))}),a}(e,t),n.length?new xo({points:n,options:{tension:0},_loop:i,_fullLoop:i}):null}function Oo(e){return e&&!1!==e.fill}function Ao(e,t,n){let i=e[t].fill;const r=[t];let a;if(!n)return i;for(;!1!==i&&-1===r.indexOf(i);){if(!un(i))return i;if(a=e[i],!a)return!1;if(a.visible)return i;r.push(i),i=a.fill}return!1}function Do(e,t,n){const i=function(e){const t=e.options,n=t.fill;let i=hn(n&&n.target,n);void 0===i&&(i=!!t.backgroundColor);if(!1===i||null===i)return!1;if(!0===i)return"origin";return i}(e);if(cn(i))return!isNaN(i.value)&&i;let r=parseFloat(i);return un(r)&&Math.floor(r)===r?function(e,t,n,i){"-"!==e&&"+"!==e||(n=t+n);if(n===t||n<0||n>=i)return!1;return n}(i[0],t,r,n):["origin","start","end","stack","shape"].indexOf(i)>=0&&i}function Ro(e,t,n){const i=[];for(let r=0;r<n.length;r++){const a=n[r],{first:s,last:o,point:l}=zo(a,t,"x");if(!(!l||s&&o))if(s)i.unshift(l);else if(e.push(l),!o)break}e.push(...i)}function zo(e,t,n){const i=e.interpolate(t,n);if(!i)return{};const r=i[n],a=e.segments,s=e.points;let o=!1,l=!1;for(let c=0;c<a.length;c++){const e=a[c],t=s[e.start][n],i=s[e.end][n];if(Jn(r,t,i)){o=r===t,l=r===i;break}}return{first:o,last:l,point:i}}class Fo{constructor(e){this.x=e.x,this.y=e.y,this.radius=e.radius}pathSegment(e,t,n){const{x:i,y:r,radius:a}=this;return t=t||{start:0,end:jn},e.arc(i,r,a,t.end,t.start,!0),!n.bounds}interpolate(e){const{x:t,y:n,radius:i}=this,r=e.angle;return{x:t+Math.cos(r)*i,y:n+Math.sin(r)*i,angle:r}}}function Io(e){const{chart:t,fill:n,line:i}=e;if(un(n))return function(e,t){const n=e.getDatasetMeta(t),i=n&&e.isDatasetVisible(t);return i?n.dataset:null}(t,n);if("stack"===n)return function(e){const{scale:t,index:n,line:i}=e,r=[],a=i.segments,s=i.points,o=function(e,t){const n=[],i=e.getMatchingVisibleMetas("line");for(let r=0;r<i.length;r++){const e=i[r];if(e.index===t)break;e.hidden||n.unshift(e.dataset)}return n}(t,n);o.push(Lo({x:null,y:t.bottom},i));for(let l=0;l<a.length;l++){const e=a[l];for(let t=e.start;t<=e.end;t++)Ro(r,s[t],o)}return new xo({points:r,options:{}})}(e);if("shape"===n)return!0;const r=function(e){const t=e.scale||{};if(t.getPointPositionForValue)return function(e){const{scale:t,fill:n}=e,i=t.options,r=t.getLabels().length,a=i.reverse?t.max:t.min,s=function(e,t,n){let i;return i="start"===e?n:"end"===e?t.options.reverse?t.min:t.max:cn(e)?e.value:t.getBaseValue(),i}(n,t,a),o=[];if(i.grid.circular){const e=t.getPointPositionForValue(0,a);return new Fo({x:e.x,y:e.y,radius:t.getDistanceFromCenterForValue(s)})}for(let l=0;l<r;++l)o.push(t.getPointPositionForValue(l,s));return o}(e);return function(e){const{scale:t={},fill:n}=e,i=function(e,t){let n=null;return"start"===e?n=t.bottom:"end"===e?n=t.top:cn(e)?n=t.getPixelForValue(e.value):t.getBasePixel&&(n=t.getBasePixel()),n}(n,t);if(un(i)){const e=t.isHorizontal();return{x:e?i:null,y:e?null:i}}return null}(e)}(e);return r instanceof Fo?r:Lo(r,i)}function Wo(e,t,n){const i=Io(t),{chart:r,index:a,line:s,scale:o,axis:l}=t,c=s.options,u=c.fill,d=c.backgroundColor,{above:h=d,below:f=d}=u||{},p=r.getDatasetMeta(a),m=Qr(r,p);i&&s.points.length&&(zi(e,n),function(e,t){const{line:n,target:i,above:r,below:a,area:s,scale:o,clip:l}=t,c=n._loop?"angle":t.axis;e.save();let u=a;a!==r&&("x"===c?(Bo(e,i,s.top),Vo(e,{line:n,target:i,color:r,scale:o,property:c,clip:l}),e.restore(),e.save(),Bo(e,i,s.bottom)):"y"===c&&(Ho(e,i,s.left),Vo(e,{line:n,target:i,color:a,scale:o,property:c,clip:l}),e.restore(),e.save(),Ho(e,i,s.right),u=r));Vo(e,{line:n,target:i,color:u,scale:o,property:c,clip:l}),e.restore()}(e,{line:s,target:i,above:h,below:f,area:n,scale:o,axis:l,clip:m}),Fi(e))}function Bo(e,t,n){const{segments:i,points:r}=t;let a=!0,s=!1;e.beginPath();for(const o of i){const{start:i,end:l}=o,c=r[i],u=r[jo(i,l,r)];a?(e.moveTo(c.x,c.y),a=!1):(e.lineTo(c.x,n),e.lineTo(c.x,c.y)),s=!!t.pathSegment(e,o,{move:s}),s?e.closePath():e.lineTo(u.x,n)}e.lineTo(t.first().x,n),e.closePath(),e.clip()}function Ho(e,t,n){const{segments:i,points:r}=t;let a=!0,s=!1;e.beginPath();for(const o of i){const{start:i,end:l}=o,c=r[i],u=r[jo(i,l,r)];a?(e.moveTo(c.x,c.y),a=!1):(e.lineTo(n,c.y),e.lineTo(c.x,c.y)),s=!!t.pathSegment(e,o,{move:s}),s?e.closePath():e.lineTo(n,u.y)}e.lineTo(n,t.first().y),e.closePath(),e.clip()}function Vo(e,t){const{line:n,target:i,property:r,color:a,scale:s,clip:o}=t,l=function(e,t,n){const i=e.segments,r=e.points,a=t.points,s=[];for(const o of i){let{start:e,end:i}=o;i=jo(e,i,r);const l=Eo(n,r[e],r[i],o.loop);if(!t.segments){s.push({source:o,target:l,start:r[e],end:r[i]});continue}const c=Ur(t,l);for(const t of c){const e=Eo(n,a[t.start],a[t.end],t.loop),i=Vr(o,r,e);for(const r of i)s.push({source:r,target:t,start:{[n]:To(l,e,"start",Math.max)},end:{[n]:To(l,e,"end",Math.min)}})}}return s}(n,i,r);for(const{source:c,target:u,start:d,end:h}of l){const{style:{backgroundColor:t=a}={}}=c,l=!0!==i;e.save(),e.fillStyle=t,Uo(e,s,o,l&&Eo(r,d,h)),e.beginPath();const f=!!n.pathSegment(e,c);let p;if(l){f?e.closePath():$o(e,i,h,r);const t=!!i.pathSegment(e,u,{move:f,reverse:!0});p=f&&t,p||$o(e,i,d,r)}e.closePath(),e.fill(p?"evenodd":"nonzero"),e.restore()}}function Uo(e,t,n,i){const r=t.chart.chartArea,{property:a,start:s,end:o}=i||{};if("x"===a||"y"===a){let t,i,l,c;"x"===a?(t=s,i=r.top,l=o,c=r.bottom):(t=r.left,i=s,l=r.right,c=o),e.beginPath(),n&&(t=Math.max(t,n.left),l=Math.min(l,n.right),i=Math.max(i,n.top),c=Math.min(c,n.bottom)),e.rect(t,i,l-t,c-i),e.clip()}}function $o(e,t,n,i){const r=t.interpolate(n,i);r&&e.lineTo(r.x,r.y)}var Yo={id:"filler",afterDatasetsUpdate(e,t,n){const i=(e.data.datasets||[]).length,r=[];let a,s,o,l;for(s=0;s<i;++s)a=e.getDatasetMeta(s),o=a.dataset,l=null,o&&o.options&&o instanceof xo&&(l={visible:e.isDatasetVisible(s),index:s,fill:Do(o,s,i),chart:e,axis:a.controller.options.indexAxis,scale:a.vScale,line:o}),a.$filler=l,r.push(l);for(s=0;s<i;++s)l=r[s],l&&!1!==l.fill&&(l.fill=Ao(r,s,n.propagate))},beforeDraw(e,t,n){const i="beforeDraw"===n.drawTime,r=e.getSortedVisibleDatasetMetas(),a=e.chartArea;for(let s=r.length-1;s>=0;--s){const t=r[s].$filler;t&&(t.line.updateControlPoints(a,t.axis),i&&t.fill&&Wo(e.ctx,t,a))}},beforeDatasetsDraw(e,t,n){if("beforeDatasetsDraw"!==n.drawTime)return;const i=e.getSortedVisibleDatasetMetas();for(let r=i.length-1;r>=0;--r){const t=i[r].$filler;Oo(t)&&Wo(e.ctx,t,e.chartArea)}},beforeDatasetDraw(e,t,n){const i=t.meta.$filler;Oo(i)&&"beforeDatasetDraw"===n.drawTime&&Wo(e.ctx,i,e.chartArea)},defaults:{propagate:!0,drawTime:"beforeDatasetDraw"}};const qo=(e,t)=>{let{boxHeight:n=t,boxWidth:i=t}=e;return e.usePointStyle&&(n=Math.min(n,t),i=e.pointStyleWidth||Math.min(i,t)),{boxWidth:i,boxHeight:n,itemHeight:Math.max(t,n)}};class Ko extends gs{constructor(e){super(),this._added=!1,this.legendHitBoxes=[],this._hoveredItem=null,this.doughnutMode=!1,this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this.legendItems=void 0,this.columnSizes=void 0,this.lineWidths=void 0,this.maxHeight=void 0,this.maxWidth=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.height=void 0,this.width=void 0,this._margins=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,t,n){this.maxWidth=e,this.maxHeight=t,this._margins=n,this.setDimensions(),this.buildLabels(),this.fit()}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=this._margins.left,this.right=this.width):(this.height=this.maxHeight,this.top=this._margins.top,this.bottom=this.height)}buildLabels(){const e=this.options.labels||{};let t=pn(e.generateLabels,[this.chart],this)||[];e.filter&&(t=t.filter(t=>e.filter(t,this.chart.data))),e.sort&&(t=t.sort((t,n)=>e.sort(t,n,this.chart.data))),this.options.reverse&&t.reverse(),this.legendItems=t}fit(){const{options:e,ctx:t}=this;if(!e.display)return void(this.width=this.height=0);const n=e.labels,i=Zi(n.font),r=i.size,a=this._computeTitleHeight(),{boxWidth:s,itemHeight:o}=qo(n,r);let l,c;t.font=i.string,this.isHorizontal()?(l=this.maxWidth,c=this._fitRows(a,r,s,o)+10):(c=this.maxHeight,l=this._fitCols(a,i,s,o)+10),this.width=Math.min(l,e.maxWidth||this.maxWidth),this.height=Math.min(c,e.maxHeight||this.maxHeight)}_fitRows(e,t,n,i){const{ctx:r,maxWidth:a,options:{labels:{padding:s}}}=this,o=this.legendHitBoxes=[],l=this.lineWidths=[0],c=i+s;let u=e;r.textAlign="left",r.textBaseline="middle";let d=-1,h=-c;return this.legendItems.forEach((e,f)=>{const p=n+t/2+r.measureText(e.text).width;(0===f||l[l.length-1]+p+2*s>a)&&(u+=c,l[l.length-(f>0?0:1)]=0,h+=c,d++),o[f]={left:0,top:h,row:d,width:p,height:i},l[l.length-1]+=p+s}),u}_fitCols(e,t,n,i){const{ctx:r,maxHeight:a,options:{labels:{padding:s}}}=this,o=this.legendHitBoxes=[],l=this.columnSizes=[],c=a-e;let u=s,d=0,h=0,f=0,p=0;return this.legendItems.forEach((e,a)=>{const{itemWidth:m,itemHeight:g}=function(e,t,n,i,r){const a=function(e,t,n,i){let r=e.text;r&&"string"!==typeof r&&(r=r.reduce((e,t)=>e.length>t.length?e:t));return t+n.size/2+i.measureText(r).width}(i,e,t,n),s=function(e,t,n){let i=e;"string"!==typeof t.text&&(i=Qo(t,n));return i}(r,i,t.lineHeight);return{itemWidth:a,itemHeight:s}}(n,t,r,e,i);a>0&&h+g+2*s>c&&(u+=d+s,l.push({width:d,height:h}),f+=d+s,p++,d=h=0),o[a]={left:f,top:h,col:p,width:m,height:g},d=Math.max(d,m),h+=g+s}),u+=d,l.push({width:d,height:h}),u}adjustHitBoxes(){if(!this.options.display)return;const e=this._computeTitleHeight(),{legendHitBoxes:t,options:{align:n,labels:{padding:i},rtl:r}}=this,a=Fr(r,this.left,this.width);if(this.isHorizontal()){let r=0,s=li(n,this.left+i,this.right-this.lineWidths[r]);for(const o of t)r!==o.row&&(r=o.row,s=li(n,this.left+i,this.right-this.lineWidths[r])),o.top+=this.top+e+i,o.left=a.leftForLtr(a.x(s),o.width),s+=o.width+i}else{let r=0,s=li(n,this.top+e+i,this.bottom-this.columnSizes[r].height);for(const o of t)o.col!==r&&(r=o.col,s=li(n,this.top+e+i,this.bottom-this.columnSizes[r].height)),o.top=s,o.left+=this.left+i,o.left=a.leftForLtr(a.x(o.left),o.width),s+=o.height+i}}isHorizontal(){return"top"===this.options.position||"bottom"===this.options.position}draw(){if(this.options.display){const e=this.ctx;zi(e,this),this._draw(),Fi(e)}}_draw(){const{options:e,columnSizes:t,lineWidths:n,ctx:i}=this,{align:r,labels:a}=e,s=Ei.color,o=Fr(e.rtl,this.left,this.width),l=Zi(a.font),{padding:c}=a,u=l.size,d=u/2;let h;this.drawTitle(),i.textAlign=o.textAlign("left"),i.textBaseline="middle",i.lineWidth=.5,i.font=l.string;const{boxWidth:f,boxHeight:p,itemHeight:m}=qo(a,u),g=this.isHorizontal(),b=this._computeTitleHeight();h=g?{x:li(r,this.left+c,this.right-n[0]),y:this.top+c+b,line:0}:{x:this.left+c,y:li(r,this.top+b+c,this.bottom-t[0].height),line:0},Ir(this.ctx,e.textDirection);const y=m+c;this.legendItems.forEach((x,v)=>{i.strokeStyle=x.fontColor,i.fillStyle=x.fontColor;const w=i.measureText(x.text).width,k=o.textAlign(x.textAlign||(x.textAlign=a.textAlign)),_=f+d+w;let S=h.x,N=h.y;o.setWidth(this.width),g?v>0&&S+_+c>this.right&&(N=h.y+=y,h.line++,S=h.x=li(r,this.left+c,this.right-n[h.line])):v>0&&N+y>this.bottom&&(S=h.x=S+t[h.line].width+c,h.line++,N=h.y=li(r,this.top+b+c,this.bottom-t[h.line].height));if(function(e,t,n){if(isNaN(f)||f<=0||isNaN(p)||p<0)return;i.save();const r=hn(n.lineWidth,1);if(i.fillStyle=hn(n.fillStyle,s),i.lineCap=hn(n.lineCap,"butt"),i.lineDashOffset=hn(n.lineDashOffset,0),i.lineJoin=hn(n.lineJoin,"miter"),i.lineWidth=r,i.strokeStyle=hn(n.strokeStyle,s),i.setLineDash(hn(n.lineDash,[])),a.usePointStyle){const s={radius:p*Math.SQRT2/2,pointStyle:n.pointStyle,rotation:n.rotation,borderWidth:r},l=o.xPlus(e,f/2);Di(i,s,l,t+d,a.pointStyleWidth&&f)}else{const a=t+Math.max((u-p)/2,0),s=o.leftForLtr(e,f),l=Gi(n.borderRadius);i.beginPath(),Object.values(l).some(e=>0!==e)?Ui(i,{x:s,y:a,w:f,h:p,radius:l}):i.rect(s,a,f,p),i.fill(),0!==r&&i.stroke()}i.restore()}(o.x(S),N,x),S=((e,t,n,i)=>e===(i?"left":"right")?n:"center"===e?(t+n)/2:t)(k,S+f+d,g?S+_:this.right,e.rtl),function(e,t,n){Vi(i,n.text,e,t+m/2,l,{strikethrough:n.hidden,textAlign:o.textAlign(n.textAlign)})}(o.x(S),N,x),g)h.x+=_+c;else if("string"!==typeof x.text){const e=l.lineHeight;h.y+=Qo(x,e)+c}else h.y+=y}),Wr(this.ctx,e.textDirection)}drawTitle(){const e=this.options,t=e.title,n=Zi(t.font),i=Ji(t.padding);if(!t.display)return;const r=Fr(e.rtl,this.left,this.width),a=this.ctx,s=t.position,o=n.size/2,l=i.top+o;let c,u=this.left,d=this.width;if(this.isHorizontal())d=Math.max(...this.lineWidths),c=this.top+l,u=li(e.align,u,this.right-d);else{const t=this.columnSizes.reduce((e,t)=>Math.max(e,t.height),0);c=l+li(e.align,this.top,this.bottom-t-e.labels.padding-this._computeTitleHeight())}const h=li(s,u,u+d);a.textAlign=r.textAlign(oi(s)),a.textBaseline="middle",a.strokeStyle=t.color,a.fillStyle=t.color,a.font=n.string,Vi(a,t.text,h,c,n)}_computeTitleHeight(){const e=this.options.title,t=Zi(e.font),n=Ji(e.padding);return e.display?t.lineHeight+n.height:0}_getLegendItemAt(e,t){let n,i,r;if(Jn(e,this.left,this.right)&&Jn(t,this.top,this.bottom))for(r=this.legendHitBoxes,n=0;n<r.length;++n)if(i=r[n],Jn(e,i.left,i.left+i.width)&&Jn(t,i.top,i.top+i.height))return this.legendItems[n];return null}handleEvent(e){const t=this.options;if(!function(e,t){if(("mousemove"===e||"mouseout"===e)&&(t.onHover||t.onLeave))return!0;if(t.onClick&&("click"===e||"mouseup"===e))return!0;return!1}(e.type,t))return;const n=this._getLegendItemAt(e.x,e.y);if("mousemove"===e.type||"mouseout"===e.type){const a=this._hoveredItem,s=(r=n,null!==(i=a)&&null!==r&&i.datasetIndex===r.datasetIndex&&i.index===r.index);a&&!s&&pn(t.onLeave,[e,a,this],this),this._hoveredItem=n,n&&!s&&pn(t.onHover,[e,n,this],this)}else n&&pn(t.onClick,[e,n,this],this);var i,r}}function Qo(e,t){return t*(e.text?e.text.length:0)}var Xo={id:"legend",_element:Ko,start(e,t,n){const i=e.legend=new Ko({ctx:e.ctx,options:n,chart:e});Ja.configure(e,i,n),Ja.addBox(e,i)},stop(e){Ja.removeBox(e,e.legend),delete e.legend},beforeUpdate(e,t,n){const i=e.legend;Ja.configure(e,i,n),i.options=n},afterUpdate(e){const t=e.legend;t.buildLabels(),t.adjustHitBoxes()},afterEvent(e,t){t.replay||e.legend.handleEvent(t.event)},defaults:{display:!0,position:"top",align:"center",fullSize:!0,reverse:!1,weight:1e3,onClick(e,t,n){const i=t.datasetIndex,r=n.chart;r.isDatasetVisible(i)?(r.hide(i),t.hidden=!0):(r.show(i),t.hidden=!1)},onHover:null,onLeave:null,labels:{color:e=>e.chart.options.color,boxWidth:40,padding:10,generateLabels(e){const t=e.data.datasets,{labels:{usePointStyle:n,pointStyle:i,textAlign:r,color:a,useBorderRadius:s,borderRadius:o}}=e.legend.options;return e._getSortedDatasetMetas().map(e=>{const l=e.controller.getStyle(n?0:void 0),c=Ji(l.borderWidth);return{text:t[e.index].label,fillStyle:l.backgroundColor,fontColor:a,hidden:!e.visible,lineCap:l.borderCapStyle,lineDash:l.borderDash,lineDashOffset:l.borderDashOffset,lineJoin:l.borderJoinStyle,lineWidth:(c.width+c.height)/4,strokeStyle:l.borderColor,pointStyle:i||l.pointStyle,rotation:l.rotation,textAlign:r||l.textAlign,borderRadius:s&&(o||l.borderRadius),datasetIndex:e.index}},this)}},title:{color:e=>e.chart.options.color,display:!1,position:"center",text:""}},descriptors:{_scriptable:e=>!e.startsWith("on"),labels:{_scriptable:e=>!["generateLabels","filter","sort"].includes(e)}}};class Go extends gs{constructor(e){super(),this.chart=e.chart,this.options=e.options,this.ctx=e.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(e,t){const n=this.options;if(this.left=0,this.top=0,!n.display)return void(this.width=this.height=this.right=this.bottom=0);this.width=this.right=e,this.height=this.bottom=t;const i=ln(n.text)?n.text.length:1;this._padding=Ji(n.padding);const r=i*Zi(n.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=r:this.width=r}isHorizontal(){const e=this.options.position;return"top"===e||"bottom"===e}_drawArgs(e){const{top:t,left:n,bottom:i,right:r,options:a}=this,s=a.align;let o,l,c,u=0;return this.isHorizontal()?(l=li(s,n,r),c=t+e,o=r-n):("left"===a.position?(l=n+e,c=li(s,i,t),u=-.5*En):(l=r-e,c=li(s,t,i),u=.5*En),o=i-t),{titleX:l,titleY:c,maxWidth:o,rotation:u}}draw(){const e=this.ctx,t=this.options;if(!t.display)return;const n=Zi(t.font),i=n.lineHeight/2+this._padding.top,{titleX:r,titleY:a,maxWidth:s,rotation:o}=this._drawArgs(i);Vi(e,t.text,0,0,n,{color:t.color,maxWidth:s,rotation:o,textAlign:oi(t.align),textBaseline:"middle",translation:[r,a]})}}var Jo={id:"title",_element:Go,start(e,t,n){!function(e,t){const n=new Go({ctx:e.ctx,options:t,chart:e});Ja.configure(e,n,t),Ja.addBox(e,n),e.titleBlock=n}(e,n)},stop(e){const t=e.titleBlock;Ja.removeBox(e,t),delete e.titleBlock},beforeUpdate(e,t,n){const i=e.titleBlock;Ja.configure(e,i,n),i.options=n},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};new WeakMap;const Zo={average(e){if(!e.length)return!1;let t,n,i=new Set,r=0,a=0;for(t=0,n=e.length;t<n;++t){const n=e[t].element;if(n&&n.hasValue()){const e=n.tooltipPosition();i.add(e.x),r+=e.y,++a}}if(0===a||0===i.size)return!1;return{x:[...i].reduce((e,t)=>e+t)/i.size,y:r/a}},nearest(e,t){if(!e.length)return!1;let n,i,r,a=t.x,s=t.y,o=Number.POSITIVE_INFINITY;for(n=0,i=e.length;n<i;++n){const i=e[n].element;if(i&&i.hasValue()){const e=qn(t,i.getCenterPoint());e<o&&(o=e,r=i)}}if(r){const e=r.tooltipPosition();a=e.x,s=e.y}return{x:a,y:s}}};function el(e,t){return t&&(ln(t)?Array.prototype.push.apply(e,t):e.push(t)),e}function tl(e){return("string"===typeof e||e instanceof String)&&e.indexOf("\n")>-1?e.split("\n"):e}function nl(e,t){const{element:n,datasetIndex:i,index:r}=t,a=e.getDatasetMeta(i).controller,{label:s,value:o}=a.getLabelAndValue(r);return{chart:e,label:s,parsed:a.getParsed(r),raw:e.data.datasets[i].data[r],formattedValue:o,dataset:a.getDataset(),dataIndex:r,datasetIndex:i,element:n}}function il(e,t){const n=e.chart.ctx,{body:i,footer:r,title:a}=e,{boxWidth:s,boxHeight:o}=t,l=Zi(t.bodyFont),c=Zi(t.titleFont),u=Zi(t.footerFont),d=a.length,h=r.length,f=i.length,p=Ji(t.padding);let m=p.height,g=0,b=i.reduce((e,t)=>e+t.before.length+t.lines.length+t.after.length,0);if(b+=e.beforeBody.length+e.afterBody.length,d&&(m+=d*c.lineHeight+(d-1)*t.titleSpacing+t.titleMarginBottom),b){m+=f*(t.displayColors?Math.max(o,l.lineHeight):l.lineHeight)+(b-f)*l.lineHeight+(b-1)*t.bodySpacing}h&&(m+=t.footerMarginTop+h*u.lineHeight+(h-1)*t.footerSpacing);let y=0;const x=function(e){g=Math.max(g,n.measureText(e).width+y)};return n.save(),n.font=c.string,mn(e.title,x),n.font=l.string,mn(e.beforeBody.concat(e.afterBody),x),y=t.displayColors?s+2+t.boxPadding:0,mn(i,e=>{mn(e.before,x),mn(e.lines,x),mn(e.after,x)}),y=0,n.font=u.string,mn(e.footer,x),n.restore(),g+=p.width,{width:g,height:m}}function rl(e,t,n,i){const{x:r,width:a}=n,{width:s,chartArea:{left:o,right:l}}=e;let c="center";return"center"===i?c=r<=(o+l)/2?"left":"right":r<=a/2?c="left":r>=s-a/2&&(c="right"),function(e,t,n,i){const{x:r,width:a}=i,s=n.caretSize+n.caretPadding;return"left"===e&&r+a+s>t.width||"right"===e&&r-a-s<0||void 0}(c,e,t,n)&&(c="center"),c}function al(e,t,n){const i=n.yAlign||t.yAlign||function(e,t){const{y:n,height:i}=t;return n<i/2?"top":n>e.height-i/2?"bottom":"center"}(e,n);return{xAlign:n.xAlign||t.xAlign||rl(e,t,n,i),yAlign:i}}function sl(e,t,n,i){const{caretSize:r,caretPadding:a,cornerRadius:s}=e,{xAlign:o,yAlign:l}=n,c=r+a,{topLeft:u,topRight:d,bottomLeft:h,bottomRight:f}=Gi(s);let p=function(e,t){let{x:n,width:i}=e;return"right"===t?n-=i:"center"===t&&(n-=i/2),n}(t,o);const m=function(e,t,n){let{y:i,height:r}=e;return"top"===t?i+=n:i-="bottom"===t?r+n:r/2,i}(t,l,c);return"center"===l?"left"===o?p+=c:"right"===o&&(p-=c):"left"===o?p-=Math.max(u,h)+r:"right"===o&&(p+=Math.max(d,f)+r),{x:Gn(p,0,i.width-t.width),y:Gn(m,0,i.height-t.height)}}function ol(e,t,n){const i=Ji(n.padding);return"center"===t?e.x+e.width/2:"right"===t?e.x+e.width-i.right:e.x+i.left}function ll(e){return el([],tl(e))}function cl(e,t){const n=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return n?e.override(n):e}const ul={beforeTitle:an,title(e){if(e.length>0){const t=e[0],n=t.chart.data.labels,i=n?n.length:0;if(this&&this.options&&"dataset"===this.options.mode)return t.dataset.label||"";if(t.label)return t.label;if(i>0&&t.dataIndex<i)return n[t.dataIndex]}return""},afterTitle:an,beforeBody:an,beforeLabel:an,label(e){if(this&&this.options&&"dataset"===this.options.mode)return e.label+": "+e.formattedValue||e.formattedValue;let t=e.dataset.label||"";t&&(t+=": ");const n=e.formattedValue;return on(n)||(t+=n),t},labelColor(e){const t=e.chart.getDatasetMeta(e.datasetIndex).controller.getStyle(e.dataIndex);return{borderColor:t.borderColor,backgroundColor:t.backgroundColor,borderWidth:t.borderWidth,borderDash:t.borderDash,borderDashOffset:t.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(e){const t=e.chart.getDatasetMeta(e.datasetIndex).controller.getStyle(e.dataIndex);return{pointStyle:t.pointStyle,rotation:t.rotation}},afterLabel:an,afterBody:an,beforeFooter:an,footer:an,afterFooter:an};function dl(e,t,n,i){const r=e[t].call(n,i);return"undefined"===typeof r?ul[t].call(n,i):r}class hl extends gs{constructor(e){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=e.chart,this.options=e.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(e){this.options=e,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const e=this._cachedAnimations;if(e)return e;const t=this.chart,n=this.options.setContext(this.getContext()),i=n.enabled&&t.options.animation&&n.animations,r=new ta(this.chart,i);return i._cacheable&&(this._cachedAnimations=Object.freeze(r)),r}getContext(){return this.$context||(this.$context=(e=this.chart.getContext(),t=this,n=this._tooltipItems,tr(e,{tooltip:t,tooltipItems:n,type:"tooltip"})));var e,t,n}getTitle(e,t){const{callbacks:n}=t,i=dl(n,"beforeTitle",this,e),r=dl(n,"title",this,e),a=dl(n,"afterTitle",this,e);let s=[];return s=el(s,tl(i)),s=el(s,tl(r)),s=el(s,tl(a)),s}getBeforeBody(e,t){return ll(dl(t.callbacks,"beforeBody",this,e))}getBody(e,t){const{callbacks:n}=t,i=[];return mn(e,e=>{const t={before:[],lines:[],after:[]},r=cl(n,e);el(t.before,tl(dl(r,"beforeLabel",this,e))),el(t.lines,dl(r,"label",this,e)),el(t.after,tl(dl(r,"afterLabel",this,e))),i.push(t)}),i}getAfterBody(e,t){return ll(dl(t.callbacks,"afterBody",this,e))}getFooter(e,t){const{callbacks:n}=t,i=dl(n,"beforeFooter",this,e),r=dl(n,"footer",this,e),a=dl(n,"afterFooter",this,e);let s=[];return s=el(s,tl(i)),s=el(s,tl(r)),s=el(s,tl(a)),s}_createItems(e){const t=this._active,n=this.chart.data,i=[],r=[],a=[];let s,o,l=[];for(s=0,o=t.length;s<o;++s)l.push(nl(this.chart,t[s]));return e.filter&&(l=l.filter((t,i,r)=>e.filter(t,i,r,n))),e.itemSort&&(l=l.sort((t,i)=>e.itemSort(t,i,n))),mn(l,t=>{const n=cl(e.callbacks,t);i.push(dl(n,"labelColor",this,t)),r.push(dl(n,"labelPointStyle",this,t)),a.push(dl(n,"labelTextColor",this,t))}),this.labelColors=i,this.labelPointStyles=r,this.labelTextColors=a,this.dataPoints=l,l}update(e,t){const n=this.options.setContext(this.getContext()),i=this._active;let r,a=[];if(i.length){const e=Zo[n.position].call(this,i,this._eventPosition);a=this._createItems(n),this.title=this.getTitle(a,n),this.beforeBody=this.getBeforeBody(a,n),this.body=this.getBody(a,n),this.afterBody=this.getAfterBody(a,n),this.footer=this.getFooter(a,n);const t=this._size=il(this,n),s=Object.assign({},e,t),o=al(this.chart,n,s),l=sl(n,s,o,this.chart);this.xAlign=o.xAlign,this.yAlign=o.yAlign,r={opacity:1,x:l.x,y:l.y,width:t.width,height:t.height,caretX:e.x,caretY:e.y}}else 0!==this.opacity&&(r={opacity:0});this._tooltipItems=a,this.$context=void 0,r&&this._resolveAnimations().update(this,r),e&&n.external&&n.external.call(this,{chart:this.chart,tooltip:this,replay:t})}drawCaret(e,t,n,i){const r=this.getCaretPosition(e,n,i);t.lineTo(r.x1,r.y1),t.lineTo(r.x2,r.y2),t.lineTo(r.x3,r.y3)}getCaretPosition(e,t,n){const{xAlign:i,yAlign:r}=this,{caretSize:a,cornerRadius:s}=n,{topLeft:o,topRight:l,bottomLeft:c,bottomRight:u}=Gi(s),{x:d,y:h}=e,{width:f,height:p}=t;let m,g,b,y,x,v;return"center"===r?(x=h+p/2,"left"===i?(m=d,g=m-a,y=x+a,v=x-a):(m=d+f,g=m+a,y=x-a,v=x+a),b=m):(g="left"===i?d+Math.max(o,c)+a:"right"===i?d+f-Math.max(l,u)-a:this.caretX,"top"===r?(y=h,x=y-a,m=g-a,b=g+a):(y=h+p,x=y+a,m=g+a,b=g-a),v=y),{x1:m,x2:g,x3:b,y1:y,y2:x,y3:v}}drawTitle(e,t,n){const i=this.title,r=i.length;let a,s,o;if(r){const l=Fr(n.rtl,this.x,this.width);for(e.x=ol(this,n.titleAlign,n),t.textAlign=l.textAlign(n.titleAlign),t.textBaseline="middle",a=Zi(n.titleFont),s=n.titleSpacing,t.fillStyle=n.titleColor,t.font=a.string,o=0;o<r;++o)t.fillText(i[o],l.x(e.x),e.y+a.lineHeight/2),e.y+=a.lineHeight+s,o+1===r&&(e.y+=n.titleMarginBottom-s)}}_drawColorBox(e,t,n,i,r){const a=this.labelColors[n],s=this.labelPointStyles[n],{boxHeight:o,boxWidth:l}=r,c=Zi(r.bodyFont),u=ol(this,"left",r),d=i.x(u),h=o<c.lineHeight?(c.lineHeight-o)/2:0,f=t.y+h;if(r.usePointStyle){const t={radius:Math.min(l,o)/2,pointStyle:s.pointStyle,rotation:s.rotation,borderWidth:1},n=i.leftForLtr(d,l)+l/2,c=f+o/2;e.strokeStyle=r.multiKeyBackground,e.fillStyle=r.multiKeyBackground,Ai(e,t,n,c),e.strokeStyle=a.borderColor,e.fillStyle=a.backgroundColor,Ai(e,t,n,c)}else{e.lineWidth=cn(a.borderWidth)?Math.max(...Object.values(a.borderWidth)):a.borderWidth||1,e.strokeStyle=a.borderColor,e.setLineDash(a.borderDash||[]),e.lineDashOffset=a.borderDashOffset||0;const t=i.leftForLtr(d,l),n=i.leftForLtr(i.xPlus(d,1),l-2),s=Gi(a.borderRadius);Object.values(s).some(e=>0!==e)?(e.beginPath(),e.fillStyle=r.multiKeyBackground,Ui(e,{x:t,y:f,w:l,h:o,radius:s}),e.fill(),e.stroke(),e.fillStyle=a.backgroundColor,e.beginPath(),Ui(e,{x:n,y:f+1,w:l-2,h:o-2,radius:s}),e.fill()):(e.fillStyle=r.multiKeyBackground,e.fillRect(t,f,l,o),e.strokeRect(t,f,l,o),e.fillStyle=a.backgroundColor,e.fillRect(n,f+1,l-2,o-2))}e.fillStyle=this.labelTextColors[n]}drawBody(e,t,n){const{body:i}=this,{bodySpacing:r,bodyAlign:a,displayColors:s,boxHeight:o,boxWidth:l,boxPadding:c}=n,u=Zi(n.bodyFont);let d=u.lineHeight,h=0;const f=Fr(n.rtl,this.x,this.width),p=function(n){t.fillText(n,f.x(e.x+h),e.y+d/2),e.y+=d+r},m=f.textAlign(a);let g,b,y,x,v,w,k;for(t.textAlign=a,t.textBaseline="middle",t.font=u.string,e.x=ol(this,m,n),t.fillStyle=n.bodyColor,mn(this.beforeBody,p),h=s&&"right"!==m?"center"===a?l/2+c:l+2+c:0,x=0,w=i.length;x<w;++x){for(g=i[x],b=this.labelTextColors[x],t.fillStyle=b,mn(g.before,p),y=g.lines,s&&y.length&&(this._drawColorBox(t,e,x,f,n),d=Math.max(u.lineHeight,o)),v=0,k=y.length;v<k;++v)p(y[v]),d=u.lineHeight;mn(g.after,p)}h=0,d=u.lineHeight,mn(this.afterBody,p),e.y-=r}drawFooter(e,t,n){const i=this.footer,r=i.length;let a,s;if(r){const o=Fr(n.rtl,this.x,this.width);for(e.x=ol(this,n.footerAlign,n),e.y+=n.footerMarginTop,t.textAlign=o.textAlign(n.footerAlign),t.textBaseline="middle",a=Zi(n.footerFont),t.fillStyle=n.footerColor,t.font=a.string,s=0;s<r;++s)t.fillText(i[s],o.x(e.x),e.y+a.lineHeight/2),e.y+=a.lineHeight+n.footerSpacing}}drawBackground(e,t,n,i){const{xAlign:r,yAlign:a}=this,{x:s,y:o}=e,{width:l,height:c}=n,{topLeft:u,topRight:d,bottomLeft:h,bottomRight:f}=Gi(i.cornerRadius);t.fillStyle=i.backgroundColor,t.strokeStyle=i.borderColor,t.lineWidth=i.borderWidth,t.beginPath(),t.moveTo(s+u,o),"top"===a&&this.drawCaret(e,t,n,i),t.lineTo(s+l-d,o),t.quadraticCurveTo(s+l,o,s+l,o+d),"center"===a&&"right"===r&&this.drawCaret(e,t,n,i),t.lineTo(s+l,o+c-f),t.quadraticCurveTo(s+l,o+c,s+l-f,o+c),"bottom"===a&&this.drawCaret(e,t,n,i),t.lineTo(s+h,o+c),t.quadraticCurveTo(s,o+c,s,o+c-h),"center"===a&&"left"===r&&this.drawCaret(e,t,n,i),t.lineTo(s,o+u),t.quadraticCurveTo(s,o,s+u,o),t.closePath(),t.fill(),i.borderWidth>0&&t.stroke()}_updateAnimationTarget(e){const t=this.chart,n=this.$animations,i=n&&n.x,r=n&&n.y;if(i||r){const n=Zo[e.position].call(this,this._active,this._eventPosition);if(!n)return;const a=this._size=il(this,e),s=Object.assign({},n,this._size),o=al(t,e,s),l=sl(e,s,o,t);i._to===l.x&&r._to===l.y||(this.xAlign=o.xAlign,this.yAlign=o.yAlign,this.width=a.width,this.height=a.height,this.caretX=n.x,this.caretY=n.y,this._resolveAnimations().update(this,l))}}_willRender(){return!!this.opacity}draw(e){const t=this.options.setContext(this.getContext());let n=this.opacity;if(!n)return;this._updateAnimationTarget(t);const i={width:this.width,height:this.height},r={x:this.x,y:this.y};n=Math.abs(n)<.001?0:n;const a=Ji(t.padding),s=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;t.enabled&&s&&(e.save(),e.globalAlpha=n,this.drawBackground(r,e,i,t),Ir(e,t.textDirection),r.y+=a.top,this.drawTitle(r,e,t),this.drawBody(r,e,t),this.drawFooter(r,e,t),Wr(e,t.textDirection),e.restore())}getActiveElements(){return this._active||[]}setActiveElements(e,t){const n=this._active,i=e.map(e=>{let{datasetIndex:t,index:n}=e;const i=this.chart.getDatasetMeta(t);if(!i)throw new Error("Cannot find a dataset at index "+t);return{datasetIndex:t,element:i.data[n],index:n}}),r=!gn(n,i),a=this._positionChanged(i,t);(r||a)&&(this._active=i,this._eventPosition=t,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(t&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const i=this.options,r=this._active||[],a=this._getActiveElements(e,r,t,n),s=this._positionChanged(a,e),o=t||!gn(a,r)||s;return o&&(this._active=a,(i.enabled||i.external)&&(this._eventPosition={x:e.x,y:e.y},this.update(!0,t))),o}_getActiveElements(e,t,n,i){const r=this.options;if("mouseout"===e.type)return[];if(!i)return t.filter(e=>this.chart.data.datasets[e.datasetIndex]&&void 0!==this.chart.getDatasetMeta(e.datasetIndex).controller.getParsed(e.index));const a=this.chart.getElementsAtEventForMode(e,r.mode,r,n);return r.reverse&&a.reverse(),a}_positionChanged(e,t){const{caretX:n,caretY:i,options:r}=this,a=Zo[r.position].call(this,e,t);return!1!==a&&(n!==a.x||i!==a.y)}}l(hl,"positioners",Zo);var fl={id:"tooltip",_element:hl,positioners:Zo,afterInit(e,t,n){n&&(e.tooltip=new hl({chart:e,options:n}))},beforeUpdate(e,t,n){e.tooltip&&e.tooltip.initialize(n)},reset(e,t,n){e.tooltip&&e.tooltip.initialize(n)},afterDraw(e){const t=e.tooltip;if(t&&t._willRender()){const n={tooltip:t};if(!1===e.notifyPlugins("beforeTooltipDraw",u(u({},n),{},{cancelable:!0})))return;t.draw(e.ctx),e.notifyPlugins("afterTooltipDraw",n)}},afterEvent(e,t){if(e.tooltip){const n=t.replay;e.tooltip.handleEvent(t.event,n,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(e,t)=>t.bodyFont.size,boxWidth:(e,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:ul},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:e=>"filter"!==e&&"itemSort"!==e&&"external"!==e,_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};function pl(e,t,n,i){const r=e.indexOf(t);if(-1===r)return((e,t,n,i)=>("string"===typeof t?(n=e.push(t)-1,i.unshift({index:n,label:t})):isNaN(t)&&(n=null),n))(e,t,n,i);return r!==e.lastIndexOf(t)?n:r}function ml(e){const t=this.getLabels();return e>=0&&e<t.length?t[e]:e}class gl extends Ms{constructor(e){super(e),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(e){const t=this._addedLabels;if(t.length){const e=this.getLabels();for(const{index:n,label:i}of t)e[n]===i&&e.splice(n,1);this._addedLabels=[]}super.init(e)}parse(e,t){if(on(e))return null;const n=this.getLabels();return((e,t)=>null===e?null:Gn(Math.round(e),0,t))(t=isFinite(t)&&n[t]===e?t:pl(n,e,hn(t,e),this._addedLabels),n.length-1)}determineDataLimits(){const{minDefined:e,maxDefined:t}=this.getUserBounds();let{min:n,max:i}=this.getMinMax(!0);"ticks"===this.options.bounds&&(e||(n=0),t||(i=this.getLabels().length-1)),this.min=n,this.max=i}buildTicks(){const e=this.min,t=this.max,n=this.options.offset,i=[];let r=this.getLabels();r=0===e&&t===r.length-1?r:r.slice(e,t+1),this._valueRange=Math.max(r.length-(n?0:1),1),this._startValue=this.min-(n?.5:0);for(let a=e;a<=t;a++)i.push({value:a});return i}getLabelForValue(e){return ml.call(this,e)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(e){return"number"!==typeof e&&(e=this.parse(e)),null===e?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getPixelForTick(e){const t=this.ticks;return e<0||e>t.length-1?null:this.getPixelForValue(t[e].value)}getValueForPixel(e){return Math.round(this._startValue+this.getDecimalForPixel(e)*this._valueRange)}getBasePixel(){return this.bottom}}function bl(e,t){const n=[],{bounds:i,step:r,min:a,max:s,precision:o,count:l,maxTicks:c,maxDigits:u,includeBounds:d}=e,h=r||1,f=c-1,{min:p,max:m}=t,g=!on(a),b=!on(s),y=!on(l),x=(m-p)/(u+1);let v,w,k,_,S=Wn((m-p)/f/h)*h;if(S<1e-14&&!g&&!b)return[{value:p},{value:m}];_=Math.ceil(m/S)-Math.floor(p/S),_>f&&(S=Wn(_*S/f/h)*h),on(o)||(v=Math.pow(10,o),S=Math.ceil(S*v)/v),"ticks"===i?(w=Math.floor(p/S)*S,k=Math.ceil(m/S)*S):(w=p,k=m),g&&b&&r&&function(e,t){const n=Math.round(e);return n-t<=e&&n+t>=e}((s-a)/r,S/1e3)?(_=Math.round(Math.min((s-a)/S,c)),S=(s-a)/_,w=a,k=s):y?(w=g?a:w,k=b?s:k,_=l-1,S=(k-w)/_):(_=(k-w)/S,_=In(_,Math.round(_),S/1e3)?Math.round(_):Math.ceil(_));const N=Math.max($n(S),$n(w));v=Math.pow(10,on(o)?N:o),w=Math.round(w*v)/v,k=Math.round(k*v)/v;let M=0;for(g&&(d&&w!==a?(n.push({value:a}),w<a&&M++,In(Math.round((w+M*S)*v)/v,a,yl(a,x,e))&&M++):w<a&&M++);M<_;++M){const e=Math.round((w+M*S)*v)/v;if(b&&e>s)break;n.push({value:e})}return b&&d&&k!==s?n.length&&In(n[n.length-1].value,s,yl(s,x,e))?n[n.length-1].value=s:n.push({value:s}):b&&k!==s||n.push({value:k}),n}function yl(e,t,n){let{horizontal:i,minRotation:r}=n;const a=Vn(r),s=(i?Math.sin(a):Math.cos(a))||.001,o=.75*t*(""+e).length;return Math.min(t/s,o)}l(gl,"id","category"),l(gl,"defaults",{ticks:{callback:ml}});class xl extends Ms{constructor(e){super(e),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(e,t){return on(e)||("number"===typeof e||e instanceof Number)&&!isFinite(+e)?null:+e}handleTickRangeOptions(){const{beginAtZero:e}=this.options,{minDefined:t,maxDefined:n}=this.getUserBounds();let{min:i,max:r}=this;const a=e=>i=t?i:e,s=e=>r=n?r:e;if(e){const e=Fn(i),t=Fn(r);e<0&&t<0?s(0):e>0&&t>0&&a(0)}if(i===r){let t=0===r?1:Math.abs(.05*r);s(r+t),e||a(i-t)}this.min=i,this.max=r}getTickLimit(){const e=this.options.ticks;let t,{maxTicksLimit:n,stepSize:i}=e;return i?(t=Math.ceil(this.max/i)-Math.floor(this.min/i)+1,t>1e3&&(console.warn("scales.".concat(this.id,".ticks.stepSize: ").concat(i," would result generating up to ").concat(t," ticks. Limiting to 1000.")),t=1e3)):(t=this.computeTickLimit(),n=n||11),n&&(t=Math.min(n,t)),t}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const e=this.options,t=e.ticks;let n=this.getTickLimit();n=Math.max(2,n);const i=bl({maxTicks:n,bounds:e.bounds,min:e.min,max:e.max,precision:t.precision,step:t.stepSize,count:t.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:t.minRotation||0,includeBounds:!1!==t.includeBounds},this._range||this);return"ticks"===e.bounds&&Hn(i,this,"value"),e.reverse?(i.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),i}configure(){const e=this.ticks;let t=this.min,n=this.max;if(super.configure(),this.options.offset&&e.length){const i=(n-t)/Math.max(e.length-1,1)/2;t-=i,n+=i}this._startValue=t,this._endValue=n,this._valueRange=n-t}getLabelForValue(e){return wi(e,this.chart.options.locale,this.options.ticks.format)}}class vl extends xl{determineDataLimits(){const{min:e,max:t}=this.getMinMax(!0);this.min=un(e)?e:0,this.max=un(t)?t:1,this.handleTickRangeOptions()}computeTickLimit(){const e=this.isHorizontal(),t=e?this.width:this.height,n=Vn(this.options.ticks.minRotation),i=(e?Math.sin(n):Math.cos(n))||.001,r=this._resolveTickFontOptions(0);return Math.ceil(t/Math.min(40,r.lineHeight/i))}getPixelForValue(e){return null===e?NaN:this.getPixelForDecimal((e-this._startValue)/this._valueRange)}getValueForPixel(e){return this._startValue+this.getDecimalForPixel(e)*this._valueRange}}l(vl,"id","linear"),l(vl,"defaults",{ticks:{callback:_i.formatters.numeric}});const wl=e=>Math.floor(zn(e)),kl=(e,t)=>Math.pow(10,wl(e)+t);function _l(e){return 1===e/Math.pow(10,wl(e))}function Sl(e,t,n){const i=Math.pow(10,n),r=Math.floor(e/i);return Math.ceil(t/i)-r}function Nl(e,t){let{min:n,max:i}=t;n=dn(e.min,n);const r=[],a=wl(n);let s=function(e,t){let n=wl(t-e);for(;Sl(e,t,n)>10;)n++;for(;Sl(e,t,n)<10;)n--;return Math.min(n,wl(e))}(n,i),o=s<0?Math.pow(10,Math.abs(s)):1;const l=Math.pow(10,s),c=a>s?Math.pow(10,a):0,u=Math.round((n-c)*o)/o,d=Math.floor((n-c)/l/10)*l*10;let h=Math.floor((u-d)/Math.pow(10,s)),f=dn(e.min,Math.round((c+d+h*Math.pow(10,s))*o)/o);for(;f<i;)r.push({value:f,major:_l(f),significand:h}),h>=10?h=h<15?15:20:h++,h>=20&&(s++,h=2,o=s>=0?1:o),f=Math.round((c+d+h*Math.pow(10,s))*o)/o;const p=dn(e.max,f);return r.push({value:p,major:_l(p),significand:h}),r}class Ml extends Ms{constructor(e){super(e),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(e,t){const n=xl.prototype.parse.apply(this,[e,t]);if(0!==n)return un(n)&&n>0?n:null;this._zero=!0}determineDataLimits(){const{min:e,max:t}=this.getMinMax(!0);this.min=un(e)?Math.max(0,e):null,this.max=un(t)?Math.max(0,t):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!un(this._userMin)&&(this.min=e===kl(this.min,0)?kl(this.min,-1):kl(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:e,maxDefined:t}=this.getUserBounds();let n=this.min,i=this.max;const r=t=>n=e?n:t,a=e=>i=t?i:e;n===i&&(n<=0?(r(1),a(10)):(r(kl(n,-1)),a(kl(i,1)))),n<=0&&r(kl(i,-1)),i<=0&&a(kl(n,1)),this.min=n,this.max=i}buildTicks(){const e=this.options,t=Nl({min:this._userMin,max:this._userMax},this);return"ticks"===e.bounds&&Hn(t,this,"value"),e.reverse?(t.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),t}getLabelForValue(e){return void 0===e?"0":wi(e,this.chart.options.locale,this.options.ticks.format)}configure(){const e=this.min;super.configure(),this._startValue=zn(e),this._valueRange=zn(this.max)-zn(e)}getPixelForValue(e){return void 0!==e&&0!==e||(e=this.min),null===e||isNaN(e)?NaN:this.getPixelForDecimal(e===this.min?0:(zn(e)-this._startValue)/this._valueRange)}getValueForPixel(e){const t=this.getDecimalForPixel(e);return Math.pow(10,this._startValue+t*this._valueRange)}}function Cl(e){const t=e.ticks;if(t.display&&e.display){const e=Ji(t.backdropPadding);return hn(t.font&&t.font.size,Ei.font.size)+e.height}return 0}function Pl(e,t,n){return n=ln(n)?n:[n],{w:Ti(e,t.string,n),h:n.length*t.lineHeight}}function El(e,t,n,i,r){return e===i||e===r?{start:t-n/2,end:t+n/2}:e<i||e>r?{start:t-n,end:t}:{start:t,end:t+n}}function jl(e){const t={l:e.left+e._padding.left,r:e.right-e._padding.right,t:e.top+e._padding.top,b:e.bottom-e._padding.bottom},n=Object.assign({},t),i=[],r=[],a=e._pointLabels.length,s=e.options.pointLabels,o=s.centerPointLabels?En/a:0;for(let l=0;l<a;l++){const a=s.setContext(e.getPointLabelContext(l));r[l]=a.padding;const c=e.getPointPosition(l,e.drawingArea+r[l],o),u=Zi(a.font),d=Pl(e.ctx,u,e._pointLabels[l]);i[l]=d;const h=Qn(e.getIndexAngle(l)+o),f=Math.round(Un(h));Tl(n,t,h,El(f,c.x,d.w,0,180),El(f,c.y,d.h,90,270))}e.setCenterPoint(t.l-n.l,n.r-t.r,t.t-n.t,n.b-t.b),e._pointLabelItems=function(e,t,n){const i=[],r=e._pointLabels.length,a=e.options,{centerPointLabels:s,display:o}=a.pointLabels,l={extra:Cl(a)/2,additionalAngle:s?En/r:0};let c;for(let u=0;u<r;u++){l.padding=n[u],l.size=t[u];const r=Ll(e,u,l);i.push(r),"auto"===o&&(r.visible=Ol(r,c),r.visible&&(c=r))}return i}(e,i,r)}function Tl(e,t,n,i,r){const a=Math.abs(Math.sin(n)),s=Math.abs(Math.cos(n));let o=0,l=0;i.start<t.l?(o=(t.l-i.start)/a,e.l=Math.min(e.l,t.l-o)):i.end>t.r&&(o=(i.end-t.r)/a,e.r=Math.max(e.r,t.r+o)),r.start<t.t?(l=(t.t-r.start)/s,e.t=Math.min(e.t,t.t-l)):r.end>t.b&&(l=(r.end-t.b)/s,e.b=Math.max(e.b,t.b+l))}function Ll(e,t,n){const i=e.drawingArea,{extra:r,additionalAngle:a,padding:s,size:o}=n,l=e.getPointPosition(t,i+r+s,a),c=Math.round(Un(Qn(l.angle+An))),u=function(e,t,n){90===n||270===n?e-=t/2:(n>270||n<90)&&(e-=t);return e}(l.y,o.h,c),d=function(e){if(0===e||180===e)return"center";if(e<180)return"left";return"right"}(c),h=function(e,t,n){"right"===n?e-=t:"center"===n&&(e-=t/2);return e}(l.x,o.w,d);return{visible:!0,x:l.x,y:u,textAlign:d,left:h,top:u,right:h+o.w,bottom:u+o.h}}function Ol(e,t){if(!t)return!0;const{left:n,top:i,right:r,bottom:a}=e;return!(Ri({x:n,y:i},t)||Ri({x:n,y:a},t)||Ri({x:r,y:i},t)||Ri({x:r,y:a},t))}function Al(e,t,n){const{left:i,top:r,right:a,bottom:s}=n,{backdropColor:o}=t;if(!on(o)){const n=Gi(t.borderRadius),l=Ji(t.backdropPadding);e.fillStyle=o;const c=i-l.left,u=r-l.top,d=a-i+l.width,h=s-r+l.height;Object.values(n).some(e=>0!==e)?(e.beginPath(),Ui(e,{x:c,y:u,w:d,h:h,radius:n}),e.fill()):e.fillRect(c,u,d,h)}}function Dl(e,t,n,i){const{ctx:r}=e;if(n)r.arc(e.xCenter,e.yCenter,t,0,jn);else{let n=e.getPointPosition(0,t);r.moveTo(n.x,n.y);for(let a=1;a<i;a++)n=e.getPointPosition(a,t),r.lineTo(n.x,n.y)}}l(Ml,"id","logarithmic"),l(Ml,"defaults",{ticks:{callback:_i.formatters.logarithmic,major:{enabled:!0}}});class Rl extends xl{constructor(e){super(e),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const e=this._padding=Ji(Cl(this.options)/2),t=this.width=this.maxWidth-e.width,n=this.height=this.maxHeight-e.height;this.xCenter=Math.floor(this.left+t/2+e.left),this.yCenter=Math.floor(this.top+n/2+e.top),this.drawingArea=Math.floor(Math.min(t,n)/2)}determineDataLimits(){const{min:e,max:t}=this.getMinMax(!1);this.min=un(e)&&!isNaN(e)?e:0,this.max=un(t)&&!isNaN(t)?t:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/Cl(this.options))}generateTickLabels(e){xl.prototype.generateTickLabels.call(this,e),this._pointLabels=this.getLabels().map((e,t)=>{const n=pn(this.options.pointLabels.callback,[e,t],this);return n||0===n?n:""}).filter((e,t)=>this.chart.getDataVisibility(t))}fit(){const e=this.options;e.display&&e.pointLabels.display?jl(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(e,t,n,i){this.xCenter+=Math.floor((e-t)/2),this.yCenter+=Math.floor((n-i)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(e,t,n,i))}getIndexAngle(e){return Qn(e*(jn/(this._pointLabels.length||1))+Vn(this.options.startAngle||0))}getDistanceFromCenterForValue(e){if(on(e))return NaN;const t=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-e)*t:(e-this.min)*t}getValueForDistanceFromCenter(e){if(on(e))return NaN;const t=e/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-t:this.min+t}getPointLabelContext(e){const t=this._pointLabels||[];if(e>=0&&e<t.length){const n=t[e];return function(e,t,n){return tr(e,{label:n,index:t,type:"pointLabel"})}(this.getContext(),e,n)}}getPointPosition(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;const i=this.getIndexAngle(e)-An+n;return{x:Math.cos(i)*t+this.xCenter,y:Math.sin(i)*t+this.yCenter,angle:i}}getPointPositionForValue(e,t){return this.getPointPosition(e,this.getDistanceFromCenterForValue(t))}getBasePosition(e){return this.getPointPositionForValue(e||0,this.getBaseValue())}getPointLabelPosition(e){const{left:t,top:n,right:i,bottom:r}=this._pointLabelItems[e];return{left:t,top:n,right:i,bottom:r}}drawBackground(){const{backgroundColor:e,grid:{circular:t}}=this.options;if(e){const n=this.ctx;n.save(),n.beginPath(),Dl(this,this.getDistanceFromCenterForValue(this._endValue),t,this._pointLabels.length),n.closePath(),n.fillStyle=e,n.fill(),n.restore()}}drawGrid(){const e=this.ctx,t=this.options,{angleLines:n,grid:i,border:r}=t,a=this._pointLabels.length;let s,o,l;if(t.pointLabels.display&&function(e,t){const{ctx:n,options:{pointLabels:i}}=e;for(let r=t-1;r>=0;r--){const t=e._pointLabelItems[r];if(!t.visible)continue;const a=i.setContext(e.getPointLabelContext(r));Al(n,a,t);const s=Zi(a.font),{x:o,y:l,textAlign:c}=t;Vi(n,e._pointLabels[r],o,l+s.lineHeight/2,s,{color:a.color,textAlign:c,textBaseline:"middle"})}}(this,a),i.display&&this.ticks.forEach((e,t)=>{if(0!==t||0===t&&this.min<0){o=this.getDistanceFromCenterForValue(e.value);const n=this.getContext(t),s=i.setContext(n),l=r.setContext(n);!function(e,t,n,i,r){const a=e.ctx,s=t.circular,{color:o,lineWidth:l}=t;!s&&!i||!o||!l||n<0||(a.save(),a.strokeStyle=o,a.lineWidth=l,a.setLineDash(r.dash||[]),a.lineDashOffset=r.dashOffset,a.beginPath(),Dl(e,n,s,i),a.closePath(),a.stroke(),a.restore())}(this,s,o,a,l)}}),n.display){for(e.save(),s=a-1;s>=0;s--){const i=n.setContext(this.getPointLabelContext(s)),{color:r,lineWidth:a}=i;a&&r&&(e.lineWidth=a,e.strokeStyle=r,e.setLineDash(i.borderDash),e.lineDashOffset=i.borderDashOffset,o=this.getDistanceFromCenterForValue(t.reverse?this.min:this.max),l=this.getPointPosition(s,o),e.beginPath(),e.moveTo(this.xCenter,this.yCenter),e.lineTo(l.x,l.y),e.stroke())}e.restore()}}drawBorder(){}drawLabels(){const e=this.ctx,t=this.options,n=t.ticks;if(!n.display)return;const i=this.getIndexAngle(0);let r,a;e.save(),e.translate(this.xCenter,this.yCenter),e.rotate(i),e.textAlign="center",e.textBaseline="middle",this.ticks.forEach((i,s)=>{if(0===s&&this.min>=0&&!t.reverse)return;const o=n.setContext(this.getContext(s)),l=Zi(o.font);if(r=this.getDistanceFromCenterForValue(this.ticks[s].value),o.showLabelBackdrop){e.font=l.string,a=e.measureText(i.label).width,e.fillStyle=o.backdropColor;const t=Ji(o.backdropPadding);e.fillRect(-a/2-t.left,-r-l.size/2-t.top,a+t.width,l.size+t.height)}Vi(e,i.label,0,-r,l,{color:o.color,strokeColor:o.textStrokeColor,strokeWidth:o.textStrokeWidth})}),e.restore()}drawTitle(){}}l(Rl,"id","radialLinear"),l(Rl,"defaults",{display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:_i.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback:e=>e,padding:5,centerPointLabels:!1}}),l(Rl,"defaultRoutes",{"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"}),l(Rl,"descriptors",{angleLines:{_fallback:"grid"}});const zl={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},Fl=Object.keys(zl);function Il(e,t){return e-t}function Wl(e,t){if(on(t))return null;const n=e._adapter,{parser:i,round:r,isoWeekday:a}=e._parseOpts;let s=t;return"function"===typeof i&&(s=i(s)),un(s)||(s="string"===typeof i?n.parse(s,i):n.parse(s)),null===s?null:(r&&(s="week"!==r||!Bn(a)&&!0!==a?n.startOf(s,r):n.startOf(s,"isoWeek",a)),+s)}function Bl(e,t,n,i){const r=Fl.length;for(let a=Fl.indexOf(e);a<r-1;++a){const e=zl[Fl[a]],r=e.steps?e.steps:Number.MAX_SAFE_INTEGER;if(e.common&&Math.ceil((n-t)/(r*e.size))<=i)return Fl[a]}return Fl[r-1]}function Hl(e,t,n){if(n){if(n.length){const{lo:i,hi:r}=Zn(n,t);e[n[i]>=t?n[i]:n[r]]=!0}}else e[t]=!0}function Vl(e,t,n){const i=[],r={},a=t.length;let s,o;for(s=0;s<a;++s)o=t[s],r[o]=s,i.push({value:o,major:!1});return 0!==a&&n?function(e,t,n,i){const r=e._adapter,a=+r.startOf(t[0].value,i),s=t[t.length-1].value;let o,l;for(o=a;o<=s;o=+r.add(o,1,i))l=n[o],l>=0&&(t[l].major=!0);return t}(e,i,r,n):i}class Ul extends Ms{constructor(e){super(e),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=e.time||(e.time={}),i=this._adapter=new La(e.adapters.date);i.init(t),wn(n.displayFormats,i.formats()),this._parseOpts={parser:n.parser,round:n.round,isoWeekday:n.isoWeekday},super.init(e),this._normalized=t.normalized}parse(e,t){return void 0===e?null:Wl(this,e)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const e=this.options,t=this._adapter,n=e.time.unit||"day";let{min:i,max:r,minDefined:a,maxDefined:s}=this.getUserBounds();function o(e){a||isNaN(e.min)||(i=Math.min(i,e.min)),s||isNaN(e.max)||(r=Math.max(r,e.max))}a&&s||(o(this._getLabelBounds()),"ticks"===e.bounds&&"labels"===e.ticks.source||o(this.getMinMax(!1))),i=un(i)&&!isNaN(i)?i:+t.startOf(Date.now(),n),r=un(r)&&!isNaN(r)?r:+t.endOf(Date.now(),n)+1,this.min=Math.min(i,r-1),this.max=Math.max(i+1,r)}_getLabelBounds(){const e=this.getLabelTimestamps();let t=Number.POSITIVE_INFINITY,n=Number.NEGATIVE_INFINITY;return e.length&&(t=e[0],n=e[e.length-1]),{min:t,max:n}}buildTicks(){const e=this.options,t=e.time,n=e.ticks,i="labels"===n.source?this.getLabelTimestamps():this._generate();"ticks"===e.bounds&&i.length&&(this.min=this._userMin||i[0],this.max=this._userMax||i[i.length-1]);const r=this.min,a=function(e,t,n){let i=0,r=e.length;for(;i<r&&e[i]<t;)i++;for(;r>i&&e[r-1]>n;)r--;return i>0||r<e.length?e.slice(i,r):e}(i,r,this.max);return this._unit=t.unit||(n.autoSkip?Bl(t.minUnit,this.min,this.max,this._getLabelCapacity(r)):function(e,t,n,i,r){for(let a=Fl.length-1;a>=Fl.indexOf(n);a--){const n=Fl[a];if(zl[n].common&&e._adapter.diff(r,i,n)>=t-1)return n}return Fl[n?Fl.indexOf(n):0]}(this,a.length,t.minUnit,this.min,this.max)),this._majorUnit=n.major.enabled&&"year"!==this._unit?function(e){for(let t=Fl.indexOf(e)+1,n=Fl.length;t<n;++t)if(zl[Fl[t]].common)return Fl[t]}(this._unit):void 0,this.initOffsets(i),e.reverse&&a.reverse(),Vl(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(e=>+e.value))}initOffsets(){let e,t,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],i=0,r=0;this.options.offset&&n.length&&(e=this.getDecimalForValue(n[0]),i=1===n.length?1-e:(this.getDecimalForValue(n[1])-e)/2,t=this.getDecimalForValue(n[n.length-1]),r=1===n.length?t:(t-this.getDecimalForValue(n[n.length-2]))/2);const a=n.length<3?.5:.25;i=Gn(i,0,a),r=Gn(r,0,a),this._offsets={start:i,end:r,factor:1/(i+1+r)}}_generate(){const e=this._adapter,t=this.min,n=this.max,i=this.options,r=i.time,a=r.unit||Bl(r.minUnit,t,n,this._getLabelCapacity(t)),s=hn(i.ticks.stepSize,1),o="week"===a&&r.isoWeekday,l=Bn(o)||!0===o,c={};let u,d,h=t;if(l&&(h=+e.startOf(h,"isoWeek",o)),h=+e.startOf(h,l?"day":a),e.diff(n,t,a)>1e5*s)throw new Error(t+" and "+n+" are too far apart with stepSize of "+s+" "+a);const f="data"===i.ticks.source&&this.getDataTimestamps();for(u=h,d=0;u<n;u=+e.add(u,s,a),d++)Hl(c,u,f);return u!==n&&"ticks"!==i.bounds&&1!==d||Hl(c,u,f),Object.keys(c).sort(Il).map(e=>+e)}getLabelForValue(e){const t=this._adapter,n=this.options.time;return n.tooltipFormat?t.format(e,n.tooltipFormat):t.format(e,n.displayFormats.datetime)}format(e,t){const n=this.options.time.displayFormats,i=this._unit,r=t||n[i];return this._adapter.format(e,r)}_tickFormatFunction(e,t,n,i){const r=this.options,a=r.ticks.callback;if(a)return pn(a,[e,t,n],this);const s=r.time.displayFormats,o=this._unit,l=this._majorUnit,c=o&&s[o],u=l&&s[l],d=n[t],h=l&&u&&d&&d.major;return this._adapter.format(e,i||(h?u:c))}generateTickLabels(e){let t,n,i;for(t=0,n=e.length;t<n;++t)i=e[t],i.label=this._tickFormatFunction(i.value,t,e)}getDecimalForValue(e){return null===e?NaN:(e-this.min)/(this.max-this.min)}getPixelForValue(e){const t=this._offsets,n=this.getDecimalForValue(e);return this.getPixelForDecimal((t.start+n)*t.factor)}getValueForPixel(e){const t=this._offsets,n=this.getDecimalForPixel(e)/t.factor-t.end;return this.min+n*(this.max-this.min)}_getLabelSize(e){const t=this.options.ticks,n=this.ctx.measureText(e).width,i=Vn(this.isHorizontal()?t.maxRotation:t.minRotation),r=Math.cos(i),a=Math.sin(i),s=this._resolveTickFontOptions(0).size;return{w:n*r+s*a,h:n*a+s*r}}_getLabelCapacity(e){const t=this.options.time,n=t.displayFormats,i=n[t.unit]||n.millisecond,r=this._tickFormatFunction(e,0,Vl(this,[e],this._majorUnit),i),a=this._getLabelSize(r),s=Math.floor(this.isHorizontal()?this.width/a.w:this.height/a.h)-1;return s>0?s:1}getDataTimestamps(){let e,t,n=this._cache.data||[];if(n.length)return n;const i=this.getMatchingVisibleMetas();if(this._normalized&&i.length)return this._cache.data=i[0].controller.getAllParsedValues(this);for(e=0,t=i.length;e<t;++e)n=n.concat(i[e].controller.getAllParsedValues(this));return this._cache.data=this.normalize(n)}getLabelTimestamps(){const e=this._cache.labels||[];let t,n;if(e.length)return e;const i=this.getLabels();for(t=0,n=i.length;t<n;++t)e.push(Wl(this,i[t]));return this._cache.labels=this._normalized?e:this.normalize(e)}normalize(e){return ri(e.sort(Il))}}function $l(e,t,n){let i,r,a,s,o=0,l=e.length-1;n?(t>=e[o].pos&&t<=e[l].pos&&({lo:o,hi:l}=ei(e,"pos",t)),({pos:i,time:a}=e[o]),({pos:r,time:s}=e[l])):(t>=e[o].time&&t<=e[l].time&&({lo:o,hi:l}=ei(e,"time",t)),({time:i,pos:a}=e[o]),({time:r,pos:s}=e[l]));const c=r-i;return c?a+(s-a)*(t-i)/c:a}l(Ul,"id","time"),l(Ul,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});class Yl extends Ul{constructor(e){super(e),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const e=this._getTimestampsForTable(),t=this._table=this.buildLookupTable(e);this._minPos=$l(t,this.min),this._tableRange=$l(t,this.max)-this._minPos,super.initOffsets(e)}buildLookupTable(e){const{min:t,max:n}=this,i=[],r=[];let a,s,o,l,c;for(a=0,s=e.length;a<s;++a)l=e[a],l>=t&&l<=n&&i.push(l);if(i.length<2)return[{time:t,pos:0},{time:n,pos:1}];for(a=0,s=i.length;a<s;++a)c=i[a+1],o=i[a-1],l=i[a],Math.round((c+o)/2)!==l&&r.push({time:l,pos:a/(s-1)});return r}_generate(){const e=this.min,t=this.max;let n=super.getDataTimestamps();return n.includes(e)&&n.length||n.splice(0,0,e),n.includes(t)&&1!==n.length||n.push(t),n.sort((e,t)=>e-t)}_getTimestampsForTable(){let e=this._cache.all||[];if(e.length)return e;const t=this.getDataTimestamps(),n=this.getLabelTimestamps();return e=t.length&&n.length?this.normalize(t.concat(n)):t.length?t:n,e=this._cache.all=e,e}getDecimalForValue(e){return($l(this._table,e)-this._minPos)/this._tableRange}getValueForPixel(e){const t=this._offsets,n=this.getDecimalForPixel(e)/t.factor-t.end;return $l(this._table,n*this._tableRange+this._minPos,!0)}}l(Yl,"id","timeseries"),l(Yl,"defaults",Ul.defaults);const ql=["height","width","redraw","datasetIdKey","type","data","options","plugins","fallbackContent","updateMode"],Kl="label";function Ql(e,t){"function"===typeof e?e(t):e&&(e.current=t)}function Xl(e,t){e.labels=t}function Gl(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Kl;const i=[];e.datasets=t.map(t=>{const r=e.datasets.find(e=>e[n]===t[n]);return r&&t.data&&!i.includes(r)?(i.push(r),Object.assign(r,t),r):u({},t)})}function Jl(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Kl;const n={labels:[],datasets:[]};return Xl(n,e.labels),Gl(n,e.datasets,t),n}function Zl(e,t){const{height:n=150,width:r=300,redraw:s=!1,datasetIdKey:o,type:l,data:c,options:d,plugins:h=[],fallbackContent:f,updateMode:p}=e,m=a(e,ql),g=(0,i.useRef)(null),b=(0,i.useRef)(null),y=()=>{g.current&&(b.current=new io(g.current,{type:l,data:Jl(c,o),options:d&&u({},d),plugins:h}),Ql(t,b.current))},x=()=>{Ql(t,null),b.current&&(b.current.destroy(),b.current=null)};return(0,i.useEffect)(()=>{!s&&b.current&&d&&function(e,t){const n=e.options;n&&t&&Object.assign(n,t)}(b.current,d)},[s,d]),(0,i.useEffect)(()=>{!s&&b.current&&Xl(b.current.config.data,c.labels)},[s,c.labels]),(0,i.useEffect)(()=>{!s&&b.current&&c.datasets&&Gl(b.current.config.data,c.datasets,o)},[s,c.datasets]),(0,i.useEffect)(()=>{b.current&&(s?(x(),setTimeout(y)):b.current.update(p))},[s,d,c.labels,c.datasets,p]),(0,i.useEffect)(()=>{b.current&&(x(),setTimeout(y))},[l]),(0,i.useEffect)(()=>(y(),()=>x()),[]),i.createElement("canvas",u({ref:g,role:"img",height:n,width:r},m),f)}const ec=(0,i.forwardRef)(Zl);function tc(e,t){return io.register(t),(0,i.forwardRef)((t,n)=>i.createElement(ec,u(u({},t),{},{ref:n,type:e})))}const nc=tc("bar",ka),ic=tc("radar",Pa),rc=tc("doughnut",Sa),ac=tc("pie",Ca),sc="#003B46",oc="#07575B",lc="#66A5AD",cc="#C4DFE6",uc="#FF5733",dc="#4A90E2",hc="#50E3C2",fc="#F5A623",pc=e=>{const t=e[0];let n=t.chart.data.labels[t.dataIndex];return Array.isArray(n)?n.join(" "):n},mc=(e,t)=>{const n={responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"bottom",labels:{font:{size:12}}},tooltip:{callbacks:{title:pc}}}};switch(t&&(n.plugins.title={display:!0,text:t,font:{size:16,weight:"bold"},padding:{top:10,bottom:20}}),e){case"bar":return u(u({},n),{},{scales:{x:{display:!1},y:{ticks:{font:{size:14,weight:"bold"}}}},plugins:u(u({},n.plugins),{},{legend:{display:!1}}),indexAxis:"y"});case"radar":return u(u({},n),{},{scales:{r:{angleLines:{color:"#ddd"},grid:{color:"#ddd"},pointLabels:{font:{size:12,weight:"bold"}},ticks:{display:!1}}},plugins:u(u({},n.plugins),{},{legend:{display:!1}})});default:return n}},gc=(e,t)=>{const[n,r]=(0,i.useState)(null),[a,s]=(0,i.useState)(null),[o,l]=(0,i.useState)(!0),[c,u]=(0,i.useState)(null);return(0,i.useEffect)(()=>{(async()=>{try{let n,i;switch(l(!0),u(null),e){case"timeAllocation":n={labels:["Pillar 2: Japanese Study (Voice)","Pillar 1: Physical Fitness (Body)","Pillar 3: Practical Skills (Hands)","Pillar 4: Cognitive Fitness (Mind)"],datasets:[{label:"Weekly Hours",data:[20,7.5,10,7.5],backgroundColor:[sc,oc,lc,cc],borderColor:"#FFFFFF",borderWidth:3}]},i=mc("doughnut",t||"Weekly Hour Distribution");break;case"keyLifts":n={labels:["Squat","Deadlift","Bench Press","Overhead Press","Barbell Row"],datasets:[{label:"Functional Strength Rating",data:[95,100,85,80,90],backgroundColor:[uc||"#FF5733","#C70039","#900C3F","#581845",dc||"#4A90E2"],borderColor:"#fff",borderWidth:2,borderRadius:5}]},i=mc("bar",t),i.plugins.tooltip.callbacks.label=function(e){let t;switch(e.label){case"Squat":t="Legs, Glutes, Core";break;case"Deadlift":t="Entire Posterior Chain";break;case"Bench Press":t="Chest, Shoulders, Triceps";break;case"Overhead Press":t="Shoulders, Triceps, Core";break;case"Barbell Row":t="Back, Biceps";break;default:t=""}return" Works: ".concat(t)};break;case"anki":n={labels:["Daily Reviews","Learn New Words (20/day)","Long-Term Memory"],datasets:[{data:[50,25,25],backgroundColor:[sc,oc,lc],borderColor:"#fff",borderWidth:4}]},i=mc("doughnut",t||"The Daily SRS Cycle");break;case"knowledge":n={labels:["Hand Tools","Power Tools","Site Safety","Materials ID","Essential Skills"],datasets:[{label:"Study Focus",data:[8,7,10,6,9],backgroundColor:"rgba(217, 83, 79, 0.2)",borderColor:"#D9534F",pointBackgroundColor:"#D9534F",pointBorderColor:"#fff",pointHoverBackgroundColor:"#fff",pointHoverBorderColor:"#D9534F",borderWidth:2}]},i=mc("radar",t);break;case"mindWorkout":n={labels:["Active Reading (45 min)","Problem Solving (30 min)","Meditation (15 min)"],datasets:[{data:[50,33,17],backgroundColor:[hc||"#50E3C2",fc||"#F5A623",dc||"#4A90E2"],borderColor:"#fff",borderWidth:4}]},i=mc("pie",t||"90-Minute Focus Block");break;default:throw new Error("Unknown chart type: ".concat(e))}r(n),s(i)}catch(n){u(n instanceof Error?n.message:"Failed to load chart data")}finally{l(!1)}})()},[e,t]),{data:n,options:a,loading:o,error:c}},bc=e=>{const t=e.split(":"),n=Number(t[0]),i=Number(t[1]);if(isNaN(n)||isNaN(i)||2!==t.length)return{original:e,formatted:e,period:"AM"};const r=n>=12?"PM":"AM",a=0===n?12:n>12?n-12:n,s=i.toString().padStart(2,"0");return{original:e,formatted:"".concat(a,":").concat(s," ").concat(r),period:r}},yc=e=>{let t=e.replace(/(\d{2}:\d{2})\s*-\s*(\d{2}:\d{2})/g,(e,t,n)=>{const i=bc(t),r=bc(n);return"".concat(i.formatted," - ").concat(r.formatted)});return t=t.replace(/(\d{2}:\d{2})/g,e=>{if(t.includes("".concat(e," AM"))||t.includes("".concat(e," PM")))return e;return bc(e).formatted}),t};io.register(co,fl,Xo,Jo);const xc=()=>{const{data:e,options:t,loading:n}=gc("timeAllocation"),r=(0,i.useMemo)(()=>[{time:"06:00 - 07:30",title:"\ud83d\udcaa Pillar 1: Physical Fitness",what:"Full-body strength training (3x/week) or Cardio & Core (2x/week).",why:"To build the raw strength and endurance needed for physical jobs and to boost mental clarity for the day.",how:"Follow a structured program like StrongLifts 5x5. For cardio, use HIIT or jogging. Use apps like Jefit to track lifts."},{time:"09:00 - 12:00",title:"\ud83d\udde3\ufe0f Pillar 2: Japanese Core Study",what:"Deep, focused study of Japanese grammar and sentence structure.",why:"Grammar is the skeleton of the language. Vocabulary is useless without it. This is the hardest but most important part.",how:"Use a textbook (Genki I & II). Complete exercises with a pen and paper. No phone, no distractions. One chapter every 4-5 days."},{time:"13:00 - 15:00",title:"\ud83d\udee0\ufe0f Pillar 3: Practical Skills Study",what:"Theoretical study of tools, safety protocols, materials, and basic trade knowledge.",why:"To become a quick learner on a job site. Knowing the 'what' and 'why' makes the 'how' much easier to pick up.",how:'Watch curated YouTube channels (e.g., Essential Craftsman). Take notes. Create a "knowledge base" in a notebook.'},{time:"15:00 - 16:30",title:"\ud83e\udde0 Pillar 4: Cognitive Fitness",what:"Active reading of non-fiction, problem-solving puzzles, and meditation.",why:"To sharpen your focus, improve memory retention, and train your brain to learn efficiently.",how:"Read a chapter, then summarize it (Feynman Technique). Use apps like Headspace for meditation and Lumosity for logic puzzles."},{time:"17:00 - 18:00",title:"\ud83d\udde3\ufe0f Pillar 2: Japanese Vocabulary Drill",what:"Spaced Repetition System (SRS) for vocabulary.",why:"To efficiently memorize thousands of words and burn them into your long-term memory.",how:"Use the Anki app. Do your reviews and learn 20 new words every single day without fail."},{time:"19:30 - 21:00",title:"\ud83d\udde3\ufe0f Pillar 2: Japanese Immersion",what:"Actively listen to and speak Japanese.",why:"To connect your grammar and vocabulary knowledge to the sound and rhythm of the real language.",how:"Listen to beginner podcasts or anime audio. Starting Month 3, use HelloTalk to speak with native speakers."}],[]);return(0,_t.jsx)("div",{className:"bg-gray-100 text-gray-800 min-h-screen",children:(0,_t.jsxs)("div",{className:"container mx-auto p-4 md:p-8",children:[(0,_t.jsxs)("header",{className:"text-center mb-12",children:[(0,_t.jsx)("h1",{className:"text-4xl md:text-6xl font-black text-reedsoft-primary tracking-tight",children:"PROJECT: JACK OF ALL TRADES"}),(0,_t.jsx)("p",{className:"text-lg md:text-xl text-reedsoft-secondary mt-2",children:"Your 16-Week Blueprint for Total Transformation"})]}),(0,_t.jsxs)("section",{id:"daily-kata",className:"mb-12",children:[(0,_t.jsx)("h2",{className:"text-3xl font-bold text-center mb-8 text-reedsoft-primary",children:'Your Daily "Kata" (Routine)'}),(0,_t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-lg mb-8",children:[(0,_t.jsxs)("p",{className:"text-center text-gray-600 mb-6",children:["A consistent daily routine, or ",(0,_t.jsx)("em",{children:"kata"})," (\u5f62)\u2014a Japanese term for a choreographed pattern of movements\u2014is essential for disciplined practice. This template integrates the Pomodoro Technique for maximum concentration and retention."]}),(0,_t.jsx)("div",{className:"overflow-x-auto",children:(0,_t.jsxs)("table",{className:"w-full text-sm",children:[(0,_t.jsx)("thead",{children:(0,_t.jsxs)("tr",{className:"bg-gray-50",children:[(0,_t.jsx)("th",{className:"text-left p-3 font-bold text-reedsoft-primary",children:"Time"}),(0,_t.jsx)("th",{className:"text-left p-3 font-bold text-reedsoft-primary",children:"Activity"}),(0,_t.jsx)("th",{className:"text-left p-3 font-bold text-reedsoft-primary",children:"Focus/Tools"})]})}),(0,_t.jsxs)("tbody",{children:[(0,_t.jsxs)("tr",{className:"border-b",children:[(0,_t.jsx)("td",{className:"p-3 font-semibold text-reedsoft-secondary",children:"6:00 - 6:45 AM"}),(0,_t.jsx)("td",{className:"p-3",children:"Physical Fitness"}),(0,_t.jsx)("td",{className:"p-3",children:"Full-body circuit, HIIT, or functional strength workout."})]}),(0,_t.jsxs)("tr",{className:"border-b bg-gray-50",children:[(0,_t.jsx)("td",{className:"p-3 font-semibold text-reedsoft-secondary",children:"6:45 - 7:30 AM"}),(0,_t.jsx)("td",{className:"p-3",children:"Morning Prep"}),(0,_t.jsx)("td",{className:"p-3",children:"Shower, breakfast, review daily goals."})]}),(0,_t.jsxs)("tr",{className:"border-b",children:[(0,_t.jsx)("td",{className:"p-3 font-semibold text-reedsoft-secondary",children:"7:30 - 9:30 AM"}),(0,_t.jsx)("td",{className:"p-3",children:(0,_t.jsx)("strong",{children:"Japanese Study Block 1"})}),(0,_t.jsx)("td",{className:"p-3",children:"4x Pomodoro sessions (25 min study / 5 min break). Focus on new grammar/kanji. Tools: Textbook, WaniKani."})]}),(0,_t.jsxs)("tr",{className:"border-b bg-gray-50",children:[(0,_t.jsx)("td",{className:"p-3 font-semibold text-reedsoft-secondary",children:"9:30 - 10:00 AM"}),(0,_t.jsx)("td",{className:"p-3",children:"Extended Break"}),(0,_t.jsx)("td",{className:"p-3",children:"Stretch, light snack, step away from study area."})]}),(0,_t.jsxs)("tr",{className:"border-b",children:[(0,_t.jsx)("td",{className:"p-3 font-semibold text-reedsoft-secondary",children:"10:00 AM - 12:00 PM"}),(0,_t.jsx)("td",{className:"p-3",children:(0,_t.jsx)("strong",{children:"Japanese Study Block 2"})}),(0,_t.jsx)("td",{className:"p-3",children:"4x Pomodoro sessions. Focus on vocabulary reinforcement and reading practice. Tools: Anki, NHK News Easy."})]}),(0,_t.jsxs)("tr",{className:"border-b bg-gray-50",children:[(0,_t.jsx)("td",{className:"p-3 font-semibold text-reedsoft-secondary",children:"12:00 - 1:00 PM"}),(0,_t.jsx)("td",{className:"p-3",children:"Lunch Break"}),(0,_t.jsx)("td",{className:"p-3",children:"Nutritious meal, mental rest."})]}),(0,_t.jsxs)("tr",{className:"border-b",children:[(0,_t.jsx)("td",{className:"p-3 font-semibold text-reedsoft-secondary",children:"1:00 - 2:00 PM"}),(0,_t.jsx)("td",{className:"p-3",children:(0,_t.jsx)("strong",{children:"Skills Development Block"})}),(0,_t.jsx)("td",{className:"p-3",children:"2x Pomodoro sessions. Work on online trade course or cognitive training. Tools: Alison, CogniFit/Lumosity."})]}),(0,_t.jsxs)("tr",{className:"border-b bg-gray-50",children:[(0,_t.jsx)("td",{className:"p-3 font-semibold text-reedsoft-secondary",children:"2:00 - 3:00 PM"}),(0,_t.jsx)("td",{className:"p-3",children:(0,_t.jsx)("strong",{children:"Japanese Study Block 3"})}),(0,_t.jsx)("td",{className:"p-3",children:"2x Pomodoro sessions. Focus on listening or speaking practice. Tools: Podcasts, iTalki prep, shadowing."})]}),(0,_t.jsxs)("tr",{className:"border-b",children:[(0,_t.jsx)("td",{className:"p-3 font-semibold text-reedsoft-secondary",children:"3:00 - 3:30 PM"}),(0,_t.jsx)("td",{className:"p-3",children:"Afternoon Break"}),(0,_t.jsx)("td",{className:"p-3",children:"Walk, hydrate, short rest."})]}),(0,_t.jsxs)("tr",{className:"border-b bg-gray-50",children:[(0,_t.jsx)("td",{className:"p-3 font-semibold text-reedsoft-secondary",children:"3:30 - 5:00 PM"}),(0,_t.jsx)("td",{className:"p-3",children:(0,_t.jsx)("strong",{children:"Career Prep Block"})}),(0,_t.jsx)("td",{className:"p-3",children:"Work on resumes, research companies, prepare for interviews, or organize visa documents."})]}),(0,_t.jsxs)("tr",{className:"border-b",children:[(0,_t.jsx)("td",{className:"p-3 font-semibold text-reedsoft-secondary",children:"5:00 PM onward"}),(0,_t.jsx)("td",{className:"p-3",children:"Free Time & Review"}),(0,_t.jsx)("td",{className:"p-3",children:"Dinner, hobbies, relaxation. A light 15-30 minute review of the day's Japanese lessons before bed."})]})]})]})})]})]}),(0,_t.jsxs)("section",{id:"missions",className:"mb-12",children:[(0,_t.jsx)("h2",{className:"text-3xl font-bold text-center mb-8 text-reedsoft-primary",children:"Your 16-Week Transformation Journey"}),(0,_t.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-lg",children:(0,_t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 items-center text-center gap-4",children:[(0,_t.jsxs)("div",{className:"p-4",children:[(0,_t.jsx)("h3",{className:"text-xl font-bold text-reedsoft-secondary",children:"Month 1 (Weeks 1-4)"}),(0,_t.jsx)("p",{className:"font-semibold text-gray-700",children:"Building the Foundation"}),(0,_t.jsx)("p",{className:"text-sm mt-1 text-gray-600",children:"\u57fa\u790e\u56fa\u3081 (Kiso-gatame)"}),(0,_t.jsx)("p",{className:"text-sm mt-2",children:"Master Hiragana & Katakana. Start Genki I. Begin WaniKani. 3x weekly bodyweight circuits. Daily cognitive training."})]}),(0,_t.jsx)("div",{className:"hidden md:block text-2xl text-reedsoft-accent",children:"\u2192"}),(0,_t.jsxs)("div",{className:"p-4",children:[(0,_t.jsx)("h3",{className:"text-xl font-bold text-reedsoft-secondary",children:"Month 2 (Weeks 5-8)"}),(0,_t.jsx)("p",{className:"font-semibold text-gray-700",children:"Accelerating Progress"}),(0,_t.jsx)("p",{className:"text-sm mt-1 text-gray-600",children:"\u9032\u6357\u52a0\u901f (Shinchoku-kasoku)"}),(0,_t.jsx)("p",{className:"text-sm mt-2",children:"Complete N5 grammar. Learn first 100 N5 Kanji. Increase workout intensity. Start online trade courses."})]}),(0,_t.jsx)("div",{className:"hidden md:block text-2xl text-reedsoft-accent",children:"\u2192"}),(0,_t.jsxs)("div",{className:"p-4",children:[(0,_t.jsx)("h3",{className:"text-xl font-bold text-reedsoft-secondary",children:"Month 3 (Weeks 9-12)"}),(0,_t.jsx)("p",{className:"font-semibold text-gray-700",children:"Skill Integration"}),(0,_t.jsx)("p",{className:"text-sm mt-1 text-gray-600",children:"\u30b9\u30ad\u30eb\u7d71\u5408 (Sukiru-t\u014dg\u014d)"}),(0,_t.jsx)("p",{className:"text-sm mt-2",children:"Start N4 prep. Begin iTalki conversation practice. Introduce HIIT elements. Draft Japanese resumes."})]}),(0,_t.jsx)("div",{className:"hidden md:block text-2xl text-reedsoft-accent",children:"\u2192"}),(0,_t.jsxs)("div",{className:"p-4",children:[(0,_t.jsx)("h3",{className:"text-xl font-bold text-reedsoft-secondary",children:"Month 4 (Weeks 13-16)"}),(0,_t.jsx)("p",{className:"font-semibold text-gray-700",children:"Final Preparation"}),(0,_t.jsx)("p",{className:"text-sm mt-1 text-gray-600",children:"\u6700\u7d42\u6e96\u5099 (Saish\u016b-junbi)"}),(0,_t.jsx)("p",{className:"text-sm mt-2",children:"Weekly N4 mock tests. Finalize visa documents. Practice interview questions. Maintain peak fitness."})]})]})})]}),(0,_t.jsxs)("section",{id:"training-matrix",className:"mb-12",children:[(0,_t.jsx)("h2",{className:"text-3xl font-bold text-center mb-8 text-reedsoft-primary",children:"16-Week At-A-Glance Training Matrix"}),(0,_t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-lg overflow-x-auto",children:[(0,_t.jsx)("p",{className:"text-center text-gray-600 mb-6",children:"A clear path, turning the four-month goal into manageable weekly sprints."}),(0,_t.jsxs)("table",{className:"w-full text-xs md:text-sm",children:[(0,_t.jsx)("thead",{children:(0,_t.jsxs)("tr",{className:"bg-gray-50",children:[(0,_t.jsx)("th",{className:"text-left p-2 font-bold text-reedsoft-primary",children:"Week"}),(0,_t.jsx)("th",{className:"text-left p-2 font-bold text-reedsoft-primary",children:"Language Focus"}),(0,_t.jsx)("th",{className:"text-left p-2 font-bold text-reedsoft-primary",children:"Physical Fitness Goal"}),(0,_t.jsx)("th",{className:"text-left p-2 font-bold text-reedsoft-primary",children:"Skills Focus"}),(0,_t.jsx)("th",{className:"text-left p-2 font-bold text-reedsoft-primary",children:"Career Prep Task"})]})}),(0,_t.jsxs)("tbody",{children:[(0,_t.jsxs)("tr",{className:"border-b",children:[(0,_t.jsx)("td",{className:"p-2 font-semibold text-reedsoft-secondary",children:"1-2"}),(0,_t.jsx)("td",{className:"p-2",children:"Master Hiragana & Katakana. Start Genki I. Begin WaniKani."}),(0,_t.jsx)("td",{className:"p-2",children:"3x weekly bodyweight circuits. Focus on form."}),(0,_t.jsx)("td",{className:"p-2",children:"Daily cognitive training (15 min). Research trade courses."}),(0,_t.jsx)("td",{className:"p-2",children:"Research SSW visa requirements. Identify recruitment agencies."})]}),(0,_t.jsxs)("tr",{className:"border-b bg-gray-50",children:[(0,_t.jsx)("td",{className:"p-2 font-semibold text-reedsoft-secondary",children:"3-4"}),(0,_t.jsx)("td",{className:"p-2",children:"Complete first chapters. Daily Anki (vocab/grammar)."}),(0,_t.jsx)("td",{className:"p-2",children:"Maintain 3x weekly workouts. Increase reps slightly."}),(0,_t.jsx)("td",{className:"p-2",children:"Start first online trade course. Continue cognitive training."}),(0,_t.jsx)("td",{className:"p-2",children:"Draft initial contact email for recruitment agencies."})]}),(0,_t.jsxs)("tr",{className:"border-b",children:[(0,_t.jsx)("td",{className:"p-2 font-semibold text-reedsoft-secondary",children:"5-6"}),(0,_t.jsx)("td",{className:"p-2",children:"Progress through textbook (N5 grammar). Learn first 50 N5 Kanji."}),(0,_t.jsx)("td",{className:"p-2",children:"Increase workout intensity. Add 4th weekly session."}),(0,_t.jsx)("td",{className:"p-2",children:"Continue first course. Focus on weak cognitive areas."}),(0,_t.jsx)("td",{className:"p-2",children:"Draft first version of Rirekisho and Shokumu Keirekisho."})]}),(0,_t.jsxs)("tr",{className:"border-b bg-gray-50",children:[(0,_t.jsx)("td",{className:"p-2 font-semibold text-reedsoft-secondary",children:"7-8"}),(0,_t.jsx)("td",{className:"p-2",children:"Complete N5 Kanji list (~100). Begin beginner listening podcasts."}),(0,_t.jsx)("td",{className:"p-2",children:"Introduce HIIT elements into 1-2 weekly workouts."}),(0,_t.jsx)("td",{className:"p-2",children:"Complete first course. Start second course (Intro to Logistics)."}),(0,_t.jsx)("td",{className:"p-2",children:"Contact recruitment agencies. Refine resumes based on feedback."})]}),(0,_t.jsxs)("tr",{className:"border-b",children:[(0,_t.jsx)("td",{className:"p-2 font-semibold text-reedsoft-secondary",children:"9-10"}),(0,_t.jsx)("td",{className:"p-2",children:"Finish primary textbook. Start N4 prep book (Shin-Kanzen Master)."}),(0,_t.jsx)("td",{className:"p-2",children:"Focus on functional strength. Maintain 4x weekly workouts."}),(0,_t.jsx)("td",{className:"p-2",children:"Continue second course. Apply learnings to resume drafts."}),(0,_t.jsx)("td",{className:"p-2",children:"Research specific Japanese companies in target sectors."})]}),(0,_t.jsxs)("tr",{className:"border-b bg-gray-50",children:[(0,_t.jsx)("td",{className:"p-2 font-semibold text-reedsoft-secondary",children:"11-12"}),(0,_t.jsx)("td",{className:"p-2",children:"Learn first 100 N4 Kanji. Start weekly iTalki conversation practice."}),(0,_t.jsx)("td",{className:"p-2",children:"Maintain HIIT and strength focus. Ensure proper recovery."}),(0,_t.jsx)("td",{className:"p-2",children:"Complete second course. Summarize key skills learned."}),(0,_t.jsx)("td",{className:"p-2",children:"Practice interview questions, focusing on cultural etiquette."})]}),(0,_t.jsxs)("tr",{className:"border-b",children:[(0,_t.jsx)("td",{className:"p-2 font-semibold text-reedsoft-secondary",children:"13-14"}),(0,_t.jsx)("td",{className:"p-2",children:"Intensive N4 grammar/vocab review. Take first full N4 mock test."}),(0,_t.jsx)("td",{className:"p-2",children:"Maintain fitness with 3-4 workouts. Focus on injury prevention."}),(0,_t.jsx)("td",{className:"p-2",children:"Maintenance cognitive training (10 min/day)."}),(0,_t.jsx)("td",{className:"p-2",children:"Finalize resumes. Begin gathering SSW application documents."})]}),(0,_t.jsxs)("tr",{className:"border-b bg-gray-50",children:[(0,_t.jsx)("td",{className:"p-2 font-semibold text-reedsoft-secondary",children:"15-16"}),(0,_t.jsx)("td",{className:"p-2",children:"Take weekly timed N4 mock tests. Drill weak areas identified."}),(0,_t.jsx)("td",{className:"p-2",children:"Taper intensity slightly to ensure body is rested and strong."}),(0,_t.jsx)("td",{className:"p-2",children:"Review notes from online trade courses."}),(0,_t.jsx)("td",{className:"p-2",children:"Conduct mock interviews. Have all visa documents ready."})]})]})]})]})]}),(0,_t.jsxs)("section",{id:"daily-grind",className:"mb-12",children:[(0,_t.jsx)("h2",{className:"text-3xl font-bold text-center mb-8 text-reedsoft-primary",children:"The Daily Grind: Weekday Blueprint"}),(0,_t.jsxs)("div",{className:"space-y-6",children:[(0,_t.jsx)("div",{className:"bg-white p-6 rounded-lg shadow-lg",children:(0,_t.jsx)("p",{className:"text-sm text-center text-gray-600 mb-4",children:"This is your core schedule, Monday to Friday. Each block is non-negotiable. It is designed for maximum efficiency in skill acquisition."})}),r.map((e,t)=>(0,_t.jsxs)("div",{className:"bg-white p-5 rounded-lg shadow-md",children:[(0,_t.jsxs)("div",{className:"font-bold text-lg text-reedsoft-primary",children:[yc(e.time)," | ",e.title]}),(0,_t.jsxs)("p",{className:"mt-2 text-sm",children:[(0,_t.jsx)("strong",{className:"text-reedsoft-secondary",children:"WHAT:"})," ",e.what]}),(0,_t.jsxs)("p",{className:"mt-1 text-sm",children:[(0,_t.jsx)("strong",{className:"text-reedsoft-secondary",children:"WHY:"})," ",e.why]}),(0,_t.jsxs)("p",{className:"mt-1 text-sm",children:[(0,_t.jsx)("strong",{className:"text-reedsoft-secondary",children:"HOW:"})," ",e.how]})]},t))]})]}),(0,_t.jsx)("section",{id:"weekend-plan",className:"mb-12",children:(0,_t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,_t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-lg",children:[(0,_t.jsx)("h2",{className:"text-2xl font-bold text-center mb-4 text-reedsoft-primary",children:"Weekly Time Allocation"}),(0,_t.jsx)("p",{className:"text-sm text-gray-600 mb-4 text-center",children:"Your approximate 40+ hours of active development each week, broken down by pillar."}),(0,_t.jsx)("div",{className:"relative w-full max-w-md mx-auto h-96",role:"img","aria-label":"Weekly time allocation chart showing distribution across four pillars",children:n?(0,_t.jsxs)("div",{className:"flex items-center justify-center h-full",role:"status","aria-live":"polite",children:[(0,_t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-reedsoft-primary","aria-hidden":"true"}),(0,_t.jsx)("span",{className:"sr-only",children:"Loading chart data..."})]}):e&&t?(0,_t.jsx)(rc,{data:e,options:t,"aria-label":"Doughnut chart showing weekly time allocation: Physical Fitness, Japanese Language, Practical Skills, and Cognitive Fitness"}):(0,_t.jsx)("div",{className:"flex items-center justify-center h-full text-gray-500",role:"status","aria-live":"polite",children:"Chart loading..."})}),(0,_t.jsxs)("div",{className:"sr-only",children:[(0,_t.jsx)("h3",{children:"Weekly Time Allocation Summary:"}),(0,_t.jsxs)("ul",{children:[(0,_t.jsx)("li",{children:"Physical Fitness: 7.5 hours per week"}),(0,_t.jsx)("li",{children:"Japanese Language: 21 hours per week"}),(0,_t.jsx)("li",{children:"Practical Skills: 10 hours per week"}),(0,_t.jsx)("li",{children:"Cognitive Fitness: 7.5 hours per week"})]})]})]}),(0,_t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-lg",children:[(0,_t.jsx)("h2",{className:"text-2xl font-bold text-center mb-4 text-reedsoft-primary",children:"The Weekend Shift"}),(0,_t.jsx)("p",{className:"text-sm text-gray-600 mb-4 text-center",children:"Weekends are for review, practical application, and strategic recovery. The pace changes, but the work continues."}),(0,_t.jsxs)("ul",{className:"space-y-4",children:[(0,_t.jsxs)("li",{className:"p-4 bg-cyan-50 rounded-lg",children:[(0,_t.jsx)("h3",{className:"font-bold text-teal-800",children:"Saturday Morning: Review & Consolidate"}),(0,_t.jsx)("p",{className:"text-sm",children:"Review all Japanese lessons and Anki cards from the week. Perform one physical fitness session."})]}),(0,_t.jsxs)("li",{className:"p-4 bg-cyan-50 rounded-lg",children:[(0,_t.jsx)("h3",{className:"font-bold text-teal-800",children:"Saturday Afternoon: Hands-On Project"}),(0,_t.jsx)("p",{className:"text-sm",children:"Apply your practical skills. Build a small shelf, practice knots for 30 minutes, disassemble and reassemble an old appliance. Turn theory into action."})]}),(0,_t.jsxs)("li",{className:"p-4 bg-cyan-50 rounded-lg",children:[(0,_t.jsx)("h3",{className:"font-bold text-teal-800",children:"Sunday Morning: Active Recovery"}),(0,_t.jsx)("p",{className:"text-sm",children:"Go for a long walk or do a deep stretching routine. Listen to Japanese music or a podcast. Let your body heal."})]}),(0,_t.jsxs)("li",{className:"p-4 bg-cyan-50 rounded-lg",children:[(0,_t.jsx)("h3",{className:"font-bold text-teal-800",children:"Sunday Afternoon: Strategize & Recharge"}),(0,_t.jsx)("p",{className:"text-sm",children:"Plan the week ahead: schedule workouts, set study goals. Then, completely disconnect. Read a novel, watch a movie for fun. Recharge your mind for the week ahead."})]})]})]})]})}),(0,_t.jsxs)("section",{className:"mb-12","aria-labelledby":"pillar-navigation-heading",children:[(0,_t.jsx)("h2",{id:"pillar-navigation-heading",className:"text-3xl font-bold text-center mb-8 text-reedsoft-primary",children:"Explore Each Pillar"}),(0,_t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",role:"navigation","aria-label":"Four pillars navigation",children:[(0,_t.jsxs)(mt,{to:"/japan/pillar-1",className:"bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-l-4 border-red-500 group focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2","aria-label":"Navigate to Pillar 1: Physical Fitness and Strength Training",children:[(0,_t.jsx)("div",{className:"text-4xl mb-3","aria-hidden":"true",children:"\ud83d\udcaa"}),(0,_t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-2 group-hover:text-red-500 transition-colors",children:"Pillar 1: The Body"}),(0,_t.jsx)("p",{className:"text-gray-600 text-sm",children:"Physical Fitness & Strength Training"})]}),(0,_t.jsxs)(mt,{to:"/japan/pillar-2",className:"bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-l-4 border-reedsoft-primary group focus:outline-none focus:ring-2 focus:ring-reedsoft-primary focus:ring-offset-2","aria-label":"Navigate to Pillar 2: Japanese Language Mastery",children:[(0,_t.jsx)("div",{className:"text-4xl mb-3","aria-hidden":"true",children:"\ud83d\udde3\ufe0f"}),(0,_t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-2 group-hover:text-reedsoft-primary transition-colors",children:"Pillar 2: The Voice"}),(0,_t.jsx)("p",{className:"text-gray-600 text-sm",children:"Japanese Language Mastery"})]}),(0,_t.jsxs)(mt,{to:"/japan/pillar-3",className:"bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-l-4 border-gray-600 group focus:outline-none focus:ring-2 focus:ring-gray-600 focus:ring-offset-2","aria-label":"Navigate to Pillar 3: Practical Skills and Trade Knowledge",children:[(0,_t.jsx)("div",{className:"text-4xl mb-3","aria-hidden":"true",children:"\ud83d\udee0\ufe0f"}),(0,_t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-2 group-hover:text-gray-600 transition-colors",children:"Pillar 3: The Hands"}),(0,_t.jsx)("p",{className:"text-gray-600 text-sm",children:"Practical Skills & Trade Knowledge"})]}),(0,_t.jsxs)(mt,{to:"/japan/pillar-4",className:"bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-l-4 border-blue-500 group focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2","aria-label":"Navigate to Pillar 4: Cognitive Fitness and Learning",children:[(0,_t.jsx)("div",{className:"text-4xl mb-3","aria-hidden":"true",children:"\ud83e\udde0"}),(0,_t.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-2 group-hover:text-blue-500 transition-colors",children:"Pillar 4: The Mind"}),(0,_t.jsx)("p",{className:"text-gray-600 text-sm",children:"Cognitive Fitness & Learning"})]})]})]}),(0,_t.jsxs)("footer",{className:"text-center mt-12 pt-8 border-t border-gray-300",children:[(0,_t.jsx)("p",{className:"text-gray-600",children:"Discipline today is freedom tomorrow. Execute the plan."}),(0,_t.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"\xa9 2025 Project Reboot Master Plan"})]})]})})};io.register(gl,vl,Po,Jo,fl,Xo);const vc=()=>{const{data:e,options:t,loading:n}=gc("keyLifts");return(0,_t.jsx)("div",{className:"bg-gray-100 text-gray-800 min-h-screen",children:(0,_t.jsxs)("div",{className:"container mx-auto p-4 md:p-8",children:[(0,_t.jsxs)("header",{className:"text-center mb-10",children:[(0,_t.jsx)("div",{className:"inline-block bg-red-500 text-white rounded-full px-5 py-2 mb-4 text-sm font-bold",children:"PILLAR 1: THE BODY"}),(0,_t.jsx)("h1",{className:"text-4xl md:text-5xl font-black text-reedsoft-primary tracking-tight",children:"Forging a Capable Machine"}),(0,_t.jsx)("p",{className:"text-lg text-gray-600 mt-2",children:"Building functional strength and endurance for real-world work."})]}),(0,_t.jsxs)("section",{id:"workout-split",className:"mb-12 bg-white p-6 rounded-lg shadow-lg",children:[(0,_t.jsx)("h2",{className:"text-2xl font-bold text-center mb-6 text-reedsoft-primary",children:"The Weekly Workout Split"}),(0,_t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 text-center",children:[(0,_t.jsxs)("div",{className:"bg-gray-50 p-6 rounded-lg border-l-4 border-red-500",children:[(0,_t.jsx)("h3",{className:"font-bold text-lg",children:"Strength Focus"}),(0,_t.jsx)("p",{className:"font-semibold text-red-500",children:"Mon / Wed / Fri"}),(0,_t.jsx)("p",{className:"mt-2 text-sm",children:"Full-body compound exercises to build a powerful foundation. The core of your physical transformation."})]}),(0,_t.jsxs)("div",{className:"bg-gray-50 p-6 rounded-lg border-l-4 border-blue-500",children:[(0,_t.jsx)("h3",{className:"font-bold text-lg",children:"Cardio & Core"}),(0,_t.jsx)("p",{className:"font-semibold text-blue-500",children:"Tue / Thu"}),(0,_t.jsx)("p",{className:"mt-2 text-sm",children:"High-Intensity Interval Training and core work to build work capacity and a resilient midsection."})]})]})]}),(0,_t.jsxs)("section",{id:"key-lifts",className:"mb-12 bg-white p-6 rounded-lg shadow-lg",children:[(0,_t.jsx)("h2",{className:"text-2xl font-bold text-center mb-6 text-reedsoft-primary",children:"The Five Foundational Lifts"}),(0,_t.jsx)("p",{className:"text-sm text-gray-600 mb-6 text-center max-w-2xl mx-auto",children:"Mastering these five compound movements is the fastest path to functional strength. They work multiple muscle groups simultaneously, mimicking real-world physical tasks."}),(0,_t.jsx)("div",{className:"relative w-full max-w-lg mx-auto h-96",children:n?(0,_t.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,_t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-reedsoft-primary"})}):e&&t?(0,_t.jsx)(nc,{data:e,options:t}):(0,_t.jsx)("div",{className:"flex items-center justify-center h-full text-gray-500",children:"Chart loading..."})})]}),(0,_t.jsxs)("section",{id:"nutrition",className:"mb-12 bg-white p-6 rounded-lg shadow-lg",children:[(0,_t.jsx)("h2",{className:"text-2xl font-bold text-center mb-6 text-reedsoft-primary",children:"The Fuel Pyramid"}),(0,_t.jsx)("p",{className:"text-sm text-gray-600 mb-6 text-center",children:"Your body is a machine; give it the right fuel. Follow this simple hierarchy for optimal performance and recovery. No complex diet needed."}),(0,_t.jsxs)("div",{className:"relative max-w-sm mx-auto text-center font-bold text-white",children:[(0,_t.jsx)("div",{className:"w-full h-24 bg-red-500 flex items-center justify-center rounded-t-lg",style:{clipPath:"polygon(15% 0, 85% 0, 100% 100%, 0% 100%)"},children:"PROTEIN (Every Meal)"}),(0,_t.jsx)("div",{className:"w-full h-20 bg-yellow-500 flex items-center justify-center",style:{clipPath:"polygon(0 0, 100% 0, 85% 100%, 15% 100%)"},children:"COMPLEX CARBS"}),(0,_t.jsx)("div",{className:"w-full h-16 bg-green-500 flex items-center justify-center",style:{clipPath:"polygon(15% 0, 85% 0, 70% 100%, 30% 100%)"},children:"VEGETABLES"}),(0,_t.jsx)("div",{className:"w-full h-12 bg-blue-500 flex items-center justify-center rounded-b-lg",style:{clipPath:"polygon(30% 0, 70% 0, 55% 100%, 45% 100%)"},children:"3L+ WATER DAILY"})]})]}),(0,_t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,_t.jsx)(mt,{to:"/japan",className:"bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors",children:"\u2190 Back to Dashboard"}),(0,_t.jsx)(mt,{to:"/japan/pillar-2",className:"bg-reedsoft-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors",children:"Next: Japanese Language \u2192"})]}),(0,_t.jsx)("footer",{className:"text-center mt-10 pt-6 border-t border-gray-300",children:(0,_t.jsx)("p",{className:"text-gray-700 font-semibold",children:"Your body is your primary tool. Keep it sharp."})})]})})};io.register(co,fl,Xo,Jo);const wc=()=>{const{data:e,options:t,loading:n}=gc("anki");return(0,_t.jsx)("div",{className:"bg-cyan-50 text-gray-800 min-h-screen",children:(0,_t.jsxs)("div",{className:"container mx-auto p-4 md:p-8",children:[(0,_t.jsxs)("header",{className:"text-center mb-10",children:[(0,_t.jsx)("div",{className:"inline-block bg-slate-800 text-white rounded-full px-5 py-2 mb-4 text-sm font-bold",children:"PILLAR 2: THE VOICE"}),(0,_t.jsx)("h1",{className:"text-4xl md:text-5xl font-black text-slate-800 tracking-tight",children:"Building a Bridge to Japan"}),(0,_t.jsx)("p",{className:"text-lg text-teal-700 mt-2",children:"Your structured path to JLPT N4 conversational fluency."})]}),(0,_t.jsxs)("section",{id:"method",className:"mb-12 bg-white p-6 rounded-lg shadow-lg",children:[(0,_t.jsx)("h2",{className:"text-2xl font-bold text-center mb-6 text-slate-800",children:"The Four-Step Language Method"}),(0,_t.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-4 text-center",children:[(0,_t.jsxs)("div",{className:"p-4 rounded-lg bg-gray-100 w-full md:w-auto",children:[(0,_t.jsx)("div",{className:"text-4xl",children:"\u2460"}),(0,_t.jsx)("h3",{className:"font-bold mt-2",children:"Kana Mastery"}),(0,_t.jsx)("p",{className:"text-xs",children:"The Alphabet"})]}),(0,_t.jsx)("div",{className:"text-4xl text-cyan-600",children:"\u2192"}),(0,_t.jsxs)("div",{className:"p-4 rounded-lg bg-gray-100 w-full md:w-auto",children:[(0,_t.jsx)("div",{className:"text-4xl",children:"\u2461"}),(0,_t.jsx)("h3",{className:"font-bold mt-2",children:"Grammar Core"}),(0,_t.jsx)("p",{className:"text-xs",children:"The Skeleton"})]}),(0,_t.jsx)("div",{className:"text-4xl text-cyan-600",children:"\u2192"}),(0,_t.jsxs)("div",{className:"p-4 rounded-lg bg-gray-100 w-full md:w-auto",children:[(0,_t.jsx)("div",{className:"text-4xl",children:"\u2462"}),(0,_t.jsx)("h3",{className:"font-bold mt-2",children:"Anki Vocab"}),(0,_t.jsx)("p",{className:"text-xs",children:"The Muscle"})]}),(0,_t.jsx)("div",{className:"text-4xl text-cyan-600",children:"\u2192"}),(0,_t.jsxs)("div",{className:"p-4 rounded-lg bg-gray-100 w-full md:w-auto",children:[(0,_t.jsx)("div",{className:"text-4xl",children:"\u2463"}),(0,_t.jsx)("h3",{className:"font-bold mt-2",children:"Immersion"}),(0,_t.jsx)("p",{className:"text-xs",children:"The Real World"})]})]})]}),(0,_t.jsxs)("section",{className:"mb-12 grid grid-cols-1 md:grid-cols-2 gap-8 items-center",children:[(0,_t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-lg",children:[(0,_t.jsx)("h2",{className:"text-2xl font-bold text-center mb-4 text-slate-800",children:"The Anki Engine"}),(0,_t.jsx)("p",{className:"text-sm text-center text-gray-600 mb-4",children:"Anki is your non-negotiable daily habit for vocabulary. This is how you burn thousands of words into your long-term memory."}),(0,_t.jsx)("div",{className:"relative w-full max-w-sm mx-auto h-80",children:n?(0,_t.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,_t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-slate-800"})}):e&&t?(0,_t.jsx)(rc,{data:e,options:t}):(0,_t.jsx)("div",{className:"flex items-center justify-center h-full text-gray-500",children:"Chart loading..."})})]}),(0,_t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-lg",children:[(0,_t.jsx)("h2",{className:"text-2xl font-bold text-center mb-4 text-slate-800",children:"Monthly Mission Focus"}),(0,_t.jsxs)("ul",{className:"space-y-4",children:[(0,_t.jsxs)("li",{className:"p-3 bg-cyan-50 rounded-md border-l-4 border-teal-700",children:[(0,_t.jsx)("strong",{className:"text-teal-700",children:"Month 1: Foundation."})," Master Hiragana & Katakana. Complete first half of Genki I. Start daily Anki habit."]}),(0,_t.jsxs)("li",{className:"p-3 bg-cyan-50 rounded-md border-l-4 border-teal-700",children:[(0,_t.jsx)("strong",{className:"text-teal-700",children:"Month 2: Acceleration."})," Finish Genki I. Start Genki II. Aggressively build vocabulary with Anki (aim for 1000+ words)."]}),(0,_t.jsxs)("li",{className:"p-3 bg-cyan-50 rounded-md border-l-4 border-teal-700",children:[(0,_t.jsx)("strong",{className:"text-teal-700",children:"Month 3: Application."})," Continue Genki II. Begin daily active listening (podcasts) and speaking practice (HelloTalk)."]}),(0,_t.jsxs)("li",{className:"p-3 bg-cyan-50 rounded-md border-l-4 border-teal-700",children:[(0,_t.jsx)("strong",{className:"text-teal-700",children:"Month 4: Refinement."})," Finish Genki II. Focus heavily on conversational practice and reading simple news (NHK Easy)."]})]})]})]}),(0,_t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,_t.jsx)(mt,{to:"/japan/pillar-1",className:"bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors",children:"\u2190 Previous: Physical Fitness"}),(0,_t.jsx)(mt,{to:"/japan/pillar-3",className:"bg-reedsoft-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors",children:"Next: Practical Skills \u2192"})]}),(0,_t.jsx)("footer",{className:"text-center mt-10 pt-6 border-t border-cyan-300",children:(0,_t.jsx)("p",{className:"text-teal-700 font-semibold",children:"Language is a skill built daily, not in a day."})})]})})};io.register(Rl,wo,xo,Yo,fl,Xo);const kc=()=>{const{data:e,options:t,loading:n}=gc("knowledge");return(0,_t.jsx)("div",{className:"bg-gray-100 text-gray-800 min-h-screen",children:(0,_t.jsxs)("div",{className:"container mx-auto p-4 md:p-8",children:[(0,_t.jsxs)("header",{className:"text-center mb-10",children:[(0,_t.jsx)("div",{className:"inline-block bg-gray-600 text-white rounded-full px-5 py-2 mb-4 text-sm font-bold",children:"PILLAR 3: THE HANDS"}),(0,_t.jsx)("h1",{className:"text-4xl md:text-5xl font-black text-gray-800 tracking-tight",children:"Acquiring Practical Knowledge"}),(0,_t.jsx)("p",{className:"text-lg text-gray-600 mt-2",children:"Know the 'What' and 'Why' to quickly master the 'How' on the job."})]}),(0,_t.jsxs)("section",{id:"knowledge-areas",className:"mb-12 bg-white p-6 rounded-lg shadow-lg",children:[(0,_t.jsx)("h2",{className:"text-2xl font-bold text-center mb-4 text-gray-800",children:"Core Knowledge Categories"}),(0,_t.jsx)("p",{className:"text-sm text-gray-600 mb-6 text-center max-w-2xl mx-auto",children:"Your study will focus on these five areas. A balanced understanding across all of them will make you a versatile and adaptable worker."}),(0,_t.jsx)("div",{className:"relative w-full max-w-lg mx-auto h-96",children:n?(0,_t.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,_t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"})}):e&&t?(0,_t.jsx)(ic,{data:e,options:t}):(0,_t.jsx)("div",{className:"flex items-center justify-center h-full text-gray-500",children:"Chart loading..."})})]}),(0,_t.jsxs)("section",{id:"learning-loop",className:"mb-12 bg-white p-6 rounded-lg shadow-lg",children:[(0,_t.jsx)("h2",{className:"text-2xl font-bold text-center mb-6 text-gray-800",children:"The Learning Loop"}),(0,_t.jsxs)("div",{className:"flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-8",children:[(0,_t.jsxs)("div",{className:"text-center",children:[(0,_t.jsx)("div",{className:"w-24 h-24 bg-red-500 text-white rounded-full flex items-center justify-center text-4xl mx-auto",children:"\u2460"}),(0,_t.jsx)("h3",{className:"font-bold mt-3",children:"Watch & Learn"}),(0,_t.jsx)("p",{className:"text-sm",children:"Use curated YouTube channels."})]}),(0,_t.jsx)("div",{className:"text-4xl font-light text-gray-400",children:"\u2192"}),(0,_t.jsxs)("div",{className:"text-center",children:[(0,_t.jsx)("div",{className:"w-24 h-24 bg-yellow-500 text-white rounded-full flex items-center justify-center text-4xl mx-auto",children:"\u2461"}),(0,_t.jsx)("h3",{className:"font-bold mt-3",children:"Note & Synthesize"}),(0,_t.jsx)("p",{className:"text-sm",children:"Keep a dedicated notebook."})]}),(0,_t.jsx)("div",{className:"text-4xl font-light text-gray-400",children:"\u2192"}),(0,_t.jsxs)("div",{className:"text-center",children:[(0,_t.jsx)("div",{className:"w-24 h-24 bg-blue-400 text-white rounded-full flex items-center justify-center text-4xl mx-auto",children:"\u2462"}),(0,_t.jsx)("h3",{className:"font-bold mt-3",children:"Apply & Practice"}),(0,_t.jsx)("p",{className:"text-sm",children:"Do weekend hands-on projects."})]})]})]}),(0,_t.jsxs)("section",{id:"safety",className:"mb-12 bg-yellow-400 text-gray-800 p-6 rounded-lg shadow-lg",children:[(0,_t.jsx)("h2",{className:"text-2xl font-bold text-center mb-4",children:"Safety is Non-Negotiable"}),(0,_t.jsx)("p",{className:"text-center text-sm mb-6",children:"Understanding and respecting safety protocols is the mark of a professional. Learn the purpose of each piece of Personal Protective Equipment (PPE)."}),(0,_t.jsxs)("div",{className:"flex justify-around text-center",children:[(0,_t.jsxs)("div",{children:[(0,_t.jsx)("div",{className:"text-5xl",children:"\ud83d\udc77"}),(0,_t.jsx)("p",{className:"font-semibold mt-2",children:"Hard Hat"})]}),(0,_t.jsxs)("div",{children:[(0,_t.jsx)("div",{className:"text-5xl",children:"\ud83d\udc53"}),(0,_t.jsx)("p",{className:"font-semibold mt-2",children:"Safety Glasses"})]}),(0,_t.jsxs)("div",{children:[(0,_t.jsx)("div",{className:"text-5xl",children:"\ud83e\udde4"}),(0,_t.jsx)("p",{className:"font-semibold mt-2",children:"Gloves"})]}),(0,_t.jsxs)("div",{children:[(0,_t.jsx)("div",{className:"text-5xl",children:"\ud83e\udd7e"}),(0,_t.jsx)("p",{className:"font-semibold mt-2",children:"Steel-Toe Boots"})]})]})]}),(0,_t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,_t.jsx)(mt,{to:"/japan/pillar-2",className:"bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors",children:"\u2190 Previous: Japanese Language"}),(0,_t.jsx)(mt,{to:"/japan/pillar-4",className:"bg-reedsoft-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors",children:"Next: Cognitive Fitness \u2192"})]}),(0,_t.jsx)("footer",{className:"text-center mt-10 pt-6 border-t border-gray-300",children:(0,_t.jsx)("p",{className:"text-gray-700 font-semibold",children:"A smart hand is a safe and productive hand."})})]})})};io.register(co,fl,Xo,Jo);const _c=()=>{const{data:e,options:t,loading:n}=gc("mindWorkout");return(0,_t.jsx)("div",{className:"bg-blue-50 text-gray-800 min-h-screen",children:(0,_t.jsxs)("div",{className:"container mx-auto p-4 md:p-8",children:[(0,_t.jsxs)("header",{className:"text-center mb-10",children:[(0,_t.jsx)("div",{className:"inline-block bg-blue-500 text-white rounded-full px-5 py-2 mb-4 text-sm font-bold",children:"PILLAR 4: THE MIND"}),(0,_t.jsx)("h1",{className:"text-4xl md:text-5xl font-black text-slate-700 tracking-tight",children:"Sharpening Your Greatest Tool"}),(0,_t.jsx)("p",{className:"text-lg text-gray-600 mt-2",children:"Rewire your brain for deep focus and accelerated learning."})]}),(0,_t.jsxs)("section",{id:"core-practices",className:"mb-12 bg-white p-6 rounded-lg shadow-lg",children:[(0,_t.jsx)("h2",{className:"text-2xl font-bold text-center mb-6 text-slate-700",children:"The Three Core Practices"}),(0,_t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 text-center",children:[(0,_t.jsxs)("div",{className:"p-4",children:[(0,_t.jsx)("div",{className:"text-6xl",children:"\ud83e\uddd8\u200d\u2642\ufe0f"}),(0,_t.jsx)("h3",{className:"font-bold text-lg mt-3 text-blue-500",children:"Meditation"}),(0,_t.jsx)("p",{className:"text-sm mt-1",children:"Train your attention span. 10 minutes daily with an app like Headspace is non-negotiable."})]}),(0,_t.jsxs)("div",{className:"p-4",children:[(0,_t.jsx)("div",{className:"text-6xl",children:"\ud83d\udcda"}),(0,_t.jsx)("h3",{className:"font-bold text-lg mt-3 text-emerald-500",children:"Active Reading"}),(0,_t.jsx)("p",{className:"text-sm mt-1",children:"Don't just read, process. Use the Feynman Technique to truly understand and retain information."})]}),(0,_t.jsxs)("div",{className:"p-4",children:[(0,_t.jsx)("div",{className:"text-6xl",children:"\ud83e\udde9"}),(0,_t.jsx)("h3",{className:"font-bold text-lg mt-3 text-amber-500",children:"Problem Solving"}),(0,_t.jsx)("p",{className:"text-sm mt-1",children:"Engage in daily logic puzzles (Sudoku, etc.) to keep your reasoning skills sharp and flexible."})]})]})]}),(0,_t.jsxs)("section",{className:"grid grid-cols-1 md:grid-cols-5 gap-8 mb-12 items-center",children:[(0,_t.jsxs)("div",{className:"md:col-span-3 bg-white p-6 rounded-lg shadow-lg",children:[(0,_t.jsx)("h2",{className:"text-2xl font-bold text-center mb-6 text-slate-700",children:"The Feynman Learning Technique"}),(0,_t.jsxs)("div",{className:"space-y-4",children:[(0,_t.jsxs)("div",{className:"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg",children:[(0,_t.jsx)("div",{className:"text-3xl font-bold text-blue-500",children:"1."}),(0,_t.jsxs)("div",{children:[(0,_t.jsx)("strong",{className:"text-gray-800",children:"Choose a Concept."})," Pick a topic you are learning (e.g., a grammar point, how a tool works)."]})]}),(0,_t.jsxs)("div",{className:"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg",children:[(0,_t.jsx)("div",{className:"text-3xl font-bold text-blue-500",children:"2."}),(0,_t.jsxs)("div",{children:[(0,_t.jsx)("strong",{className:"text-gray-800",children:"Teach it Simply."})," Explain it out loud, in the simplest terms possible, as if to a child."]})]}),(0,_t.jsxs)("div",{className:"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg",children:[(0,_t.jsx)("div",{className:"text-3xl font-bold text-blue-500",children:"3."}),(0,_t.jsxs)("div",{children:[(0,_t.jsx)("strong",{className:"text-gray-800",children:"Identify Gaps."})," When you get stuck or use complex language, you've found a weak spot in your understanding."]})]}),(0,_t.jsxs)("div",{className:"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg",children:[(0,_t.jsx)("div",{className:"text-3xl font-bold text-blue-500",children:"4."}),(0,_t.jsxs)("div",{children:[(0,_t.jsx)("strong",{className:"text-gray-800",children:"Review & Simplify."})," Go back to the source material to fill the gap, then refine your simple explanation."]})]})]})]}),(0,_t.jsxs)("div",{className:"md:col-span-2 bg-white p-6 rounded-lg shadow-lg",children:[(0,_t.jsx)("h2",{className:"text-2xl font-bold text-center mb-4 text-slate-700",children:"Daily Cognitive Workout"}),(0,_t.jsx)("p",{className:"text-sm text-center text-gray-600 mb-4",children:"Your 90-minute daily block dedicated to mental fitness."}),(0,_t.jsx)("div",{className:"relative w-full max-w-sm mx-auto h-80",children:n?(0,_t.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,_t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-slate-700"})}):e&&t?(0,_t.jsx)(ac,{data:e,options:t}):(0,_t.jsx)("div",{className:"flex items-center justify-center h-full text-gray-500",children:"Chart loading..."})})]})]}),(0,_t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,_t.jsx)(mt,{to:"/japan/pillar-3",className:"bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors",children:"\u2190 Previous: Practical Skills"}),(0,_t.jsx)(mt,{to:"/japan",className:"bg-reedsoft-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors",children:"Back to Dashboard \u2192"})]}),(0,_t.jsx)("footer",{className:"text-center mt-10 pt-6 border-t border-gray-300",children:(0,_t.jsx)("p",{className:"text-gray-700 font-semibold",children:"The quality of your work is determined by the quality of your focus."})})]})})},Sc=()=>{const e=he(),t=(e=>{const t=[{path:"/",label:"Reedsoft"}];return e.startsWith("/japan")&&(t.push({path:"/japan",label:"Japan Plan"}),"/japan/pillar-1"===e?t.push({path:"/japan/pillar-1",label:"Physical Fitness"}):"/japan/pillar-2"===e?t.push({path:"/japan/pillar-2",label:"Japanese Language"}):"/japan/pillar-3"===e?t.push({path:"/japan/pillar-3",label:"Practical Skills"}):"/japan/pillar-4"===e&&t.push({path:"/japan/pillar-4",label:"Cognitive Fitness"})),t})(e.pathname),n="/"===e.pathname;return(0,_t.jsx)("nav",{className:"bg-white shadow-lg border-b border-gray-200",role:"navigation","aria-label":"Main navigation",children:(0,_t.jsxs)("div",{className:"container mx-auto px-4",children:[(0,_t.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,_t.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,_t.jsx)(mt,{to:"/",className:"text-2xl font-black text-reedsoft-primary hover:text-reedsoft-secondary transition-colors focus:outline-none focus:ring-2 focus:ring-reedsoft-primary focus:ring-offset-2 rounded-md","aria-label":"Reedsoft homepage",children:"REEDSOFT"}),(0,_t.jsx)("div",{className:"hidden md:flex space-x-6",role:"menubar",children:[{path:"/",label:"Home"},{path:"/japan",label:"Japan Plan"}].map(t=>(0,_t.jsx)(mt,{to:t.path,role:"menuitem","aria-current":e.pathname===t.path?"page":void 0,className:"px-3 py-2 rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-reedsoft-primary focus:ring-offset-2 ".concat(e.pathname===t.path?"text-reedsoft-primary bg-reedsoft-light":"text-gray-600 hover:text-reedsoft-primary hover:bg-gray-100"),children:t.label},t.path))})]}),(0,_t.jsx)("div",{className:"md:hidden",children:(0,_t.jsx)("button",{className:"text-gray-600 hover:text-reedsoft-primary focus:outline-none focus:ring-2 focus:ring-reedsoft-primary focus:ring-offset-2 rounded-md p-2","aria-label":"Open mobile menu","aria-expanded":"false",children:(0,_t.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true",children:(0,_t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})})]}),!n&&t.length>1&&(0,_t.jsx)("div",{className:"py-3 border-t border-gray-100",children:(0,_t.jsx)("nav",{className:"flex","aria-label":"Breadcrumb navigation",children:(0,_t.jsx)("ol",{className:"flex items-center space-x-2",children:t.map((e,n)=>(0,_t.jsxs)("li",{className:"flex items-center",children:[n>0&&(0,_t.jsx)("svg",{className:"flex-shrink-0 h-4 w-4 text-gray-400 mx-2",fill:"currentColor",viewBox:"0 0 20 20","aria-hidden":"true",children:(0,_t.jsx)("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})}),n===t.length-1?(0,_t.jsx)("span",{className:"text-sm font-medium text-reedsoft-primary","aria-current":"page",children:e.label}):(0,_t.jsx)(mt,{to:e.path,className:"text-sm font-medium text-gray-500 hover:text-reedsoft-primary transition-colors focus:outline-none focus:ring-2 focus:ring-reedsoft-primary focus:ring-offset-2 rounded-md","aria-label":"Navigate to ".concat(e.label),children:e.label})]},e.path))})})})]})})};class Nc extends i.Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("ErrorBoundary caught an error:",e,t),this.setState({error:e,errorInfo:t})}render(){return this.state.hasError?(0,_t.jsx)("div",{className:"min-h-screen bg-gray-100 flex items-center justify-center px-4",children:(0,_t.jsxs)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-lg p-6",children:[(0,_t.jsx)("div",{className:"flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full mb-4",children:(0,_t.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,_t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,_t.jsx)("h1",{className:"text-xl font-bold text-gray-900 text-center mb-2",children:"Something went wrong"}),(0,_t.jsx)("p",{className:"text-gray-600 text-center mb-6",children:"We're sorry, but something unexpected happened. Please try refreshing the page."}),(0,_t.jsxs)("div",{className:"flex flex-col space-y-3",children:[(0,_t.jsx)("button",{onClick:()=>window.location.reload(),className:"w-full bg-reedsoft-primary text-white py-2 px-4 rounded-md hover:bg-reedsoft-secondary transition-colors font-medium",children:"Refresh Page"}),(0,_t.jsx)("button",{onClick:()=>window.location.href="/",className:"w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors font-medium",children:"Go to Homepage"})]}),!1]})}):this.props.children}}const Mc=Nc;const Cc=function(){return(0,_t.jsx)(Mc,{children:(0,_t.jsx)(ft,{children:(0,_t.jsxs)("div",{className:"App min-h-screen bg-gray-100",children:[(0,_t.jsx)("a",{href:"#main-content",className:"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-reedsoft-primary text-white px-4 py-2 rounded-md z-50 focus:outline-none focus:ring-2 focus:ring-white",children:"Skip to main content"}),(0,_t.jsx)(Sc,{}),(0,_t.jsx)("main",{id:"main-content",className:"container mx-auto",role:"main",children:(0,_t.jsxs)(Ae,{children:[(0,_t.jsx)(Le,{path:"/",element:(0,_t.jsx)(St,{})}),(0,_t.jsx)(Le,{path:"/japan",element:(0,_t.jsx)(xc,{})}),(0,_t.jsx)(Le,{path:"/japan/pillar-1",element:(0,_t.jsx)(vc,{})}),(0,_t.jsx)(Le,{path:"/japan/pillar-2",element:(0,_t.jsx)(wc,{})}),(0,_t.jsx)(Le,{path:"/japan/pillar-3",element:(0,_t.jsx)(kc,{})}),(0,_t.jsx)(Le,{path:"/japan/pillar-4",element:(0,_t.jsx)(_c,{})})]})})]})})})},Pc=e=>{e&&e instanceof Function&&n.e(317).then(n.bind(n,317)).then(t=>{let{getCLS:n,getFID:i,getFCP:r,getLCP:a,getTTFB:s}=t;n(e),i(e),r(e),a(e),s(e)})};r.createRoot(document.getElementById("root")).render((0,_t.jsx)(i.StrictMode,{children:(0,_t.jsx)(Cc,{})})),Pc()})();
//# sourceMappingURL=main.086eb425.js.map