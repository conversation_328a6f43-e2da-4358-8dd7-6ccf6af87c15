{"ast": null, "code": "import{Component}from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";class ErrorBoundary extends Component{constructor(props){super(props);this.state={hasError:false};}static getDerivedStateFromError(error){return{hasError:true,error};}componentDidCatch(error,errorInfo){console.error('ErrorBoundary caught an error:',error,errorInfo);this.setState({error,errorInfo});}render(){if(this.state.hasError){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gray-100 flex items-center justify-center px-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-md w-full bg-white rounded-lg shadow-lg p-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full mb-4\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-6 h-6 text-red-600\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"})})}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-xl font-bold text-gray-900 text-center mb-2\",children:\"Something went wrong\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 text-center mb-6\",children:\"We're sorry, but something unexpected happened. Please try refreshing the page.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col space-y-3\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>window.location.reload(),className:\"w-full bg-reedsoft-primary text-white py-2 px-4 rounded-md hover:bg-reedsoft-secondary transition-colors font-medium\",children:\"Refresh Page\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>window.location.href='/',className:\"w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors font-medium\",children:\"Go to Homepage\"})]}),process.env.NODE_ENV==='development'&&this.state.error&&/*#__PURE__*/_jsxs(\"details\",{className:\"mt-6 p-4 bg-gray-50 rounded-md\",children:[/*#__PURE__*/_jsx(\"summary\",{className:\"cursor-pointer text-sm font-medium text-gray-700 mb-2\",children:\"Error Details (Development Only)\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-xs text-gray-600 font-mono\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"mb-2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Error:\"}),\" \",this.state.error.message]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mb-2\",children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Stack:\"}),/*#__PURE__*/_jsx(\"pre\",{className:\"whitespace-pre-wrap mt-1\",children:this.state.error.stack})]}),this.state.errorInfo&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Component Stack:\"}),/*#__PURE__*/_jsx(\"pre\",{className:\"whitespace-pre-wrap mt-1\",children:this.state.errorInfo.componentStack})]})]})]})]})});}return this.props.children;}}export default ErrorBoundary;", "map": {"version": 3, "names": ["Component", "jsx", "_jsx", "jsxs", "_jsxs", "Error<PERSON>ou<PERSON><PERSON>", "constructor", "props", "state", "<PERSON><PERSON><PERSON><PERSON>", "getDerivedStateFromError", "error", "componentDidCatch", "errorInfo", "console", "setState", "render", "className", "children", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onClick", "window", "location", "reload", "href", "process", "env", "NODE_ENV", "message", "stack", "componentStack"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/components/ErrorBoundary.tsx"], "sourcesContent": ["import { Component, ErrorInfo, ReactNode } from 'react';\n\ninterface Props {\n  children: ReactNode;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: Error;\n  errorInfo?: ErrorInfo;\n}\n\nclass ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    this.setState({ error, errorInfo });\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return (\n        <div className=\"min-h-screen bg-gray-100 flex items-center justify-center px-4\">\n          <div className=\"max-w-md w-full bg-white rounded-lg shadow-lg p-6\">\n            <div className=\"flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full mb-4\">\n              <svg\n                className=\"w-6 h-6 text-red-600\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                />\n              </svg>\n            </div>\n            \n            <h1 className=\"text-xl font-bold text-gray-900 text-center mb-2\">\n              Something went wrong\n            </h1>\n            \n            <p className=\"text-gray-600 text-center mb-6\">\n              We're sorry, but something unexpected happened. Please try refreshing the page.\n            </p>\n\n            <div className=\"flex flex-col space-y-3\">\n              <button\n                onClick={() => window.location.reload()}\n                className=\"w-full bg-reedsoft-primary text-white py-2 px-4 rounded-md hover:bg-reedsoft-secondary transition-colors font-medium\"\n              >\n                Refresh Page\n              </button>\n              \n              <button\n                onClick={() => window.location.href = '/'}\n                className=\"w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors font-medium\"\n              >\n                Go to Homepage\n              </button>\n            </div>\n\n            {process.env.NODE_ENV === 'development' && this.state.error && (\n              <details className=\"mt-6 p-4 bg-gray-50 rounded-md\">\n                <summary className=\"cursor-pointer text-sm font-medium text-gray-700 mb-2\">\n                  Error Details (Development Only)\n                </summary>\n                <div className=\"text-xs text-gray-600 font-mono\">\n                  <div className=\"mb-2\">\n                    <strong>Error:</strong> {this.state.error.message}\n                  </div>\n                  <div className=\"mb-2\">\n                    <strong>Stack:</strong>\n                    <pre className=\"whitespace-pre-wrap mt-1\">\n                      {this.state.error.stack}\n                    </pre>\n                  </div>\n                  {this.state.errorInfo && (\n                    <div>\n                      <strong>Component Stack:</strong>\n                      <pre className=\"whitespace-pre-wrap mt-1\">\n                        {this.state.errorInfo.componentStack}\n                      </pre>\n                    </div>\n                  )}\n                </div>\n              </details>\n            )}\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "mappings": "AAAA,OAASA,SAAS,KAA8B,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAYxD,KAAM,CAAAC,aAAa,QAAS,CAAAL,SAAwB,CAClDM,WAAWA,CAACC,KAAY,CAAE,CACxB,KAAK,CAACA,KAAK,CAAC,CACZ,IAAI,CAACC,KAAK,CAAG,CAAEC,QAAQ,CAAE,KAAM,CAAC,CAClC,CAEA,MAAO,CAAAC,wBAAwBA,CAACC,KAAY,CAAS,CACnD,MAAO,CAAEF,QAAQ,CAAE,IAAI,CAAEE,KAAM,CAAC,CAClC,CAEAC,iBAAiBA,CAACD,KAAY,CAAEE,SAAoB,CAAE,CACpDC,OAAO,CAACH,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAEE,SAAS,CAAC,CACjE,IAAI,CAACE,QAAQ,CAAC,CAAEJ,KAAK,CAAEE,SAAU,CAAC,CAAC,CACrC,CAEAG,MAAMA,CAAA,CAAG,CACP,GAAI,IAAI,CAACR,KAAK,CAACC,QAAQ,CAAE,CACvB,mBACEP,IAAA,QAAKe,SAAS,CAAC,gEAAgE,CAAAC,QAAA,cAC7Ed,KAAA,QAAKa,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChEhB,IAAA,QAAKe,SAAS,CAAC,iFAAiF,CAAAC,QAAA,cAC9FhB,IAAA,QACEe,SAAS,CAAC,sBAAsB,CAChCE,IAAI,CAAC,MAAM,CACXC,MAAM,CAAC,cAAc,CACrBC,OAAO,CAAC,WAAW,CAAAH,QAAA,cAEnBhB,IAAA,SACEoB,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,WAAW,CAAE,CAAE,CACfC,CAAC,CAAC,2IAA2I,CAC9I,CAAC,CACC,CAAC,CACH,CAAC,cAENvB,IAAA,OAAIe,SAAS,CAAC,kDAAkD,CAAAC,QAAA,CAAC,sBAEjE,CAAI,CAAC,cAELhB,IAAA,MAAGe,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,iFAE9C,CAAG,CAAC,cAEJd,KAAA,QAAKa,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eACtChB,IAAA,WACEwB,OAAO,CAAEA,CAAA,GAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE,CACxCZ,SAAS,CAAC,sHAAsH,CAAAC,QAAA,CACjI,cAED,CAAQ,CAAC,cAEThB,IAAA,WACEwB,OAAO,CAAEA,CAAA,GAAMC,MAAM,CAACC,QAAQ,CAACE,IAAI,CAAG,GAAI,CAC1Cb,SAAS,CAAC,uGAAuG,CAAAC,QAAA,CAClH,gBAED,CAAQ,CAAC,EACN,CAAC,CAELa,OAAO,CAACC,GAAG,CAACC,QAAQ,GAAK,aAAa,EAAI,IAAI,CAACzB,KAAK,CAACG,KAAK,eACzDP,KAAA,YAASa,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eACjDhB,IAAA,YAASe,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,kCAE3E,CAAS,CAAC,cACVd,KAAA,QAAKa,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9Cd,KAAA,QAAKa,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBhB,IAAA,WAAAgB,QAAA,CAAQ,QAAM,CAAQ,CAAC,IAAC,CAAC,IAAI,CAACV,KAAK,CAACG,KAAK,CAACuB,OAAO,EAC9C,CAAC,cACN9B,KAAA,QAAKa,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBhB,IAAA,WAAAgB,QAAA,CAAQ,QAAM,CAAQ,CAAC,cACvBhB,IAAA,QAAKe,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CACtC,IAAI,CAACV,KAAK,CAACG,KAAK,CAACwB,KAAK,CACpB,CAAC,EACH,CAAC,CACL,IAAI,CAAC3B,KAAK,CAACK,SAAS,eACnBT,KAAA,QAAAc,QAAA,eACEhB,IAAA,WAAAgB,QAAA,CAAQ,kBAAgB,CAAQ,CAAC,cACjChB,IAAA,QAAKe,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CACtC,IAAI,CAACV,KAAK,CAACK,SAAS,CAACuB,cAAc,CACjC,CAAC,EACH,CACN,EACE,CAAC,EACC,CACV,EACE,CAAC,CACH,CAAC,CAEV,CAEA,MAAO,KAAI,CAAC7B,KAAK,CAACW,QAAQ,CAC5B,CACF,CAEA,cAAe,CAAAb,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}