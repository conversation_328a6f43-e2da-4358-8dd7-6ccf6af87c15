{"ast": null, "code": "var _jsxFileName = \"C:\\\\Developer\\\\Web Development\\\\reedsoft\\\\frontend\\\\src\\\\pages\\\\Pillar4.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { Pie } from 'react-chartjs-2';\nimport { Chart as ChartJS, ArcElement, Tooltip, Legend, Title } from 'chart.js';\nimport { useChartData } from '../hooks/useChartData';\n\n// Register Chart.js components\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(ArcElement, Tooltip, Legend, Title);\nconst Pillar4 = () => {\n  _s();\n  const {\n    data: mindWorkoutData,\n    options: mindWorkoutOptions,\n    loading\n  } = useChartData('mindWorkout');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-blue-50 text-gray-800 min-h-screen\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto p-4 md:p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        className: \"text-center mb-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-block bg-blue-500 text-white rounded-full px-5 py-2 mb-4 text-sm font-bold\",\n          children: \"PILLAR 4: THE MIND\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl md:text-5xl font-black text-slate-700 tracking-tight\",\n          children: \"Sharpening Your Greatest Tool\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 mt-2\",\n          children: \"Rewire your brain for deep focus and accelerated learning.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        id: \"core-practices\",\n        className: \"mb-12 bg-white p-6 rounded-lg shadow-lg\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-center mb-6 text-slate-700\",\n          children: \"The Three Core Practices\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-6 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-6xl\",\n              children: \"\\uD83E\\uDDD8\\u200D\\u2642\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 39,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-bold text-lg mt-3 text-blue-500\",\n              children: \"Meditation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm mt-1\",\n              children: \"Train your attention span. 10 minutes daily with an app like Headspace is non-negotiable.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-6xl\",\n              children: \"\\uD83D\\uDCDA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-bold text-lg mt-3 text-emerald-500\",\n              children: \"Active Reading\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm mt-1\",\n              children: \"Don't just read, process. Use the Feynman Technique to truly understand and retain information.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 46,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-6xl\",\n              children: \"\\uD83E\\uDDE9\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"font-bold text-lg mt-3 text-amber-500\",\n              children: \"Problem Solving\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm mt-1\",\n              children: \"Engage in daily logic puzzles (Sudoku, etc.) to keep your reasoning skills sharp and flexible.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"grid grid-cols-1 md:grid-cols-5 gap-8 mb-12 items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:col-span-3 bg-white p-6 rounded-lg shadow-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-center mb-6 text-slate-700\",\n            children: \"The Feynman Learning Technique\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-blue-500\",\n                children: \"1.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 62,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-gray-800\",\n                  children: \"Choose a Concept.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 63,\n                  columnNumber: 22\n                }, this), \" Pick a topic you are learning (e.g., a grammar point, how a tool works).\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-blue-500\",\n                children: \"2.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 66,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-gray-800\",\n                  children: \"Teach it Simply.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 67,\n                  columnNumber: 22\n                }, this), \" Explain it out loud, in the simplest terms possible, as if to a child.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 67,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-blue-500\",\n                children: \"3.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 70,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-gray-800\",\n                  children: \"Identify Gaps.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 71,\n                  columnNumber: 22\n                }, this), \" When you get stuck or use complex language, you've found a weak spot in your understanding.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 71,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-blue-500\",\n                children: \"4.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 74,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  className: \"text-gray-800\",\n                  children: \"Review & Simplify.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 75,\n                  columnNumber: 22\n                }, this), \" Go back to the source material to fill the gap, then refine your simple explanation.\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 75,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"md:col-span-2 bg-white p-6 rounded-lg shadow-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-2xl font-bold text-center mb-4 text-slate-700\",\n            children: \"Daily Cognitive Workout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-center text-gray-600 mb-4\",\n            children: \"Your 90-minute daily block dedicated to mental fitness.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative w-full max-w-sm mx-auto h-80\",\n            children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center h-full\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-slate-700\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 17\n            }, this) : mindWorkoutData && mindWorkoutOptions ? /*#__PURE__*/_jsxDEV(Pie, {\n              data: mindWorkoutData,\n              options: mindWorkoutOptions\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-center h-full text-gray-500\",\n              children: \"Chart loading...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/japan/pillar-3\",\n          className: \"bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors\",\n          children: \"\\u2190 Previous: Practical Skills\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/japan\",\n          className: \"bg-reedsoft-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors\",\n          children: \"Back to Dashboard \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n        className: \"text-center mt-10 pt-6 border-t border-gray-300\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-700 font-semibold\",\n          children: \"The quality of your work is determined by the quality of your focus.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_s(Pillar4, \"rrGcnOQQb/y4ykJ3uYUyTfG2P4U=\", false, function () {\n  return [useChartData];\n});\n_c = Pillar4;\nexport default Pillar4;\nvar _c;\n$RefreshReg$(_c, \"Pillar4\");", "map": {"version": 3, "names": ["React", "Link", "Pie", "Chart", "ChartJS", "ArcElement", "<PERSON><PERSON><PERSON>", "Legend", "Title", "useChartData", "jsxDEV", "_jsxDEV", "register", "Pillar4", "_s", "data", "mindWorkoutData", "options", "mindWorkoutOptions", "loading", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "to", "_c", "$RefreshReg$"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/pages/Pillar4.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { Pie } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  ArcElement,\n  Tooltip,\n  Legend,\n  Title\n} from 'chart.js';\nimport { useChartData } from '../hooks/useChartData';\n\n// Register Chart.js components\nChartJS.register(ArcE<PERSON>, Tooltip, Legend, Title);\n\nconst Pillar4: React.FC = () => {\n  const { data: mindWorkoutData, options: mindWorkoutOptions, loading } = useChartData('mindWorkout');\n\n  return (\n    <div className=\"bg-blue-50 text-gray-800 min-h-screen\">\n      <div className=\"container mx-auto p-4 md:p-8\">\n        <header className=\"text-center mb-10\">\n          <div className=\"inline-block bg-blue-500 text-white rounded-full px-5 py-2 mb-4 text-sm font-bold\">\n            PILLAR 4: THE MIND\n          </div>\n          <h1 className=\"text-4xl md:text-5xl font-black text-slate-700 tracking-tight\">\n            Sharpening Your Greatest Tool\n          </h1>\n          <p className=\"text-lg text-gray-600 mt-2\">\n            Rewire your brain for deep focus and accelerated learning.\n          </p>\n        </header>\n\n        {/* Core Practices Section */}\n        <section id=\"core-practices\" className=\"mb-12 bg-white p-6 rounded-lg shadow-lg\">\n          <h2 className=\"text-2xl font-bold text-center mb-6 text-slate-700\">The Three Core Practices</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 text-center\">\n            <div className=\"p-4\">\n              <div className=\"text-6xl\">🧘‍♂️</div>\n              <h3 className=\"font-bold text-lg mt-3 text-blue-500\">Meditation</h3>\n              <p className=\"text-sm mt-1\">Train your attention span. 10 minutes daily with an app like Headspace is non-negotiable.</p>\n            </div>\n            <div className=\"p-4\">\n              <div className=\"text-6xl\">📚</div>\n              <h3 className=\"font-bold text-lg mt-3 text-emerald-500\">Active Reading</h3>\n              <p className=\"text-sm mt-1\">Don't just read, process. Use the Feynman Technique to truly understand and retain information.</p>\n            </div>\n            <div className=\"p-4\">\n              <div className=\"text-6xl\">🧩</div>\n              <h3 className=\"font-bold text-lg mt-3 text-amber-500\">Problem Solving</h3>\n              <p className=\"text-sm mt-1\">Engage in daily logic puzzles (Sudoku, etc.) to keep your reasoning skills sharp and flexible.</p>\n            </div>\n          </div>\n        </section>\n\n        {/* Feynman Technique and Mind Workout Section */}\n        <section className=\"grid grid-cols-1 md:grid-cols-5 gap-8 mb-12 items-center\">\n          <div className=\"md:col-span-3 bg-white p-6 rounded-lg shadow-lg\">\n            <h2 className=\"text-2xl font-bold text-center mb-6 text-slate-700\">The Feynman Learning Technique</h2>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\">\n                <div className=\"text-3xl font-bold text-blue-500\">1.</div>\n                <div><strong className=\"text-gray-800\">Choose a Concept.</strong> Pick a topic you are learning (e.g., a grammar point, how a tool works).</div>\n              </div>\n              <div className=\"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\">\n                <div className=\"text-3xl font-bold text-blue-500\">2.</div>\n                <div><strong className=\"text-gray-800\">Teach it Simply.</strong> Explain it out loud, in the simplest terms possible, as if to a child.</div>\n              </div>\n              <div className=\"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\">\n                <div className=\"text-3xl font-bold text-blue-500\">3.</div>\n                <div><strong className=\"text-gray-800\">Identify Gaps.</strong> When you get stuck or use complex language, you've found a weak spot in your understanding.</div>\n              </div>\n              <div className=\"flex items-start space-x-4 p-3 bg-gray-50 rounded-lg\">\n                <div className=\"text-3xl font-bold text-blue-500\">4.</div>\n                <div><strong className=\"text-gray-800\">Review & Simplify.</strong> Go back to the source material to fill the gap, then refine your simple explanation.</div>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"md:col-span-2 bg-white p-6 rounded-lg shadow-lg\">\n            <h2 className=\"text-2xl font-bold text-center mb-4 text-slate-700\">Daily Cognitive Workout</h2>\n            <p className=\"text-sm text-center text-gray-600 mb-4\">Your 90-minute daily block dedicated to mental fitness.</p>\n            <div className=\"relative w-full max-w-sm mx-auto h-80\">\n              {loading ? (\n                <div className=\"flex items-center justify-center h-full\">\n                  <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-slate-700\"></div>\n                </div>\n              ) : mindWorkoutData && mindWorkoutOptions ? (\n                <Pie data={mindWorkoutData} options={mindWorkoutOptions as any} />\n              ) : (\n                <div className=\"flex items-center justify-center h-full text-gray-500\">\n                  Chart loading...\n                </div>\n              )}\n            </div>\n          </div>\n        </section>\n\n        {/* Navigation */}\n        <div className=\"flex justify-between items-center\">\n          <Link\n            to=\"/japan/pillar-3\"\n            className=\"bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors\"\n          >\n            ← Previous: Practical Skills\n          </Link>\n          <Link\n            to=\"/japan\"\n            className=\"bg-reedsoft-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors\"\n          >\n            Back to Dashboard →\n          </Link>\n        </div>\n\n        <footer className=\"text-center mt-10 pt-6 border-t border-gray-300\">\n          <p className=\"text-gray-700 font-semibold\">\n            The quality of your work is determined by the quality of your focus.\n          </p>\n        </footer>\n      </div>\n    </div>\n  );\n};\n\nexport default Pillar4;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,GAAG,QAAQ,iBAAiB;AACrC,SACEC,KAAK,IAAIC,OAAO,EAChBC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,KAAK,QACA,UAAU;AACjB,SAASC,YAAY,QAAQ,uBAAuB;;AAEpD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAP,OAAO,CAACQ,QAAQ,CAACP,UAAU,EAAEC,OAAO,EAAEC,MAAM,EAAEC,KAAK,CAAC;AAEpD,MAAMK,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC,IAAI,EAAEC,eAAe;IAAEC,OAAO,EAAEC,kBAAkB;IAAEC;EAAQ,CAAC,GAAGV,YAAY,CAAC,aAAa,CAAC;EAEnG,oBACEE,OAAA;IAAKS,SAAS,EAAC,uCAAuC;IAAAC,QAAA,eACpDV,OAAA;MAAKS,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAC3CV,OAAA;QAAQS,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBACnCV,OAAA;UAAKS,SAAS,EAAC,mFAAmF;UAAAC,QAAA,EAAC;QAEnG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNd,OAAA;UAAIS,SAAS,EAAC,+DAA+D;UAAAC,QAAA,EAAC;QAE9E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLd,OAAA;UAAGS,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGTd,OAAA;QAASe,EAAE,EAAC,gBAAgB;QAACN,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBAC9EV,OAAA;UAAIS,SAAS,EAAC,oDAAoD;UAAAC,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChGd,OAAA;UAAKS,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChEV,OAAA;YAAKS,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBV,OAAA;cAAKS,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACrCd,OAAA;cAAIS,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpEd,OAAA;cAAGS,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAyF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtH,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBV,OAAA;cAAKS,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClCd,OAAA;cAAIS,SAAS,EAAC,yCAAyC;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3Ed,OAAA;cAAGS,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAA+F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5H,CAAC,eACNd,OAAA;YAAKS,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBV,OAAA;cAAKS,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAClCd,OAAA;cAAIS,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1Ed,OAAA;cAAGS,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAA8F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3H,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGVd,OAAA;QAASS,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBAC3EV,OAAA;UAAKS,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9DV,OAAA;YAAIS,SAAS,EAAC,oDAAoD;YAAAC,QAAA,EAAC;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtGd,OAAA;YAAKS,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBV,OAAA;cAAKS,SAAS,EAAC,sDAAsD;cAAAC,QAAA,gBACnEV,OAAA;gBAAKS,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1Dd,OAAA;gBAAAU,QAAA,gBAAKV,OAAA;kBAAQS,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,6EAAyE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7I,CAAC,eACNd,OAAA;cAAKS,SAAS,EAAC,sDAAsD;cAAAC,QAAA,gBACnEV,OAAA;gBAAKS,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1Dd,OAAA;gBAAAU,QAAA,gBAAKV,OAAA;kBAAQS,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,2EAAuE;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1I,CAAC,eACNd,OAAA;cAAKS,SAAS,EAAC,sDAAsD;cAAAC,QAAA,gBACnEV,OAAA;gBAAKS,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1Dd,OAAA;gBAAAU,QAAA,gBAAKV,OAAA;kBAAQS,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,gGAA4F;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7J,CAAC,eACNd,OAAA;cAAKS,SAAS,EAAC,sDAAsD;cAAAC,QAAA,gBACnEV,OAAA;gBAAKS,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1Dd,OAAA;gBAAAU,QAAA,gBAAKV,OAAA;kBAAQS,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,yFAAqF;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1J,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENd,OAAA;UAAKS,SAAS,EAAC,iDAAiD;UAAAC,QAAA,gBAC9DV,OAAA;YAAIS,SAAS,EAAC,oDAAoD;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/Fd,OAAA;YAAGS,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAuD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACjHd,OAAA;YAAKS,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EACnDF,OAAO,gBACNR,OAAA;cAAKS,SAAS,EAAC,yCAAyC;cAAAC,QAAA,eACtDV,OAAA;gBAAKS,SAAS,EAAC;cAAiE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC,GACJT,eAAe,IAAIE,kBAAkB,gBACvCP,OAAA,CAACT,GAAG;cAACa,IAAI,EAAEC,eAAgB;cAACC,OAAO,EAAEC;YAA0B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAElEd,OAAA;cAAKS,SAAS,EAAC,uDAAuD;cAAAC,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGVd,OAAA;QAAKS,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDV,OAAA,CAACV,IAAI;UACH0B,EAAE,EAAC,iBAAiB;UACpBP,SAAS,EAAC,kGAAkG;UAAAC,QAAA,EAC7G;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPd,OAAA,CAACV,IAAI;UACH0B,EAAE,EAAC,QAAQ;UACXP,SAAS,EAAC,iHAAiH;UAAAC,QAAA,EAC5H;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENd,OAAA;QAAQS,SAAS,EAAC,iDAAiD;QAAAC,QAAA,eACjEV,OAAA;UAAGS,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAC;QAE3C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACX,EAAA,CA3GID,OAAiB;EAAA,QACmDJ,YAAY;AAAA;AAAAmB,EAAA,GADhFf,OAAiB;AA6GvB,eAAeA,OAAO;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}