{"ast": null, "code": "var _jsxFileName = \"C:\\\\Developer\\\\Web Development\\\\reedsoft\\\\frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport './App.css';\n\n// Pages\nimport ReedsoftLanding from './pages/ReedsoftLanding';\nimport JapanPlan from './pages/JapanPlan';\nimport Pillar1 from './pages/Pillar1';\nimport Pillar2 from './pages/Pillar2';\nimport Pillar3 from './pages/Pillar3';\nimport Pillar4 from './pages/Pillar4';\n\n// Components\nimport Navigation from './components/Navigation';\nimport ErrorBoundary from './components/ErrorBoundary';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App min-h-screen bg-gray-100\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#main-content\",\n          className: \"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-reedsoft-primary text-white px-4 py-2 rounded-md z-50 focus:outline-none focus:ring-2 focus:ring-white\",\n          children: \"Skip to main content\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Navigation, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          id: \"main-content\",\n          className: \"container mx-auto\",\n          role: \"main\",\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(ReedsoftLanding, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/japan\",\n              element: /*#__PURE__*/_jsxDEV(JapanPlan, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 34,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/japan/pillar-1\",\n              element: /*#__PURE__*/_jsxDEV(Pillar1, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 35,\n                columnNumber: 54\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/japan/pillar-2\",\n              element: /*#__PURE__*/_jsxDEV(Pillar2, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 36,\n                columnNumber: 54\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/japan/pillar-3\",\n              element: /*#__PURE__*/_jsxDEV(Pillar3, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 54\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/japan/pillar-4\",\n              element: /*#__PURE__*/_jsxDEV(Pillar4, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 38,\n                columnNumber: 54\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 38,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "ReedsoftLanding", "JapanPlan", "Pillar1", "Pillar2", "Pillar3", "Pillar4", "Navigation", "Error<PERSON>ou<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "App", "children", "className", "href", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "role", "path", "element", "_c", "$RefreshReg$"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport './App.css';\n\n// Pages\nimport ReedsoftLanding from './pages/ReedsoftLanding';\nimport JapanPlan from './pages/JapanPlan';\nimport Pillar1 from './pages/Pillar1';\nimport Pillar2 from './pages/Pillar2';\nimport Pillar3 from './pages/Pillar3';\nimport Pillar4 from './pages/Pillar4';\n\n// Components\nimport Navigation from './components/Navigation';\nimport ErrorBoundary from './components/ErrorBoundary';\n\nfunction App() {\n  return (\n    <ErrorBoundary>\n      <Router>\n        <div className=\"App min-h-screen bg-gray-100\">\n          {/* Skip Navigation Link */}\n          <a\n            href=\"#main-content\"\n            className=\"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-reedsoft-primary text-white px-4 py-2 rounded-md z-50 focus:outline-none focus:ring-2 focus:ring-white\"\n          >\n            Skip to main content\n          </a>\n\n          <Navigation />\n          <main id=\"main-content\" className=\"container mx-auto\" role=\"main\">\n            <Routes>\n              <Route path=\"/\" element={<ReedsoftLanding />} />\n              <Route path=\"/japan\" element={<JapanPlan />} />\n              <Route path=\"/japan/pillar-1\" element={<Pillar1 />} />\n              <Route path=\"/japan/pillar-2\" element={<Pillar2 />} />\n              <Route path=\"/japan/pillar-3\" element={<Pillar3 />} />\n              <Route path=\"/japan/pillar-4\" element={<Pillar4 />} />\n            </Routes>\n          </main>\n        </div>\n      </Router>\n    </ErrorBoundary>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAO,WAAW;;AAElB;AACA,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,aAAa,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACF,aAAa;IAAAI,QAAA,eACZF,OAAA,CAACZ,MAAM;MAAAc,QAAA,eACLF,OAAA;QAAKG,SAAS,EAAC,8BAA8B;QAAAD,QAAA,gBAE3CF,OAAA;UACEI,IAAI,EAAC,eAAe;UACpBD,SAAS,EAAC,6KAA6K;UAAAD,QAAA,EACxL;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJR,OAAA,CAACH,UAAU;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACdR,OAAA;UAAMS,EAAE,EAAC,cAAc;UAACN,SAAS,EAAC,mBAAmB;UAACO,IAAI,EAAC,MAAM;UAAAR,QAAA,eAC/DF,OAAA,CAACX,MAAM;YAAAa,QAAA,gBACLF,OAAA,CAACV,KAAK;cAACqB,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEZ,OAAA,CAACT,eAAe;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChDR,OAAA,CAACV,KAAK;cAACqB,IAAI,EAAC,QAAQ;cAACC,OAAO,eAAEZ,OAAA,CAACR,SAAS;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/CR,OAAA,CAACV,KAAK;cAACqB,IAAI,EAAC,iBAAiB;cAACC,OAAO,eAAEZ,OAAA,CAACP,OAAO;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtDR,OAAA,CAACV,KAAK;cAACqB,IAAI,EAAC,iBAAiB;cAACC,OAAO,eAAEZ,OAAA,CAACN,OAAO;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtDR,OAAA,CAACV,KAAK;cAACqB,IAAI,EAAC,iBAAiB;cAACC,OAAO,eAAEZ,OAAA,CAACL,OAAO;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtDR,OAAA,CAACV,KAAK;cAACqB,IAAI,EAAC,iBAAiB;cAACC,OAAO,eAAEZ,OAAA,CAACJ,OAAO;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB;AAACK,EAAA,GA5BQZ,GAAG;AA8BZ,eAAeA,GAAG;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}