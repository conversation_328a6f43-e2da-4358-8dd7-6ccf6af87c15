import { render, screen } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import Navigation from './Navigation';

// Helper function to render component with router
const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('Navigation Component', () => {
  test('renders navigation with all required links', () => {
    renderWithRouter(<Navigation />);
    
    // Check for main navigation links
    expect(screen.getByText('Reedsoft')).toBeInTheDocument();
    expect(screen.getByText('Japan Plan')).toBeInTheDocument();
    
    // Check for pillar links
    expect(screen.getByText('Physical Fitness')).toBeInTheDocument();
    expect(screen.getByText('Japanese Language')).toBeInTheDocument();
    expect(screen.getByText('Practical Skills')).toBeInTheDocument();
    expect(screen.getByText('Cognitive Fitness')).toBeInTheDocument();
  });

  test('has proper accessibility attributes', () => {
    renderWithRouter(<Navigation />);
    
    const nav = screen.getByRole('navigation');
    expect(nav).toHaveAttribute('aria-label', 'Main navigation');
    
    // Check for proper link roles
    const links = screen.getAllByRole('link');
    expect(links.length).toBeGreaterThan(0);
  });

  test('applies correct CSS classes for styling', () => {
    renderWithRouter(<Navigation />);
    
    const nav = screen.getByRole('navigation');
    expect(nav).toHaveClass('bg-reedsoft-primary');
  });

  test('mobile menu toggle functionality', () => {
    renderWithRouter(<Navigation />);
    
    // Check if mobile menu button exists (for responsive design)
    const mobileMenuButton = screen.queryByRole('button');
    if (mobileMenuButton) {
      expect(mobileMenuButton).toBeInTheDocument();
    }
  });
});
