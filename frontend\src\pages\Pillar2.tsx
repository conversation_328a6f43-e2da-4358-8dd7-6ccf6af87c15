import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Doughn<PERSON> } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  Title
} from 'chart.js';
import { useChartData } from '../hooks/useChartData';

// Register Chart.js components
ChartJS.register(Arc<PERSON><PERSON>, Tooltip, Legend, Title);

const Pillar2: React.FC = () => {
  const { data: ankiData, options: ankiOptions, loading } = useChartData('anki');

  return (
    <div className="bg-cyan-50 text-gray-800 min-h-screen">
      <div className="container mx-auto p-4 md:p-8">
        <header className="text-center mb-10">
          <div className="inline-block bg-slate-800 text-white rounded-full px-5 py-2 mb-4 text-sm font-bold">
            PILLAR 2: THE VOICE
          </div>
          <h1 className="text-4xl md:text-5xl font-black text-slate-800 tracking-tight">
            Building a Bridge to Japan
          </h1>
          <p className="text-lg text-teal-700 mt-2">
            Your structured path to JLPT N4 conversational fluency.
          </p>
        </header>

        {/* Four-Step Method Section */}
        <section id="method" className="mb-12 bg-white p-6 rounded-lg shadow-lg">
          <h2 className="text-2xl font-bold text-center mb-6 text-slate-800">The Four-Step Language Method</h2>
          <div className="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-4 text-center">
            <div className="p-4 rounded-lg bg-gray-100 w-full md:w-auto">
              <div className="text-4xl">①</div>
              <h3 className="font-bold mt-2">Kana Mastery</h3>
              <p className="text-xs">The Alphabet</p>
            </div>
            <div className="text-4xl text-cyan-600">→</div>
            <div className="p-4 rounded-lg bg-gray-100 w-full md:w-auto">
              <div className="text-4xl">②</div>
              <h3 className="font-bold mt-2">Grammar Core</h3>
              <p className="text-xs">The Skeleton</p>
            </div>
            <div className="text-4xl text-cyan-600">→</div>
            <div className="p-4 rounded-lg bg-gray-100 w-full md:w-auto">
              <div className="text-4xl">③</div>
              <h3 className="font-bold mt-2">Anki Vocab</h3>
              <p className="text-xs">The Muscle</p>
            </div>
            <div className="text-4xl text-cyan-600">→</div>
            <div className="p-4 rounded-lg bg-gray-100 w-full md:w-auto">
              <div className="text-4xl">④</div>
              <h3 className="font-bold mt-2">Immersion</h3>
              <p className="text-xs">The Real World</p>
            </div>
          </div>
        </section>

        {/* Anki Engine and Monthly Missions */}
        <section className="mb-12 grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <h2 className="text-2xl font-bold text-center mb-4 text-slate-800">The Anki Engine</h2>
            <p className="text-sm text-center text-gray-600 mb-4">
              Anki is your non-negotiable daily habit for vocabulary. This is how you burn thousands of words into your long-term memory.
            </p>
            <div className="relative w-full max-w-sm mx-auto h-80">
              {loading ? (
                <div className="flex items-center justify-center h-full">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-slate-800"></div>
                </div>
              ) : ankiData && ankiOptions ? (
                <Doughnut data={ankiData} options={ankiOptions as any} />
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  Chart loading...
                </div>
              )}
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-lg">
            <h2 className="text-2xl font-bold text-center mb-4 text-slate-800">Monthly Mission Focus</h2>
            <ul className="space-y-4">
              <li className="p-3 bg-cyan-50 rounded-md border-l-4 border-teal-700">
                <strong className="text-teal-700">Month 1: Foundation.</strong> Master Hiragana & Katakana. Complete first half of Genki I. Start daily Anki habit.
              </li>
              <li className="p-3 bg-cyan-50 rounded-md border-l-4 border-teal-700">
                <strong className="text-teal-700">Month 2: Acceleration.</strong> Finish Genki I. Start Genki II. Aggressively build vocabulary with Anki (aim for 1000+ words).
              </li>
              <li className="p-3 bg-cyan-50 rounded-md border-l-4 border-teal-700">
                <strong className="text-teal-700">Month 3: Application.</strong> Continue Genki II. Begin daily active listening (podcasts) and speaking practice (HelloTalk).
              </li>
              <li className="p-3 bg-cyan-50 rounded-md border-l-4 border-teal-700">
                <strong className="text-teal-700">Month 4: Refinement.</strong> Finish Genki II. Focus heavily on conversational practice and reading simple news (NHK Easy).
              </li>
            </ul>
          </div>
        </section>

        {/* Navigation */}
        <div className="flex justify-between items-center">
          <Link
            to="/japan/pillar-1"
            className="bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors"
          >
            ← Previous: Physical Fitness
          </Link>
          <Link
            to="/japan/pillar-3"
            className="bg-reedsoft-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors"
          >
            Next: Practical Skills →
          </Link>
        </div>

        <footer className="text-center mt-10 pt-6 border-t border-cyan-300">
          <p className="text-teal-700 font-semibold">
            Language is a skill built daily, not in a day.
          </p>
        </footer>
      </div>
    </div>
  );
};

export default Pillar2;
