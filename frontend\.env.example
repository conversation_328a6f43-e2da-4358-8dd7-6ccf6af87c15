# Reedsoft Frontend Environment Variables
# Copy this file to .env.local and update values as needed

# Application Configuration
REACT_APP_NAME=Reedsoft
REACT_APP_VERSION=1.0.0
REACT_APP_ENVIRONMENT=development

# API Configuration (for future backend integration)
REACT_APP_API_BASE_URL=http://localhost:8000/api
REACT_APP_API_TIMEOUT=10000

# Feature Flags
REACT_APP_ENABLE_ANALYTICS=false
REACT_APP_ENABLE_ERROR_REPORTING=false
REACT_APP_ENABLE_DEBUG_MODE=true

# Chart.js Configuration
REACT_APP_CHART_ANIMATION_DURATION=1000
REACT_APP_CHART_RESPONSIVE=true

# Accessibility Configuration
REACT_APP_ENABLE_HIGH_CONTRAST=false
REACT_APP_ENABLE_REDUCED_MOTION=false

# Development Configuration
REACT_APP_SHOW_DEV_TOOLS=true
GENERATE_SOURCEMAP=true
