# Reedsoft Monorepo

A comprehensive monorepo for Reedsoft startup projects, featuring a React TypeScript frontend, Python backend, and mobile application structure.

## 🏗️ Project Structure

```
reedsoft/
├── frontend/          # React TypeScript web application
├── backend/           # Python backend services (placeholder)
├── mobile/            # Expo/React Native app (placeholder)
├── project-joat-reference/  # Original HTML reference files
├── package.json       # Root workspace configuration
├── .gitignore        # Git ignore rules
└── README.md         # This file
```

## 🚀 Featured Projects

### Japan Work Preparation Plan
A comprehensive 4-month training program dashboard featuring:
- **Physical Fitness Pillar**: Strength training and cardio programs
- **Japanese Language Pillar**: JLPT N4 preparation with structured learning
- **Practical Skills Pillar**: Trade knowledge and safety protocols
- **Cognitive Fitness Pillar**: Mental training and learning techniques

**Live Routes:**
- `/` - Reedsoft company homepage
- `/japan` - Japan preparation dashboard
- `/japan/pillar-1` - Physical Fitness detailed view
- `/japan/pillar-2` - Japanese Language detailed view
- `/japan/pillar-3` - Practical Skills detailed view
- `/japan/pillar-4` - Cognitive Fitness detailed view

## 🛠️ Technology Stack

### Frontend
- **React 18** with TypeScript
- **React Router** for client-side routing
- **Tailwind CSS** for styling
- **Chart.js** with react-chartjs-2 for data visualizations
- **Responsive design** for desktop, tablet, and mobile

### Backend (Planned)
- **Python** with FastAPI or Django
- **PostgreSQL** database
- **RESTful API** architecture

### Mobile (Planned)
- **Expo** with React Native
- **TypeScript** support
- **Shared components** with web frontend

## 📦 Installation & Setup

### Prerequisites
- Node.js 18+ and npm 9+
- Git

### Quick Start
```bash
# Clone the repository
git clone <repository-url>
cd reedsoft

# Install all dependencies
npm run install:all

# Start the development server
npm run dev
```

### Individual Commands
```bash
# Frontend development
npm run start:frontend

# Build for production
npm run build:frontend

# Run tests
npm run test:frontend

# Lint code
npm run lint

# Clean all node_modules
npm run clean
```

## 🎨 Design System

### Color Palette
- **Primary**: `#003B46` (Deep teal)
- **Secondary**: `#07575B` (Medium teal)
- **Accent**: `#66A5AD` (Light teal)
- **Background**: `#C4DFE6` (Very light teal)
- **Highlights**: Various colors for different pillars

### Typography
- **Font Family**: Inter (Google Fonts)
- **Weights**: 400 (regular), 600 (semibold), 700 (bold), 900 (black)

## 📊 Features

### Interactive Visualizations
- **Doughnut Charts**: Time allocation and learning cycles (JapanPlan, Pillar2)
- **Bar Charts**: Strength training progress (Pillar1)
- **Radar Charts**: Skill development areas (Pillar3)
- **Pie Charts**: Daily activity breakdowns (Pillar4)

### Accessibility Features
- **ARIA Labels**: Comprehensive screen reader support
- **Keyboard Navigation**: Full keyboard accessibility
- **Focus Management**: Visible focus indicators
- **Skip Navigation**: Direct access to main content
- **High Contrast Support**: Automatic contrast adjustments
- **Reduced Motion**: Respects user motion preferences

### Responsive Design
- Mobile-first approach with Tailwind CSS breakpoints
- Tablet and desktop optimizations
- Accessible navigation and interactions
- Error boundaries for production resilience

### Time Format Conversion
- Automatic conversion from 24-hour to 12-hour AM/PM format
- Consistent time display across all components
- Custom hooks for time formatting utilities

### Performance Optimizations
- **Code Splitting**: Lazy loading of components
- **Production Build**: Optimized bundle size (153KB gzipped)
- **TypeScript**: Full type safety and IntelliSense support
- **ESLint**: Code quality and consistency enforcement

## 🔄 Development Workflow

1. **Planning**: Task management with detailed breakdown using built-in task management tools
2. **Implementation**: Component-by-component conversion from HTML to React
3. **Testing**: Comprehensive testing including TypeScript compilation, ESLint, and production builds
4. **Documentation**: Inline comments, README updates, and accessibility documentation

### Quality Assurance Checklist
- ✅ TypeScript compilation without errors
- ✅ ESLint passing with accessibility rules
- ✅ Production build successful (153KB gzipped)
- ✅ All Chart.js visualizations working
- ✅ Responsive design tested across breakpoints
- ✅ Accessibility features implemented and tested
- ✅ Time format conversion working correctly
- ✅ Navigation and routing functional

## 🧪 Testing

### Automated Testing
```bash
# TypeScript type checking
npx tsc --noEmit

# ESLint code quality
npx eslint src --ext .ts,.tsx

# Production build test
npm run build
```

### Manual Testing Checklist
- [ ] All pages load without errors
- [ ] Charts render correctly with data
- [ ] Navigation works between all routes
- [ ] Time format displays in 12-hour AM/PM
- [ ] Responsive design works on mobile/tablet/desktop
- [ ] Accessibility features work with screen readers
- [ ] Error boundaries catch and display errors gracefully

## 🚀 Future Roadmap

### Phase 1: Backend Development
- [ ] Python FastAPI backend setup
- [ ] PostgreSQL database integration
- [ ] User authentication system
- [ ] Progress tracking API endpoints

### Phase 2: Enhanced Features
- [ ] User profiles and progress saving
- [ ] Advanced analytics and reporting
- [ ] Mobile app development with Expo
- [ ] Push notifications for study reminders

### Phase 3: Expansion
- [ ] Additional Reedsoft project showcases
- [ ] Multi-language support (Japanese/English)
- [ ] Community features and sharing
- [ ] Integration with external learning platforms

## 📝 License

MIT License - see LICENSE file for details

## 🤝 Contributing

This is a private Reedsoft project. For internal development guidelines, see the development documentation.

---

**Reedsoft** - Building innovative solutions for personal and professional development.
