{"ast": null, "code": "import React from'react';import{<PERSON>}from'react-router-dom';import{Bar}from'react-chartjs-2';import{Chart as ChartJS,CategoryScale,LinearScale,BarElement,Title,Tooltip,Legend}from'chart.js';import{useChartData}from'../hooks/useChartData';// Register Chart.js components\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";ChartJS.register(CategoryScale,LinearScale,BarElement,Title,Tooltip,Legend);const Pillar1=()=>{const{data:keyLiftsData,options:keyLiftsOptions,loading}=useChartData('keyLifts');return/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-100 text-gray-800 min-h-screen\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto p-4 md:p-8\",children:[/*#__PURE__*/_jsxs(\"header\",{className:\"text-center mb-10\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"inline-block bg-red-500 text-white rounded-full px-5 py-2 mb-4 text-sm font-bold\",children:\"PILLAR 1: THE BODY\"}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-4xl md:text-5xl font-black text-reedsoft-primary tracking-tight\",children:\"Forging a Capable Machine\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg text-gray-600 mt-2\",children:\"Building functional strength and endurance for real-world work.\"})]}),/*#__PURE__*/_jsxs(\"section\",{id:\"workout-split\",className:\"mb-12 bg-white p-6 rounded-lg shadow-lg\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-center mb-6 text-reedsoft-primary\",children:\"The Weekly Workout Split\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-6 text-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-50 p-6 rounded-lg border-l-4 border-red-500\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-bold text-lg\",children:\"Strength Focus\"}),/*#__PURE__*/_jsx(\"p\",{className:\"font-semibold text-red-500\",children:\"Mon / Wed / Fri\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-sm\",children:\"Full-body compound exercises to build a powerful foundation. The core of your physical transformation.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-gray-50 p-6 rounded-lg border-l-4 border-blue-500\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-bold text-lg\",children:\"Cardio & Core\"}),/*#__PURE__*/_jsx(\"p\",{className:\"font-semibold text-blue-500\",children:\"Tue / Thu\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-sm\",children:\"High-Intensity Interval Training and core work to build work capacity and a resilient midsection.\"})]})]})]}),/*#__PURE__*/_jsxs(\"section\",{id:\"key-lifts\",className:\"mb-12 bg-white p-6 rounded-lg shadow-lg\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-center mb-6 text-reedsoft-primary\",children:\"The Five Foundational Lifts\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600 mb-6 text-center max-w-2xl mx-auto\",children:\"Mastering these five compound movements is the fastest path to functional strength. They work multiple muscle groups simultaneously, mimicking real-world physical tasks.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"relative w-full max-w-lg mx-auto h-96\",children:loading?/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-full\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-b-2 border-reedsoft-primary\"})}):keyLiftsData&&keyLiftsOptions?/*#__PURE__*/_jsx(Bar,{data:keyLiftsData,options:keyLiftsOptions}):/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-full text-gray-500\",children:\"Chart loading...\"})})]}),/*#__PURE__*/_jsxs(\"section\",{id:\"nutrition\",className:\"mb-12 bg-white p-6 rounded-lg shadow-lg\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-center mb-6 text-reedsoft-primary\",children:\"The Fuel Pyramid\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600 mb-6 text-center\",children:\"Your body is a machine; give it the right fuel. Follow this simple hierarchy for optimal performance and recovery. No complex diet needed.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative max-w-sm mx-auto text-center font-bold text-white\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-full h-24 bg-red-500 flex items-center justify-center rounded-t-lg\",style:{clipPath:'polygon(15% 0, 85% 0, 100% 100%, 0% 100%)'},children:\"PROTEIN (Every Meal)\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full h-20 bg-yellow-500 flex items-center justify-center\",style:{clipPath:'polygon(0 0, 100% 0, 85% 100%, 15% 100%)'},children:\"COMPLEX CARBS\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full h-16 bg-green-500 flex items-center justify-center\",style:{clipPath:'polygon(15% 0, 85% 0, 70% 100%, 30% 100%)'},children:\"VEGETABLES\"}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full h-12 bg-blue-500 flex items-center justify-center rounded-b-lg\",style:{clipPath:'polygon(30% 0, 70% 0, 55% 100%, 45% 100%)'},children:\"3L+ WATER DAILY\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center\",children:[/*#__PURE__*/_jsx(Link,{to:\"/japan\",className:\"bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors\",children:\"\\u2190 Back to Dashboard\"}),/*#__PURE__*/_jsx(Link,{to:\"/japan/pillar-2\",className:\"bg-reedsoft-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors\",children:\"Next: Japanese Language \\u2192\"})]}),/*#__PURE__*/_jsx(\"footer\",{className:\"text-center mt-10 pt-6 border-t border-gray-300\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-700 font-semibold\",children:\"Your body is your primary tool. Keep it sharp.\"})})]})});};export default Pillar1;", "map": {"version": 3, "names": ["React", "Link", "Bar", "Chart", "ChartJS", "CategoryScale", "LinearScale", "BarElement", "Title", "<PERSON><PERSON><PERSON>", "Legend", "useChartData", "jsx", "_jsx", "jsxs", "_jsxs", "register", "Pillar1", "data", "keyLiftsData", "options", "keyLiftsOptions", "loading", "className", "children", "id", "style", "clipPath", "to"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/pages/Pillar1.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { Bar } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend,\n} from 'chart.js';\nimport { useChartData } from '../hooks/useChartData';\n\n// Register Chart.js components\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  BarElement,\n  Title,\n  Tooltip,\n  Legend\n);\n\nconst Pillar1: React.FC = () => {\n  const { data: keyLiftsData, options: keyLiftsOptions, loading } = useChartData('keyLifts');\n\n  return (\n    <div className=\"bg-gray-100 text-gray-800 min-h-screen\">\n      <div className=\"container mx-auto p-4 md:p-8\">\n        <header className=\"text-center mb-10\">\n          <div className=\"inline-block bg-red-500 text-white rounded-full px-5 py-2 mb-4 text-sm font-bold\">\n            PILLAR 1: THE BODY\n          </div>\n          <h1 className=\"text-4xl md:text-5xl font-black text-reedsoft-primary tracking-tight\">\n            Forging a Capable Machine\n          </h1>\n          <p className=\"text-lg text-gray-600 mt-2\">\n            Building functional strength and endurance for real-world work.\n          </p>\n        </header>\n\n        {/* Weekly Workout Split */}\n        <section id=\"workout-split\" className=\"mb-12 bg-white p-6 rounded-lg shadow-lg\">\n          <h2 className=\"text-2xl font-bold text-center mb-6 text-reedsoft-primary\">The Weekly Workout Split</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 text-center\">\n            <div className=\"bg-gray-50 p-6 rounded-lg border-l-4 border-red-500\">\n              <h3 className=\"font-bold text-lg\">Strength Focus</h3>\n              <p className=\"font-semibold text-red-500\">Mon / Wed / Fri</p>\n              <p className=\"mt-2 text-sm\">\n                Full-body compound exercises to build a powerful foundation. The core of your physical transformation.\n              </p>\n            </div>\n            <div className=\"bg-gray-50 p-6 rounded-lg border-l-4 border-blue-500\">\n              <h3 className=\"font-bold text-lg\">Cardio & Core</h3>\n              <p className=\"font-semibold text-blue-500\">Tue / Thu</p>\n              <p className=\"mt-2 text-sm\">\n                High-Intensity Interval Training and core work to build work capacity and a resilient midsection.\n              </p>\n            </div>\n          </div>\n        </section>\n\n        {/* Key Lifts Chart */}\n        <section id=\"key-lifts\" className=\"mb-12 bg-white p-6 rounded-lg shadow-lg\">\n          <h2 className=\"text-2xl font-bold text-center mb-6 text-reedsoft-primary\">The Five Foundational Lifts</h2>\n          <p className=\"text-sm text-gray-600 mb-6 text-center max-w-2xl mx-auto\">\n            Mastering these five compound movements is the fastest path to functional strength.\n            They work multiple muscle groups simultaneously, mimicking real-world physical tasks.\n          </p>\n          <div className=\"relative w-full max-w-lg mx-auto h-96\">\n            {loading ? (\n              <div className=\"flex items-center justify-center h-full\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-reedsoft-primary\"></div>\n              </div>\n            ) : keyLiftsData && keyLiftsOptions ? (\n              <Bar data={keyLiftsData} options={keyLiftsOptions as any} />\n            ) : (\n              <div className=\"flex items-center justify-center h-full text-gray-500\">\n                Chart loading...\n              </div>\n            )}\n          </div>\n        </section>\n\n        {/* Nutrition Pyramid */}\n        <section id=\"nutrition\" className=\"mb-12 bg-white p-6 rounded-lg shadow-lg\">\n          <h2 className=\"text-2xl font-bold text-center mb-6 text-reedsoft-primary\">The Fuel Pyramid</h2>\n          <p className=\"text-sm text-gray-600 mb-6 text-center\">\n            Your body is a machine; give it the right fuel. Follow this simple hierarchy for optimal performance and recovery. No complex diet needed.\n          </p>\n          <div className=\"relative max-w-sm mx-auto text-center font-bold text-white\">\n            <div className=\"w-full h-24 bg-red-500 flex items-center justify-center rounded-t-lg\" style={{clipPath: 'polygon(15% 0, 85% 0, 100% 100%, 0% 100%)'}}>\n              PROTEIN (Every Meal)\n            </div>\n            <div className=\"w-full h-20 bg-yellow-500 flex items-center justify-center\" style={{clipPath: 'polygon(0 0, 100% 0, 85% 100%, 15% 100%)'}}>\n              COMPLEX CARBS\n            </div>\n            <div className=\"w-full h-16 bg-green-500 flex items-center justify-center\" style={{clipPath: 'polygon(15% 0, 85% 0, 70% 100%, 30% 100%)'}}>\n              VEGETABLES\n            </div>\n            <div className=\"w-full h-12 bg-blue-500 flex items-center justify-center rounded-b-lg\" style={{clipPath: 'polygon(30% 0, 70% 0, 55% 100%, 45% 100%)'}}>\n              3L+ WATER DAILY\n            </div>\n          </div>\n        </section>\n\n        {/* Navigation */}\n        <div className=\"flex justify-between items-center\">\n          <Link\n            to=\"/japan\"\n            className=\"bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors\"\n          >\n            ← Back to Dashboard\n          </Link>\n          <Link\n            to=\"/japan/pillar-2\"\n            className=\"bg-reedsoft-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors\"\n          >\n            Next: Japanese Language →\n          </Link>\n        </div>\n\n        <footer className=\"text-center mt-10 pt-6 border-t border-gray-300\">\n          <p className=\"text-gray-700 font-semibold\">Your body is your primary tool. Keep it sharp.</p>\n        </footer>\n      </div>\n    </div>\n  );\n};\n\nexport default Pillar1;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,GAAG,KAAQ,iBAAiB,CACrC,OACEC,KAAK,GAAI,CAAAC,OAAO,CAChBC,aAAa,CACbC,WAAW,CACXC,UAAU,CACVC,KAAK,CACLC,OAAO,CACPC,MAAM,KACD,UAAU,CACjB,OAASC,YAAY,KAAQ,uBAAuB,CAEpD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACAX,OAAO,CAACY,QAAQ,CACdX,aAAa,CACbC,WAAW,CACXC,UAAU,CACVC,KAAK,CACLC,OAAO,CACPC,MACF,CAAC,CAED,KAAM,CAAAO,OAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAEC,IAAI,CAAEC,YAAY,CAAEC,OAAO,CAAEC,eAAe,CAAEC,OAAQ,CAAC,CAAGX,YAAY,CAAC,UAAU,CAAC,CAE1F,mBACEE,IAAA,QAAKU,SAAS,CAAC,wCAAwC,CAAAC,QAAA,cACrDT,KAAA,QAAKQ,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3CT,KAAA,WAAQQ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACnCX,IAAA,QAAKU,SAAS,CAAC,kFAAkF,CAAAC,QAAA,CAAC,oBAElG,CAAK,CAAC,cACNX,IAAA,OAAIU,SAAS,CAAC,sEAAsE,CAAAC,QAAA,CAAC,2BAErF,CAAI,CAAC,cACLX,IAAA,MAAGU,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,iEAE1C,CAAG,CAAC,EACE,CAAC,cAGTT,KAAA,YAASU,EAAE,CAAC,eAAe,CAACF,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eAC7EX,IAAA,OAAIU,SAAS,CAAC,2DAA2D,CAAAC,QAAA,CAAC,0BAAwB,CAAI,CAAC,cACvGT,KAAA,QAAKQ,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChET,KAAA,QAAKQ,SAAS,CAAC,qDAAqD,CAAAC,QAAA,eAClEX,IAAA,OAAIU,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cACrDX,IAAA,MAAGU,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,iBAAe,CAAG,CAAC,cAC7DX,IAAA,MAAGU,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,wGAE5B,CAAG,CAAC,EACD,CAAC,cACNT,KAAA,QAAKQ,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEX,IAAA,OAAIU,SAAS,CAAC,mBAAmB,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,cACpDX,IAAA,MAAGU,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,WAAS,CAAG,CAAC,cACxDX,IAAA,MAAGU,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,mGAE5B,CAAG,CAAC,EACD,CAAC,EACH,CAAC,EACC,CAAC,cAGVT,KAAA,YAASU,EAAE,CAAC,WAAW,CAACF,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eACzEX,IAAA,OAAIU,SAAS,CAAC,2DAA2D,CAAAC,QAAA,CAAC,6BAA2B,CAAI,CAAC,cAC1GX,IAAA,MAAGU,SAAS,CAAC,0DAA0D,CAAAC,QAAA,CAAC,2KAGxE,CAAG,CAAC,cACJX,IAAA,QAAKU,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CACnDF,OAAO,cACNT,IAAA,QAAKU,SAAS,CAAC,yCAAyC,CAAAC,QAAA,cACtDX,IAAA,QAAKU,SAAS,CAAC,wEAAwE,CAAM,CAAC,CAC3F,CAAC,CACJJ,YAAY,EAAIE,eAAe,cACjCR,IAAA,CAACX,GAAG,EAACgB,IAAI,CAAEC,YAAa,CAACC,OAAO,CAAEC,eAAuB,CAAE,CAAC,cAE5DR,IAAA,QAAKU,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,kBAEvE,CAAK,CACN,CACE,CAAC,EACC,CAAC,cAGVT,KAAA,YAASU,EAAE,CAAC,WAAW,CAACF,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eACzEX,IAAA,OAAIU,SAAS,CAAC,2DAA2D,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cAC/FX,IAAA,MAAGU,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,4IAEtD,CAAG,CAAC,cACJT,KAAA,QAAKQ,SAAS,CAAC,4DAA4D,CAAAC,QAAA,eACzEX,IAAA,QAAKU,SAAS,CAAC,sEAAsE,CAACG,KAAK,CAAE,CAACC,QAAQ,CAAE,2CAA2C,CAAE,CAAAH,QAAA,CAAC,sBAEtJ,CAAK,CAAC,cACNX,IAAA,QAAKU,SAAS,CAAC,4DAA4D,CAACG,KAAK,CAAE,CAACC,QAAQ,CAAE,0CAA0C,CAAE,CAAAH,QAAA,CAAC,eAE3I,CAAK,CAAC,cACNX,IAAA,QAAKU,SAAS,CAAC,2DAA2D,CAACG,KAAK,CAAE,CAACC,QAAQ,CAAE,2CAA2C,CAAE,CAAAH,QAAA,CAAC,YAE3I,CAAK,CAAC,cACNX,IAAA,QAAKU,SAAS,CAAC,uEAAuE,CAACG,KAAK,CAAE,CAACC,QAAQ,CAAE,2CAA2C,CAAE,CAAAH,QAAA,CAAC,iBAEvJ,CAAK,CAAC,EACH,CAAC,EACC,CAAC,cAGVT,KAAA,QAAKQ,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDX,IAAA,CAACZ,IAAI,EACH2B,EAAE,CAAC,QAAQ,CACXL,SAAS,CAAC,kGAAkG,CAAAC,QAAA,CAC7G,0BAED,CAAM,CAAC,cACPX,IAAA,CAACZ,IAAI,EACH2B,EAAE,CAAC,iBAAiB,CACpBL,SAAS,CAAC,iHAAiH,CAAAC,QAAA,CAC5H,gCAED,CAAM,CAAC,EACJ,CAAC,cAENX,IAAA,WAAQU,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cACjEX,IAAA,MAAGU,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,gDAA8C,CAAG,CAAC,CACvF,CAAC,EACN,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAP,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}