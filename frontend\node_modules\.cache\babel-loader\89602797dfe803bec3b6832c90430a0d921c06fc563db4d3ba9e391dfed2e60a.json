{"ast": null, "code": "// React import not needed with new JSX transform\nimport{BrowserRouter as Router,Routes,Route}from'react-router-dom';import'./App.css';// Pages\nimport ReedsoftLanding from'./pages/ReedsoftLanding';import JapanPlan from'./pages/JapanPlan';import Pillar1 from'./pages/Pillar1';import Pillar2 from'./pages/Pillar2';import Pillar3 from'./pages/Pillar3';import Pillar4 from'./pages/Pillar4';// Components\nimport Navigation from'./components/Navigation';import ErrorBoundary from'./components/ErrorBoundary';/**\n * Main App component that sets up routing and global layout\n * Features:\n * - Error boundary for production resilience\n * - Skip navigation link for accessibility\n * - React Router for client-side navigation\n * - Consistent navigation across all pages\n */import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){return/*#__PURE__*/_jsx(ErrorBoundary,{children:/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"App min-h-screen bg-gray-100\",children:[/*#__PURE__*/_jsx(\"a\",{href:\"#main-content\",className:\"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-reedsoft-primary text-white px-4 py-2 rounded-md z-50 focus:outline-none focus:ring-2 focus:ring-white\",children:\"Skip to main content\"}),/*#__PURE__*/_jsx(Navigation,{}),/*#__PURE__*/_jsx(\"main\",{id:\"main-content\",className:\"container mx-auto\",role:\"main\",children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(ReedsoftLanding,{})}),/*#__PURE__*/_jsx(Route,{path:\"/japan\",element:/*#__PURE__*/_jsx(JapanPlan,{})}),/*#__PURE__*/_jsx(Route,{path:\"/japan/pillar-1\",element:/*#__PURE__*/_jsx(Pillar1,{})}),/*#__PURE__*/_jsx(Route,{path:\"/japan/pillar-2\",element:/*#__PURE__*/_jsx(Pillar2,{})}),/*#__PURE__*/_jsx(Route,{path:\"/japan/pillar-3\",element:/*#__PURE__*/_jsx(Pillar3,{})}),/*#__PURE__*/_jsx(Route,{path:\"/japan/pillar-4\",element:/*#__PURE__*/_jsx(Pillar4,{})})]})})]})})});}export default App;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "ReedsoftLanding", "JapanPlan", "Pillar1", "Pillar2", "Pillar3", "Pillar4", "Navigation", "Error<PERSON>ou<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "App", "children", "className", "href", "id", "role", "path", "element"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/App.tsx"], "sourcesContent": ["// React import not needed with new JSX transform\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport './App.css';\n\n// Pages\nimport ReedsoftLanding from './pages/ReedsoftLanding';\nimport JapanPlan from './pages/JapanPlan';\nimport Pillar1 from './pages/Pillar1';\nimport Pillar2 from './pages/Pillar2';\nimport Pillar3 from './pages/Pillar3';\nimport Pillar4 from './pages/Pillar4';\n\n// Components\nimport Navigation from './components/Navigation';\nimport ErrorBoundary from './components/ErrorBoundary';\n\n/**\n * Main App component that sets up routing and global layout\n * Features:\n * - Error boundary for production resilience\n * - Skip navigation link for accessibility\n * - React Router for client-side navigation\n * - Consistent navigation across all pages\n */\nfunction App() {\n  return (\n    <ErrorBoundary>\n      <Router>\n        <div className=\"App min-h-screen bg-gray-100\">\n          {/* Skip Navigation Link */}\n          <a\n            href=\"#main-content\"\n            className=\"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-reedsoft-primary text-white px-4 py-2 rounded-md z-50 focus:outline-none focus:ring-2 focus:ring-white\"\n          >\n            Skip to main content\n          </a>\n\n          <Navigation />\n          <main id=\"main-content\" className=\"container mx-auto\" role=\"main\">\n            <Routes>\n              <Route path=\"/\" element={<ReedsoftLanding />} />\n              <Route path=\"/japan\" element={<JapanPlan />} />\n              <Route path=\"/japan/pillar-1\" element={<Pillar1 />} />\n              <Route path=\"/japan/pillar-2\" element={<Pillar2 />} />\n              <Route path=\"/japan/pillar-3\" element={<Pillar3 />} />\n              <Route path=\"/japan/pillar-4\" element={<Pillar4 />} />\n            </Routes>\n          </main>\n        </div>\n      </Router>\n    </ErrorBoundary>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA;AACA,OAASA,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,KAAQ,kBAAkB,CACzE,MAAO,WAAW,CAElB;AACA,MAAO,CAAAC,eAAe,KAAM,yBAAyB,CACrD,MAAO,CAAAC,SAAS,KAAM,mBAAmB,CACzC,MAAO,CAAAC,OAAO,KAAM,iBAAiB,CACrC,MAAO,CAAAC,OAAO,KAAM,iBAAiB,CACrC,MAAO,CAAAC,OAAO,KAAM,iBAAiB,CACrC,MAAO,CAAAC,OAAO,KAAM,iBAAiB,CAErC;AACA,MAAO,CAAAC,UAAU,KAAM,yBAAyB,CAChD,MAAO,CAAAC,aAAa,KAAM,4BAA4B,CAEtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAPA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQA,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,mBACEH,IAAA,CAACF,aAAa,EAAAM,QAAA,cACZJ,IAAA,CAACZ,MAAM,EAAAgB,QAAA,cACLF,KAAA,QAAKG,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAE3CJ,IAAA,MACEM,IAAI,CAAC,eAAe,CACpBD,SAAS,CAAC,6KAA6K,CAAAD,QAAA,CACxL,sBAED,CAAG,CAAC,cAEJJ,IAAA,CAACH,UAAU,GAAE,CAAC,cACdG,IAAA,SAAMO,EAAE,CAAC,cAAc,CAACF,SAAS,CAAC,mBAAmB,CAACG,IAAI,CAAC,MAAM,CAAAJ,QAAA,cAC/DF,KAAA,CAACb,MAAM,EAAAe,QAAA,eACLJ,IAAA,CAACV,KAAK,EAACmB,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEV,IAAA,CAACT,eAAe,GAAE,CAAE,CAAE,CAAC,cAChDS,IAAA,CAACV,KAAK,EAACmB,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEV,IAAA,CAACR,SAAS,GAAE,CAAE,CAAE,CAAC,cAC/CQ,IAAA,CAACV,KAAK,EAACmB,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEV,IAAA,CAACP,OAAO,GAAE,CAAE,CAAE,CAAC,cACtDO,IAAA,CAACV,KAAK,EAACmB,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEV,IAAA,CAACN,OAAO,GAAE,CAAE,CAAE,CAAC,cACtDM,IAAA,CAACV,KAAK,EAACmB,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEV,IAAA,CAACL,OAAO,GAAE,CAAE,CAAE,CAAC,cACtDK,IAAA,CAACV,KAAK,EAACmB,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEV,IAAA,CAACJ,OAAO,GAAE,CAAE,CAAE,CAAC,EAChD,CAAC,CACL,CAAC,EACJ,CAAC,CACA,CAAC,CACI,CAAC,CAEpB,CAEA,cAAe,CAAAO,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}