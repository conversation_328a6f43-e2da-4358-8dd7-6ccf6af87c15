import { ChartData, ChartOptions, ColorPalette } from '../types';

// Reedsoft color palette
export const COLORS: ColorPalette = {
  primary: '#003B46',
  secondary: '#07575B',
  accent: '#66A5AD',
  light: '#C4DFE6',
  // Additional colors for charts
  red: '#FF5733',
  orange: '#F0AD4E',
  blue: '#4A90E2',
  green: '#50E3C2',
  yellow: '#F5A623',
  purple: '#9013FE',
  teal: '#00BCD4',
  pink: '#E91E63'
};

/**
 * Wraps long labels for better chart display
 * @param label - The label to wrap
 * @param maxLength - Maximum length per line
 * @returns Array of wrapped lines or original label
 */
export const wrapLabel = (label: string, maxLength: number = 16): string[] => {
  if (label.length <= maxLength) return [label];
  
  const words = label.split(' ');
  const lines: string[] = [];
  let currentLine = '';
  
  for (const word of words) {
    if ((currentLine + ' ' + word).trim().length > maxLength) {
      if (currentLine) {
        lines.push(currentLine);
        currentLine = word;
      } else {
        lines.push(word);
      }
    } else {
      currentLine = (currentLine + ' ' + word).trim();
    }
  }
  
  if (currentLine) {
    lines.push(currentLine);
  }
  
  return lines;
};

/**
 * Tooltip title callback for wrapped labels
 * @param tooltipItems - Chart.js tooltip items
 * @returns Formatted title string
 */
export const tooltipTitleCallback = (tooltipItems: any[]): string => {
  const item = tooltipItems[0];
  let label = item.chart.data.labels[item.dataIndex];
  
  if (Array.isArray(label)) {
    return label.join(' ');
  }
  
  return label;
};

/**
 * Creates default chart options with Reedsoft styling
 * @param type - Chart type
 * @param title - Chart title
 * @returns Chart options object
 */
export const createChartOptions = (
  type: 'doughnut' | 'bar' | 'radar' | 'pie' | 'line',
  title?: string
): ChartOptions => {
  const baseOptions: ChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          font: { size: 12 }
        }
      },
      tooltip: {
        callbacks: {
          title: tooltipTitleCallback
        }
      }
    }
  };

  if (title) {
    baseOptions.plugins!.title = {
      display: true,
      text: title,
      font: { size: 16, weight: 'bold' },
      padding: { top: 10, bottom: 20 }
    };
  }

  // Type-specific configurations
  switch (type) {
    case 'bar':
      return {
        ...baseOptions,
        scales: {
          x: { display: false },
          y: { 
            ticks: { 
              font: { size: 14, weight: 'bold' } 
            } 
          }
        },
        plugins: {
          ...baseOptions.plugins,
          legend: { display: false }
        },
        indexAxis: 'y'
      };

    case 'radar':
      return {
        ...baseOptions,
        scales: {
          r: {
            angleLines: { color: '#ddd' },
            grid: { color: '#ddd' },
            pointLabels: { 
              font: { size: 12, weight: 'bold' } 
            },
            ticks: { display: false }
          }
        },
        plugins: {
          ...baseOptions.plugins,
          legend: { display: false }
        }
      };

    default:
      return baseOptions;
  }
};

/**
 * Creates time allocation chart data
 * @returns Chart data for time allocation doughnut chart
 */
export const createTimeAllocationData = (): ChartData => ({
  labels: [
    'Pillar 2: Japanese Study (Voice)',
    'Pillar 1: Physical Fitness (Body)',
    'Pillar 3: Practical Skills (Hands)',
    'Pillar 4: Cognitive Fitness (Mind)'
  ],
  datasets: [{
    label: 'Weekly Hours',
    data: [20, 7.5, 10, 7.5],
    backgroundColor: [
      COLORS.primary,
      COLORS.secondary,
      COLORS.accent,
      COLORS.light
    ],
    borderColor: '#FFFFFF',
    borderWidth: 3
  }]
});

/**
 * Creates key lifts chart data
 * @returns Chart data for key lifts bar chart
 */
export const createKeyLiftsData = (): ChartData => ({
  labels: ['Squat', 'Deadlift', 'Bench Press', 'Overhead Press', 'Barbell Row'],
  datasets: [{
    label: 'Functional Strength Rating',
    data: [95, 100, 85, 80, 90],
    backgroundColor: [COLORS.red || '#FF5733', '#C70039', '#900C3F', '#581845', COLORS.blue || '#4A90E2'],
    borderColor: '#fff',
    borderWidth: 2,
    borderRadius: 5
  }]
});

/**
 * Creates Anki chart data
 * @returns Chart data for Anki doughnut chart
 */
export const createAnkiData = (): ChartData => ({
  labels: ['Daily Reviews', 'Learn New Words (20/day)', 'Long-Term Memory'],
  datasets: [{
    data: [50, 25, 25],
    backgroundColor: [COLORS.primary, COLORS.secondary, COLORS.accent],
    borderColor: '#fff',
    borderWidth: 4
  }]
});

/**
 * Creates knowledge areas radar chart data
 * @returns Chart data for knowledge areas radar chart
 */
export const createKnowledgeData = (): ChartData => ({
  labels: ['Hand Tools', 'Power Tools', 'Site Safety', 'Materials ID', 'Essential Skills'],
  datasets: [{
    label: 'Study Focus',
    data: [8, 7, 10, 6, 9],
    backgroundColor: 'rgba(217, 83, 79, 0.2)',
    borderColor: '#D9534F',
    pointBackgroundColor: '#D9534F',
    pointBorderColor: '#fff',
    pointHoverBackgroundColor: '#fff',
    pointHoverBorderColor: '#D9534F',
    borderWidth: 2
  }]
});

/**
 * Creates mind workout pie chart data
 * @returns Chart data for mind workout pie chart
 */
export const createMindWorkoutData = (): ChartData => ({
  labels: ['Active Reading (45 min)', 'Problem Solving (30 min)', 'Meditation (15 min)'],
  datasets: [{
    data: [50, 33, 17],
    backgroundColor: [COLORS.green || '#50E3C2', COLORS.yellow || '#F5A623', COLORS.blue || '#4A90E2'],
    borderColor: '#fff',
    borderWidth: 4
  }]
});
