{"ast": null, "code": "var _jsxFileName = \"C:\\\\Developer\\\\Web Development\\\\reedsoft\\\\frontend\\\\src\\\\pages\\\\ReedsoftLanding.tsx\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ReedsoftLanding = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-reedsoft-light to-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"pt-20 pb-16 px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-5xl md:text-7xl font-black text-reedsoft-primary mb-6 tracking-tight\",\n          children: \"REEDSOFT\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl md:text-2xl text-reedsoft-secondary mb-8 max-w-3xl mx-auto\",\n          children: \"Building innovative solutions for personal and professional development\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 mb-12 max-w-2xl mx-auto\",\n          children: \"We create comprehensive digital experiences that transform ambitious goals into structured, achievable plans.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/japan\",\n            className: \"bg-reedsoft-primary text-white px-8 py-4 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors shadow-lg\",\n            children: \"Explore Japan Project\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-white text-reedsoft-primary px-8 py-4 rounded-lg font-semibold border-2 border-reedsoft-primary hover:bg-reedsoft-primary hover:text-white transition-colors\",\n            children: \"Learn More\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 px-4 bg-white\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl md:text-4xl font-bold text-center text-reedsoft-primary mb-12\",\n          children: \"Featured Project\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-reedsoft-primary to-reedsoft-secondary rounded-2xl p-8 md:p-12 text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid md:grid-cols-2 gap-8 items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-3xl md:text-4xl font-bold mb-4\",\n                children: \"Japan Work Preparation Plan\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg mb-6 opacity-90\",\n                children: \"A comprehensive 4-month training program featuring four core pillars: Physical Fitness, Japanese Language, Practical Skills, and Cognitive Fitness.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 47,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                className: \"space-y-2 mb-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"w-2 h-2 bg-reedsoft-light rounded-full mr-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 52,\n                    columnNumber: 21\n                  }, this), \"Interactive progress tracking\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 51,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"w-2 h-2 bg-reedsoft-light rounded-full mr-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 56,\n                    columnNumber: 21\n                  }, this), \"Data visualizations with Chart.js\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 55,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"w-2 h-2 bg-reedsoft-light rounded-full mr-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 60,\n                    columnNumber: 21\n                  }, this), \"Structured daily schedules\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 59,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  className: \"flex items-center\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"w-2 h-2 bg-reedsoft-light rounded-full mr-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 64,\n                    columnNumber: 21\n                  }, this), \"Responsive design for all devices\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 63,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/japan\",\n                className: \"inline-block bg-white text-reedsoft-primary px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-light transition-colors\",\n                children: \"View Project \\u2192\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 68,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white bg-opacity-10 rounded-lg p-4 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold mb-1\",\n                  children: \"4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 78,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm opacity-80\",\n                  children: \"Core Pillars\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 79,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white bg-opacity-10 rounded-lg p-4 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold mb-1\",\n                  children: \"16\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm opacity-80\",\n                  children: \"Weeks Program\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white bg-opacity-10 rounded-lg p-4 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold mb-1\",\n                  children: \"45+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 86,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm opacity-80\",\n                  children: \"Hours/Week\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 87,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"bg-white bg-opacity-10 rounded-lg p-4 text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-2xl font-bold mb-1\",\n                  children: \"100%\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 90,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm opacity-80\",\n                  children: \"Commitment\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 91,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-16 px-4 bg-gray-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl md:text-4xl font-bold text-center text-reedsoft-primary mb-12\",\n          children: \"Built with Modern Technology\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid md:grid-cols-3 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-reedsoft-primary rounded-full flex items-center justify-center mx-auto mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-bold text-xl\",\n                children: \"R\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold mb-2\",\n              children: \"React & TypeScript\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 111,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Modern frontend with type safety and component-based architecture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-reedsoft-secondary rounded-full flex items-center justify-center mx-auto mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-bold text-xl\",\n                children: \"T\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold mb-2\",\n              children: \"Tailwind CSS\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Utility-first CSS framework for rapid UI development\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-16 bg-reedsoft-accent rounded-full flex items-center justify-center mx-auto mb-4\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-white font-bold text-xl\",\n                children: \"C\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 125,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-semibold mb-2\",\n              children: \"Chart.js\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Interactive data visualizations and progress tracking\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"bg-reedsoft-primary text-white py-12 px-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-6xl mx-auto text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold mb-4\",\n          children: \"Ready to Transform Your Goals?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 137,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-reedsoft-light mb-8 max-w-2xl mx-auto\",\n          children: \"Join the journey of structured personal development with data-driven insights and comprehensive planning.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/japan\",\n          className: \"inline-block bg-white text-reedsoft-primary px-8 py-4 rounded-lg font-semibold hover:bg-reedsoft-light transition-colors\",\n          children: \"Start Your Journey\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-12 pt-8 border-t border-reedsoft-secondary\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-reedsoft-light\",\n            children: \"\\xA9 2025 Reedsoft. Building innovative solutions for personal and professional development.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 135,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = ReedsoftLanding;\nexport default ReedsoftLanding;\nvar _c;\n$RefreshReg$(_c, \"ReedsoftLanding\");", "map": {"version": 3, "names": ["React", "Link", "jsxDEV", "_jsxDEV", "ReedsoftLanding", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "_c", "$RefreshReg$"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/pages/ReedsoftLanding.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst ReedsoftLanding: React.FC = () => {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-reedsoft-light to-white\">\n      {/* Hero Section */}\n      <section className=\"pt-20 pb-16 px-4\">\n        <div className=\"max-w-6xl mx-auto text-center\">\n          <h1 className=\"text-5xl md:text-7xl font-black text-reedsoft-primary mb-6 tracking-tight\">\n            REEDSOFT\n          </h1>\n          <p className=\"text-xl md:text-2xl text-reedsoft-secondary mb-8 max-w-3xl mx-auto\">\n            Building innovative solutions for personal and professional development\n          </p>\n          <p className=\"text-lg text-gray-600 mb-12 max-w-2xl mx-auto\">\n            We create comprehensive digital experiences that transform ambitious goals into structured, achievable plans.\n          </p>\n          \n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              to=\"/japan\"\n              className=\"bg-reedsoft-primary text-white px-8 py-4 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors shadow-lg\"\n            >\n              Explore Japan Project\n            </Link>\n            <button className=\"bg-white text-reedsoft-primary px-8 py-4 rounded-lg font-semibold border-2 border-reedsoft-primary hover:bg-reedsoft-primary hover:text-white transition-colors\">\n              Learn More\n            </button>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Project Section */}\n      <section className=\"py-16 px-4 bg-white\">\n        <div className=\"max-w-6xl mx-auto\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-center text-reedsoft-primary mb-12\">\n            Featured Project\n          </h2>\n          \n          <div className=\"bg-gradient-to-r from-reedsoft-primary to-reedsoft-secondary rounded-2xl p-8 md:p-12 text-white\">\n            <div className=\"grid md:grid-cols-2 gap-8 items-center\">\n              <div>\n                <h3 className=\"text-3xl md:text-4xl font-bold mb-4\">\n                  Japan Work Preparation Plan\n                </h3>\n                <p className=\"text-lg mb-6 opacity-90\">\n                  A comprehensive 4-month training program featuring four core pillars: Physical Fitness, Japanese Language, Practical Skills, and Cognitive Fitness.\n                </p>\n                <ul className=\"space-y-2 mb-8\">\n                  <li className=\"flex items-center\">\n                    <span className=\"w-2 h-2 bg-reedsoft-light rounded-full mr-3\"></span>\n                    Interactive progress tracking\n                  </li>\n                  <li className=\"flex items-center\">\n                    <span className=\"w-2 h-2 bg-reedsoft-light rounded-full mr-3\"></span>\n                    Data visualizations with Chart.js\n                  </li>\n                  <li className=\"flex items-center\">\n                    <span className=\"w-2 h-2 bg-reedsoft-light rounded-full mr-3\"></span>\n                    Structured daily schedules\n                  </li>\n                  <li className=\"flex items-center\">\n                    <span className=\"w-2 h-2 bg-reedsoft-light rounded-full mr-3\"></span>\n                    Responsive design for all devices\n                  </li>\n                </ul>\n                <Link\n                  to=\"/japan\"\n                  className=\"inline-block bg-white text-reedsoft-primary px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-light transition-colors\"\n                >\n                  View Project →\n                </Link>\n              </div>\n              \n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"bg-white bg-opacity-10 rounded-lg p-4 text-center\">\n                  <div className=\"text-2xl font-bold mb-1\">4</div>\n                  <div className=\"text-sm opacity-80\">Core Pillars</div>\n                </div>\n                <div className=\"bg-white bg-opacity-10 rounded-lg p-4 text-center\">\n                  <div className=\"text-2xl font-bold mb-1\">16</div>\n                  <div className=\"text-sm opacity-80\">Weeks Program</div>\n                </div>\n                <div className=\"bg-white bg-opacity-10 rounded-lg p-4 text-center\">\n                  <div className=\"text-2xl font-bold mb-1\">45+</div>\n                  <div className=\"text-sm opacity-80\">Hours/Week</div>\n                </div>\n                <div className=\"bg-white bg-opacity-10 rounded-lg p-4 text-center\">\n                  <div className=\"text-2xl font-bold mb-1\">100%</div>\n                  <div className=\"text-sm opacity-80\">Commitment</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Technology Stack Section */}\n      <section className=\"py-16 px-4 bg-gray-50\">\n        <div className=\"max-w-6xl mx-auto\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-center text-reedsoft-primary mb-12\">\n            Built with Modern Technology\n          </h2>\n          \n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-reedsoft-primary rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-white font-bold text-xl\">R</span>\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">React & TypeScript</h3>\n              <p className=\"text-gray-600\">Modern frontend with type safety and component-based architecture</p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-reedsoft-secondary rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-white font-bold text-xl\">T</span>\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">Tailwind CSS</h3>\n              <p className=\"text-gray-600\">Utility-first CSS framework for rapid UI development</p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-reedsoft-accent rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-white font-bold text-xl\">C</span>\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">Chart.js</h3>\n              <p className=\"text-gray-600\">Interactive data visualizations and progress tracking</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-reedsoft-primary text-white py-12 px-4\">\n        <div className=\"max-w-6xl mx-auto text-center\">\n          <h3 className=\"text-2xl font-bold mb-4\">Ready to Transform Your Goals?</h3>\n          <p className=\"text-reedsoft-light mb-8 max-w-2xl mx-auto\">\n            Join the journey of structured personal development with data-driven insights and comprehensive planning.\n          </p>\n          <Link\n            to=\"/japan\"\n            className=\"inline-block bg-white text-reedsoft-primary px-8 py-4 rounded-lg font-semibold hover:bg-reedsoft-light transition-colors\"\n          >\n            Start Your Journey\n          </Link>\n          \n          <div className=\"mt-12 pt-8 border-t border-reedsoft-secondary\">\n            <p className=\"text-reedsoft-light\">\n              © 2025 Reedsoft. Building innovative solutions for personal and professional development.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default ReedsoftLanding;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EACtC,oBACED,OAAA;IAAKE,SAAS,EAAC,6DAA6D;IAAAC,QAAA,gBAE1EH,OAAA;MAASE,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eACnCH,OAAA;QAAKE,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5CH,OAAA;UAAIE,SAAS,EAAC,2EAA2E;UAAAC,QAAA,EAAC;QAE1F;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLP,OAAA;UAAGE,SAAS,EAAC,oEAAoE;UAAAC,QAAA,EAAC;QAElF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJP,OAAA;UAAGE,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAAC;QAE7D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJP,OAAA;UAAKE,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7DH,OAAA,CAACF,IAAI;YACHU,EAAE,EAAC,QAAQ;YACXN,SAAS,EAAC,2HAA2H;YAAAC,QAAA,EACtI;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACPP,OAAA;YAAQE,SAAS,EAAC,iKAAiK;YAAAC,QAAA,EAAC;UAEpL;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVP,OAAA;MAASE,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eACtCH,OAAA;QAAKE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCH,OAAA;UAAIE,SAAS,EAAC,wEAAwE;UAAAC,QAAA,EAAC;QAEvF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELP,OAAA;UAAKE,SAAS,EAAC,iGAAiG;UAAAC,QAAA,eAC9GH,OAAA;YAAKE,SAAS,EAAC,wCAAwC;YAAAC,QAAA,gBACrDH,OAAA;cAAAG,QAAA,gBACEH,OAAA;gBAAIE,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAEpD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLP,OAAA;gBAAGE,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAEvC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJP,OAAA;gBAAIE,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC5BH,OAAA;kBAAIE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC/BH,OAAA;oBAAME,SAAS,EAAC;kBAA6C;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,iCAEvE;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLP,OAAA;kBAAIE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC/BH,OAAA;oBAAME,SAAS,EAAC;kBAA6C;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,qCAEvE;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLP,OAAA;kBAAIE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC/BH,OAAA;oBAAME,SAAS,EAAC;kBAA6C;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,8BAEvE;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLP,OAAA;kBAAIE,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAC/BH,OAAA;oBAAME,SAAS,EAAC;kBAA6C;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,qCAEvE;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACLP,OAAA,CAACF,IAAI;gBACHU,EAAE,EAAC,QAAQ;gBACXN,SAAS,EAAC,0HAA0H;gBAAAC,QAAA,EACrI;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENP,OAAA;cAAKE,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrCH,OAAA;gBAAKE,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,gBAChEH,OAAA;kBAAKE,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChDP,OAAA;kBAAKE,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACNP,OAAA;gBAAKE,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,gBAChEH,OAAA;kBAAKE,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjDP,OAAA;kBAAKE,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,eACNP,OAAA;gBAAKE,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,gBAChEH,OAAA;kBAAKE,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClDP,OAAA;kBAAKE,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACNP,OAAA;gBAAKE,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,gBAChEH,OAAA;kBAAKE,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnDP,OAAA;kBAAKE,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVP,OAAA;MAASE,SAAS,EAAC,uBAAuB;MAAAC,QAAA,eACxCH,OAAA;QAAKE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCH,OAAA;UAAIE,SAAS,EAAC,wEAAwE;UAAAC,QAAA,EAAC;QAEvF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELP,OAAA;UAAKE,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBACxCH,OAAA;YAAKE,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BH,OAAA;cAAKE,SAAS,EAAC,0FAA0F;cAAAC,QAAA,eACvGH,OAAA;gBAAME,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNP,OAAA;cAAIE,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClEP,OAAA;cAAGE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAiE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC,eAENP,OAAA;YAAKE,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BH,OAAA;cAAKE,SAAS,EAAC,4FAA4F;cAAAC,QAAA,eACzGH,OAAA;gBAAME,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNP,OAAA;cAAIE,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5DP,OAAA;cAAGE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAoD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC,eAENP,OAAA;YAAKE,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BH,OAAA;cAAKE,SAAS,EAAC,yFAAyF;cAAAC,QAAA,eACtGH,OAAA;gBAAME,SAAS,EAAC,8BAA8B;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNP,OAAA;cAAIE,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDP,OAAA;cAAGE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAqD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVP,OAAA;MAAQE,SAAS,EAAC,2CAA2C;MAAAC,QAAA,eAC3DH,OAAA;QAAKE,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5CH,OAAA;UAAIE,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3EP,OAAA;UAAGE,SAAS,EAAC,4CAA4C;UAAAC,QAAA,EAAC;QAE1D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJP,OAAA,CAACF,IAAI;UACHU,EAAE,EAAC,QAAQ;UACXN,SAAS,EAAC,0HAA0H;UAAAC,QAAA,EACrI;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAEPP,OAAA;UAAKE,SAAS,EAAC,+CAA+C;UAAAC,QAAA,eAC5DH,OAAA;YAAGE,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAEnC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACE,EAAA,GAzJIR,eAAyB;AA2J/B,eAAeA,eAAe;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}