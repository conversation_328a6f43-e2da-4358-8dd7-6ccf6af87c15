{"ast": null, "code": "var _jsxFileName = \"C:\\\\Developer\\\\Web Development\\\\reedsoft\\\\frontend\\\\src\\\\pages\\\\JapanPlan.tsx\",\n  _s = $RefreshSig$();\nimport React, { useMemo } from 'react';\nimport { Link } from 'react-router-dom';\nimport { Doughnut } from 'react-chartjs-2';\nimport { Chart as ChartJS, ArcElement, Tooltip, Legend, Title } from 'chart.js';\nimport { useChartData } from '../hooks/useChartData';\nimport { formatTimeInText } from '../utils/timeFormat';\n\n// Register Chart.js components\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nChartJS.register(ArcElement, Tooltip, Legend, Title);\nconst JapanPlan = () => {\n  _s();\n  const {\n    data: timeAllocationData,\n    options: timeAllocationOptions,\n    loading\n  } = useChartData('timeAllocation');\n\n  // Time blocks with 24-hour format that will be converted\n  const timeBlocks = useMemo(() => [{\n    time: \"06:00 - 07:30\",\n    title: \"💪 Pillar 1: Physical Fitness\",\n    what: \"Full-body strength training (3x/week) or Cardio & Core (2x/week).\",\n    why: \"To build the raw strength and endurance needed for physical jobs and to boost mental clarity for the day.\",\n    how: \"Follow a structured program like StrongLifts 5x5. For cardio, use HIIT or jogging. Use apps like Jefit to track lifts.\"\n  }, {\n    time: \"09:00 - 12:00\",\n    title: \"🗣️ Pillar 2: Japanese Core Study\",\n    what: \"Deep, focused study of Japanese grammar and sentence structure.\",\n    why: \"Grammar is the skeleton of the language. Vocabulary is useless without it. This is the hardest but most important part.\",\n    how: \"Use a textbook (Genki I & II). Complete exercises with a pen and paper. No phone, no distractions. One chapter every 4-5 days.\"\n  }, {\n    time: \"13:00 - 15:00\",\n    title: \"🛠️ Pillar 3: Practical Skills Study\",\n    what: \"Theoretical study of tools, safety protocols, materials, and basic trade knowledge.\",\n    why: \"To become a quick learner on a job site. Knowing the 'what' and 'why' makes the 'how' much easier to pick up.\",\n    how: \"Watch curated YouTube channels (e.g., Essential Craftsman). Take notes. Create a \\\"knowledge base\\\" in a notebook.\"\n  }, {\n    time: \"15:00 - 16:30\",\n    title: \"🧠 Pillar 4: Cognitive Fitness\",\n    what: \"Active reading of non-fiction, problem-solving puzzles, and meditation.\",\n    why: \"To sharpen your focus, improve memory retention, and train your brain to learn efficiently.\",\n    how: \"Read a chapter, then summarize it (Feynman Technique). Use apps like Headspace for meditation and Lumosity for logic puzzles.\"\n  }, {\n    time: \"17:00 - 18:00\",\n    title: \"🗣️ Pillar 2: Japanese Vocabulary Drill\",\n    what: \"Spaced Repetition System (SRS) for vocabulary.\",\n    why: \"To efficiently memorize thousands of words and burn them into your long-term memory.\",\n    how: \"Use the Anki app. Do your reviews and learn 20 new words every single day without fail.\"\n  }, {\n    time: \"19:30 - 21:00\",\n    title: \"🗣️ Pillar 2: Japanese Immersion\",\n    what: \"Actively listen to and speak Japanese.\",\n    why: \"To connect your grammar and vocabulary knowledge to the sound and rhythm of the real language.\",\n    how: \"Listen to beginner podcasts or anime audio. Starting Month 3, use HelloTalk to speak with native speakers.\"\n  }], []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gray-100 text-gray-800 min-h-screen\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto p-4 md:p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        className: \"text-center mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-4xl md:text-6xl font-black text-reedsoft-primary tracking-tight\",\n          children: \"PROJECT: JACK OF ALL TRADES\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg md:text-xl text-reedsoft-secondary mt-2\",\n          children: \"Your 16-Week Blueprint for Total Transformation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        id: \"daily-kata\",\n        className: \"mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-center mb-8 text-reedsoft-primary\",\n          children: \"Your Daily \\\"Kata\\\" (Routine)\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-6 rounded-lg shadow-lg mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-center text-gray-600 mb-6\",\n            children: [\"A consistent daily routine, or \", /*#__PURE__*/_jsxDEV(\"em\", {\n              children: \"kata\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 46\n            }, this), \" (\\u5F62)\\u2014a Japanese term for a choreographed pattern of movements\\u2014is essential for disciplined practice. This template integrates the Pomodoro Technique for maximum concentration and retention.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"overflow-x-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"table\", {\n              className: \"w-full text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n                children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"bg-gray-50\",\n                  children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"text-left p-3 font-bold text-reedsoft-primary\",\n                    children: \"Time\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 91,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"text-left p-3 font-bold text-reedsoft-primary\",\n                    children: \"Activity\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 92,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                    className: \"text-left p-3 font-bold text-reedsoft-primary\",\n                    children: \"Focus/Tools\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 93,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 90,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n                children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"border-b\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3 font-semibold text-reedsoft-secondary\",\n                    children: \"6:00 - 6:45 AM\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 98,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3\",\n                    children: \"Physical Fitness\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 99,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3\",\n                    children: \"Full-body circuit, HIIT, or functional strength workout.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 100,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"border-b bg-gray-50\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3 font-semibold text-reedsoft-secondary\",\n                    children: \"6:45 - 7:30 AM\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 103,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3\",\n                    children: \"Morning Prep\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 104,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3\",\n                    children: \"Shower, breakfast, review daily goals.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 105,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"border-b\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3 font-semibold text-reedsoft-secondary\",\n                    children: \"7:30 - 9:30 AM\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 108,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Japanese Study Block 1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 109,\n                      columnNumber: 41\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 109,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3\",\n                    children: \"4x Pomodoro sessions (25 min study / 5 min break). Focus on new grammar/kanji. Tools: Textbook, WaniKani.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 110,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"border-b bg-gray-50\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3 font-semibold text-reedsoft-secondary\",\n                    children: \"9:30 - 10:00 AM\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 113,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3\",\n                    children: \"Extended Break\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 114,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3\",\n                    children: \"Stretch, light snack, step away from study area.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 115,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 112,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"border-b\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3 font-semibold text-reedsoft-secondary\",\n                    children: \"10:00 AM - 12:00 PM\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 118,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Japanese Study Block 2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 119,\n                      columnNumber: 41\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 119,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3\",\n                    children: \"4x Pomodoro sessions. Focus on vocabulary reinforcement and reading practice. Tools: Anki, NHK News Easy.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 120,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"border-b bg-gray-50\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3 font-semibold text-reedsoft-secondary\",\n                    children: \"12:00 - 1:00 PM\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 123,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3\",\n                    children: \"Lunch Break\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 124,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3\",\n                    children: \"Nutritious meal, mental rest.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 125,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 122,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"border-b\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3 font-semibold text-reedsoft-secondary\",\n                    children: \"1:00 - 2:00 PM\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 128,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Skills Development Block\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 129,\n                      columnNumber: 41\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3\",\n                    children: \"2x Pomodoro sessions. Work on online trade course or cognitive training. Tools: Alison, CogniFit/Lumosity.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 130,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"border-b bg-gray-50\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3 font-semibold text-reedsoft-secondary\",\n                    children: \"2:00 - 3:00 PM\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 133,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Japanese Study Block 3\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 134,\n                      columnNumber: 41\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 134,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3\",\n                    children: \"2x Pomodoro sessions. Focus on listening or speaking practice. Tools: Podcasts, iTalki prep, shadowing.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"border-b\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3 font-semibold text-reedsoft-secondary\",\n                    children: \"3:00 - 3:30 PM\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 138,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3\",\n                    children: \"Afternoon Break\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3\",\n                    children: \"Walk, hydrate, short rest.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 140,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"border-b bg-gray-50\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3 font-semibold text-reedsoft-secondary\",\n                    children: \"3:30 - 5:00 PM\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 143,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                      children: \"Career Prep Block\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 144,\n                      columnNumber: 41\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 144,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3\",\n                    children: \"Work on resumes, research companies, prepare for interviews, or organize visa documents.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"border-b\",\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3 font-semibold text-reedsoft-secondary\",\n                    children: \"5:00 PM onward\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 148,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3\",\n                    children: \"Free Time & Review\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 149,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"p-3\",\n                    children: \"Dinner, hobbies, relaxation. A light 15-30 minute review of the day's Japanese lessons before bed.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 150,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 147,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        id: \"missions\",\n        className: \"mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-center mb-8 text-reedsoft-primary\",\n          children: \"Your 16-Week Transformation Journey\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-6 rounded-lg shadow-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-4 items-center text-center gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-reedsoft-secondary\",\n                children: \"Month 1 (Weeks 1-4)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-semibold text-gray-700\",\n                children: \"Building the Foundation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm mt-1 text-gray-600\",\n                children: \"\\u57FA\\u790E\\u56FA\\u3081 (Kiso-gatame)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm mt-2\",\n                children: \"Master Hiragana & Katakana. Start Genki I. Begin WaniKani. 3x weekly bodyweight circuits. Daily cognitive training.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:block text-2xl text-reedsoft-accent\",\n              children: \"\\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-reedsoft-secondary\",\n                children: \"Month 2 (Weeks 5-8)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-semibold text-gray-700\",\n                children: \"Accelerating Progress\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm mt-1 text-gray-600\",\n                children: \"\\u9032\\u6357\\u52A0\\u901F (Shinchoku-kasoku)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm mt-2\",\n                children: \"Complete N5 grammar. Learn first 100 N5 Kanji. Increase workout intensity. Start online trade courses.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:block text-2xl text-reedsoft-accent\",\n              children: \"\\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-reedsoft-secondary\",\n                children: \"Month 3 (Weeks 9-12)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-semibold text-gray-700\",\n                children: \"Skill Integration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm mt-1 text-gray-600\",\n                children: \"\\u30B9\\u30AD\\u30EB\\u7D71\\u5408 (Sukiru-t\\u014Dg\\u014D)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm mt-2\",\n                children: \"Start N4 prep. Begin iTalki conversation practice. Introduce HIIT elements. Draft Japanese resumes.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:block text-2xl text-reedsoft-accent\",\n              children: \"\\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-reedsoft-secondary\",\n                children: \"Month 4 (Weeks 13-16)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"font-semibold text-gray-700\",\n                children: \"Final Preparation\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm mt-1 text-gray-600\",\n                children: \"\\u6700\\u7D42\\u6E96\\u5099 (Saish\\u016B-junbi)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm mt-2\",\n                children: \"Weekly N4 mock tests. Finalize visa documents. Practice interview questions. Maintain peak fitness.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        id: \"training-matrix\",\n        className: \"mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-center mb-8 text-reedsoft-primary\",\n          children: \"16-Week At-A-Glance Training Matrix\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white p-6 rounded-lg shadow-lg overflow-x-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-center text-gray-600 mb-6\",\n            children: \"A clear path, turning the four-month goal into manageable weekly sprints.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"w-full text-xs md:text-sm\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"bg-gray-50\",\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"text-left p-2 font-bold text-reedsoft-primary\",\n                  children: \"Week\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"text-left p-2 font-bold text-reedsoft-primary\",\n                  children: \"Language Focus\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"text-left p-2 font-bold text-reedsoft-primary\",\n                  children: \"Physical Fitness Goal\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"text-left p-2 font-bold text-reedsoft-primary\",\n                  children: \"Skills Focus\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"text-left p-2 font-bold text-reedsoft-primary\",\n                  children: \"Career Prep Task\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: [/*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2 font-semibold text-reedsoft-secondary\",\n                  children: \"1-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Master Hiragana & Katakana. Start Genki I. Begin WaniKani.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"3x weekly bodyweight circuits. Focus on form.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Daily cognitive training (15 min). Research trade courses.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Research SSW visa requirements. Identify recruitment agencies.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b bg-gray-50\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2 font-semibold text-reedsoft-secondary\",\n                  children: \"3-4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Complete first chapters. Daily Anki (vocab/grammar).\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Maintain 3x weekly workouts. Increase reps slightly.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Start first online trade course. Continue cognitive training.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Draft initial contact email for recruitment agencies.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2 font-semibold text-reedsoft-secondary\",\n                  children: \"5-6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Progress through textbook (N5 grammar). Learn first 50 N5 Kanji.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 228,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Increase workout intensity. Add 4th weekly session.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Continue first course. Focus on weak cognitive areas.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Draft first version of Rirekisho and Shokumu Keirekisho.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b bg-gray-50\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2 font-semibold text-reedsoft-secondary\",\n                  children: \"7-8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Complete N5 Kanji list (~100). Begin beginner listening podcasts.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Introduce HIIT elements into 1-2 weekly workouts.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Complete first course. Start second course (Intro to Logistics).\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Contact recruitment agencies. Refine resumes based on feedback.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2 font-semibold text-reedsoft-secondary\",\n                  children: \"9-10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Finish primary textbook. Start N4 prep book (Shin-Kanzen Master).\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Focus on functional strength. Maintain 4x weekly workouts.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Continue second course. Apply learnings to resume drafts.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Research specific Japanese companies in target sectors.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b bg-gray-50\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2 font-semibold text-reedsoft-secondary\",\n                  children: \"11-12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Learn first 100 N4 Kanji. Start weekly iTalki conversation practice.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Maintain HIIT and strength focus. Ensure proper recovery.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 250,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Complete second course. Summarize key skills learned.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Practice interview questions, focusing on cultural etiquette.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2 font-semibold text-reedsoft-secondary\",\n                  children: \"13-14\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Intensive N4 grammar/vocab review. Take first full N4 mock test.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Maintain fitness with 3-4 workouts. Focus on injury prevention.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Maintenance cognitive training (10 min/day).\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Finalize resumes. Begin gathering SSW application documents.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"border-b bg-gray-50\",\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2 font-semibold text-reedsoft-secondary\",\n                  children: \"15-16\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Take weekly timed N4 mock tests. Drill weak areas identified.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Taper intensity slightly to ensure body is rested and strong.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Review notes from online trade courses.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 265,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"p-2\",\n                  children: \"Conduct mock interviews. Have all visa documents ready.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        id: \"daily-grind\",\n        className: \"mb-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold text-center mb-8 text-reedsoft-primary\",\n          children: \"The Daily Grind: Weekday Blueprint\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white p-6 rounded-lg shadow-lg\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-center text-gray-600 mb-4\",\n              children: \"This is your core schedule, Monday to Friday. Each block is non-negotiable. It is designed for maximum efficiency in skill acquisition.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), timeBlocks.map((block, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white p-5 rounded-lg shadow-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"font-bold text-lg text-reedsoft-primary\",\n              children: [formatTimeInText(block.time), \" | \", block.title]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                className: \"text-reedsoft-secondary\",\n                children: \"WHAT:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 290,\n                columnNumber: 19\n              }, this), \" \", block.what]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 289,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                className: \"text-reedsoft-secondary\",\n                children: \"WHY:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 19\n              }, this), \" \", block.why]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                className: \"text-reedsoft-secondary\",\n                children: \"HOW:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 296,\n                columnNumber: 19\n              }, this), \" \", block.how]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        id: \"weekend-plan\",\n        className: \"mb-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white p-6 rounded-lg shadow-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-center mb-4 text-reedsoft-primary\",\n              children: \"Weekly Time Allocation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mb-4 text-center\",\n              children: \"Your approximate 40+ hours of active development each week, broken down by pillar.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative w-full max-w-md mx-auto h-96\",\n              role: \"img\",\n              \"aria-label\": \"Weekly time allocation chart showing distribution across four pillars\",\n              children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                role: \"status\",\n                \"aria-live\": \"polite\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-reedsoft-primary\",\n                  \"aria-hidden\": \"true\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"sr-only\",\n                  children: \"Loading chart data...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 315,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 19\n              }, this) : timeAllocationData && timeAllocationOptions ? /*#__PURE__*/_jsxDEV(Doughnut, {\n                data: timeAllocationData,\n                options: timeAllocationOptions,\n                \"aria-label\": \"Doughnut chart showing weekly time allocation: Physical Fitness, Japanese Language, Practical Skills, and Cognitive Fitness\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-center h-full text-gray-500\",\n                role: \"status\",\n                \"aria-live\": \"polite\",\n                children: \"Chart loading...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sr-only\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Weekly Time Allocation Summary:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Physical Fitness: 7.5 hours per week\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Japanese Language: 21 hours per week\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Practical Skills: 10 hours per week\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Cognitive Fitness: 7.5 hours per week\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-white p-6 rounded-lg shadow-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-center mb-4 text-reedsoft-primary\",\n              children: \"The Weekend Shift\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 342,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600 mb-4 text-center\",\n              children: \"Weekends are for review, practical application, and strategic recovery. The pace changes, but the work continues.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              className: \"space-y-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"p-4 bg-cyan-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold text-teal-800\",\n                  children: \"Saturday Morning: Review & Consolidate\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm\",\n                  children: \"Review all Japanese lessons and Anki cards from the week. Perform one physical fitness session.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 349,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"p-4 bg-cyan-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold text-teal-800\",\n                  children: \"Saturday Afternoon: Hands-On Project\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm\",\n                  children: \"Apply your practical skills. Build a small shelf, practice knots for 30 minutes, disassemble and reassemble an old appliance. Turn theory into action.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 353,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 351,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"p-4 bg-cyan-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold text-teal-800\",\n                  children: \"Sunday Morning: Active Recovery\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm\",\n                  children: \"Go for a long walk or do a deep stretching routine. Listen to Japanese music or a podcast. Let your body heal.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                className: \"p-4 bg-cyan-50 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-bold text-teal-800\",\n                  children: \"Sunday Afternoon: Strategize & Recharge\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 360,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm\",\n                  children: \"Plan the week ahead: schedule workouts, set study goals. Then, completely disconnect. Read a novel, watch a movie for fun. Recharge your mind for the week ahead.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 361,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 359,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: \"mb-12\",\n        \"aria-labelledby\": \"pillar-navigation-heading\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          id: \"pillar-navigation-heading\",\n          className: \"text-3xl font-bold text-center mb-8 text-reedsoft-primary\",\n          children: \"Explore Each Pillar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n          role: \"navigation\",\n          \"aria-label\": \"Four pillars navigation\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/japan/pillar-1\",\n            className: \"bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-l-4 border-red-500 group focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2\",\n            \"aria-label\": \"Navigate to Pillar 1: Physical Fitness and Strength Training\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-3\",\n              \"aria-hidden\": \"true\",\n              children: \"\\uD83D\\uDCAA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-gray-800 mb-2 group-hover:text-red-500 transition-colors\",\n              children: \"Pillar 1: The Body\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm\",\n              children: \"Physical Fitness & Strength Training\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/japan/pillar-2\",\n            className: \"bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-l-4 border-reedsoft-primary group focus:outline-none focus:ring-2 focus:ring-reedsoft-primary focus:ring-offset-2\",\n            \"aria-label\": \"Navigate to Pillar 2: Japanese Language Mastery\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-3\",\n              \"aria-hidden\": \"true\",\n              children: \"\\uD83D\\uDDE3\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-gray-800 mb-2 group-hover:text-reedsoft-primary transition-colors\",\n              children: \"Pillar 2: The Voice\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm\",\n              children: \"Japanese Language Mastery\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 393,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/japan/pillar-3\",\n            className: \"bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-l-4 border-gray-600 group focus:outline-none focus:ring-2 focus:ring-gray-600 focus:ring-offset-2\",\n            \"aria-label\": \"Navigate to Pillar 3: Practical Skills and Trade Knowledge\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-3\",\n              \"aria-hidden\": \"true\",\n              children: \"\\uD83D\\uDEE0\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-gray-800 mb-2 group-hover:text-gray-600 transition-colors\",\n              children: \"Pillar 3: The Hands\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm\",\n              children: \"Practical Skills & Trade Knowledge\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/japan/pillar-4\",\n            className: \"bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-l-4 border-blue-500 group focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n            \"aria-label\": \"Navigate to Pillar 4: Cognitive Fitness and Learning\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-4xl mb-3\",\n              \"aria-hidden\": \"true\",\n              children: \"\\uD83E\\uDDE0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-gray-800 mb-2 group-hover:text-blue-500 transition-colors\",\n              children: \"Pillar 4: The Mind\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-sm\",\n              children: \"Cognitive Fitness & Learning\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n        className: \"text-center mt-12 pt-8 border-t border-gray-300\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Discipline today is freedom tomorrow. Execute the plan.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500 mt-2\",\n          children: \"\\xA9 2025 Project Reboot Master Plan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 424,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n};\n_s(JapanPlan, \"4rASc0gwH19gujhlv8hhsPdiTT8=\", false, function () {\n  return [useChartData];\n});\n_c = JapanPlan;\nexport default JapanPlan;\nvar _c;\n$RefreshReg$(_c, \"JapanPlan\");", "map": {"version": 3, "names": ["React", "useMemo", "Link", "Doughnut", "Chart", "ChartJS", "ArcElement", "<PERSON><PERSON><PERSON>", "Legend", "Title", "useChartData", "formatTimeInText", "jsxDEV", "_jsxDEV", "register", "JapanPlan", "_s", "data", "timeAllocationData", "options", "timeAllocationOptions", "loading", "timeBlocks", "time", "title", "what", "why", "how", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "id", "map", "block", "index", "role", "to", "_c", "$RefreshReg$"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/pages/JapanPlan.tsx"], "sourcesContent": ["import React, { useMemo } from 'react';\nimport { <PERSON> } from 'react-router-dom';\nimport { Doughnut } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  ArcElement,\n  Tooltip,\n  Legend,\n  Title\n} from 'chart.js';\nimport { useChartData } from '../hooks/useChartData';\nimport { formatTimeInText } from '../utils/timeFormat';\n\n// Register Chart.js components\nChartJS.register(ArcElement, Tooltip, Legend, Title);\n\nconst JapanPlan: React.FC = () => {\n  const { data: timeAllocationData, options: timeAllocationOptions, loading } = useChartData('timeAllocation');\n\n  // Time blocks with 24-hour format that will be converted\n  const timeBlocks = useMemo(() => [\n    {\n      time: \"06:00 - 07:30\",\n      title: \"💪 Pillar 1: Physical Fitness\",\n      what: \"Full-body strength training (3x/week) or Cardio & Core (2x/week).\",\n      why: \"To build the raw strength and endurance needed for physical jobs and to boost mental clarity for the day.\",\n      how: \"Follow a structured program like StrongLifts 5x5. For cardio, use HIIT or jogging. Use apps like Jefit to track lifts.\"\n    },\n    {\n      time: \"09:00 - 12:00\",\n      title: \"🗣️ Pillar 2: Japanese Core Study\",\n      what: \"Deep, focused study of Japanese grammar and sentence structure.\",\n      why: \"Grammar is the skeleton of the language. Vocabulary is useless without it. This is the hardest but most important part.\",\n      how: \"Use a textbook (Genki I & II). Complete exercises with a pen and paper. No phone, no distractions. One chapter every 4-5 days.\"\n    },\n    {\n      time: \"13:00 - 15:00\",\n      title: \"🛠️ Pillar 3: Practical Skills Study\",\n      what: \"Theoretical study of tools, safety protocols, materials, and basic trade knowledge.\",\n      why: \"To become a quick learner on a job site. Knowing the 'what' and 'why' makes the 'how' much easier to pick up.\",\n      how: \"Watch curated YouTube channels (e.g., Essential Craftsman). Take notes. Create a \\\"knowledge base\\\" in a notebook.\"\n    },\n    {\n      time: \"15:00 - 16:30\",\n      title: \"🧠 Pillar 4: Cognitive Fitness\",\n      what: \"Active reading of non-fiction, problem-solving puzzles, and meditation.\",\n      why: \"To sharpen your focus, improve memory retention, and train your brain to learn efficiently.\",\n      how: \"Read a chapter, then summarize it (Feynman Technique). Use apps like Headspace for meditation and Lumosity for logic puzzles.\"\n    },\n    {\n      time: \"17:00 - 18:00\",\n      title: \"🗣️ Pillar 2: Japanese Vocabulary Drill\",\n      what: \"Spaced Repetition System (SRS) for vocabulary.\",\n      why: \"To efficiently memorize thousands of words and burn them into your long-term memory.\",\n      how: \"Use the Anki app. Do your reviews and learn 20 new words every single day without fail.\"\n    },\n    {\n      time: \"19:30 - 21:00\",\n      title: \"🗣️ Pillar 2: Japanese Immersion\",\n      what: \"Actively listen to and speak Japanese.\",\n      why: \"To connect your grammar and vocabulary knowledge to the sound and rhythm of the real language.\",\n      how: \"Listen to beginner podcasts or anime audio. Starting Month 3, use HelloTalk to speak with native speakers.\"\n    }\n  ], []);\n\n  return (\n    <div className=\"bg-gray-100 text-gray-800 min-h-screen\">\n      <div className=\"container mx-auto p-4 md:p-8\">\n        <header className=\"text-center mb-12\">\n          <h1 className=\"text-4xl md:text-6xl font-black text-reedsoft-primary tracking-tight\">\n            PROJECT: JACK OF ALL TRADES\n          </h1>\n          <p className=\"text-lg md:text-xl text-reedsoft-secondary mt-2\">\n            Your 16-Week Blueprint for Total Transformation\n          </p>\n        </header>\n\n        {/* Daily Kata (Routine) Section */}\n        <section id=\"daily-kata\" className=\"mb-12\">\n          <h2 className=\"text-3xl font-bold text-center mb-8 text-reedsoft-primary\">Your Daily \"Kata\" (Routine)</h2>\n          <div className=\"bg-white p-6 rounded-lg shadow-lg mb-8\">\n            <p className=\"text-center text-gray-600 mb-6\">\n              A consistent daily routine, or <em>kata</em> (形)—a Japanese term for a choreographed pattern of movements—is essential for disciplined practice.\n              This template integrates the Pomodoro Technique for maximum concentration and retention.\n            </p>\n\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full text-sm\">\n                <thead>\n                  <tr className=\"bg-gray-50\">\n                    <th className=\"text-left p-3 font-bold text-reedsoft-primary\">Time</th>\n                    <th className=\"text-left p-3 font-bold text-reedsoft-primary\">Activity</th>\n                    <th className=\"text-left p-3 font-bold text-reedsoft-primary\">Focus/Tools</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <tr className=\"border-b\">\n                    <td className=\"p-3 font-semibold text-reedsoft-secondary\">6:00 - 6:45 AM</td>\n                    <td className=\"p-3\">Physical Fitness</td>\n                    <td className=\"p-3\">Full-body circuit, HIIT, or functional strength workout.</td>\n                  </tr>\n                  <tr className=\"border-b bg-gray-50\">\n                    <td className=\"p-3 font-semibold text-reedsoft-secondary\">6:45 - 7:30 AM</td>\n                    <td className=\"p-3\">Morning Prep</td>\n                    <td className=\"p-3\">Shower, breakfast, review daily goals.</td>\n                  </tr>\n                  <tr className=\"border-b\">\n                    <td className=\"p-3 font-semibold text-reedsoft-secondary\">7:30 - 9:30 AM</td>\n                    <td className=\"p-3\"><strong>Japanese Study Block 1</strong></td>\n                    <td className=\"p-3\">4x Pomodoro sessions (25 min study / 5 min break). Focus on new grammar/kanji. Tools: Textbook, WaniKani.</td>\n                  </tr>\n                  <tr className=\"border-b bg-gray-50\">\n                    <td className=\"p-3 font-semibold text-reedsoft-secondary\">9:30 - 10:00 AM</td>\n                    <td className=\"p-3\">Extended Break</td>\n                    <td className=\"p-3\">Stretch, light snack, step away from study area.</td>\n                  </tr>\n                  <tr className=\"border-b\">\n                    <td className=\"p-3 font-semibold text-reedsoft-secondary\">10:00 AM - 12:00 PM</td>\n                    <td className=\"p-3\"><strong>Japanese Study Block 2</strong></td>\n                    <td className=\"p-3\">4x Pomodoro sessions. Focus on vocabulary reinforcement and reading practice. Tools: Anki, NHK News Easy.</td>\n                  </tr>\n                  <tr className=\"border-b bg-gray-50\">\n                    <td className=\"p-3 font-semibold text-reedsoft-secondary\">12:00 - 1:00 PM</td>\n                    <td className=\"p-3\">Lunch Break</td>\n                    <td className=\"p-3\">Nutritious meal, mental rest.</td>\n                  </tr>\n                  <tr className=\"border-b\">\n                    <td className=\"p-3 font-semibold text-reedsoft-secondary\">1:00 - 2:00 PM</td>\n                    <td className=\"p-3\"><strong>Skills Development Block</strong></td>\n                    <td className=\"p-3\">2x Pomodoro sessions. Work on online trade course or cognitive training. Tools: Alison, CogniFit/Lumosity.</td>\n                  </tr>\n                  <tr className=\"border-b bg-gray-50\">\n                    <td className=\"p-3 font-semibold text-reedsoft-secondary\">2:00 - 3:00 PM</td>\n                    <td className=\"p-3\"><strong>Japanese Study Block 3</strong></td>\n                    <td className=\"p-3\">2x Pomodoro sessions. Focus on listening or speaking practice. Tools: Podcasts, iTalki prep, shadowing.</td>\n                  </tr>\n                  <tr className=\"border-b\">\n                    <td className=\"p-3 font-semibold text-reedsoft-secondary\">3:00 - 3:30 PM</td>\n                    <td className=\"p-3\">Afternoon Break</td>\n                    <td className=\"p-3\">Walk, hydrate, short rest.</td>\n                  </tr>\n                  <tr className=\"border-b bg-gray-50\">\n                    <td className=\"p-3 font-semibold text-reedsoft-secondary\">3:30 - 5:00 PM</td>\n                    <td className=\"p-3\"><strong>Career Prep Block</strong></td>\n                    <td className=\"p-3\">Work on resumes, research companies, prepare for interviews, or organize visa documents.</td>\n                  </tr>\n                  <tr className=\"border-b\">\n                    <td className=\"p-3 font-semibold text-reedsoft-secondary\">5:00 PM onward</td>\n                    <td className=\"p-3\">Free Time & Review</td>\n                    <td className=\"p-3\">Dinner, hobbies, relaxation. A light 15-30 minute review of the day's Japanese lessons before bed.</td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </section>\n\n        {/* Monthly Missions Section */}\n        <section id=\"missions\" className=\"mb-12\">\n          <h2 className=\"text-3xl font-bold text-center mb-8 text-reedsoft-primary\">Your 16-Week Transformation Journey</h2>\n          <div className=\"bg-white p-6 rounded-lg shadow-lg\">\n            <div className=\"grid grid-cols-1 md:grid-cols-4 items-center text-center gap-4\">\n              <div className=\"p-4\">\n                <h3 className=\"text-xl font-bold text-reedsoft-secondary\">Month 1 (Weeks 1-4)</h3>\n                <p className=\"font-semibold text-gray-700\">Building the Foundation</p>\n                <p className=\"text-sm mt-1 text-gray-600\">基礎固め (Kiso-gatame)</p>\n                <p className=\"text-sm mt-2\">Master Hiragana & Katakana. Start Genki I. Begin WaniKani. 3x weekly bodyweight circuits. Daily cognitive training.</p>\n              </div>\n              <div className=\"hidden md:block text-2xl text-reedsoft-accent\">→</div>\n              <div className=\"p-4\">\n                <h3 className=\"text-xl font-bold text-reedsoft-secondary\">Month 2 (Weeks 5-8)</h3>\n                <p className=\"font-semibold text-gray-700\">Accelerating Progress</p>\n                <p className=\"text-sm mt-1 text-gray-600\">進捗加速 (Shinchoku-kasoku)</p>\n                <p className=\"text-sm mt-2\">Complete N5 grammar. Learn first 100 N5 Kanji. Increase workout intensity. Start online trade courses.</p>\n              </div>\n              <div className=\"hidden md:block text-2xl text-reedsoft-accent\">→</div>\n              <div className=\"p-4\">\n                <h3 className=\"text-xl font-bold text-reedsoft-secondary\">Month 3 (Weeks 9-12)</h3>\n                <p className=\"font-semibold text-gray-700\">Skill Integration</p>\n                <p className=\"text-sm mt-1 text-gray-600\">スキル統合 (Sukiru-tōgō)</p>\n                <p className=\"text-sm mt-2\">Start N4 prep. Begin iTalki conversation practice. Introduce HIIT elements. Draft Japanese resumes.</p>\n              </div>\n              <div className=\"hidden md:block text-2xl text-reedsoft-accent\">→</div>\n              <div className=\"p-4\">\n                <h3 className=\"text-xl font-bold text-reedsoft-secondary\">Month 4 (Weeks 13-16)</h3>\n                <p className=\"font-semibold text-gray-700\">Final Preparation</p>\n                <p className=\"text-sm mt-1 text-gray-600\">最終準備 (Saishū-junbi)</p>\n                <p className=\"text-sm mt-2\">Weekly N4 mock tests. Finalize visa documents. Practice interview questions. Maintain peak fitness.</p>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* 16-Week Training Matrix Section */}\n        <section id=\"training-matrix\" className=\"mb-12\">\n          <h2 className=\"text-3xl font-bold text-center mb-8 text-reedsoft-primary\">16-Week At-A-Glance Training Matrix</h2>\n          <div className=\"bg-white p-6 rounded-lg shadow-lg overflow-x-auto\">\n            <p className=\"text-center text-gray-600 mb-6\">\n              A clear path, turning the four-month goal into manageable weekly sprints.\n            </p>\n            <table className=\"w-full text-xs md:text-sm\">\n              <thead>\n                <tr className=\"bg-gray-50\">\n                  <th className=\"text-left p-2 font-bold text-reedsoft-primary\">Week</th>\n                  <th className=\"text-left p-2 font-bold text-reedsoft-primary\">Language Focus</th>\n                  <th className=\"text-left p-2 font-bold text-reedsoft-primary\">Physical Fitness Goal</th>\n                  <th className=\"text-left p-2 font-bold text-reedsoft-primary\">Skills Focus</th>\n                  <th className=\"text-left p-2 font-bold text-reedsoft-primary\">Career Prep Task</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr className=\"border-b\">\n                  <td className=\"p-2 font-semibold text-reedsoft-secondary\">1-2</td>\n                  <td className=\"p-2\">Master Hiragana & Katakana. Start Genki I. Begin WaniKani.</td>\n                  <td className=\"p-2\">3x weekly bodyweight circuits. Focus on form.</td>\n                  <td className=\"p-2\">Daily cognitive training (15 min). Research trade courses.</td>\n                  <td className=\"p-2\">Research SSW visa requirements. Identify recruitment agencies.</td>\n                </tr>\n                <tr className=\"border-b bg-gray-50\">\n                  <td className=\"p-2 font-semibold text-reedsoft-secondary\">3-4</td>\n                  <td className=\"p-2\">Complete first chapters. Daily Anki (vocab/grammar).</td>\n                  <td className=\"p-2\">Maintain 3x weekly workouts. Increase reps slightly.</td>\n                  <td className=\"p-2\">Start first online trade course. Continue cognitive training.</td>\n                  <td className=\"p-2\">Draft initial contact email for recruitment agencies.</td>\n                </tr>\n                <tr className=\"border-b\">\n                  <td className=\"p-2 font-semibold text-reedsoft-secondary\">5-6</td>\n                  <td className=\"p-2\">Progress through textbook (N5 grammar). Learn first 50 N5 Kanji.</td>\n                  <td className=\"p-2\">Increase workout intensity. Add 4th weekly session.</td>\n                  <td className=\"p-2\">Continue first course. Focus on weak cognitive areas.</td>\n                  <td className=\"p-2\">Draft first version of Rirekisho and Shokumu Keirekisho.</td>\n                </tr>\n                <tr className=\"border-b bg-gray-50\">\n                  <td className=\"p-2 font-semibold text-reedsoft-secondary\">7-8</td>\n                  <td className=\"p-2\">Complete N5 Kanji list (~100). Begin beginner listening podcasts.</td>\n                  <td className=\"p-2\">Introduce HIIT elements into 1-2 weekly workouts.</td>\n                  <td className=\"p-2\">Complete first course. Start second course (Intro to Logistics).</td>\n                  <td className=\"p-2\">Contact recruitment agencies. Refine resumes based on feedback.</td>\n                </tr>\n                <tr className=\"border-b\">\n                  <td className=\"p-2 font-semibold text-reedsoft-secondary\">9-10</td>\n                  <td className=\"p-2\">Finish primary textbook. Start N4 prep book (Shin-Kanzen Master).</td>\n                  <td className=\"p-2\">Focus on functional strength. Maintain 4x weekly workouts.</td>\n                  <td className=\"p-2\">Continue second course. Apply learnings to resume drafts.</td>\n                  <td className=\"p-2\">Research specific Japanese companies in target sectors.</td>\n                </tr>\n                <tr className=\"border-b bg-gray-50\">\n                  <td className=\"p-2 font-semibold text-reedsoft-secondary\">11-12</td>\n                  <td className=\"p-2\">Learn first 100 N4 Kanji. Start weekly iTalki conversation practice.</td>\n                  <td className=\"p-2\">Maintain HIIT and strength focus. Ensure proper recovery.</td>\n                  <td className=\"p-2\">Complete second course. Summarize key skills learned.</td>\n                  <td className=\"p-2\">Practice interview questions, focusing on cultural etiquette.</td>\n                </tr>\n                <tr className=\"border-b\">\n                  <td className=\"p-2 font-semibold text-reedsoft-secondary\">13-14</td>\n                  <td className=\"p-2\">Intensive N4 grammar/vocab review. Take first full N4 mock test.</td>\n                  <td className=\"p-2\">Maintain fitness with 3-4 workouts. Focus on injury prevention.</td>\n                  <td className=\"p-2\">Maintenance cognitive training (10 min/day).</td>\n                  <td className=\"p-2\">Finalize resumes. Begin gathering SSW application documents.</td>\n                </tr>\n                <tr className=\"border-b bg-gray-50\">\n                  <td className=\"p-2 font-semibold text-reedsoft-secondary\">15-16</td>\n                  <td className=\"p-2\">Take weekly timed N4 mock tests. Drill weak areas identified.</td>\n                  <td className=\"p-2\">Taper intensity slightly to ensure body is rested and strong.</td>\n                  <td className=\"p-2\">Review notes from online trade courses.</td>\n                  <td className=\"p-2\">Conduct mock interviews. Have all visa documents ready.</td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </section>\n\n        {/* Daily Grind Section */}\n        <section id=\"daily-grind\" className=\"mb-12\">\n          <h2 className=\"text-3xl font-bold text-center mb-8 text-reedsoft-primary\">The Daily Grind: Weekday Blueprint</h2>\n          <div className=\"space-y-6\">\n            <div className=\"bg-white p-6 rounded-lg shadow-lg\">\n              <p className=\"text-sm text-center text-gray-600 mb-4\">\n                This is your core schedule, Monday to Friday. Each block is non-negotiable.\n                It is designed for maximum efficiency in skill acquisition.\n              </p>\n            </div>\n\n            {timeBlocks.map((block, index) => (\n              <div key={index} className=\"bg-white p-5 rounded-lg shadow-md\">\n                <div className=\"font-bold text-lg text-reedsoft-primary\">\n                  {formatTimeInText(block.time)} | {block.title}\n                </div>\n                <p className=\"mt-2 text-sm\">\n                  <strong className=\"text-reedsoft-secondary\">WHAT:</strong> {block.what}\n                </p>\n                <p className=\"mt-1 text-sm\">\n                  <strong className=\"text-reedsoft-secondary\">WHY:</strong> {block.why}\n                </p>\n                <p className=\"mt-1 text-sm\">\n                  <strong className=\"text-reedsoft-secondary\">HOW:</strong> {block.how}\n                </p>\n              </div>\n            ))}\n          </div>\n        </section>\n\n        {/* Weekend Plan Section */}\n        <section id=\"weekend-plan\" className=\"mb-12\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n            <div className=\"bg-white p-6 rounded-lg shadow-lg\">\n              <h2 className=\"text-2xl font-bold text-center mb-4 text-reedsoft-primary\">Weekly Time Allocation</h2>\n              <p className=\"text-sm text-gray-600 mb-4 text-center\">\n                Your approximate 40+ hours of active development each week, broken down by pillar.\n              </p>\n              <div className=\"relative w-full max-w-md mx-auto h-96\" role=\"img\" aria-label=\"Weekly time allocation chart showing distribution across four pillars\">\n                {loading ? (\n                  <div className=\"flex items-center justify-center h-full\" role=\"status\" aria-live=\"polite\">\n                    <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-reedsoft-primary\" aria-hidden=\"true\"></div>\n                    <span className=\"sr-only\">Loading chart data...</span>\n                  </div>\n                ) : timeAllocationData && timeAllocationOptions ? (\n                  <Doughnut\n                    data={timeAllocationData}\n                    options={timeAllocationOptions as any}\n                    aria-label=\"Doughnut chart showing weekly time allocation: Physical Fitness, Japanese Language, Practical Skills, and Cognitive Fitness\"\n                  />\n                ) : (\n                  <div className=\"flex items-center justify-center h-full text-gray-500\" role=\"status\" aria-live=\"polite\">\n                    Chart loading...\n                  </div>\n                )}\n              </div>\n              {/* Screen reader accessible data summary */}\n              <div className=\"sr-only\">\n                <h3>Weekly Time Allocation Summary:</h3>\n                <ul>\n                  <li>Physical Fitness: 7.5 hours per week</li>\n                  <li>Japanese Language: 21 hours per week</li>\n                  <li>Practical Skills: 10 hours per week</li>\n                  <li>Cognitive Fitness: 7.5 hours per week</li>\n                </ul>\n              </div>\n            </div>\n\n            <div className=\"bg-white p-6 rounded-lg shadow-lg\">\n              <h2 className=\"text-2xl font-bold text-center mb-4 text-reedsoft-primary\">The Weekend Shift</h2>\n              <p className=\"text-sm text-gray-600 mb-4 text-center\">\n                Weekends are for review, practical application, and strategic recovery. The pace changes, but the work continues.\n              </p>\n              <ul className=\"space-y-4\">\n                <li className=\"p-4 bg-cyan-50 rounded-lg\">\n                  <h3 className=\"font-bold text-teal-800\">Saturday Morning: Review & Consolidate</h3>\n                  <p className=\"text-sm\">Review all Japanese lessons and Anki cards from the week. Perform one physical fitness session.</p>\n                </li>\n                <li className=\"p-4 bg-cyan-50 rounded-lg\">\n                  <h3 className=\"font-bold text-teal-800\">Saturday Afternoon: Hands-On Project</h3>\n                  <p className=\"text-sm\">Apply your practical skills. Build a small shelf, practice knots for 30 minutes, disassemble and reassemble an old appliance. Turn theory into action.</p>\n                </li>\n                <li className=\"p-4 bg-cyan-50 rounded-lg\">\n                  <h3 className=\"font-bold text-teal-800\">Sunday Morning: Active Recovery</h3>\n                  <p className=\"text-sm\">Go for a long walk or do a deep stretching routine. Listen to Japanese music or a podcast. Let your body heal.</p>\n                </li>\n                <li className=\"p-4 bg-cyan-50 rounded-lg\">\n                  <h3 className=\"font-bold text-teal-800\">Sunday Afternoon: Strategize & Recharge</h3>\n                  <p className=\"text-sm\">Plan the week ahead: schedule workouts, set study goals. Then, completely disconnect. Read a novel, watch a movie for fun. Recharge your mind for the week ahead.</p>\n                </li>\n              </ul>\n            </div>\n          </div>\n        </section>\n\n        {/* Pillar Navigation */}\n        <section className=\"mb-12\" aria-labelledby=\"pillar-navigation-heading\">\n          <h2 id=\"pillar-navigation-heading\" className=\"text-3xl font-bold text-center mb-8 text-reedsoft-primary\">Explore Each Pillar</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\" role=\"navigation\" aria-label=\"Four pillars navigation\">\n            <Link\n              to=\"/japan/pillar-1\"\n              className=\"bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-l-4 border-red-500 group focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2\"\n              aria-label=\"Navigate to Pillar 1: Physical Fitness and Strength Training\"\n            >\n              <div className=\"text-4xl mb-3\" aria-hidden=\"true\">💪</div>\n              <h3 className=\"text-xl font-bold text-gray-800 mb-2 group-hover:text-red-500 transition-colors\">\n                Pillar 1: The Body\n              </h3>\n              <p className=\"text-gray-600 text-sm\">Physical Fitness & Strength Training</p>\n            </Link>\n\n            <Link\n              to=\"/japan/pillar-2\"\n              className=\"bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-l-4 border-reedsoft-primary group focus:outline-none focus:ring-2 focus:ring-reedsoft-primary focus:ring-offset-2\"\n              aria-label=\"Navigate to Pillar 2: Japanese Language Mastery\"\n            >\n              <div className=\"text-4xl mb-3\" aria-hidden=\"true\">🗣️</div>\n              <h3 className=\"text-xl font-bold text-gray-800 mb-2 group-hover:text-reedsoft-primary transition-colors\">\n                Pillar 2: The Voice\n              </h3>\n              <p className=\"text-gray-600 text-sm\">Japanese Language Mastery</p>\n            </Link>\n\n            <Link\n              to=\"/japan/pillar-3\"\n              className=\"bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-l-4 border-gray-600 group focus:outline-none focus:ring-2 focus:ring-gray-600 focus:ring-offset-2\"\n              aria-label=\"Navigate to Pillar 3: Practical Skills and Trade Knowledge\"\n            >\n              <div className=\"text-4xl mb-3\" aria-hidden=\"true\">🛠️</div>\n              <h3 className=\"text-xl font-bold text-gray-800 mb-2 group-hover:text-gray-600 transition-colors\">\n                Pillar 3: The Hands\n              </h3>\n              <p className=\"text-gray-600 text-sm\">Practical Skills & Trade Knowledge</p>\n            </Link>\n\n            <Link\n              to=\"/japan/pillar-4\"\n              className=\"bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-l-4 border-blue-500 group focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n              aria-label=\"Navigate to Pillar 4: Cognitive Fitness and Learning\"\n            >\n              <div className=\"text-4xl mb-3\" aria-hidden=\"true\">🧠</div>\n              <h3 className=\"text-xl font-bold text-gray-800 mb-2 group-hover:text-blue-500 transition-colors\">\n                Pillar 4: The Mind\n              </h3>\n              <p className=\"text-gray-600 text-sm\">Cognitive Fitness & Learning</p>\n            </Link>\n          </div>\n        </section>\n\n        <footer className=\"text-center mt-12 pt-8 border-t border-gray-300\">\n          <p className=\"text-gray-600\">Discipline today is freedom tomorrow. Execute the plan.</p>\n          <p className=\"text-sm text-gray-500 mt-2\">© 2025 Project Reboot Master Plan</p>\n        </footer>\n      </div>\n    </div>\n  );\n};\n\nexport default JapanPlan;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,OAAO,QAAQ,OAAO;AACtC,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SACEC,KAAK,IAAIC,OAAO,EAChBC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,KAAK,QACA,UAAU;AACjB,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,gBAAgB,QAAQ,qBAAqB;;AAEtD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAR,OAAO,CAACS,QAAQ,CAACR,UAAU,EAAEC,OAAO,EAAEC,MAAM,EAAEC,KAAK,CAAC;AAEpD,MAAMM,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM;IAAEC,IAAI,EAAEC,kBAAkB;IAAEC,OAAO,EAAEC,qBAAqB;IAAEC;EAAQ,CAAC,GAAGX,YAAY,CAAC,gBAAgB,CAAC;;EAE5G;EACA,MAAMY,UAAU,GAAGrB,OAAO,CAAC,MAAM,CAC/B;IACEsB,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,+BAA+B;IACtCC,IAAI,EAAE,mEAAmE;IACzEC,GAAG,EAAE,2GAA2G;IAChHC,GAAG,EAAE;EACP,CAAC,EACD;IACEJ,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,mCAAmC;IAC1CC,IAAI,EAAE,iEAAiE;IACvEC,GAAG,EAAE,yHAAyH;IAC9HC,GAAG,EAAE;EACP,CAAC,EACD;IACEJ,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,sCAAsC;IAC7CC,IAAI,EAAE,qFAAqF;IAC3FC,GAAG,EAAE,+GAA+G;IACpHC,GAAG,EAAE;EACP,CAAC,EACD;IACEJ,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,gCAAgC;IACvCC,IAAI,EAAE,yEAAyE;IAC/EC,GAAG,EAAE,6FAA6F;IAClGC,GAAG,EAAE;EACP,CAAC,EACD;IACEJ,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,yCAAyC;IAChDC,IAAI,EAAE,gDAAgD;IACtDC,GAAG,EAAE,sFAAsF;IAC3FC,GAAG,EAAE;EACP,CAAC,EACD;IACEJ,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,kCAAkC;IACzCC,IAAI,EAAE,wCAAwC;IAC9CC,GAAG,EAAE,gGAAgG;IACrGC,GAAG,EAAE;EACP,CAAC,CACF,EAAE,EAAE,CAAC;EAEN,oBACEd,OAAA;IAAKe,SAAS,EAAC,wCAAwC;IAAAC,QAAA,eACrDhB,OAAA;MAAKe,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAC3ChB,OAAA;QAAQe,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBACnChB,OAAA;UAAIe,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EAAC;QAErF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLpB,OAAA;UAAGe,SAAS,EAAC,iDAAiD;UAAAC,QAAA,EAAC;QAE/D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGTpB,OAAA;QAASqB,EAAE,EAAC,YAAY;QAACN,SAAS,EAAC,OAAO;QAAAC,QAAA,gBACxChB,OAAA;UAAIe,SAAS,EAAC,2DAA2D;UAAAC,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1GpB,OAAA;UAAKe,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDhB,OAAA;YAAGe,SAAS,EAAC,gCAAgC;YAAAC,QAAA,GAAC,iCACb,eAAAhB,OAAA;cAAAgB,QAAA,EAAI;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,gNAE9C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJpB,OAAA;YAAKe,SAAS,EAAC,iBAAiB;YAAAC,QAAA,eAC9BhB,OAAA;cAAOe,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC/BhB,OAAA;gBAAAgB,QAAA,eACEhB,OAAA;kBAAIe,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACxBhB,OAAA;oBAAIe,SAAS,EAAC,+CAA+C;oBAAAC,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvEpB,OAAA;oBAAIe,SAAS,EAAC,+CAA+C;oBAAAC,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3EpB,OAAA;oBAAIe,SAAS,EAAC,+CAA+C;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5E;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC,eACRpB,OAAA;gBAAAgB,QAAA,gBACEhB,OAAA;kBAAIe,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACtBhB,OAAA;oBAAIe,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7EpB,OAAA;oBAAIe,SAAS,EAAC,KAAK;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzCpB,OAAA;oBAAIe,SAAS,EAAC,KAAK;oBAAAC,QAAA,EAAC;kBAAwD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC,eACLpB,OAAA;kBAAIe,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBACjChB,OAAA;oBAAIe,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7EpB,OAAA;oBAAIe,SAAS,EAAC,KAAK;oBAAAC,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrCpB,OAAA;oBAAIe,SAAS,EAAC,KAAK;oBAAAC,QAAA,EAAC;kBAAsC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,eACLpB,OAAA;kBAAIe,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACtBhB,OAAA;oBAAIe,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7EpB,OAAA;oBAAIe,SAAS,EAAC,KAAK;oBAAAC,QAAA,eAAChB,OAAA;sBAAAgB,QAAA,EAAQ;oBAAsB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChEpB,OAAA;oBAAIe,SAAS,EAAC,KAAK;oBAAAC,QAAA,EAAC;kBAAyG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChI,CAAC,eACLpB,OAAA;kBAAIe,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBACjChB,OAAA;oBAAIe,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9EpB,OAAA;oBAAIe,SAAS,EAAC,KAAK;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvCpB,OAAA;oBAAIe,SAAS,EAAC,KAAK;oBAAAC,QAAA,EAAC;kBAAgD;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvE,CAAC,eACLpB,OAAA;kBAAIe,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACtBhB,OAAA;oBAAIe,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClFpB,OAAA;oBAAIe,SAAS,EAAC,KAAK;oBAAAC,QAAA,eAAChB,OAAA;sBAAAgB,QAAA,EAAQ;oBAAsB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChEpB,OAAA;oBAAIe,SAAS,EAAC,KAAK;oBAAAC,QAAA,EAAC;kBAAyG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChI,CAAC,eACLpB,OAAA;kBAAIe,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBACjChB,OAAA;oBAAIe,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC9EpB,OAAA;oBAAIe,SAAS,EAAC,KAAK;oBAAAC,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACpCpB,OAAA;oBAAIe,SAAS,EAAC,KAAK;oBAAAC,QAAA,EAAC;kBAA6B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eACLpB,OAAA;kBAAIe,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACtBhB,OAAA;oBAAIe,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7EpB,OAAA;oBAAIe,SAAS,EAAC,KAAK;oBAAAC,QAAA,eAAChB,OAAA;sBAAAgB,QAAA,EAAQ;oBAAwB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClEpB,OAAA;oBAAIe,SAAS,EAAC,KAAK;oBAAAC,QAAA,EAAC;kBAA0G;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjI,CAAC,eACLpB,OAAA;kBAAIe,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBACjChB,OAAA;oBAAIe,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7EpB,OAAA;oBAAIe,SAAS,EAAC,KAAK;oBAAAC,QAAA,eAAChB,OAAA;sBAAAgB,QAAA,EAAQ;oBAAsB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAChEpB,OAAA;oBAAIe,SAAS,EAAC,KAAK;oBAAAC,QAAA,EAAC;kBAAuG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9H,CAAC,eACLpB,OAAA;kBAAIe,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACtBhB,OAAA;oBAAIe,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7EpB,OAAA;oBAAIe,SAAS,EAAC,KAAK;oBAAAC,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACxCpB,OAAA;oBAAIe,SAAS,EAAC,KAAK;oBAAAC,QAAA,EAAC;kBAA0B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eACLpB,OAAA;kBAAIe,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBACjChB,OAAA;oBAAIe,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7EpB,OAAA;oBAAIe,SAAS,EAAC,KAAK;oBAAAC,QAAA,eAAChB,OAAA;sBAAAgB,QAAA,EAAQ;oBAAiB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3DpB,OAAA;oBAAIe,SAAS,EAAC,KAAK;oBAAAC,QAAA,EAAC;kBAAwF;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/G,CAAC,eACLpB,OAAA;kBAAIe,SAAS,EAAC,UAAU;kBAAAC,QAAA,gBACtBhB,OAAA;oBAAIe,SAAS,EAAC,2CAA2C;oBAAAC,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7EpB,OAAA;oBAAIe,SAAS,EAAC,KAAK;oBAAAC,QAAA,EAAC;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC3CpB,OAAA;oBAAIe,SAAS,EAAC,KAAK;oBAAAC,QAAA,EAAC;kBAAkG;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGVpB,OAAA;QAASqB,EAAE,EAAC,UAAU;QAACN,SAAS,EAAC,OAAO;QAAAC,QAAA,gBACtChB,OAAA;UAAIe,SAAS,EAAC,2DAA2D;UAAAC,QAAA,EAAC;QAAmC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClHpB,OAAA;UAAKe,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAChDhB,OAAA;YAAKe,SAAS,EAAC,gEAAgE;YAAAC,QAAA,gBAC7EhB,OAAA;cAAKe,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBhB,OAAA;gBAAIe,SAAS,EAAC,2CAA2C;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClFpB,OAAA;gBAAGe,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACtEpB,OAAA;gBAAGe,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChEpB,OAAA;gBAAGe,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAmH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChJ,CAAC,eACNpB,OAAA;cAAKe,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtEpB,OAAA;cAAKe,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBhB,OAAA;gBAAIe,SAAS,EAAC,2CAA2C;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClFpB,OAAA;gBAAGe,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACpEpB,OAAA;gBAAGe,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAuB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACrEpB,OAAA;gBAAGe,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAsG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnI,CAAC,eACNpB,OAAA;cAAKe,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtEpB,OAAA;cAAKe,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBhB,OAAA;gBAAIe,SAAS,EAAC,2CAA2C;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnFpB,OAAA;gBAAGe,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChEpB,OAAA;gBAAGe,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACjEpB,OAAA;gBAAGe,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAmG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChI,CAAC,eACNpB,OAAA;cAAKe,SAAS,EAAC,+CAA+C;cAAAC,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtEpB,OAAA;cAAKe,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBhB,OAAA;gBAAIe,SAAS,EAAC,2CAA2C;gBAAAC,QAAA,EAAC;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpFpB,OAAA;gBAAGe,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChEpB,OAAA;gBAAGe,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAmB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACjEpB,OAAA;gBAAGe,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAmG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGVpB,OAAA;QAASqB,EAAE,EAAC,iBAAiB;QAACN,SAAS,EAAC,OAAO;QAAAC,QAAA,gBAC7ChB,OAAA;UAAIe,SAAS,EAAC,2DAA2D;UAAAC,QAAA,EAAC;QAAmC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClHpB,OAAA;UAAKe,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChEhB,OAAA;YAAGe,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAE9C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJpB,OAAA;YAAOe,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBAC1ChB,OAAA;cAAAgB,QAAA,eACEhB,OAAA;gBAAIe,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACxBhB,OAAA;kBAAIe,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvEpB,OAAA;kBAAIe,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjFpB,OAAA;kBAAIe,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxFpB,OAAA;kBAAIe,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/EpB,OAAA;kBAAIe,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRpB,OAAA;cAAAgB,QAAA,gBACEhB,OAAA;gBAAIe,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACtBhB,OAAA;kBAAIe,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClEpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAA0D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnFpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAA6C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtEpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAA0D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnFpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAA8D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrF,CAAC,eACLpB,OAAA;gBAAIe,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBACjChB,OAAA;kBAAIe,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClEpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAAoD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7EpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAAoD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7EpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAA6D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtFpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAAqD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5E,CAAC,eACLpB,OAAA;gBAAIe,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACtBhB,OAAA;kBAAIe,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClEpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAAgE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzFpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAAmD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5EpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAAqD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9EpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAAwD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/E,CAAC,eACLpB,OAAA;gBAAIe,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBACjChB,OAAA;kBAAIe,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClEpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAAiE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1FpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAAiD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1EpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAAgE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzFpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAA+D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC,eACLpB,OAAA;gBAAIe,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACtBhB,OAAA;kBAAIe,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnEpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAAiE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1FpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAA0D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnFpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAAyD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClFpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAAuD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,eACLpB,OAAA;gBAAIe,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBACjChB,OAAA;kBAAIe,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpEpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAAoE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7FpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAAyD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClFpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAAqD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9EpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAA6D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpF,CAAC,eACLpB,OAAA;gBAAIe,SAAS,EAAC,UAAU;gBAAAC,QAAA,gBACtBhB,OAAA;kBAAIe,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpEpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAAgE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzFpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAA+D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxFpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAA4C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrEpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAA4D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC,eACLpB,OAAA;gBAAIe,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBACjChB,OAAA;kBAAIe,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpEpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAA6D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtFpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAA6D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtFpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAAuC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAChEpB,OAAA;kBAAIe,SAAS,EAAC,KAAK;kBAAAC,QAAA,EAAC;gBAAuD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGVpB,OAAA;QAASqB,EAAE,EAAC,aAAa;QAACN,SAAS,EAAC,OAAO;QAAAC,QAAA,gBACzChB,OAAA;UAAIe,SAAS,EAAC,2DAA2D;UAAAC,QAAA,EAAC;QAAkC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjHpB,OAAA;UAAKe,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBhB,OAAA;YAAKe,SAAS,EAAC,mCAAmC;YAAAC,QAAA,eAChDhB,OAAA;cAAGe,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAGtD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,EAELX,UAAU,CAACa,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC3BxB,OAAA;YAAiBe,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAC5DhB,OAAA;cAAKe,SAAS,EAAC,yCAAyC;cAAAC,QAAA,GACrDlB,gBAAgB,CAACyB,KAAK,CAACb,IAAI,CAAC,EAAC,KAAG,EAACa,KAAK,CAACZ,KAAK;YAAA;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC,eACNpB,OAAA;cAAGe,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBhB,OAAA;gBAAQe,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACG,KAAK,CAACX,IAAI;YAAA;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE,CAAC,eACJpB,OAAA;cAAGe,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBhB,OAAA;gBAAQe,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACG,KAAK,CAACV,GAAG;YAAA;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eACJpB,OAAA;cAAGe,SAAS,EAAC,cAAc;cAAAC,QAAA,gBACzBhB,OAAA;gBAAQe,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACG,KAAK,CAACT,GAAG;YAAA;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC;UAAA,GAZII,KAAK;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaV,CACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGVpB,OAAA;QAASqB,EAAE,EAAC,cAAc;QAACN,SAAS,EAAC,OAAO;QAAAC,QAAA,eAC1ChB,OAAA;UAAKe,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDhB,OAAA;YAAKe,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDhB,OAAA;cAAIe,SAAS,EAAC,2DAA2D;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrGpB,OAAA;cAAGe,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAEtD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJpB,OAAA;cAAKe,SAAS,EAAC,uCAAuC;cAACU,IAAI,EAAC,KAAK;cAAC,cAAW,uEAAuE;cAAAT,QAAA,EACjJR,OAAO,gBACNR,OAAA;gBAAKe,SAAS,EAAC,yCAAyC;gBAACU,IAAI,EAAC,QAAQ;gBAAC,aAAU,QAAQ;gBAAAT,QAAA,gBACvFhB,OAAA;kBAAKe,SAAS,EAAC,wEAAwE;kBAAC,eAAY;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjHpB,OAAA;kBAAMe,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,GACJf,kBAAkB,IAAIE,qBAAqB,gBAC7CP,OAAA,CAACV,QAAQ;gBACPc,IAAI,EAAEC,kBAAmB;gBACzBC,OAAO,EAAEC,qBAA6B;gBACtC,cAAW;cAA6H;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzI,CAAC,gBAEFpB,OAAA;gBAAKe,SAAS,EAAC,uDAAuD;gBAACU,IAAI,EAAC,QAAQ;gBAAC,aAAU,QAAQ;gBAAAT,QAAA,EAAC;cAExG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAENpB,OAAA;cAAKe,SAAS,EAAC,SAAS;cAAAC,QAAA,gBACtBhB,OAAA;gBAAAgB,QAAA,EAAI;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxCpB,OAAA;gBAAAgB,QAAA,gBACEhB,OAAA;kBAAAgB,QAAA,EAAI;gBAAoC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7CpB,OAAA;kBAAAgB,QAAA,EAAI;gBAAoC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7CpB,OAAA;kBAAAgB,QAAA,EAAI;gBAAmC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5CpB,OAAA;kBAAAgB,QAAA,EAAI;gBAAqC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpB,OAAA;YAAKe,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDhB,OAAA;cAAIe,SAAS,EAAC,2DAA2D;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChGpB,OAAA;cAAGe,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAEtD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJpB,OAAA;cAAIe,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACvBhB,OAAA;gBAAIe,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACvChB,OAAA;kBAAIe,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAsC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnFpB,OAAA;kBAAGe,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAA+F;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxH,CAAC,eACLpB,OAAA;gBAAIe,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACvChB,OAAA;kBAAIe,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAoC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACjFpB,OAAA;kBAAGe,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAsJ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/K,CAAC,eACLpB,OAAA;gBAAIe,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACvChB,OAAA;kBAAIe,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAA+B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5EpB,OAAA;kBAAGe,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAA8G;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvI,CAAC,eACLpB,OAAA;gBAAIe,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,gBACvChB,OAAA;kBAAIe,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,EAAC;gBAAuC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpFpB,OAAA;kBAAGe,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAiK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1L,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGVpB,OAAA;QAASe,SAAS,EAAC,OAAO;QAAC,mBAAgB,2BAA2B;QAAAC,QAAA,gBACpEhB,OAAA;UAAIqB,EAAE,EAAC,2BAA2B;UAACN,SAAS,EAAC,2DAA2D;UAAAC,QAAA,EAAC;QAAmB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjIpB,OAAA;UAAKe,SAAS,EAAC,sDAAsD;UAACU,IAAI,EAAC,YAAY;UAAC,cAAW,yBAAyB;UAAAT,QAAA,gBAC1HhB,OAAA,CAACX,IAAI;YACHqC,EAAE,EAAC,iBAAiB;YACpBX,SAAS,EAAC,4KAA4K;YACtL,cAAW,8DAA8D;YAAAC,QAAA,gBAEzEhB,OAAA;cAAKe,SAAS,EAAC,eAAe;cAAC,eAAY,MAAM;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1DpB,OAAA;cAAIe,SAAS,EAAC,iFAAiF;cAAAC,QAAA,EAAC;YAEhG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLpB,OAAA;cAAGe,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAoC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eAEPpB,OAAA,CAACX,IAAI;YACHqC,EAAE,EAAC,iBAAiB;YACpBX,SAAS,EAAC,8LAA8L;YACxM,cAAW,iDAAiD;YAAAC,QAAA,gBAE5DhB,OAAA;cAAKe,SAAS,EAAC,eAAe;cAAC,eAAY,MAAM;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3DpB,OAAA;cAAIe,SAAS,EAAC,0FAA0F;cAAAC,QAAA,EAAC;YAEzG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLpB,OAAA;cAAGe,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eAEPpB,OAAA,CAACX,IAAI;YACHqC,EAAE,EAAC,iBAAiB;YACpBX,SAAS,EAAC,8KAA8K;YACxL,cAAW,4DAA4D;YAAAC,QAAA,gBAEvEhB,OAAA;cAAKe,SAAS,EAAC,eAAe;cAAC,eAAY,MAAM;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3DpB,OAAA;cAAIe,SAAS,EAAC,kFAAkF;cAAAC,QAAA,EAAC;YAEjG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLpB,OAAA;cAAGe,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAkC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eAEPpB,OAAA,CAACX,IAAI;YACHqC,EAAE,EAAC,iBAAiB;YACpBX,SAAS,EAAC,8KAA8K;YACxL,cAAW,sDAAsD;YAAAC,QAAA,gBAEjEhB,OAAA;cAAKe,SAAS,EAAC,eAAe;cAAC,eAAY,MAAM;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC1DpB,OAAA;cAAIe,SAAS,EAAC,kFAAkF;cAAAC,QAAA,EAAC;YAEjG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLpB,OAAA;cAAGe,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEVpB,OAAA;QAAQe,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBACjEhB,OAAA;UAAGe,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAuD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACxFpB,OAAA;UAAGe,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjB,EAAA,CA5ZID,SAAmB;EAAA,QACuDL,YAAY;AAAA;AAAA8B,EAAA,GADtFzB,SAAmB;AA8ZzB,eAAeA,SAAS;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}