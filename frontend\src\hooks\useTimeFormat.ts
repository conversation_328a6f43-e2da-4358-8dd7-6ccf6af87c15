import { useState, useEffect, useMemo } from 'react';
import { FormattedTime, TimeBlock } from '../types';
import { 
  convertTo12Hour, 
  formatTimeRange, 
  formatTimeInText, 
  getCurrentTime12Hour 
} from '../utils/timeFormat';

/**
 * Custom hook for formatting individual time values
 * @param time24 - Time in 24-hour format
 * @returns Formatted time object
 */
export const useTimeFormat = (time24: string): FormattedTime => {
  return useMemo(() => convertTo12Hour(time24), [time24]);
};

/**
 * Custom hook for formatting time ranges
 * @param timeRange - Time range string (e.g., "06:00 - 07:30")
 * @returns Formatted time range string
 */
export const useTimeRangeFormat = (timeRange: string): string => {
  return useMemo(() => formatTimeRange(timeRange), [timeRange]);
};

/**
 * Custom hook for formatting text containing time information
 * @param text - Text containing time patterns
 * @returns Formatted text with converted times
 */
export const useTextTimeFormat = (text: string): string => {
  return useMemo(() => formatTimeInText(text), [text]);
};

/**
 * Custom hook for formatting multiple time blocks
 * @param timeBlocks - Array of time blocks to format
 * @returns Array of formatted time blocks
 */
export const useTimeBlocksFormat = (timeBlocks: TimeBlock[]) => {
  return useMemo(() => {
    return timeBlocks.map(block => ({
      ...block,
      time: formatTimeInText(block.time),
      title: formatTimeInText(block.title),
      description: formatTimeInText(block.description)
    }));
  }, [timeBlocks]);
};

/**
 * Custom hook for live current time display
 * @param updateInterval - Update interval in milliseconds (default: 60000 = 1 minute)
 * @returns Current time in 12-hour format, updates automatically
 */
export const useCurrentTime = (updateInterval: number = 60000): string => {
  const [currentTime, setCurrentTime] = useState<string>(getCurrentTime12Hour());

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(getCurrentTime12Hour());
    }, updateInterval);

    return () => clearInterval(interval);
  }, [updateInterval]);

  return currentTime;
};

/**
 * Custom hook for batch time formatting operations
 * @param times - Array of time strings in 24-hour format
 * @returns Object with various formatted versions
 */
export const useBatchTimeFormat = (times: string[]) => {
  return useMemo(() => {
    const formatted = times.map(time => convertTo12Hour(time));
    const formattedStrings = formatted.map(f => f.formatted);
    const amTimes = formatted.filter(f => f.period === 'AM').map(f => f.formatted);
    const pmTimes = formatted.filter(f => f.period === 'PM').map(f => f.formatted);

    return {
      all: formatted,
      strings: formattedStrings,
      am: amTimes,
      pm: pmTimes,
      count: {
        total: times.length,
        am: amTimes.length,
        pm: pmTimes.length
      }
    };
  }, [times]);
};

/**
 * Custom hook for time-based conditional rendering
 * @param targetTime - Target time in 24-hour format
 * @param tolerance - Tolerance in minutes (default: 30)
 * @returns Object with time comparison utilities
 */
export const useTimeComparison = (targetTime: string, tolerance: number = 30) => {
  const [currentTime, setCurrentTime] = useState<Date>(new Date());

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 60000); // Update every minute

    return () => clearInterval(interval);
  }, []);

  return useMemo(() => {
    const timeParts = targetTime.split(':');
    const targetHours = Number(timeParts[0]) || 0;
    const targetMinutes = Number(timeParts[1]) || 0;
    const targetDate = new Date();
    targetDate.setHours(targetHours, targetMinutes, 0, 0);

    const diffMinutes = Math.abs(currentTime.getTime() - targetDate.getTime()) / (1000 * 60);
    const isNear = diffMinutes <= tolerance;
    const isPast = currentTime > targetDate;
    const isUpcoming = !isPast && diffMinutes <= tolerance;

    return {
      isNear,
      isPast,
      isUpcoming,
      diffMinutes: Math.round(diffMinutes),
      targetFormatted: convertTo12Hour(targetTime).formatted,
      currentFormatted: getCurrentTime12Hour()
    };
  }, [targetTime, tolerance, currentTime]);
};
