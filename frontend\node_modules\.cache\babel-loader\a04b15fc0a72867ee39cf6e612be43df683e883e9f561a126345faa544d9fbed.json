{"ast": null, "code": "import _objectSpread from\"C:/Developer/Web Development/reedsoft/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";// Reedsoft color palette\nexport const COLORS={primary:'#003B46',secondary:'#07575B',accent:'#66A5AD',light:'#C4DFE6',// Additional colors for charts\nred:'#FF5733',orange:'#F0AD4E',blue:'#4A90E2',green:'#50E3C2',yellow:'#F5A623',purple:'#9013FE',teal:'#00BCD4',pink:'#E91E63'};/**\n * Wraps long labels for better chart display\n * @param label - The label to wrap\n * @param maxLength - Maximum length per line\n * @returns Array of wrapped lines or original label\n */export const wrapLabel=function(label){let maxLength=arguments.length>1&&arguments[1]!==undefined?arguments[1]:16;if(label.length<=maxLength)return[label];const words=label.split(' ');const lines=[];let currentLine='';for(const word of words){if((currentLine+' '+word).trim().length>maxLength){if(currentLine){lines.push(currentLine);currentLine=word;}else{lines.push(word);}}else{currentLine=(currentLine+' '+word).trim();}}if(currentLine){lines.push(currentLine);}return lines;};/**\n * Tooltip title callback for wrapped labels\n * @param tooltipItems - Chart.js tooltip items\n * @returns Formatted title string\n */export const tooltipTitleCallback=tooltipItems=>{const item=tooltipItems[0];let label=item.chart.data.labels[item.dataIndex];if(Array.isArray(label)){return label.join(' ');}return label;};/**\n * Creates default chart options with Reedsoft styling\n * @param type - Chart type\n * @param title - Chart title\n * @returns Chart options object\n */export const createChartOptions=(type,title)=>{const baseOptions={responsive:true,maintainAspectRatio:false,plugins:{legend:{position:'bottom',labels:{font:{size:12}}},tooltip:{callbacks:{title:tooltipTitleCallback}}}};if(title){baseOptions.plugins.title={display:true,text:title,font:{size:16,weight:'bold'},padding:{top:10,bottom:20}};}// Type-specific configurations\nswitch(type){case'bar':return _objectSpread(_objectSpread({},baseOptions),{},{scales:{x:{display:false},y:{ticks:{font:{size:14,weight:'bold'}}}},plugins:_objectSpread(_objectSpread({},baseOptions.plugins),{},{legend:{display:false}}),indexAxis:'y'});case'radar':return _objectSpread(_objectSpread({},baseOptions),{},{scales:{r:{angleLines:{color:'#ddd'},grid:{color:'#ddd'},pointLabels:{font:{size:12,weight:'bold'}},ticks:{display:false}}},plugins:_objectSpread(_objectSpread({},baseOptions.plugins),{},{legend:{display:false}})});default:return baseOptions;}};/**\n * Creates time allocation chart data\n * @returns Chart data for time allocation doughnut chart\n */export const createTimeAllocationData=()=>({labels:['Pillar 2: Japanese Study (Voice)','Pillar 1: Physical Fitness (Body)','Pillar 3: Practical Skills (Hands)','Pillar 4: Cognitive Fitness (Mind)'],datasets:[{label:'Weekly Hours',data:[20,7.5,10,7.5],backgroundColor:[COLORS.primary,COLORS.secondary,COLORS.accent,COLORS.light],borderColor:'#FFFFFF',borderWidth:3}]});/**\n * Creates key lifts chart data\n * @returns Chart data for key lifts bar chart\n */export const createKeyLiftsData=()=>({labels:['Squat','Deadlift','Bench Press','Overhead Press','Barbell Row'],datasets:[{label:'Functional Strength Rating',data:[95,100,85,80,90],backgroundColor:[COLORS.red,'#C70039','#900C3F','#581845',COLORS.blue],borderColor:'#fff',borderWidth:2,borderRadius:5}]});/**\n * Creates Anki chart data\n * @returns Chart data for Anki doughnut chart\n */export const createAnkiData=()=>({labels:['Daily Reviews','Learn New Words (20/day)','Long-Term Memory'],datasets:[{data:[50,25,25],backgroundColor:[COLORS.primary,COLORS.secondary,COLORS.accent],borderColor:'#fff',borderWidth:4}]});/**\n * Creates knowledge areas radar chart data\n * @returns Chart data for knowledge areas radar chart\n */export const createKnowledgeData=()=>({labels:['Hand Tools','Power Tools','Site Safety','Materials ID','Essential Skills'],datasets:[{label:'Study Focus',data:[8,7,10,6,9],backgroundColor:'rgba(217, 83, 79, 0.2)',borderColor:'#D9534F',pointBackgroundColor:'#D9534F',pointBorderColor:'#fff',pointHoverBackgroundColor:'#fff',pointHoverBorderColor:'#D9534F',borderWidth:2}]});/**\n * Creates mind workout pie chart data\n * @returns Chart data for mind workout pie chart\n */export const createMindWorkoutData=()=>({labels:['Active Reading (45 min)','Problem Solving (30 min)','Meditation (15 min)'],datasets:[{data:[50,33,17],backgroundColor:[COLORS.green,COLORS.yellow,COLORS.blue],borderColor:'#fff',borderWidth:4}]});", "map": {"version": 3, "names": ["COLORS", "primary", "secondary", "accent", "light", "red", "orange", "blue", "green", "yellow", "purple", "teal", "pink", "wrapLabel", "label", "max<PERSON><PERSON><PERSON>", "arguments", "length", "undefined", "words", "split", "lines", "currentLine", "word", "trim", "push", "tooltipTitleCallback", "tooltipItems", "item", "chart", "data", "labels", "dataIndex", "Array", "isArray", "join", "createChartOptions", "type", "title", "baseOptions", "responsive", "maintainAspectRatio", "plugins", "legend", "position", "font", "size", "tooltip", "callbacks", "display", "text", "weight", "padding", "top", "bottom", "_objectSpread", "scales", "x", "y", "ticks", "indexAxis", "r", "angleLines", "color", "grid", "point<PERSON><PERSON><PERSON>", "createTimeAllocationData", "datasets", "backgroundColor", "borderColor", "borderWidth", "createKeyLiftsData", "borderRadius", "createAnkiData", "createKnowledgeData", "pointBackgroundColor", "pointBorderColor", "pointHoverBackgroundColor", "pointHoverBorderColor", "createMindWorkoutData"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/utils/chartConfig.ts"], "sourcesContent": ["import { ChartData, ChartOptions, ColorPalette } from '../types';\n\n// Reedsoft color palette\nexport const COLORS: ColorPalette = {\n  primary: '#003B46',\n  secondary: '#07575B',\n  accent: '#66A5AD',\n  light: '#C4DFE6',\n  // Additional colors for charts\n  red: '#FF5733',\n  orange: '#F0AD4E',\n  blue: '#4A90E2',\n  green: '#50E3C2',\n  yellow: '#F5A623',\n  purple: '#9013FE',\n  teal: '#00BCD4',\n  pink: '#E91E63'\n};\n\n/**\n * Wraps long labels for better chart display\n * @param label - The label to wrap\n * @param maxLength - Maximum length per line\n * @returns Array of wrapped lines or original label\n */\nexport const wrapLabel = (label: string, maxLength: number = 16): string[] => {\n  if (label.length <= maxLength) return [label];\n  \n  const words = label.split(' ');\n  const lines: string[] = [];\n  let currentLine = '';\n  \n  for (const word of words) {\n    if ((currentLine + ' ' + word).trim().length > maxLength) {\n      if (currentLine) {\n        lines.push(currentLine);\n        currentLine = word;\n      } else {\n        lines.push(word);\n      }\n    } else {\n      currentLine = (currentLine + ' ' + word).trim();\n    }\n  }\n  \n  if (currentLine) {\n    lines.push(currentLine);\n  }\n  \n  return lines;\n};\n\n/**\n * Tooltip title callback for wrapped labels\n * @param tooltipItems - Chart.js tooltip items\n * @returns Formatted title string\n */\nexport const tooltipTitleCallback = (tooltipItems: any[]): string => {\n  const item = tooltipItems[0];\n  let label = item.chart.data.labels[item.dataIndex];\n  \n  if (Array.isArray(label)) {\n    return label.join(' ');\n  }\n  \n  return label;\n};\n\n/**\n * Creates default chart options with Reedsoft styling\n * @param type - Chart type\n * @param title - Chart title\n * @returns Chart options object\n */\nexport const createChartOptions = (\n  type: 'doughnut' | 'bar' | 'radar' | 'pie' | 'line',\n  title?: string\n): ChartOptions => {\n  const baseOptions: ChartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        position: 'bottom',\n        labels: {\n          font: { size: 12 }\n        }\n      },\n      tooltip: {\n        callbacks: {\n          title: tooltipTitleCallback\n        }\n      }\n    }\n  };\n\n  if (title) {\n    baseOptions.plugins!.title = {\n      display: true,\n      text: title,\n      font: { size: 16, weight: 'bold' },\n      padding: { top: 10, bottom: 20 }\n    };\n  }\n\n  // Type-specific configurations\n  switch (type) {\n    case 'bar':\n      return {\n        ...baseOptions,\n        scales: {\n          x: { display: false },\n          y: { \n            ticks: { \n              font: { size: 14, weight: 'bold' } \n            } \n          }\n        },\n        plugins: {\n          ...baseOptions.plugins,\n          legend: { display: false }\n        },\n        indexAxis: 'y'\n      };\n\n    case 'radar':\n      return {\n        ...baseOptions,\n        scales: {\n          r: {\n            angleLines: { color: '#ddd' },\n            grid: { color: '#ddd' },\n            pointLabels: { \n              font: { size: 12, weight: 'bold' } \n            },\n            ticks: { display: false }\n          }\n        },\n        plugins: {\n          ...baseOptions.plugins,\n          legend: { display: false }\n        }\n      };\n\n    default:\n      return baseOptions;\n  }\n};\n\n/**\n * Creates time allocation chart data\n * @returns Chart data for time allocation doughnut chart\n */\nexport const createTimeAllocationData = (): ChartData => ({\n  labels: [\n    'Pillar 2: Japanese Study (Voice)',\n    'Pillar 1: Physical Fitness (Body)',\n    'Pillar 3: Practical Skills (Hands)',\n    'Pillar 4: Cognitive Fitness (Mind)'\n  ],\n  datasets: [{\n    label: 'Weekly Hours',\n    data: [20, 7.5, 10, 7.5],\n    backgroundColor: [\n      COLORS.primary,\n      COLORS.secondary,\n      COLORS.accent,\n      COLORS.light\n    ],\n    borderColor: '#FFFFFF',\n    borderWidth: 3\n  }]\n});\n\n/**\n * Creates key lifts chart data\n * @returns Chart data for key lifts bar chart\n */\nexport const createKeyLiftsData = (): ChartData => ({\n  labels: ['Squat', 'Deadlift', 'Bench Press', 'Overhead Press', 'Barbell Row'],\n  datasets: [{\n    label: 'Functional Strength Rating',\n    data: [95, 100, 85, 80, 90],\n    backgroundColor: [COLORS.red, '#C70039', '#900C3F', '#581845', COLORS.blue],\n    borderColor: '#fff',\n    borderWidth: 2,\n    borderRadius: 5\n  }]\n});\n\n/**\n * Creates Anki chart data\n * @returns Chart data for Anki doughnut chart\n */\nexport const createAnkiData = (): ChartData => ({\n  labels: ['Daily Reviews', 'Learn New Words (20/day)', 'Long-Term Memory'],\n  datasets: [{\n    data: [50, 25, 25],\n    backgroundColor: [COLORS.primary, COLORS.secondary, COLORS.accent],\n    borderColor: '#fff',\n    borderWidth: 4\n  }]\n});\n\n/**\n * Creates knowledge areas radar chart data\n * @returns Chart data for knowledge areas radar chart\n */\nexport const createKnowledgeData = (): ChartData => ({\n  labels: ['Hand Tools', 'Power Tools', 'Site Safety', 'Materials ID', 'Essential Skills'],\n  datasets: [{\n    label: 'Study Focus',\n    data: [8, 7, 10, 6, 9],\n    backgroundColor: 'rgba(217, 83, 79, 0.2)',\n    borderColor: '#D9534F',\n    pointBackgroundColor: '#D9534F',\n    pointBorderColor: '#fff',\n    pointHoverBackgroundColor: '#fff',\n    pointHoverBorderColor: '#D9534F',\n    borderWidth: 2\n  }]\n});\n\n/**\n * Creates mind workout pie chart data\n * @returns Chart data for mind workout pie chart\n */\nexport const createMindWorkoutData = (): ChartData => ({\n  labels: ['Active Reading (45 min)', 'Problem Solving (30 min)', 'Meditation (15 min)'],\n  datasets: [{\n    data: [50, 33, 17],\n    backgroundColor: [COLORS.green, COLORS.yellow, COLORS.blue],\n    borderColor: '#fff',\n    borderWidth: 4\n  }]\n});\n"], "mappings": "0HAEA;AACA,MAAO,MAAM,CAAAA,MAAoB,CAAG,CAClCC,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,SAAS,CACpBC,MAAM,CAAE,SAAS,CACjBC,KAAK,CAAE,SAAS,CAChB;AACAC,GAAG,CAAE,SAAS,CACdC,MAAM,CAAE,SAAS,CACjBC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SAAS,CAChBC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,SAAS,CACjBC,IAAI,CAAE,SAAS,CACfC,IAAI,CAAE,SACR,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,SAAS,CAAG,QAAAA,CAACC,KAAa,CAAuC,IAArC,CAAAC,SAAiB,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CAC7D,GAAIF,KAAK,CAACG,MAAM,EAAIF,SAAS,CAAE,MAAO,CAACD,KAAK,CAAC,CAE7C,KAAM,CAAAK,KAAK,CAAGL,KAAK,CAACM,KAAK,CAAC,GAAG,CAAC,CAC9B,KAAM,CAAAC,KAAe,CAAG,EAAE,CAC1B,GAAI,CAAAC,WAAW,CAAG,EAAE,CAEpB,IAAK,KAAM,CAAAC,IAAI,GAAI,CAAAJ,KAAK,CAAE,CACxB,GAAI,CAACG,WAAW,CAAG,GAAG,CAAGC,IAAI,EAAEC,IAAI,CAAC,CAAC,CAACP,MAAM,CAAGF,SAAS,CAAE,CACxD,GAAIO,WAAW,CAAE,CACfD,KAAK,CAACI,IAAI,CAACH,WAAW,CAAC,CACvBA,WAAW,CAAGC,IAAI,CACpB,CAAC,IAAM,CACLF,KAAK,CAACI,IAAI,CAACF,IAAI,CAAC,CAClB,CACF,CAAC,IAAM,CACLD,WAAW,CAAG,CAACA,WAAW,CAAG,GAAG,CAAGC,IAAI,EAAEC,IAAI,CAAC,CAAC,CACjD,CACF,CAEA,GAAIF,WAAW,CAAE,CACfD,KAAK,CAACI,IAAI,CAACH,WAAW,CAAC,CACzB,CAEA,MAAO,CAAAD,KAAK,CACd,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAK,oBAAoB,CAAIC,YAAmB,EAAa,CACnE,KAAM,CAAAC,IAAI,CAAGD,YAAY,CAAC,CAAC,CAAC,CAC5B,GAAI,CAAAb,KAAK,CAAGc,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,MAAM,CAACH,IAAI,CAACI,SAAS,CAAC,CAElD,GAAIC,KAAK,CAACC,OAAO,CAACpB,KAAK,CAAC,CAAE,CACxB,MAAO,CAAAA,KAAK,CAACqB,IAAI,CAAC,GAAG,CAAC,CACxB,CAEA,MAAO,CAAArB,KAAK,CACd,CAAC,CAED;AACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAsB,kBAAkB,CAAGA,CAChCC,IAAmD,CACnDC,KAAc,GACG,CACjB,KAAM,CAAAC,WAAyB,CAAG,CAChCC,UAAU,CAAE,IAAI,CAChBC,mBAAmB,CAAE,KAAK,CAC1BC,OAAO,CAAE,CACPC,MAAM,CAAE,CACNC,QAAQ,CAAE,QAAQ,CAClBb,MAAM,CAAE,CACNc,IAAI,CAAE,CAAEC,IAAI,CAAE,EAAG,CACnB,CACF,CAAC,CACDC,OAAO,CAAE,CACPC,SAAS,CAAE,CACTV,KAAK,CAAEZ,oBACT,CACF,CACF,CACF,CAAC,CAED,GAAIY,KAAK,CAAE,CACTC,WAAW,CAACG,OAAO,CAAEJ,KAAK,CAAG,CAC3BW,OAAO,CAAE,IAAI,CACbC,IAAI,CAAEZ,KAAK,CACXO,IAAI,CAAE,CAAEC,IAAI,CAAE,EAAE,CAAEK,MAAM,CAAE,MAAO,CAAC,CAClCC,OAAO,CAAE,CAAEC,GAAG,CAAE,EAAE,CAAEC,MAAM,CAAE,EAAG,CACjC,CAAC,CACH,CAEA;AACA,OAAQjB,IAAI,EACV,IAAK,KAAK,CACR,OAAAkB,aAAA,CAAAA,aAAA,IACKhB,WAAW,MACdiB,MAAM,CAAE,CACNC,CAAC,CAAE,CAAER,OAAO,CAAE,KAAM,CAAC,CACrBS,CAAC,CAAE,CACDC,KAAK,CAAE,CACLd,IAAI,CAAE,CAAEC,IAAI,CAAE,EAAE,CAAEK,MAAM,CAAE,MAAO,CACnC,CACF,CACF,CAAC,CACDT,OAAO,CAAAa,aAAA,CAAAA,aAAA,IACFhB,WAAW,CAACG,OAAO,MACtBC,MAAM,CAAE,CAAEM,OAAO,CAAE,KAAM,CAAC,EAC3B,CACDW,SAAS,CAAE,GAAG,GAGlB,IAAK,OAAO,CACV,OAAAL,aAAA,CAAAA,aAAA,IACKhB,WAAW,MACdiB,MAAM,CAAE,CACNK,CAAC,CAAE,CACDC,UAAU,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAC,CAC7BC,IAAI,CAAE,CAAED,KAAK,CAAE,MAAO,CAAC,CACvBE,WAAW,CAAE,CACXpB,IAAI,CAAE,CAAEC,IAAI,CAAE,EAAE,CAAEK,MAAM,CAAE,MAAO,CACnC,CAAC,CACDQ,KAAK,CAAE,CAAEV,OAAO,CAAE,KAAM,CAC1B,CACF,CAAC,CACDP,OAAO,CAAAa,aAAA,CAAAA,aAAA,IACFhB,WAAW,CAACG,OAAO,MACtBC,MAAM,CAAE,CAAEM,OAAO,CAAE,KAAM,CAAC,EAC3B,GAGL,QACE,MAAO,CAAAV,WAAW,CACtB,CACF,CAAC,CAED;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAA2B,wBAAwB,CAAGA,CAAA,IAAkB,CACxDnC,MAAM,CAAE,CACN,kCAAkC,CAClC,mCAAmC,CACnC,oCAAoC,CACpC,oCAAoC,CACrC,CACDoC,QAAQ,CAAE,CAAC,CACTrD,KAAK,CAAE,cAAc,CACrBgB,IAAI,CAAE,CAAC,EAAE,CAAE,GAAG,CAAE,EAAE,CAAE,GAAG,CAAC,CACxBsC,eAAe,CAAE,CACfpE,MAAM,CAACC,OAAO,CACdD,MAAM,CAACE,SAAS,CAChBF,MAAM,CAACG,MAAM,CACbH,MAAM,CAACI,KAAK,CACb,CACDiE,WAAW,CAAE,SAAS,CACtBC,WAAW,CAAE,CACf,CAAC,CACH,CAAC,CAAC,CAEF;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,kBAAkB,CAAGA,CAAA,IAAkB,CAClDxC,MAAM,CAAE,CAAC,OAAO,CAAE,UAAU,CAAE,aAAa,CAAE,gBAAgB,CAAE,aAAa,CAAC,CAC7EoC,QAAQ,CAAE,CAAC,CACTrD,KAAK,CAAE,4BAA4B,CACnCgB,IAAI,CAAE,CAAC,EAAE,CAAE,GAAG,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAC3BsC,eAAe,CAAE,CAACpE,MAAM,CAACK,GAAG,CAAE,SAAS,CAAE,SAAS,CAAE,SAAS,CAAEL,MAAM,CAACO,IAAI,CAAC,CAC3E8D,WAAW,CAAE,MAAM,CACnBC,WAAW,CAAE,CAAC,CACdE,YAAY,CAAE,CAChB,CAAC,CACH,CAAC,CAAC,CAEF;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,cAAc,CAAGA,CAAA,IAAkB,CAC9C1C,MAAM,CAAE,CAAC,eAAe,CAAE,0BAA0B,CAAE,kBAAkB,CAAC,CACzEoC,QAAQ,CAAE,CAAC,CACTrC,IAAI,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAClBsC,eAAe,CAAE,CAACpE,MAAM,CAACC,OAAO,CAAED,MAAM,CAACE,SAAS,CAAEF,MAAM,CAACG,MAAM,CAAC,CAClEkE,WAAW,CAAE,MAAM,CACnBC,WAAW,CAAE,CACf,CAAC,CACH,CAAC,CAAC,CAEF;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAI,mBAAmB,CAAGA,CAAA,IAAkB,CACnD3C,MAAM,CAAE,CAAC,YAAY,CAAE,aAAa,CAAE,aAAa,CAAE,cAAc,CAAE,kBAAkB,CAAC,CACxFoC,QAAQ,CAAE,CAAC,CACTrD,KAAK,CAAE,aAAa,CACpBgB,IAAI,CAAE,CAAC,CAAC,CAAE,CAAC,CAAE,EAAE,CAAE,CAAC,CAAE,CAAC,CAAC,CACtBsC,eAAe,CAAE,wBAAwB,CACzCC,WAAW,CAAE,SAAS,CACtBM,oBAAoB,CAAE,SAAS,CAC/BC,gBAAgB,CAAE,MAAM,CACxBC,yBAAyB,CAAE,MAAM,CACjCC,qBAAqB,CAAE,SAAS,CAChCR,WAAW,CAAE,CACf,CAAC,CACH,CAAC,CAAC,CAEF;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAS,qBAAqB,CAAGA,CAAA,IAAkB,CACrDhD,MAAM,CAAE,CAAC,yBAAyB,CAAE,0BAA0B,CAAE,qBAAqB,CAAC,CACtFoC,QAAQ,CAAE,CAAC,CACTrC,IAAI,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAC,CAClBsC,eAAe,CAAE,CAACpE,MAAM,CAACQ,KAAK,CAAER,MAAM,CAACS,MAAM,CAAET,MAAM,CAACO,IAAI,CAAC,CAC3D8D,WAAW,CAAE,MAAM,CACnBC,WAAW,CAAE,CACf,CAAC,CACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}