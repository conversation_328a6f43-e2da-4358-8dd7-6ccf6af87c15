{"version": 3, "file": "static/css/main.fc905814.css", "mappings": "8FAAA,wCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,mCAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd,kCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,mCAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,gHAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,mGAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,4BAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,gCAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CAAd,4OAAc,CAAd,uBAAc,CAAd,eAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,cAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,kWAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,uBAAc,CAAd,0GAAc,CAAd,wGAAc,CAAd,mGAAc,CAAd,6BAAc,CAAd,kBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,gDAAc,CAAd,8CAAc,CAAd,kBAAc,CAAd,2CAAc,CAAd,6VAAc,CAAd,uQAAc,CAAd,sCAAc,CAAd,2BAAc,CAAd,2BAAc,CAAd,oBAAc,CAAd,gCAAc,CAAd,wBAAc,CAAd,qEAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,uBAAc,CAAd,oBAAc,CAAd,kCAAc,CAAd,0BAAc,CAAd,0EAAc,CAAd,eAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,oBAAc,CAAd,gBAAc,CAAd,aAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,WAAc,CAAd,SAAc,CAAd,gCAAc,CAAd,wBAAc,CAAd,wBAAc,CAAd,gBAAc,CAAd,qBAAc,CAAd,UAAc,CAAd,+BAAc,CAAd,+BAAc,CAAd,oFAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,uBAAc,CAAd,0GAAc,CAAd,wGAAc,CAAd,4GAAc,CAAd,kBAAc,CAAd,0EAAc,CAAd,uBAAc,CAAd,qDAAc,CAAd,wBAAc,CAAd,mTAAc,CAAd,6EAAc,CAAd,eAAc,EAAd,uMAAc,CAAd,0EAAc,CAAd,eAAc,EAAd,wKAAc,CAAd,2DAAc,CAAd,qPAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,wBAAc,CAAd,mFAAc,CAAd,eAAc,EAAd,8HAAc,CAAd,0FAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,eAAc,CAAd,cAAc,CAAd,iBAAc,CAAd,6BAAc,CAAd,8CAAc,CAAd,yCAAc,CACd,qBAAoB,CAApB,mDAAoB,EAApB,mDAAoB,EAApB,qDAAoB,EAApB,qDAAoB,EAApB,qDAAoB,EACpB,uBAAmB,CAAnB,uBAAmB,CAAnB,2BAAmB,CAAnB,gBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,yBAAmB,CAAnB,iBAAmB,CAAnB,0BAAmB,CAAnB,2BAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,uBAAmB,CAAnB,wBAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,uBAAmB,CAAnB,oBAAmB,CAAnB,kCAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,gBAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,8BAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,4BAAmB,CAAnB,wMAAmB,CAAnB,0CAAmB,EAAnB,+CAAmB,CAAnB,8BAAmB,CAAnB,0DAAmB,CAAnB,0DAAmB,CAAnB,+BAAmB,CAAnB,mCAAmB,CAAnB,gCAAmB,CAAnB,sCAAmB,CAAnB,8CAAmB,CAAnB,4CAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,gCAAmB,CAAnB,yCAAmB,CAAnB,+BAAmB,CAAnB,kCAAmB,CAAnB,+BAAmB,CAAnB,iCAAmB,CAAnB,8EAAmB,CAAnB,0CAAmB,CAAnB,6BAAmB,CAAnB,0BAAmB,CAAnB,iCAAmB,CAAnB,mCAAmB,CAAnB,iCAAmB,CAAnB,8BAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,qDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,qDAAmB,CAAnB,qCAAmB,CAAnB,oBAAmB,CAAnB,sDAAmB,CAAnB,8CAAmB,CAAnB,oBAAmB,CAAnB,oDAAmB,CAAnB,gDAAmB,CAAnB,oBAAmB,CAAnB,oDAAmB,CAAnB,uCAAmB,CAAnB,oBAAmB,CAAnB,qDAAmB,CAAnB,uCAAmB,CAAnB,oBAAmB,CAAnB,qDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,qCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,oCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,wBAAmB,CAAnB,oDAAmB,CAAnB,wCAAmB,CAAnB,wBAAmB,CAAnB,oDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,kCAAmB,CAAnB,6FAAmB,CAAnB,qFAAmB,CAAnB,gFAAmB,CAAnB,mEAAmB,CAAnB,iEAAmB,CAAnB,kFAAmB,CAAnB,+DAAmB,CAAnB,iEAAmB,CAAnB,8EAAmB,CAAnB,8DAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,oBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,0BAAmB,CAAnB,qBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,2CAAmB,CAAnB,2CAAmB,CAAnB,4CAAmB,CAAnB,8CAAmB,CAAnB,0CAAmB,CAAnB,0BAAmB,CAAnB,uBAAmB,CAAnB,wBAAmB,CAAnB,sBAAmB,CAAnB,0BAAmB,CAAnB,8BAAmB,CAAnB,8GAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,mBAAmB,CAAnB,2BAAmB,CAAnB,kBAAmB,CAAnB,wBAAmB,CAAnB,aAAmB,CAAnB,2BAAmB,CAAnB,aAAmB,CAAnB,2BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,2BAAmB,CAAnB,0BAAmB,CAAnB,2BAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,sCAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,qCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,yCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,wCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,0CAAmB,CAAnB,aAAmB,CAAnB,2CAAmB,CAAnB,4CAAmB,CAAnB,aAAmB,CAAnB,2CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,+BAAmB,CAAnB,UAAmB,CAAnB,+CAAmB,CAAnB,sBAAmB,CAAnB,sBAAmB,CAAnB,oFAAmB,CAAnB,iGAAmB,CAAnB,qEAAmB,CAAnB,kGAAmB,CAAnB,kFAAmB,CAAnB,+FAAmB,CAAnB,wLAAmB,CAAnB,6IAAmB,CAAnB,yFAAmB,CAAnB,uHAAmB,CAAnB,kDAAmB,CAAnB,0EAAmB,CAAnB,kDAAmB,CAInB,KAKE,kCAAmC,CACnC,iCAAkC,CAJlC,yIAEY,CAHZ,QAMF,CAEA,KACE,uEAEF,CAGA,SAOE,kBAAsB,CAEtB,QAAS,CANT,UAAW,CAEX,WAAY,CACZ,eAAgB,CAFhB,SAAU,CAHV,iBAAkB,CAOlB,kBAAmB,CANnB,SAQF,CAEA,0BAKE,cAAe,CADf,eAKF,CAGA,+BACE,UACE,+BACF,CAEA,eACE,oBACF,CAEA,iBACE,2BACF,CACF,CAGA,uCACE,iBAGE,kCAAqC,CACrC,qCAAuC,CAEvC,8BAAgC,CADhC,mCAEF,CACF,CArEA,2CAsEA,CAtEA,wBAsEA,CAtEA,wDAsEA,CAtEA,2CAsEA,CAtEA,wBAsEA,CAtEA,wDAsEA,CAtEA,iDAsEA,CAtEA,wBAsEA,CAtEA,wDAsEA,CAtEA,mDAsEA,CAtEA,wBAsEA,CAtEA,oDAsEA,CAtEA,qDAsEA,CAtEA,wBAsEA,CAtEA,oDAsEA,CAtEA,uDAsEA,CAtEA,aAsEA,CAtEA,2CAsEA,CAtEA,yDAsEA,CAtEA,aAsEA,CAtEA,2CAsEA,CAtEA,4CAsEA,CAtEA,UAsEA,CAtEA,+CAsEA,CAtEA,kGAsEA,CAtEA,kGAsEA,CAtEA,+CAsEA,CAtEA,kGAsEA,CAtEA,mCAsEA,CAtEA,WAsEA,CAtEA,QAsEA,CAtEA,gBAsEA,CAtEA,SAsEA,CAtEA,eAsEA,CAtEA,6BAsEA,CAtEA,wCAsEA,CAtEA,8BAsEA,CAtEA,4BAsEA,CAtEA,wDAsEA,CAtEA,kBAsEA,CAtEA,+HAsEA,CAtEA,wGAsEA,CAtEA,uEAsEA,CAtEA,wFAsEA,CAtEA,+CAsEA,CAtEA,wDAsEA,CAtEA,+CAsEA,CAtEA,sDAsEA,CAtEA,8CAsEA,CAtEA,uDAsEA,CAtEA,uDAsEA,CAtEA,qDAsEA,CAtEA,4CAsEA,CAtEA,yDAsEA,CAtEA,sDAsEA,CAtEA,4DAsEA,CAtEA,aAsEA,CAtEA,8CAsEA,CAtEA,4DAsEA,CAtEA,aAsEA,CAtEA,4CAsEA,CAtEA,2DAsEA,CAtEA,aAsEA,CAtEA,6CAsEA,CAtEA,oEAsEA,CAtEA,aAsEA,CAtEA,2CAsEA,CAtEA,yDAsEA,EAtEA,kEAsEA,CAtEA,yCAsEA,CAtEA,wBAsEA,CAtEA,sBAsEA,CAtEA,wBAsEA,CAtEA,sBAsEA,CAtEA,8DAsEA,CAtEA,8DAsEA,CAtEA,8DAsEA,CAtEA,8DAsEA,CAtEA,gCAsEA,CAtEA,mEAsEA,CAtEA,wGAsEA,CAtEA,mEAsEA,CAtEA,wGAsEA,CAtEA,mEAsEA,CAtEA,sGAsEA,CAtEA,sBAsEA,CAtEA,qBAsEA,CAtEA,8BAsEA,CAtEA,gBAsEA,CAtEA,+BAsEA,CAtEA,kBAsEA,CAtEA,4BAsEA,CAtEA,aAsEA,CAtEA,+BAsEA,CAtEA,aAsEA,CAtEA,8BAsEA,CAtEA,aAsEA,CAtEA,8BAsEA,CAtEA,mBAsEA,CAtEA,8BAsEA,CAtEA,mBAsEA,EAtEA,wFAsEA,ECtEA,KACE,iBACF,CAEA,UACE,aAAc,CACd,mBACF,CAEA,8CACE,UACE,2CACF,CACF,CAEA,YAKE,kBAAmB,CAJnB,wBAAyB,CAOzB,UAAY,CALZ,YAAa,CACb,qBAAsB,CAGtB,4BAA6B,CAD7B,sBAAuB,CAJvB,gBAOF,CAEA,UACE,aACF,CAEA,yBACE,GACE,sBACF,CACA,GACE,uBACF,CACF", "sources": ["index.css", "App.css"], "sourcesContent": ["@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700;900&display=swap');\n\nbody {\n  margin: 0;\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n\n/* Accessibility utilities */\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n}\n\n.focus\\:not-sr-only:focus {\n  position: static;\n  width: auto;\n  height: auto;\n  padding: inherit;\n  margin: inherit;\n  overflow: visible;\n  clip: auto;\n  white-space: normal;\n}\n\n/* High contrast mode support */\n@media (prefers-contrast: high) {\n  .bg-white {\n    background-color: white !important;\n  }\n\n  .text-gray-600 {\n    color: black !important;\n  }\n\n  .border-gray-200 {\n    border-color: black !important;\n  }\n}\n\n/* Reduced motion support */\n@media (prefers-reduced-motion: reduce) {\n  *,\n  *::before,\n  *::after {\n    animation-duration: 0.01ms !important;\n    animation-iteration-count: 1 !important;\n    transition-duration: 0.01ms !important;\n    scroll-behavior: auto !important;\n  }\n}\n", ".App {\n  text-align: center;\n}\n\n.App-logo {\n  height: 40vmin;\n  pointer-events: none;\n}\n\n@media (prefers-reduced-motion: no-preference) {\n  .App-logo {\n    animation: App-logo-spin infinite 20s linear;\n  }\n}\n\n.App-header {\n  background-color: #282c34;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  font-size: calc(10px + 2vmin);\n  color: white;\n}\n\n.App-link {\n  color: #61dafb;\n}\n\n@keyframes App-logo-spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n"], "names": [], "sourceRoot": ""}