import React from 'react';
import { Link } from 'react-router-dom';
import { Pie } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  Title
} from 'chart.js';
import { useChartData } from '../hooks/useChartData';

// Register Chart.js components
ChartJS.register(ArcE<PERSON>, Tooltip, Legend, Title);

const Pillar4: React.FC = () => {
  const { data: mindWorkoutData, options: mindWorkoutOptions, loading } = useChartData('mindWorkout');

  return (
    <div className="bg-blue-50 text-gray-800 min-h-screen">
      <div className="container mx-auto p-4 md:p-8">
        <header className="text-center mb-10">
          <div className="inline-block bg-blue-500 text-white rounded-full px-5 py-2 mb-4 text-sm font-bold">
            PILLAR 4: THE MIND
          </div>
          <h1 className="text-4xl md:text-5xl font-black text-slate-700 tracking-tight">
            Sharpening Your Greatest Tool
          </h1>
          <p className="text-lg text-gray-600 mt-2">
            Rewire your brain for deep focus and accelerated learning.
          </p>
        </header>

        {/* Core Practices Section */}
        <section id="core-practices" className="mb-12 bg-white p-6 rounded-lg shadow-lg">
          <h2 className="text-2xl font-bold text-center mb-6 text-slate-700">The Three Core Practices</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
            <div className="p-4">
              <div className="text-6xl">🧘‍♂️</div>
              <h3 className="font-bold text-lg mt-3 text-blue-500">Meditation</h3>
              <p className="text-sm mt-1">Train your attention span. 10 minutes daily with an app like Headspace is non-negotiable.</p>
            </div>
            <div className="p-4">
              <div className="text-6xl">📚</div>
              <h3 className="font-bold text-lg mt-3 text-emerald-500">Active Reading</h3>
              <p className="text-sm mt-1">Don't just read, process. Use the Feynman Technique to truly understand and retain information.</p>
            </div>
            <div className="p-4">
              <div className="text-6xl">🧩</div>
              <h3 className="font-bold text-lg mt-3 text-amber-500">Problem Solving</h3>
              <p className="text-sm mt-1">Engage in daily logic puzzles (Sudoku, etc.) to keep your reasoning skills sharp and flexible.</p>
            </div>
          </div>
        </section>

        {/* Feynman Technique and Mind Workout Section */}
        <section className="grid grid-cols-1 md:grid-cols-5 gap-8 mb-12 items-center">
          <div className="md:col-span-3 bg-white p-6 rounded-lg shadow-lg">
            <h2 className="text-2xl font-bold text-center mb-6 text-slate-700">The Feynman Learning Technique</h2>
            <div className="space-y-4">
              <div className="flex items-start space-x-4 p-3 bg-gray-50 rounded-lg">
                <div className="text-3xl font-bold text-blue-500">1.</div>
                <div><strong className="text-gray-800">Choose a Concept.</strong> Pick a topic you are learning (e.g., a grammar point, how a tool works).</div>
              </div>
              <div className="flex items-start space-x-4 p-3 bg-gray-50 rounded-lg">
                <div className="text-3xl font-bold text-blue-500">2.</div>
                <div><strong className="text-gray-800">Teach it Simply.</strong> Explain it out loud, in the simplest terms possible, as if to a child.</div>
              </div>
              <div className="flex items-start space-x-4 p-3 bg-gray-50 rounded-lg">
                <div className="text-3xl font-bold text-blue-500">3.</div>
                <div><strong className="text-gray-800">Identify Gaps.</strong> When you get stuck or use complex language, you've found a weak spot in your understanding.</div>
              </div>
              <div className="flex items-start space-x-4 p-3 bg-gray-50 rounded-lg">
                <div className="text-3xl font-bold text-blue-500">4.</div>
                <div><strong className="text-gray-800">Review & Simplify.</strong> Go back to the source material to fill the gap, then refine your simple explanation.</div>
              </div>
            </div>
          </div>

          <div className="md:col-span-2 bg-white p-6 rounded-lg shadow-lg">
            <h2 className="text-2xl font-bold text-center mb-4 text-slate-700">Daily Cognitive Workout</h2>
            <p className="text-sm text-center text-gray-600 mb-4">Your 90-minute daily block dedicated to mental fitness.</p>
            <div className="relative w-full max-w-sm mx-auto h-80">
              {loading ? (
                <div className="flex items-center justify-center h-full">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-slate-700"></div>
                </div>
              ) : mindWorkoutData && mindWorkoutOptions ? (
                <Pie data={mindWorkoutData} options={mindWorkoutOptions as any} />
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  Chart loading...
                </div>
              )}
            </div>
          </div>
        </section>

        {/* Navigation */}
        <div className="flex justify-between items-center">
          <Link
            to="/japan/pillar-3"
            className="bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors"
          >
            ← Previous: Practical Skills
          </Link>
          <Link
            to="/japan"
            className="bg-reedsoft-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors"
          >
            Back to Dashboard →
          </Link>
        </div>

        <footer className="text-center mt-10 pt-6 border-t border-gray-300">
          <p className="text-gray-700 font-semibold">
            The quality of your work is determined by the quality of your focus.
          </p>
        </footer>
      </div>
    </div>
  );
};

export default Pillar4;
