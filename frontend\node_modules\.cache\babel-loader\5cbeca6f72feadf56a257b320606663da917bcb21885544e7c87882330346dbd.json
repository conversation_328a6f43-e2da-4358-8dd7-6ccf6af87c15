{"ast": null, "code": "import React,{useMemo}from'react';import{<PERSON>}from'react-router-dom';import{Doughnut}from'react-chartjs-2';import{Chart as ChartJS,ArcElement,Tooltip,Legend,Title}from'chart.js';import{useChartData}from'../hooks/useChartData';import{formatTimeInText}from'../utils/timeFormat';// Register Chart.js components\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";ChartJS.register(ArcElement,Tooltip,Legend,Title);const JapanPlan=()=>{const{data:timeAllocationData,options:timeAllocationOptions,loading}=useChartData('timeAllocation');// Time blocks with 24-hour format that will be converted\nconst timeBlocks=useMemo(()=>[{time:\"06:00 - 07:30\",title:\"💪 Pillar 1: Physical Fitness\",what:\"Full-body strength training (3x/week) or Cardio & Core (2x/week).\",why:\"To build the raw strength and endurance needed for physical jobs and to boost mental clarity for the day.\",how:\"Follow a structured program like StrongLifts 5x5. For cardio, use HIIT or jogging. Use apps like Jefit to track lifts.\"},{time:\"09:00 - 12:00\",title:\"🗣️ Pillar 2: Japanese Core Study\",what:\"Deep, focused study of Japanese grammar and sentence structure.\",why:\"Grammar is the skeleton of the language. Vocabulary is useless without it. This is the hardest but most important part.\",how:\"Use a textbook (Genki I & II). Complete exercises with a pen and paper. No phone, no distractions. One chapter every 4-5 days.\"},{time:\"13:00 - 15:00\",title:\"🛠️ Pillar 3: Practical Skills Study\",what:\"Theoretical study of tools, safety protocols, materials, and basic trade knowledge.\",why:\"To become a quick learner on a job site. Knowing the 'what' and 'why' makes the 'how' much easier to pick up.\",how:\"Watch curated YouTube channels (e.g., Essential Craftsman). Take notes. Create a \\\"knowledge base\\\" in a notebook.\"},{time:\"15:00 - 16:30\",title:\"🧠 Pillar 4: Cognitive Fitness\",what:\"Active reading of non-fiction, problem-solving puzzles, and meditation.\",why:\"To sharpen your focus, improve memory retention, and train your brain to learn efficiently.\",how:\"Read a chapter, then summarize it (Feynman Technique). Use apps like Headspace for meditation and Lumosity for logic puzzles.\"},{time:\"17:00 - 18:00\",title:\"🗣️ Pillar 2: Japanese Vocabulary Drill\",what:\"Spaced Repetition System (SRS) for vocabulary.\",why:\"To efficiently memorize thousands of words and burn them into your long-term memory.\",how:\"Use the Anki app. Do your reviews and learn 20 new words every single day without fail.\"},{time:\"19:30 - 21:00\",title:\"🗣️ Pillar 2: Japanese Immersion\",what:\"Actively listen to and speak Japanese.\",why:\"To connect your grammar and vocabulary knowledge to the sound and rhythm of the real language.\",how:\"Listen to beginner podcasts or anime audio. Starting Month 3, use HelloTalk to speak with native speakers.\"}],[]);return/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-100 text-gray-800 min-h-screen\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto p-4 md:p-8\",children:[/*#__PURE__*/_jsxs(\"header\",{className:\"text-center mb-12\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-4xl md:text-6xl font-black text-reedsoft-primary tracking-tight\",children:\"PROJECT: JACK OF ALL TRADES\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg md:text-xl text-reedsoft-secondary mt-2\",children:\"Your 16-Week Blueprint for Total Transformation\"})]}),/*#__PURE__*/_jsxs(\"section\",{id:\"daily-kata\",className:\"mb-12\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl font-bold text-center mb-8 text-reedsoft-primary\",children:\"Your Daily \\\"Kata\\\" (Routine)\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white p-6 rounded-lg shadow-lg mb-8\",children:[/*#__PURE__*/_jsxs(\"p\",{className:\"text-center text-gray-600 mb-6\",children:[\"A consistent daily routine, or \",/*#__PURE__*/_jsx(\"em\",{children:\"kata\"}),\" (\\u5F62)\\u2014a Japanese term for a choreographed pattern of movements\\u2014is essential for disciplined practice. This template integrates the Pomodoro Technique for maximum concentration and retention.\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"overflow-x-auto\",children:/*#__PURE__*/_jsxs(\"table\",{className:\"w-full text-sm\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\"bg-gray-50\",children:[/*#__PURE__*/_jsx(\"th\",{className:\"text-left p-3 font-bold text-reedsoft-primary\",children:\"Time\"}),/*#__PURE__*/_jsx(\"th\",{className:\"text-left p-3 font-bold text-reedsoft-primary\",children:\"Activity\"}),/*#__PURE__*/_jsx(\"th\",{className:\"text-left p-3 font-bold text-reedsoft-primary\",children:\"Focus/Tools\"})]})}),/*#__PURE__*/_jsxs(\"tbody\",{children:[/*#__PURE__*/_jsxs(\"tr\",{className:\"border-b\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"p-3 font-semibold text-reedsoft-secondary\",children:\"6:00 - 6:45 AM\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-3\",children:\"Physical Fitness\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-3\",children:\"Full-body circuit, HIIT, or functional strength workout.\"})]}),/*#__PURE__*/_jsxs(\"tr\",{className:\"border-b bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"p-3 font-semibold text-reedsoft-secondary\",children:\"6:45 - 7:30 AM\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-3\",children:\"Morning Prep\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-3\",children:\"Shower, breakfast, review daily goals.\"})]}),/*#__PURE__*/_jsxs(\"tr\",{className:\"border-b\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"p-3 font-semibold text-reedsoft-secondary\",children:\"7:30 - 9:30 AM\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-3\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"Japanese Study Block 1\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"p-3\",children:\"4x Pomodoro sessions (25 min study / 5 min break). Focus on new grammar/kanji. Tools: Textbook, WaniKani.\"})]}),/*#__PURE__*/_jsxs(\"tr\",{className:\"border-b bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"p-3 font-semibold text-reedsoft-secondary\",children:\"9:30 - 10:00 AM\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-3\",children:\"Extended Break\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-3\",children:\"Stretch, light snack, step away from study area.\"})]}),/*#__PURE__*/_jsxs(\"tr\",{className:\"border-b\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"p-3 font-semibold text-reedsoft-secondary\",children:\"10:00 AM - 12:00 PM\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-3\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"Japanese Study Block 2\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"p-3\",children:\"4x Pomodoro sessions. Focus on vocabulary reinforcement and reading practice. Tools: Anki, NHK News Easy.\"})]}),/*#__PURE__*/_jsxs(\"tr\",{className:\"border-b bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"p-3 font-semibold text-reedsoft-secondary\",children:\"12:00 - 1:00 PM\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-3\",children:\"Lunch Break\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-3\",children:\"Nutritious meal, mental rest.\"})]}),/*#__PURE__*/_jsxs(\"tr\",{className:\"border-b\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"p-3 font-semibold text-reedsoft-secondary\",children:\"1:00 - 2:00 PM\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-3\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"Skills Development Block\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"p-3\",children:\"2x Pomodoro sessions. Work on online trade course or cognitive training. Tools: Alison, CogniFit/Lumosity.\"})]}),/*#__PURE__*/_jsxs(\"tr\",{className:\"border-b bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"p-3 font-semibold text-reedsoft-secondary\",children:\"2:00 - 3:00 PM\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-3\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"Japanese Study Block 3\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"p-3\",children:\"2x Pomodoro sessions. Focus on listening or speaking practice. Tools: Podcasts, iTalki prep, shadowing.\"})]}),/*#__PURE__*/_jsxs(\"tr\",{className:\"border-b\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"p-3 font-semibold text-reedsoft-secondary\",children:\"3:00 - 3:30 PM\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-3\",children:\"Afternoon Break\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-3\",children:\"Walk, hydrate, short rest.\"})]}),/*#__PURE__*/_jsxs(\"tr\",{className:\"border-b bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"p-3 font-semibold text-reedsoft-secondary\",children:\"3:30 - 5:00 PM\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-3\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"Career Prep Block\"})}),/*#__PURE__*/_jsx(\"td\",{className:\"p-3\",children:\"Work on resumes, research companies, prepare for interviews, or organize visa documents.\"})]}),/*#__PURE__*/_jsxs(\"tr\",{className:\"border-b\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"p-3 font-semibold text-reedsoft-secondary\",children:\"5:00 PM onward\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-3\",children:\"Free Time & Review\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-3\",children:\"Dinner, hobbies, relaxation. A light 15-30 minute review of the day's Japanese lessons before bed.\"})]})]})]})})]})]}),/*#__PURE__*/_jsxs(\"section\",{id:\"missions\",className:\"mb-12\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl font-bold text-center mb-8 text-reedsoft-primary\",children:\"Your 16-Week Transformation Journey\"}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-white p-6 rounded-lg shadow-lg\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-4 items-center text-center gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"p-4\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-bold text-reedsoft-secondary\",children:\"Month 1 (Weeks 1-4)\"}),/*#__PURE__*/_jsx(\"p\",{className:\"font-semibold text-gray-700\",children:\"Building the Foundation\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm mt-1 text-gray-600\",children:\"\\u57FA\\u790E\\u56FA\\u3081 (Kiso-gatame)\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm mt-2\",children:\"Master Hiragana & Katakana. Start Genki I. Begin WaniKani. 3x weekly bodyweight circuits. Daily cognitive training.\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"hidden md:block text-2xl text-reedsoft-accent\",children:\"\\u2192\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-bold text-reedsoft-secondary\",children:\"Month 2 (Weeks 5-8)\"}),/*#__PURE__*/_jsx(\"p\",{className:\"font-semibold text-gray-700\",children:\"Accelerating Progress\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm mt-1 text-gray-600\",children:\"\\u9032\\u6357\\u52A0\\u901F (Shinchoku-kasoku)\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm mt-2\",children:\"Complete N5 grammar. Learn first 100 N5 Kanji. Increase workout intensity. Start online trade courses.\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"hidden md:block text-2xl text-reedsoft-accent\",children:\"\\u2192\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-bold text-reedsoft-secondary\",children:\"Month 3 (Weeks 9-12)\"}),/*#__PURE__*/_jsx(\"p\",{className:\"font-semibold text-gray-700\",children:\"Skill Integration\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm mt-1 text-gray-600\",children:\"\\u30B9\\u30AD\\u30EB\\u7D71\\u5408 (Sukiru-t\\u014Dg\\u014D)\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm mt-2\",children:\"Start N4 prep. Begin iTalki conversation practice. Introduce HIIT elements. Draft Japanese resumes.\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"hidden md:block text-2xl text-reedsoft-accent\",children:\"\\u2192\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-4\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-bold text-reedsoft-secondary\",children:\"Month 4 (Weeks 13-16)\"}),/*#__PURE__*/_jsx(\"p\",{className:\"font-semibold text-gray-700\",children:\"Final Preparation\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm mt-1 text-gray-600\",children:\"\\u6700\\u7D42\\u6E96\\u5099 (Saish\\u016B-junbi)\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm mt-2\",children:\"Weekly N4 mock tests. Finalize visa documents. Practice interview questions. Maintain peak fitness.\"})]})]})})]}),/*#__PURE__*/_jsxs(\"section\",{id:\"training-matrix\",className:\"mb-12\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl font-bold text-center mb-8 text-reedsoft-primary\",children:\"16-Week At-A-Glance Training Matrix\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white p-6 rounded-lg shadow-lg overflow-x-auto\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-center text-gray-600 mb-6\",children:\"A clear path, turning the four-month goal into manageable weekly sprints.\"}),/*#__PURE__*/_jsxs(\"table\",{className:\"w-full text-xs md:text-sm\",children:[/*#__PURE__*/_jsx(\"thead\",{children:/*#__PURE__*/_jsxs(\"tr\",{className:\"bg-gray-50\",children:[/*#__PURE__*/_jsx(\"th\",{className:\"text-left p-2 font-bold text-reedsoft-primary\",children:\"Week\"}),/*#__PURE__*/_jsx(\"th\",{className:\"text-left p-2 font-bold text-reedsoft-primary\",children:\"Language Focus\"}),/*#__PURE__*/_jsx(\"th\",{className:\"text-left p-2 font-bold text-reedsoft-primary\",children:\"Physical Fitness Goal\"}),/*#__PURE__*/_jsx(\"th\",{className:\"text-left p-2 font-bold text-reedsoft-primary\",children:\"Skills Focus\"}),/*#__PURE__*/_jsx(\"th\",{className:\"text-left p-2 font-bold text-reedsoft-primary\",children:\"Career Prep Task\"})]})}),/*#__PURE__*/_jsxs(\"tbody\",{children:[/*#__PURE__*/_jsxs(\"tr\",{className:\"border-b\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"p-2 font-semibold text-reedsoft-secondary\",children:\"1-2\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Master Hiragana & Katakana. Start Genki I. Begin WaniKani.\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"3x weekly bodyweight circuits. Focus on form.\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Daily cognitive training (15 min). Research trade courses.\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Research SSW visa requirements. Identify recruitment agencies.\"})]}),/*#__PURE__*/_jsxs(\"tr\",{className:\"border-b bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"p-2 font-semibold text-reedsoft-secondary\",children:\"3-4\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Complete first chapters. Daily Anki (vocab/grammar).\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Maintain 3x weekly workouts. Increase reps slightly.\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Start first online trade course. Continue cognitive training.\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Draft initial contact email for recruitment agencies.\"})]}),/*#__PURE__*/_jsxs(\"tr\",{className:\"border-b\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"p-2 font-semibold text-reedsoft-secondary\",children:\"5-6\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Progress through textbook (N5 grammar). Learn first 50 N5 Kanji.\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Increase workout intensity. Add 4th weekly session.\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Continue first course. Focus on weak cognitive areas.\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Draft first version of Rirekisho and Shokumu Keirekisho.\"})]}),/*#__PURE__*/_jsxs(\"tr\",{className:\"border-b bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"p-2 font-semibold text-reedsoft-secondary\",children:\"7-8\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Complete N5 Kanji list (~100). Begin beginner listening podcasts.\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Introduce HIIT elements into 1-2 weekly workouts.\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Complete first course. Start second course (Intro to Logistics).\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Contact recruitment agencies. Refine resumes based on feedback.\"})]}),/*#__PURE__*/_jsxs(\"tr\",{className:\"border-b\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"p-2 font-semibold text-reedsoft-secondary\",children:\"9-10\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Finish primary textbook. Start N4 prep book (Shin-Kanzen Master).\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Focus on functional strength. Maintain 4x weekly workouts.\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Continue second course. Apply learnings to resume drafts.\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Research specific Japanese companies in target sectors.\"})]}),/*#__PURE__*/_jsxs(\"tr\",{className:\"border-b bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"p-2 font-semibold text-reedsoft-secondary\",children:\"11-12\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Learn first 100 N4 Kanji. Start weekly iTalki conversation practice.\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Maintain HIIT and strength focus. Ensure proper recovery.\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Complete second course. Summarize key skills learned.\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Practice interview questions, focusing on cultural etiquette.\"})]}),/*#__PURE__*/_jsxs(\"tr\",{className:\"border-b\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"p-2 font-semibold text-reedsoft-secondary\",children:\"13-14\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Intensive N4 grammar/vocab review. Take first full N4 mock test.\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Maintain fitness with 3-4 workouts. Focus on injury prevention.\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Maintenance cognitive training (10 min/day).\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Finalize resumes. Begin gathering SSW application documents.\"})]}),/*#__PURE__*/_jsxs(\"tr\",{className:\"border-b bg-gray-50\",children:[/*#__PURE__*/_jsx(\"td\",{className:\"p-2 font-semibold text-reedsoft-secondary\",children:\"15-16\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Take weekly timed N4 mock tests. Drill weak areas identified.\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Taper intensity slightly to ensure body is rested and strong.\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Review notes from online trade courses.\"}),/*#__PURE__*/_jsx(\"td\",{className:\"p-2\",children:\"Conduct mock interviews. Have all visa documents ready.\"})]})]})]})]})]}),/*#__PURE__*/_jsxs(\"section\",{id:\"daily-grind\",className:\"mb-12\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl font-bold text-center mb-8 text-reedsoft-primary\",children:\"The Daily Grind: Weekday Blueprint\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"bg-white p-6 rounded-lg shadow-lg\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-center text-gray-600 mb-4\",children:\"This is your core schedule, Monday to Friday. Each block is non-negotiable. It is designed for maximum efficiency in skill acquisition.\"})}),timeBlocks.map((block,index)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white p-5 rounded-lg shadow-md\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"font-bold text-lg text-reedsoft-primary\",children:[formatTimeInText(block.time),\" | \",block.title]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mt-2 text-sm\",children:[/*#__PURE__*/_jsx(\"strong\",{className:\"text-reedsoft-secondary\",children:\"WHAT:\"}),\" \",block.what]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mt-1 text-sm\",children:[/*#__PURE__*/_jsx(\"strong\",{className:\"text-reedsoft-secondary\",children:\"WHY:\"}),\" \",block.why]}),/*#__PURE__*/_jsxs(\"p\",{className:\"mt-1 text-sm\",children:[/*#__PURE__*/_jsx(\"strong\",{className:\"text-reedsoft-secondary\",children:\"HOW:\"}),\" \",block.how]})]},index))]})]}),/*#__PURE__*/_jsx(\"section\",{id:\"weekend-plan\",className:\"mb-12\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 gap-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white p-6 rounded-lg shadow-lg\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-center mb-4 text-reedsoft-primary\",children:\"Weekly Time Allocation\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600 mb-4 text-center\",children:\"Your approximate 40+ hours of active development each week, broken down by pillar.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"relative w-full max-w-md mx-auto h-96\",children:loading?/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-full\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-b-2 border-reedsoft-primary\"})}):timeAllocationData&&timeAllocationOptions?/*#__PURE__*/_jsx(Doughnut,{data:timeAllocationData,options:timeAllocationOptions}):/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-full text-gray-500\",children:\"Chart loading...\"})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white p-6 rounded-lg shadow-lg\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-center mb-4 text-reedsoft-primary\",children:\"The Weekend Shift\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600 mb-4 text-center\",children:\"Weekends are for review, practical application, and strategic recovery. The pace changes, but the work continues.\"}),/*#__PURE__*/_jsxs(\"ul\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"li\",{className:\"p-4 bg-cyan-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-bold text-teal-800\",children:\"Saturday Morning: Review & Consolidate\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm\",children:\"Review all Japanese lessons and Anki cards from the week. Perform one physical fitness session.\"})]}),/*#__PURE__*/_jsxs(\"li\",{className:\"p-4 bg-cyan-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-bold text-teal-800\",children:\"Saturday Afternoon: Hands-On Project\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm\",children:\"Apply your practical skills. Build a small shelf, practice knots for 30 minutes, disassemble and reassemble an old appliance. Turn theory into action.\"})]}),/*#__PURE__*/_jsxs(\"li\",{className:\"p-4 bg-cyan-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-bold text-teal-800\",children:\"Sunday Morning: Active Recovery\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm\",children:\"Go for a long walk or do a deep stretching routine. Listen to Japanese music or a podcast. Let your body heal.\"})]}),/*#__PURE__*/_jsxs(\"li\",{className:\"p-4 bg-cyan-50 rounded-lg\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"font-bold text-teal-800\",children:\"Sunday Afternoon: Strategize & Recharge\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm\",children:\"Plan the week ahead: schedule workouts, set study goals. Then, completely disconnect. Read a novel, watch a movie for fun. Recharge your mind for the week ahead.\"})]})]})]})]})}),/*#__PURE__*/_jsxs(\"section\",{className:\"mb-12\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl font-bold text-center mb-8 text-reedsoft-primary\",children:\"Explore Each Pillar\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",children:[/*#__PURE__*/_jsxs(Link,{to:\"/japan/pillar-1\",className:\"bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-l-4 border-red-500 group\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-4xl mb-3\",children:\"\\uD83D\\uDCAA\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-bold text-gray-800 mb-2 group-hover:text-red-500 transition-colors\",children:\"Pillar 1: The Body\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 text-sm\",children:\"Physical Fitness & Strength Training\"})]}),/*#__PURE__*/_jsxs(Link,{to:\"/japan/pillar-2\",className:\"bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-l-4 border-reedsoft-primary group\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-4xl mb-3\",children:\"\\uD83D\\uDDE3\\uFE0F\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-bold text-gray-800 mb-2 group-hover:text-reedsoft-primary transition-colors\",children:\"Pillar 2: The Voice\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 text-sm\",children:\"Japanese Language Mastery\"})]}),/*#__PURE__*/_jsxs(Link,{to:\"/japan/pillar-3\",className:\"bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-l-4 border-gray-600 group\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-4xl mb-3\",children:\"\\uD83D\\uDEE0\\uFE0F\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-bold text-gray-800 mb-2 group-hover:text-gray-600 transition-colors\",children:\"Pillar 3: The Hands\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 text-sm\",children:\"Practical Skills & Trade Knowledge\"})]}),/*#__PURE__*/_jsxs(Link,{to:\"/japan/pillar-4\",className:\"bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-l-4 border-blue-500 group\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-4xl mb-3\",children:\"\\uD83E\\uDDE0\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-bold text-gray-800 mb-2 group-hover:text-blue-500 transition-colors\",children:\"Pillar 4: The Mind\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 text-sm\",children:\"Cognitive Fitness & Learning\"})]})]})]}),/*#__PURE__*/_jsxs(\"footer\",{className:\"text-center mt-12 pt-8 border-t border-gray-300\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Discipline today is freedom tomorrow. Execute the plan.\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500 mt-2\",children:\"\\xA9 2025 Project Reboot Master Plan\"})]})]})});};export default JapanPlan;", "map": {"version": 3, "names": ["React", "useMemo", "Link", "Doughnut", "Chart", "ChartJS", "ArcElement", "<PERSON><PERSON><PERSON>", "Legend", "Title", "useChartData", "formatTimeInText", "jsx", "_jsx", "jsxs", "_jsxs", "register", "JapanPlan", "data", "timeAllocationData", "options", "timeAllocationOptions", "loading", "timeBlocks", "time", "title", "what", "why", "how", "className", "children", "id", "map", "block", "index", "to"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/pages/JapanPlan.tsx"], "sourcesContent": ["import React, { useMemo } from 'react';\nimport { <PERSON> } from 'react-router-dom';\nimport { Doughnut } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  ArcElement,\n  Tooltip,\n  Legend,\n  Title\n} from 'chart.js';\nimport { useChartData } from '../hooks/useChartData';\nimport { formatTimeInText } from '../utils/timeFormat';\n\n// Register Chart.js components\nChartJS.register(ArcElement, Tooltip, Legend, Title);\n\nconst JapanPlan: React.FC = () => {\n  const { data: timeAllocationData, options: timeAllocationOptions, loading } = useChartData('timeAllocation');\n\n  // Time blocks with 24-hour format that will be converted\n  const timeBlocks = useMemo(() => [\n    {\n      time: \"06:00 - 07:30\",\n      title: \"💪 Pillar 1: Physical Fitness\",\n      what: \"Full-body strength training (3x/week) or Cardio & Core (2x/week).\",\n      why: \"To build the raw strength and endurance needed for physical jobs and to boost mental clarity for the day.\",\n      how: \"Follow a structured program like StrongLifts 5x5. For cardio, use HIIT or jogging. Use apps like Jefit to track lifts.\"\n    },\n    {\n      time: \"09:00 - 12:00\",\n      title: \"🗣️ Pillar 2: Japanese Core Study\",\n      what: \"Deep, focused study of Japanese grammar and sentence structure.\",\n      why: \"Grammar is the skeleton of the language. Vocabulary is useless without it. This is the hardest but most important part.\",\n      how: \"Use a textbook (Genki I & II). Complete exercises with a pen and paper. No phone, no distractions. One chapter every 4-5 days.\"\n    },\n    {\n      time: \"13:00 - 15:00\",\n      title: \"🛠️ Pillar 3: Practical Skills Study\",\n      what: \"Theoretical study of tools, safety protocols, materials, and basic trade knowledge.\",\n      why: \"To become a quick learner on a job site. Knowing the 'what' and 'why' makes the 'how' much easier to pick up.\",\n      how: \"Watch curated YouTube channels (e.g., Essential Craftsman). Take notes. Create a \\\"knowledge base\\\" in a notebook.\"\n    },\n    {\n      time: \"15:00 - 16:30\",\n      title: \"🧠 Pillar 4: Cognitive Fitness\",\n      what: \"Active reading of non-fiction, problem-solving puzzles, and meditation.\",\n      why: \"To sharpen your focus, improve memory retention, and train your brain to learn efficiently.\",\n      how: \"Read a chapter, then summarize it (Feynman Technique). Use apps like Headspace for meditation and Lumosity for logic puzzles.\"\n    },\n    {\n      time: \"17:00 - 18:00\",\n      title: \"🗣️ Pillar 2: Japanese Vocabulary Drill\",\n      what: \"Spaced Repetition System (SRS) for vocabulary.\",\n      why: \"To efficiently memorize thousands of words and burn them into your long-term memory.\",\n      how: \"Use the Anki app. Do your reviews and learn 20 new words every single day without fail.\"\n    },\n    {\n      time: \"19:30 - 21:00\",\n      title: \"🗣️ Pillar 2: Japanese Immersion\",\n      what: \"Actively listen to and speak Japanese.\",\n      why: \"To connect your grammar and vocabulary knowledge to the sound and rhythm of the real language.\",\n      how: \"Listen to beginner podcasts or anime audio. Starting Month 3, use HelloTalk to speak with native speakers.\"\n    }\n  ], []);\n\n  return (\n    <div className=\"bg-gray-100 text-gray-800 min-h-screen\">\n      <div className=\"container mx-auto p-4 md:p-8\">\n        <header className=\"text-center mb-12\">\n          <h1 className=\"text-4xl md:text-6xl font-black text-reedsoft-primary tracking-tight\">\n            PROJECT: JACK OF ALL TRADES\n          </h1>\n          <p className=\"text-lg md:text-xl text-reedsoft-secondary mt-2\">\n            Your 16-Week Blueprint for Total Transformation\n          </p>\n        </header>\n\n        {/* Daily Kata (Routine) Section */}\n        <section id=\"daily-kata\" className=\"mb-12\">\n          <h2 className=\"text-3xl font-bold text-center mb-8 text-reedsoft-primary\">Your Daily \"Kata\" (Routine)</h2>\n          <div className=\"bg-white p-6 rounded-lg shadow-lg mb-8\">\n            <p className=\"text-center text-gray-600 mb-6\">\n              A consistent daily routine, or <em>kata</em> (形)—a Japanese term for a choreographed pattern of movements—is essential for disciplined practice.\n              This template integrates the Pomodoro Technique for maximum concentration and retention.\n            </p>\n\n            <div className=\"overflow-x-auto\">\n              <table className=\"w-full text-sm\">\n                <thead>\n                  <tr className=\"bg-gray-50\">\n                    <th className=\"text-left p-3 font-bold text-reedsoft-primary\">Time</th>\n                    <th className=\"text-left p-3 font-bold text-reedsoft-primary\">Activity</th>\n                    <th className=\"text-left p-3 font-bold text-reedsoft-primary\">Focus/Tools</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  <tr className=\"border-b\">\n                    <td className=\"p-3 font-semibold text-reedsoft-secondary\">6:00 - 6:45 AM</td>\n                    <td className=\"p-3\">Physical Fitness</td>\n                    <td className=\"p-3\">Full-body circuit, HIIT, or functional strength workout.</td>\n                  </tr>\n                  <tr className=\"border-b bg-gray-50\">\n                    <td className=\"p-3 font-semibold text-reedsoft-secondary\">6:45 - 7:30 AM</td>\n                    <td className=\"p-3\">Morning Prep</td>\n                    <td className=\"p-3\">Shower, breakfast, review daily goals.</td>\n                  </tr>\n                  <tr className=\"border-b\">\n                    <td className=\"p-3 font-semibold text-reedsoft-secondary\">7:30 - 9:30 AM</td>\n                    <td className=\"p-3\"><strong>Japanese Study Block 1</strong></td>\n                    <td className=\"p-3\">4x Pomodoro sessions (25 min study / 5 min break). Focus on new grammar/kanji. Tools: Textbook, WaniKani.</td>\n                  </tr>\n                  <tr className=\"border-b bg-gray-50\">\n                    <td className=\"p-3 font-semibold text-reedsoft-secondary\">9:30 - 10:00 AM</td>\n                    <td className=\"p-3\">Extended Break</td>\n                    <td className=\"p-3\">Stretch, light snack, step away from study area.</td>\n                  </tr>\n                  <tr className=\"border-b\">\n                    <td className=\"p-3 font-semibold text-reedsoft-secondary\">10:00 AM - 12:00 PM</td>\n                    <td className=\"p-3\"><strong>Japanese Study Block 2</strong></td>\n                    <td className=\"p-3\">4x Pomodoro sessions. Focus on vocabulary reinforcement and reading practice. Tools: Anki, NHK News Easy.</td>\n                  </tr>\n                  <tr className=\"border-b bg-gray-50\">\n                    <td className=\"p-3 font-semibold text-reedsoft-secondary\">12:00 - 1:00 PM</td>\n                    <td className=\"p-3\">Lunch Break</td>\n                    <td className=\"p-3\">Nutritious meal, mental rest.</td>\n                  </tr>\n                  <tr className=\"border-b\">\n                    <td className=\"p-3 font-semibold text-reedsoft-secondary\">1:00 - 2:00 PM</td>\n                    <td className=\"p-3\"><strong>Skills Development Block</strong></td>\n                    <td className=\"p-3\">2x Pomodoro sessions. Work on online trade course or cognitive training. Tools: Alison, CogniFit/Lumosity.</td>\n                  </tr>\n                  <tr className=\"border-b bg-gray-50\">\n                    <td className=\"p-3 font-semibold text-reedsoft-secondary\">2:00 - 3:00 PM</td>\n                    <td className=\"p-3\"><strong>Japanese Study Block 3</strong></td>\n                    <td className=\"p-3\">2x Pomodoro sessions. Focus on listening or speaking practice. Tools: Podcasts, iTalki prep, shadowing.</td>\n                  </tr>\n                  <tr className=\"border-b\">\n                    <td className=\"p-3 font-semibold text-reedsoft-secondary\">3:00 - 3:30 PM</td>\n                    <td className=\"p-3\">Afternoon Break</td>\n                    <td className=\"p-3\">Walk, hydrate, short rest.</td>\n                  </tr>\n                  <tr className=\"border-b bg-gray-50\">\n                    <td className=\"p-3 font-semibold text-reedsoft-secondary\">3:30 - 5:00 PM</td>\n                    <td className=\"p-3\"><strong>Career Prep Block</strong></td>\n                    <td className=\"p-3\">Work on resumes, research companies, prepare for interviews, or organize visa documents.</td>\n                  </tr>\n                  <tr className=\"border-b\">\n                    <td className=\"p-3 font-semibold text-reedsoft-secondary\">5:00 PM onward</td>\n                    <td className=\"p-3\">Free Time & Review</td>\n                    <td className=\"p-3\">Dinner, hobbies, relaxation. A light 15-30 minute review of the day's Japanese lessons before bed.</td>\n                  </tr>\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </section>\n\n        {/* Monthly Missions Section */}\n        <section id=\"missions\" className=\"mb-12\">\n          <h2 className=\"text-3xl font-bold text-center mb-8 text-reedsoft-primary\">Your 16-Week Transformation Journey</h2>\n          <div className=\"bg-white p-6 rounded-lg shadow-lg\">\n            <div className=\"grid grid-cols-1 md:grid-cols-4 items-center text-center gap-4\">\n              <div className=\"p-4\">\n                <h3 className=\"text-xl font-bold text-reedsoft-secondary\">Month 1 (Weeks 1-4)</h3>\n                <p className=\"font-semibold text-gray-700\">Building the Foundation</p>\n                <p className=\"text-sm mt-1 text-gray-600\">基礎固め (Kiso-gatame)</p>\n                <p className=\"text-sm mt-2\">Master Hiragana & Katakana. Start Genki I. Begin WaniKani. 3x weekly bodyweight circuits. Daily cognitive training.</p>\n              </div>\n              <div className=\"hidden md:block text-2xl text-reedsoft-accent\">→</div>\n              <div className=\"p-4\">\n                <h3 className=\"text-xl font-bold text-reedsoft-secondary\">Month 2 (Weeks 5-8)</h3>\n                <p className=\"font-semibold text-gray-700\">Accelerating Progress</p>\n                <p className=\"text-sm mt-1 text-gray-600\">進捗加速 (Shinchoku-kasoku)</p>\n                <p className=\"text-sm mt-2\">Complete N5 grammar. Learn first 100 N5 Kanji. Increase workout intensity. Start online trade courses.</p>\n              </div>\n              <div className=\"hidden md:block text-2xl text-reedsoft-accent\">→</div>\n              <div className=\"p-4\">\n                <h3 className=\"text-xl font-bold text-reedsoft-secondary\">Month 3 (Weeks 9-12)</h3>\n                <p className=\"font-semibold text-gray-700\">Skill Integration</p>\n                <p className=\"text-sm mt-1 text-gray-600\">スキル統合 (Sukiru-tōgō)</p>\n                <p className=\"text-sm mt-2\">Start N4 prep. Begin iTalki conversation practice. Introduce HIIT elements. Draft Japanese resumes.</p>\n              </div>\n              <div className=\"hidden md:block text-2xl text-reedsoft-accent\">→</div>\n              <div className=\"p-4\">\n                <h3 className=\"text-xl font-bold text-reedsoft-secondary\">Month 4 (Weeks 13-16)</h3>\n                <p className=\"font-semibold text-gray-700\">Final Preparation</p>\n                <p className=\"text-sm mt-1 text-gray-600\">最終準備 (Saishū-junbi)</p>\n                <p className=\"text-sm mt-2\">Weekly N4 mock tests. Finalize visa documents. Practice interview questions. Maintain peak fitness.</p>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* 16-Week Training Matrix Section */}\n        <section id=\"training-matrix\" className=\"mb-12\">\n          <h2 className=\"text-3xl font-bold text-center mb-8 text-reedsoft-primary\">16-Week At-A-Glance Training Matrix</h2>\n          <div className=\"bg-white p-6 rounded-lg shadow-lg overflow-x-auto\">\n            <p className=\"text-center text-gray-600 mb-6\">\n              A clear path, turning the four-month goal into manageable weekly sprints.\n            </p>\n            <table className=\"w-full text-xs md:text-sm\">\n              <thead>\n                <tr className=\"bg-gray-50\">\n                  <th className=\"text-left p-2 font-bold text-reedsoft-primary\">Week</th>\n                  <th className=\"text-left p-2 font-bold text-reedsoft-primary\">Language Focus</th>\n                  <th className=\"text-left p-2 font-bold text-reedsoft-primary\">Physical Fitness Goal</th>\n                  <th className=\"text-left p-2 font-bold text-reedsoft-primary\">Skills Focus</th>\n                  <th className=\"text-left p-2 font-bold text-reedsoft-primary\">Career Prep Task</th>\n                </tr>\n              </thead>\n              <tbody>\n                <tr className=\"border-b\">\n                  <td className=\"p-2 font-semibold text-reedsoft-secondary\">1-2</td>\n                  <td className=\"p-2\">Master Hiragana & Katakana. Start Genki I. Begin WaniKani.</td>\n                  <td className=\"p-2\">3x weekly bodyweight circuits. Focus on form.</td>\n                  <td className=\"p-2\">Daily cognitive training (15 min). Research trade courses.</td>\n                  <td className=\"p-2\">Research SSW visa requirements. Identify recruitment agencies.</td>\n                </tr>\n                <tr className=\"border-b bg-gray-50\">\n                  <td className=\"p-2 font-semibold text-reedsoft-secondary\">3-4</td>\n                  <td className=\"p-2\">Complete first chapters. Daily Anki (vocab/grammar).</td>\n                  <td className=\"p-2\">Maintain 3x weekly workouts. Increase reps slightly.</td>\n                  <td className=\"p-2\">Start first online trade course. Continue cognitive training.</td>\n                  <td className=\"p-2\">Draft initial contact email for recruitment agencies.</td>\n                </tr>\n                <tr className=\"border-b\">\n                  <td className=\"p-2 font-semibold text-reedsoft-secondary\">5-6</td>\n                  <td className=\"p-2\">Progress through textbook (N5 grammar). Learn first 50 N5 Kanji.</td>\n                  <td className=\"p-2\">Increase workout intensity. Add 4th weekly session.</td>\n                  <td className=\"p-2\">Continue first course. Focus on weak cognitive areas.</td>\n                  <td className=\"p-2\">Draft first version of Rirekisho and Shokumu Keirekisho.</td>\n                </tr>\n                <tr className=\"border-b bg-gray-50\">\n                  <td className=\"p-2 font-semibold text-reedsoft-secondary\">7-8</td>\n                  <td className=\"p-2\">Complete N5 Kanji list (~100). Begin beginner listening podcasts.</td>\n                  <td className=\"p-2\">Introduce HIIT elements into 1-2 weekly workouts.</td>\n                  <td className=\"p-2\">Complete first course. Start second course (Intro to Logistics).</td>\n                  <td className=\"p-2\">Contact recruitment agencies. Refine resumes based on feedback.</td>\n                </tr>\n                <tr className=\"border-b\">\n                  <td className=\"p-2 font-semibold text-reedsoft-secondary\">9-10</td>\n                  <td className=\"p-2\">Finish primary textbook. Start N4 prep book (Shin-Kanzen Master).</td>\n                  <td className=\"p-2\">Focus on functional strength. Maintain 4x weekly workouts.</td>\n                  <td className=\"p-2\">Continue second course. Apply learnings to resume drafts.</td>\n                  <td className=\"p-2\">Research specific Japanese companies in target sectors.</td>\n                </tr>\n                <tr className=\"border-b bg-gray-50\">\n                  <td className=\"p-2 font-semibold text-reedsoft-secondary\">11-12</td>\n                  <td className=\"p-2\">Learn first 100 N4 Kanji. Start weekly iTalki conversation practice.</td>\n                  <td className=\"p-2\">Maintain HIIT and strength focus. Ensure proper recovery.</td>\n                  <td className=\"p-2\">Complete second course. Summarize key skills learned.</td>\n                  <td className=\"p-2\">Practice interview questions, focusing on cultural etiquette.</td>\n                </tr>\n                <tr className=\"border-b\">\n                  <td className=\"p-2 font-semibold text-reedsoft-secondary\">13-14</td>\n                  <td className=\"p-2\">Intensive N4 grammar/vocab review. Take first full N4 mock test.</td>\n                  <td className=\"p-2\">Maintain fitness with 3-4 workouts. Focus on injury prevention.</td>\n                  <td className=\"p-2\">Maintenance cognitive training (10 min/day).</td>\n                  <td className=\"p-2\">Finalize resumes. Begin gathering SSW application documents.</td>\n                </tr>\n                <tr className=\"border-b bg-gray-50\">\n                  <td className=\"p-2 font-semibold text-reedsoft-secondary\">15-16</td>\n                  <td className=\"p-2\">Take weekly timed N4 mock tests. Drill weak areas identified.</td>\n                  <td className=\"p-2\">Taper intensity slightly to ensure body is rested and strong.</td>\n                  <td className=\"p-2\">Review notes from online trade courses.</td>\n                  <td className=\"p-2\">Conduct mock interviews. Have all visa documents ready.</td>\n                </tr>\n              </tbody>\n            </table>\n          </div>\n        </section>\n\n        {/* Daily Grind Section */}\n        <section id=\"daily-grind\" className=\"mb-12\">\n          <h2 className=\"text-3xl font-bold text-center mb-8 text-reedsoft-primary\">The Daily Grind: Weekday Blueprint</h2>\n          <div className=\"space-y-6\">\n            <div className=\"bg-white p-6 rounded-lg shadow-lg\">\n              <p className=\"text-sm text-center text-gray-600 mb-4\">\n                This is your core schedule, Monday to Friday. Each block is non-negotiable.\n                It is designed for maximum efficiency in skill acquisition.\n              </p>\n            </div>\n\n            {timeBlocks.map((block, index) => (\n              <div key={index} className=\"bg-white p-5 rounded-lg shadow-md\">\n                <div className=\"font-bold text-lg text-reedsoft-primary\">\n                  {formatTimeInText(block.time)} | {block.title}\n                </div>\n                <p className=\"mt-2 text-sm\">\n                  <strong className=\"text-reedsoft-secondary\">WHAT:</strong> {block.what}\n                </p>\n                <p className=\"mt-1 text-sm\">\n                  <strong className=\"text-reedsoft-secondary\">WHY:</strong> {block.why}\n                </p>\n                <p className=\"mt-1 text-sm\">\n                  <strong className=\"text-reedsoft-secondary\">HOW:</strong> {block.how}\n                </p>\n              </div>\n            ))}\n          </div>\n        </section>\n\n        {/* Weekend Plan Section */}\n        <section id=\"weekend-plan\" className=\"mb-12\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n            <div className=\"bg-white p-6 rounded-lg shadow-lg\">\n              <h2 className=\"text-2xl font-bold text-center mb-4 text-reedsoft-primary\">Weekly Time Allocation</h2>\n              <p className=\"text-sm text-gray-600 mb-4 text-center\">\n                Your approximate 40+ hours of active development each week, broken down by pillar.\n              </p>\n              <div className=\"relative w-full max-w-md mx-auto h-96\">\n                {loading ? (\n                  <div className=\"flex items-center justify-center h-full\">\n                    <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-reedsoft-primary\"></div>\n                  </div>\n                ) : timeAllocationData && timeAllocationOptions ? (\n                  <Doughnut data={timeAllocationData} options={timeAllocationOptions as any} />\n                ) : (\n                  <div className=\"flex items-center justify-center h-full text-gray-500\">\n                    Chart loading...\n                  </div>\n                )}\n              </div>\n            </div>\n\n            <div className=\"bg-white p-6 rounded-lg shadow-lg\">\n              <h2 className=\"text-2xl font-bold text-center mb-4 text-reedsoft-primary\">The Weekend Shift</h2>\n              <p className=\"text-sm text-gray-600 mb-4 text-center\">\n                Weekends are for review, practical application, and strategic recovery. The pace changes, but the work continues.\n              </p>\n              <ul className=\"space-y-4\">\n                <li className=\"p-4 bg-cyan-50 rounded-lg\">\n                  <h3 className=\"font-bold text-teal-800\">Saturday Morning: Review & Consolidate</h3>\n                  <p className=\"text-sm\">Review all Japanese lessons and Anki cards from the week. Perform one physical fitness session.</p>\n                </li>\n                <li className=\"p-4 bg-cyan-50 rounded-lg\">\n                  <h3 className=\"font-bold text-teal-800\">Saturday Afternoon: Hands-On Project</h3>\n                  <p className=\"text-sm\">Apply your practical skills. Build a small shelf, practice knots for 30 minutes, disassemble and reassemble an old appliance. Turn theory into action.</p>\n                </li>\n                <li className=\"p-4 bg-cyan-50 rounded-lg\">\n                  <h3 className=\"font-bold text-teal-800\">Sunday Morning: Active Recovery</h3>\n                  <p className=\"text-sm\">Go for a long walk or do a deep stretching routine. Listen to Japanese music or a podcast. Let your body heal.</p>\n                </li>\n                <li className=\"p-4 bg-cyan-50 rounded-lg\">\n                  <h3 className=\"font-bold text-teal-800\">Sunday Afternoon: Strategize & Recharge</h3>\n                  <p className=\"text-sm\">Plan the week ahead: schedule workouts, set study goals. Then, completely disconnect. Read a novel, watch a movie for fun. Recharge your mind for the week ahead.</p>\n                </li>\n              </ul>\n            </div>\n          </div>\n        </section>\n\n        {/* Pillar Navigation */}\n        <section className=\"mb-12\">\n          <h2 className=\"text-3xl font-bold text-center mb-8 text-reedsoft-primary\">Explore Each Pillar</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <Link\n              to=\"/japan/pillar-1\"\n              className=\"bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-l-4 border-red-500 group\"\n            >\n              <div className=\"text-4xl mb-3\">💪</div>\n              <h3 className=\"text-xl font-bold text-gray-800 mb-2 group-hover:text-red-500 transition-colors\">\n                Pillar 1: The Body\n              </h3>\n              <p className=\"text-gray-600 text-sm\">Physical Fitness & Strength Training</p>\n            </Link>\n\n            <Link\n              to=\"/japan/pillar-2\"\n              className=\"bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-l-4 border-reedsoft-primary group\"\n            >\n              <div className=\"text-4xl mb-3\">🗣️</div>\n              <h3 className=\"text-xl font-bold text-gray-800 mb-2 group-hover:text-reedsoft-primary transition-colors\">\n                Pillar 2: The Voice\n              </h3>\n              <p className=\"text-gray-600 text-sm\">Japanese Language Mastery</p>\n            </Link>\n\n            <Link\n              to=\"/japan/pillar-3\"\n              className=\"bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-l-4 border-gray-600 group\"\n            >\n              <div className=\"text-4xl mb-3\">🛠️</div>\n              <h3 className=\"text-xl font-bold text-gray-800 mb-2 group-hover:text-gray-600 transition-colors\">\n                Pillar 3: The Hands\n              </h3>\n              <p className=\"text-gray-600 text-sm\">Practical Skills & Trade Knowledge</p>\n            </Link>\n\n            <Link\n              to=\"/japan/pillar-4\"\n              className=\"bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-shadow border-l-4 border-blue-500 group\"\n            >\n              <div className=\"text-4xl mb-3\">🧠</div>\n              <h3 className=\"text-xl font-bold text-gray-800 mb-2 group-hover:text-blue-500 transition-colors\">\n                Pillar 4: The Mind\n              </h3>\n              <p className=\"text-gray-600 text-sm\">Cognitive Fitness & Learning</p>\n            </Link>\n          </div>\n        </section>\n\n        <footer className=\"text-center mt-12 pt-8 border-t border-gray-300\">\n          <p className=\"text-gray-600\">Discipline today is freedom tomorrow. Execute the plan.</p>\n          <p className=\"text-sm text-gray-500 mt-2\">© 2025 Project Reboot Master Plan</p>\n        </footer>\n      </div>\n    </div>\n  );\n};\n\nexport default JapanPlan;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,OAAO,KAAQ,OAAO,CACtC,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,QAAQ,KAAQ,iBAAiB,CAC1C,OACEC,KAAK,GAAI,CAAAC,OAAO,CAChBC,UAAU,CACVC,OAAO,CACPC,MAAM,CACNC,KAAK,KACA,UAAU,CACjB,OAASC,YAAY,KAAQ,uBAAuB,CACpD,OAASC,gBAAgB,KAAQ,qBAAqB,CAEtD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACAV,OAAO,CAACW,QAAQ,CAACV,UAAU,CAAEC,OAAO,CAAEC,MAAM,CAAEC,KAAK,CAAC,CAEpD,KAAM,CAAAQ,SAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAEC,IAAI,CAAEC,kBAAkB,CAAEC,OAAO,CAAEC,qBAAqB,CAAEC,OAAQ,CAAC,CAAGZ,YAAY,CAAC,gBAAgB,CAAC,CAE5G;AACA,KAAM,CAAAa,UAAU,CAAGtB,OAAO,CAAC,IAAM,CAC/B,CACEuB,IAAI,CAAE,eAAe,CACrBC,KAAK,CAAE,+BAA+B,CACtCC,IAAI,CAAE,mEAAmE,CACzEC,GAAG,CAAE,2GAA2G,CAChHC,GAAG,CAAE,wHACP,CAAC,CACD,CACEJ,IAAI,CAAE,eAAe,CACrBC,KAAK,CAAE,mCAAmC,CAC1CC,IAAI,CAAE,iEAAiE,CACvEC,GAAG,CAAE,yHAAyH,CAC9HC,GAAG,CAAE,gIACP,CAAC,CACD,CACEJ,IAAI,CAAE,eAAe,CACrBC,KAAK,CAAE,sCAAsC,CAC7CC,IAAI,CAAE,qFAAqF,CAC3FC,GAAG,CAAE,+GAA+G,CACpHC,GAAG,CAAE,oHACP,CAAC,CACD,CACEJ,IAAI,CAAE,eAAe,CACrBC,KAAK,CAAE,gCAAgC,CACvCC,IAAI,CAAE,yEAAyE,CAC/EC,GAAG,CAAE,6FAA6F,CAClGC,GAAG,CAAE,+HACP,CAAC,CACD,CACEJ,IAAI,CAAE,eAAe,CACrBC,KAAK,CAAE,yCAAyC,CAChDC,IAAI,CAAE,gDAAgD,CACtDC,GAAG,CAAE,sFAAsF,CAC3FC,GAAG,CAAE,yFACP,CAAC,CACD,CACEJ,IAAI,CAAE,eAAe,CACrBC,KAAK,CAAE,kCAAkC,CACzCC,IAAI,CAAE,wCAAwC,CAC9CC,GAAG,CAAE,gGAAgG,CACrGC,GAAG,CAAE,4GACP,CAAC,CACF,CAAE,EAAE,CAAC,CAEN,mBACEf,IAAA,QAAKgB,SAAS,CAAC,wCAAwC,CAAAC,QAAA,cACrDf,KAAA,QAAKc,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3Cf,KAAA,WAAQc,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACnCjB,IAAA,OAAIgB,SAAS,CAAC,sEAAsE,CAAAC,QAAA,CAAC,6BAErF,CAAI,CAAC,cACLjB,IAAA,MAAGgB,SAAS,CAAC,iDAAiD,CAAAC,QAAA,CAAC,iDAE/D,CAAG,CAAC,EACE,CAAC,cAGTf,KAAA,YAASgB,EAAE,CAAC,YAAY,CAACF,SAAS,CAAC,OAAO,CAAAC,QAAA,eACxCjB,IAAA,OAAIgB,SAAS,CAAC,2DAA2D,CAAAC,QAAA,CAAC,+BAA2B,CAAI,CAAC,cAC1Gf,KAAA,QAAKc,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDf,KAAA,MAAGc,SAAS,CAAC,gCAAgC,CAAAC,QAAA,EAAC,iCACb,cAAAjB,IAAA,OAAAiB,QAAA,CAAI,MAAI,CAAI,CAAC,+MAE9C,EAAG,CAAC,cAEJjB,IAAA,QAAKgB,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9Bf,KAAA,UAAOc,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC/BjB,IAAA,UAAAiB,QAAA,cACEf,KAAA,OAAIc,SAAS,CAAC,YAAY,CAAAC,QAAA,eACxBjB,IAAA,OAAIgB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,MAAI,CAAI,CAAC,cACvEjB,IAAA,OAAIgB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,UAAQ,CAAI,CAAC,cAC3EjB,IAAA,OAAIgB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,aAAW,CAAI,CAAC,EAC5E,CAAC,CACA,CAAC,cACRf,KAAA,UAAAe,QAAA,eACEf,KAAA,OAAIc,SAAS,CAAC,UAAU,CAAAC,QAAA,eACtBjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cAC7EjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cACzCjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,0DAAwD,CAAI,CAAC,EAC/E,CAAC,cACLf,KAAA,OAAIc,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACjCjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cAC7EjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cACrCjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,wCAAsC,CAAI,CAAC,EAC7D,CAAC,cACLf,KAAA,OAAIc,SAAS,CAAC,UAAU,CAAAC,QAAA,eACtBjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cAC7EjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,cAACjB,IAAA,WAAAiB,QAAA,CAAQ,wBAAsB,CAAQ,CAAC,CAAI,CAAC,cAChEjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,2GAAyG,CAAI,CAAC,EAChI,CAAC,cACLf,KAAA,OAAIc,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACjCjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cAC9EjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cACvCjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,kDAAgD,CAAI,CAAC,EACvE,CAAC,cACLf,KAAA,OAAIc,SAAS,CAAC,UAAU,CAAAC,QAAA,eACtBjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,qBAAmB,CAAI,CAAC,cAClFjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,cAACjB,IAAA,WAAAiB,QAAA,CAAQ,wBAAsB,CAAQ,CAAC,CAAI,CAAC,cAChEjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,2GAAyG,CAAI,CAAC,EAChI,CAAC,cACLf,KAAA,OAAIc,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACjCjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cAC9EjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,aAAW,CAAI,CAAC,cACpCjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,+BAA6B,CAAI,CAAC,EACpD,CAAC,cACLf,KAAA,OAAIc,SAAS,CAAC,UAAU,CAAAC,QAAA,eACtBjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cAC7EjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,cAACjB,IAAA,WAAAiB,QAAA,CAAQ,0BAAwB,CAAQ,CAAC,CAAI,CAAC,cAClEjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,4GAA0G,CAAI,CAAC,EACjI,CAAC,cACLf,KAAA,OAAIc,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACjCjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cAC7EjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,cAACjB,IAAA,WAAAiB,QAAA,CAAQ,wBAAsB,CAAQ,CAAC,CAAI,CAAC,cAChEjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,yGAAuG,CAAI,CAAC,EAC9H,CAAC,cACLf,KAAA,OAAIc,SAAS,CAAC,UAAU,CAAAC,QAAA,eACtBjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cAC7EjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,iBAAe,CAAI,CAAC,cACxCjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,4BAA0B,CAAI,CAAC,EACjD,CAAC,cACLf,KAAA,OAAIc,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACjCjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cAC7EjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,cAACjB,IAAA,WAAAiB,QAAA,CAAQ,mBAAiB,CAAQ,CAAC,CAAI,CAAC,cAC3DjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,0FAAwF,CAAI,CAAC,EAC/G,CAAC,cACLf,KAAA,OAAIc,SAAS,CAAC,UAAU,CAAAC,QAAA,eACtBjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cAC7EjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,oBAAkB,CAAI,CAAC,cAC3CjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,oGAAkG,CAAI,CAAC,EACzH,CAAC,EACA,CAAC,EACH,CAAC,CACL,CAAC,EACH,CAAC,EACC,CAAC,cAGVf,KAAA,YAASgB,EAAE,CAAC,UAAU,CAACF,SAAS,CAAC,OAAO,CAAAC,QAAA,eACtCjB,IAAA,OAAIgB,SAAS,CAAC,2DAA2D,CAAAC,QAAA,CAAC,qCAAmC,CAAI,CAAC,cAClHjB,IAAA,QAAKgB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAChDf,KAAA,QAAKc,SAAS,CAAC,gEAAgE,CAAAC,QAAA,eAC7Ef,KAAA,QAAKc,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,qBAAmB,CAAI,CAAC,cAClFjB,IAAA,MAAGgB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,yBAAuB,CAAG,CAAC,cACtEjB,IAAA,MAAGgB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,wCAAkB,CAAG,CAAC,cAChEjB,IAAA,MAAGgB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,qHAAmH,CAAG,CAAC,EAChJ,CAAC,cACNjB,IAAA,QAAKgB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,QAAC,CAAK,CAAC,cACtEf,KAAA,QAAKc,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,qBAAmB,CAAI,CAAC,cAClFjB,IAAA,MAAGgB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,uBAAqB,CAAG,CAAC,cACpEjB,IAAA,MAAGgB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,6CAAuB,CAAG,CAAC,cACrEjB,IAAA,MAAGgB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,wGAAsG,CAAG,CAAC,EACnI,CAAC,cACNjB,IAAA,QAAKgB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,QAAC,CAAK,CAAC,cACtEf,KAAA,QAAKc,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,sBAAoB,CAAI,CAAC,cACnFjB,IAAA,MAAGgB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,mBAAiB,CAAG,CAAC,cAChEjB,IAAA,MAAGgB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,wDAAmB,CAAG,CAAC,cACjEjB,IAAA,MAAGgB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,qGAAmG,CAAG,CAAC,EAChI,CAAC,cACNjB,IAAA,QAAKgB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,QAAC,CAAK,CAAC,cACtEf,KAAA,QAAKc,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,uBAAqB,CAAI,CAAC,cACpFjB,IAAA,MAAGgB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,mBAAiB,CAAG,CAAC,cAChEjB,IAAA,MAAGgB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,8CAAmB,CAAG,CAAC,cACjEjB,IAAA,MAAGgB,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,qGAAmG,CAAG,CAAC,EAChI,CAAC,EACH,CAAC,CACH,CAAC,EACC,CAAC,cAGVf,KAAA,YAASgB,EAAE,CAAC,iBAAiB,CAACF,SAAS,CAAC,OAAO,CAAAC,QAAA,eAC7CjB,IAAA,OAAIgB,SAAS,CAAC,2DAA2D,CAAAC,QAAA,CAAC,qCAAmC,CAAI,CAAC,cAClHf,KAAA,QAAKc,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChEjB,IAAA,MAAGgB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,2EAE9C,CAAG,CAAC,cACJf,KAAA,UAAOc,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eAC1CjB,IAAA,UAAAiB,QAAA,cACEf,KAAA,OAAIc,SAAS,CAAC,YAAY,CAAAC,QAAA,eACxBjB,IAAA,OAAIgB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,MAAI,CAAI,CAAC,cACvEjB,IAAA,OAAIgB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,gBAAc,CAAI,CAAC,cACjFjB,IAAA,OAAIgB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,uBAAqB,CAAI,CAAC,cACxFjB,IAAA,OAAIgB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cAC/EjB,IAAA,OAAIgB,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,EACjF,CAAC,CACA,CAAC,cACRf,KAAA,UAAAe,QAAA,eACEf,KAAA,OAAIc,SAAS,CAAC,UAAU,CAAAC,QAAA,eACtBjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,KAAG,CAAI,CAAC,cAClEjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,4DAA0D,CAAI,CAAC,cACnFjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,+CAA6C,CAAI,CAAC,cACtEjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,4DAA0D,CAAI,CAAC,cACnFjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,gEAA8D,CAAI,CAAC,EACrF,CAAC,cACLf,KAAA,OAAIc,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACjCjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,KAAG,CAAI,CAAC,cAClEjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,sDAAoD,CAAI,CAAC,cAC7EjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,sDAAoD,CAAI,CAAC,cAC7EjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,+DAA6D,CAAI,CAAC,cACtFjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,uDAAqD,CAAI,CAAC,EAC5E,CAAC,cACLf,KAAA,OAAIc,SAAS,CAAC,UAAU,CAAAC,QAAA,eACtBjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,KAAG,CAAI,CAAC,cAClEjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,kEAAgE,CAAI,CAAC,cACzFjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,qDAAmD,CAAI,CAAC,cAC5EjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,uDAAqD,CAAI,CAAC,cAC9EjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,0DAAwD,CAAI,CAAC,EAC/E,CAAC,cACLf,KAAA,OAAIc,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACjCjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,KAAG,CAAI,CAAC,cAClEjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,mEAAiE,CAAI,CAAC,cAC1FjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,mDAAiD,CAAI,CAAC,cAC1EjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,kEAAgE,CAAI,CAAC,cACzFjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,iEAA+D,CAAI,CAAC,EACtF,CAAC,cACLf,KAAA,OAAIc,SAAS,CAAC,UAAU,CAAAC,QAAA,eACtBjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,MAAI,CAAI,CAAC,cACnEjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,mEAAiE,CAAI,CAAC,cAC1FjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,4DAA0D,CAAI,CAAC,cACnFjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,2DAAyD,CAAI,CAAC,cAClFjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,yDAAuD,CAAI,CAAC,EAC9E,CAAC,cACLf,KAAA,OAAIc,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACjCjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,OAAK,CAAI,CAAC,cACpEjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,sEAAoE,CAAI,CAAC,cAC7FjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,2DAAyD,CAAI,CAAC,cAClFjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,uDAAqD,CAAI,CAAC,cAC9EjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,+DAA6D,CAAI,CAAC,EACpF,CAAC,cACLf,KAAA,OAAIc,SAAS,CAAC,UAAU,CAAAC,QAAA,eACtBjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,OAAK,CAAI,CAAC,cACpEjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,kEAAgE,CAAI,CAAC,cACzFjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,iEAA+D,CAAI,CAAC,cACxFjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,8CAA4C,CAAI,CAAC,cACrEjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,8DAA4D,CAAI,CAAC,EACnF,CAAC,cACLf,KAAA,OAAIc,SAAS,CAAC,qBAAqB,CAAAC,QAAA,eACjCjB,IAAA,OAAIgB,SAAS,CAAC,2CAA2C,CAAAC,QAAA,CAAC,OAAK,CAAI,CAAC,cACpEjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,+DAA6D,CAAI,CAAC,cACtFjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,+DAA6D,CAAI,CAAC,cACtFjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,yCAAuC,CAAI,CAAC,cAChEjB,IAAA,OAAIgB,SAAS,CAAC,KAAK,CAAAC,QAAA,CAAC,yDAAuD,CAAI,CAAC,EAC9E,CAAC,EACA,CAAC,EACH,CAAC,EACL,CAAC,EACC,CAAC,cAGVf,KAAA,YAASgB,EAAE,CAAC,aAAa,CAACF,SAAS,CAAC,OAAO,CAAAC,QAAA,eACzCjB,IAAA,OAAIgB,SAAS,CAAC,2DAA2D,CAAAC,QAAA,CAAC,oCAAkC,CAAI,CAAC,cACjHf,KAAA,QAAKc,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBjB,IAAA,QAAKgB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,cAChDjB,IAAA,MAAGgB,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,yIAGtD,CAAG,CAAC,CACD,CAAC,CAELP,UAAU,CAACS,GAAG,CAAC,CAACC,KAAK,CAAEC,KAAK,gBAC3BnB,KAAA,QAAiBc,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAC5Df,KAAA,QAAKc,SAAS,CAAC,yCAAyC,CAAAC,QAAA,EACrDnB,gBAAgB,CAACsB,KAAK,CAACT,IAAI,CAAC,CAAC,KAAG,CAACS,KAAK,CAACR,KAAK,EAC1C,CAAC,cACNV,KAAA,MAAGc,SAAS,CAAC,cAAc,CAAAC,QAAA,eACzBjB,IAAA,WAAQgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,OAAK,CAAQ,CAAC,IAAC,CAACG,KAAK,CAACP,IAAI,EACrE,CAAC,cACJX,KAAA,MAAGc,SAAS,CAAC,cAAc,CAAAC,QAAA,eACzBjB,IAAA,WAAQgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,MAAI,CAAQ,CAAC,IAAC,CAACG,KAAK,CAACN,GAAG,EACnE,CAAC,cACJZ,KAAA,MAAGc,SAAS,CAAC,cAAc,CAAAC,QAAA,eACzBjB,IAAA,WAAQgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,MAAI,CAAQ,CAAC,IAAC,CAACG,KAAK,CAACL,GAAG,EACnE,CAAC,GAZIM,KAaL,CACN,CAAC,EACC,CAAC,EACC,CAAC,cAGVrB,IAAA,YAASkB,EAAE,CAAC,cAAc,CAACF,SAAS,CAAC,OAAO,CAAAC,QAAA,cAC1Cf,KAAA,QAAKc,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpDf,KAAA,QAAKc,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDjB,IAAA,OAAIgB,SAAS,CAAC,2DAA2D,CAAAC,QAAA,CAAC,wBAAsB,CAAI,CAAC,cACrGjB,IAAA,MAAGgB,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,oFAEtD,CAAG,CAAC,cACJjB,IAAA,QAAKgB,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CACnDR,OAAO,cACNT,IAAA,QAAKgB,SAAS,CAAC,yCAAyC,CAAAC,QAAA,cACtDjB,IAAA,QAAKgB,SAAS,CAAC,wEAAwE,CAAM,CAAC,CAC3F,CAAC,CACJV,kBAAkB,EAAIE,qBAAqB,cAC7CR,IAAA,CAACV,QAAQ,EAACe,IAAI,CAAEC,kBAAmB,CAACC,OAAO,CAAEC,qBAA6B,CAAE,CAAC,cAE7ER,IAAA,QAAKgB,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,kBAEvE,CAAK,CACN,CACE,CAAC,EACH,CAAC,cAENf,KAAA,QAAKc,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDjB,IAAA,OAAIgB,SAAS,CAAC,2DAA2D,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cAChGjB,IAAA,MAAGgB,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,mHAEtD,CAAG,CAAC,cACJf,KAAA,OAAIc,SAAS,CAAC,WAAW,CAAAC,QAAA,eACvBf,KAAA,OAAIc,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACvCjB,IAAA,OAAIgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,wCAAsC,CAAI,CAAC,cACnFjB,IAAA,MAAGgB,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,iGAA+F,CAAG,CAAC,EACxH,CAAC,cACLf,KAAA,OAAIc,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACvCjB,IAAA,OAAIgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,sCAAoC,CAAI,CAAC,cACjFjB,IAAA,MAAGgB,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,wJAAsJ,CAAG,CAAC,EAC/K,CAAC,cACLf,KAAA,OAAIc,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACvCjB,IAAA,OAAIgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,iCAA+B,CAAI,CAAC,cAC5EjB,IAAA,MAAGgB,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,gHAA8G,CAAG,CAAC,EACvI,CAAC,cACLf,KAAA,OAAIc,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACvCjB,IAAA,OAAIgB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,yCAAuC,CAAI,CAAC,cACpFjB,IAAA,MAAGgB,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,mKAAiK,CAAG,CAAC,EAC1L,CAAC,EACH,CAAC,EACF,CAAC,EACH,CAAC,CACC,CAAC,cAGVf,KAAA,YAASc,SAAS,CAAC,OAAO,CAAAC,QAAA,eACxBjB,IAAA,OAAIgB,SAAS,CAAC,2DAA2D,CAAAC,QAAA,CAAC,qBAAmB,CAAI,CAAC,cAClGf,KAAA,QAAKc,SAAS,CAAC,sDAAsD,CAAAC,QAAA,eACnEf,KAAA,CAACb,IAAI,EACHiC,EAAE,CAAC,iBAAiB,CACpBN,SAAS,CAAC,qGAAqG,CAAAC,QAAA,eAE/GjB,IAAA,QAAKgB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACvCjB,IAAA,OAAIgB,SAAS,CAAC,iFAAiF,CAAAC,QAAA,CAAC,oBAEhG,CAAI,CAAC,cACLjB,IAAA,MAAGgB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,sCAAoC,CAAG,CAAC,EACzE,CAAC,cAEPf,KAAA,CAACb,IAAI,EACHiC,EAAE,CAAC,iBAAiB,CACpBN,SAAS,CAAC,8GAA8G,CAAAC,QAAA,eAExHjB,IAAA,QAAKgB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,oBAAG,CAAK,CAAC,cACxCjB,IAAA,OAAIgB,SAAS,CAAC,0FAA0F,CAAAC,QAAA,CAAC,qBAEzG,CAAI,CAAC,cACLjB,IAAA,MAAGgB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,2BAAyB,CAAG,CAAC,EAC9D,CAAC,cAEPf,KAAA,CAACb,IAAI,EACHiC,EAAE,CAAC,iBAAiB,CACpBN,SAAS,CAAC,sGAAsG,CAAAC,QAAA,eAEhHjB,IAAA,QAAKgB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,oBAAG,CAAK,CAAC,cACxCjB,IAAA,OAAIgB,SAAS,CAAC,kFAAkF,CAAAC,QAAA,CAAC,qBAEjG,CAAI,CAAC,cACLjB,IAAA,MAAGgB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,oCAAkC,CAAG,CAAC,EACvE,CAAC,cAEPf,KAAA,CAACb,IAAI,EACHiC,EAAE,CAAC,iBAAiB,CACpBN,SAAS,CAAC,sGAAsG,CAAAC,QAAA,eAEhHjB,IAAA,QAAKgB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACvCjB,IAAA,OAAIgB,SAAS,CAAC,kFAAkF,CAAAC,QAAA,CAAC,oBAEjG,CAAI,CAAC,cACLjB,IAAA,MAAGgB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,8BAA4B,CAAG,CAAC,EACjE,CAAC,EACJ,CAAC,EACC,CAAC,cAEVf,KAAA,WAAQc,SAAS,CAAC,iDAAiD,CAAAC,QAAA,eACjEjB,IAAA,MAAGgB,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,yDAAuD,CAAG,CAAC,cACxFjB,IAAA,MAAGgB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,sCAAiC,CAAG,CAAC,EACzE,CAAC,EACN,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAb,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}