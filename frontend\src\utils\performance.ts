/**
 * Performance monitoring utilities for the Reedsoft application
 */

// Performance metrics interface
export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  interactionTime: number;
  memoryUsage?: number;
}

/**
 * Measure component render time
 */
export const measureRenderTime = (componentName: string, renderFn: () => void): number => {
  const startTime = performance.now();
  renderFn();
  const endTime = performance.now();
  const renderTime = endTime - startTime;
  
  if (process.env.NODE_ENV === 'development') {
    console.log(`${componentName} render time: ${renderTime.toFixed(2)}ms`);
  }
  
  return renderTime;
};

/**
 * Measure page load time
 */
export const measurePageLoadTime = (): number => {
  if (typeof window !== 'undefined' && window.performance) {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const loadTime = navigation.loadEventEnd - navigation.fetchStart;
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`Page load time: ${loadTime.toFixed(2)}ms`);
    }
    
    return loadTime;
  }
  return 0;
};

/**
 * Get memory usage information
 */
export const getMemoryUsage = (): number | undefined => {
  if (typeof window !== 'undefined' && 'memory' in performance) {
    const memory = (performance as any).memory;
    return memory.usedJSHeapSize;
  }
  return undefined;
};

/**
 * Log performance metrics
 */
export const logPerformanceMetrics = (metrics: PerformanceMetrics): void => {
  if (process.env.NODE_ENV === 'development') {
    console.group('Performance Metrics');
    console.log(`Load Time: ${metrics.loadTime.toFixed(2)}ms`);
    console.log(`Render Time: ${metrics.renderTime.toFixed(2)}ms`);
    console.log(`Interaction Time: ${metrics.interactionTime.toFixed(2)}ms`);
    if (metrics.memoryUsage) {
      console.log(`Memory Usage: ${(metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB`);
    }
    console.groupEnd();
  }
};

/**
 * Debounce function for performance optimization
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Throttle function for performance optimization
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};
