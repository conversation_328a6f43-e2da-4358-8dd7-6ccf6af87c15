{"ast": null, "code": "import React from'react';import{<PERSON>}from'react-router-dom';import{Radar}from'react-chartjs-2';import{Chart as ChartJS,RadialLinearScale,PointElement,LineElement,Filler,Tooltip,Legend}from'chart.js';import{useChartData}from'../hooks/useChartData';// Register Chart.js components\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";ChartJS.register(RadialLinearScale,PointElement,LineElement,Filler,Tooltip,Legend);const Pillar3=()=>{const{data:knowledgeData,options:knowledgeOptions,loading}=useChartData('knowledge');return/*#__PURE__*/_jsx(\"div\",{className:\"bg-gray-100 text-gray-800 min-h-screen\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"container mx-auto p-4 md:p-8\",children:[/*#__PURE__*/_jsxs(\"header\",{className:\"text-center mb-10\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"inline-block bg-gray-600 text-white rounded-full px-5 py-2 mb-4 text-sm font-bold\",children:\"PILLAR 3: THE HANDS\"}),/*#__PURE__*/_jsx(\"h1\",{className:\"text-4xl md:text-5xl font-black text-gray-800 tracking-tight\",children:\"Acquiring Practical Knowledge\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg text-gray-600 mt-2\",children:\"Know the 'What' and 'Why' to quickly master the 'How' on the job.\"})]}),/*#__PURE__*/_jsxs(\"section\",{id:\"knowledge-areas\",className:\"mb-12 bg-white p-6 rounded-lg shadow-lg\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-center mb-4 text-gray-800\",children:\"Core Knowledge Categories\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-600 mb-6 text-center max-w-2xl mx-auto\",children:\"Your study will focus on these five areas. A balanced understanding across all of them will make you a versatile and adaptable worker.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"relative w-full max-w-lg mx-auto h-96\",children:loading?/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-full\",children:/*#__PURE__*/_jsx(\"div\",{className:\"animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800\"})}):knowledgeData&&knowledgeOptions?/*#__PURE__*/_jsx(Radar,{data:knowledgeData,options:knowledgeOptions}):/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-full text-gray-500\",children:\"Chart loading...\"})})]}),/*#__PURE__*/_jsxs(\"section\",{id:\"learning-loop\",className:\"mb-12 bg-white p-6 rounded-lg shadow-lg\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-center mb-6 text-gray-800\",children:\"The Learning Loop\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-24 h-24 bg-red-500 text-white rounded-full flex items-center justify-center text-4xl mx-auto\",children:\"\\u2460\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"font-bold mt-3\",children:\"Watch & Learn\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm\",children:\"Use curated YouTube channels.\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-4xl font-light text-gray-400\",children:\"\\u2192\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-24 h-24 bg-yellow-500 text-white rounded-full flex items-center justify-center text-4xl mx-auto\",children:\"\\u2461\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"font-bold mt-3\",children:\"Note & Synthesize\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm\",children:\"Keep a dedicated notebook.\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-4xl font-light text-gray-400\",children:\"\\u2192\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-24 h-24 bg-blue-400 text-white rounded-full flex items-center justify-center text-4xl mx-auto\",children:\"\\u2462\"}),/*#__PURE__*/_jsx(\"h3\",{className:\"font-bold mt-3\",children:\"Apply & Practice\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm\",children:\"Do weekend hands-on projects.\"})]})]})]}),/*#__PURE__*/_jsxs(\"section\",{id:\"safety\",className:\"mb-12 bg-yellow-400 text-gray-800 p-6 rounded-lg shadow-lg\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-center mb-4\",children:\"Safety is Non-Negotiable\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-center text-sm mb-6\",children:\"Understanding and respecting safety protocols is the mark of a professional. Learn the purpose of each piece of Personal Protective Equipment (PPE).\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-around text-center\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-5xl\",children:\"\\uD83D\\uDC77\"}),/*#__PURE__*/_jsx(\"p\",{className:\"font-semibold mt-2\",children:\"Hard Hat\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-5xl\",children:\"\\uD83D\\uDC53\"}),/*#__PURE__*/_jsx(\"p\",{className:\"font-semibold mt-2\",children:\"Safety Glasses\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-5xl\",children:\"\\uD83E\\uDDE4\"}),/*#__PURE__*/_jsx(\"p\",{className:\"font-semibold mt-2\",children:\"Gloves\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-5xl\",children:\"\\uD83E\\uDD7E\"}),/*#__PURE__*/_jsx(\"p\",{className:\"font-semibold mt-2\",children:\"Steel-Toe Boots\"})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex justify-between items-center\",children:[/*#__PURE__*/_jsx(Link,{to:\"/japan/pillar-2\",className:\"bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors\",children:\"\\u2190 Previous: Japanese Language\"}),/*#__PURE__*/_jsx(Link,{to:\"/japan/pillar-4\",className:\"bg-reedsoft-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors\",children:\"Next: Cognitive Fitness \\u2192\"})]}),/*#__PURE__*/_jsx(\"footer\",{className:\"text-center mt-10 pt-6 border-t border-gray-300\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-700 font-semibold\",children:\"A smart hand is a safe and productive hand.\"})})]})});};export default Pillar3;", "map": {"version": 3, "names": ["React", "Link", "Radar", "Chart", "ChartJS", "RadialLinearScale", "PointElement", "LineElement", "Filler", "<PERSON><PERSON><PERSON>", "Legend", "useChartData", "jsx", "_jsx", "jsxs", "_jsxs", "register", "Pillar3", "data", "knowledgeData", "options", "knowledgeOptions", "loading", "className", "children", "id", "to"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/pages/Pillar3.tsx"], "sourcesContent": ["import React from 'react';\nimport { <PERSON> } from 'react-router-dom';\nimport { Radar } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  RadialLinearScale,\n  PointElement,\n  LineElement,\n  Filler,\n  Tooltip,\n  Legend\n} from 'chart.js';\nimport { useChartData } from '../hooks/useChartData';\n\n// Register Chart.js components\nChartJS.register(RadialLinearScale, PointElement, LineElement, Filler, Tooltip, Legend);\n\nconst Pillar3: React.FC = () => {\n  const { data: knowledgeData, options: knowledgeOptions, loading } = useChartData('knowledge');\n\n  return (\n    <div className=\"bg-gray-100 text-gray-800 min-h-screen\">\n      <div className=\"container mx-auto p-4 md:p-8\">\n        <header className=\"text-center mb-10\">\n          <div className=\"inline-block bg-gray-600 text-white rounded-full px-5 py-2 mb-4 text-sm font-bold\">\n            PILLAR 3: THE HANDS\n          </div>\n          <h1 className=\"text-4xl md:text-5xl font-black text-gray-800 tracking-tight\">\n            Acquiring Practical Knowledge\n          </h1>\n          <p className=\"text-lg text-gray-600 mt-2\">\n            Know the 'What' and 'Why' to quickly master the 'How' on the job.\n          </p>\n        </header>\n\n        {/* Core Knowledge Categories Section */}\n        <section id=\"knowledge-areas\" className=\"mb-12 bg-white p-6 rounded-lg shadow-lg\">\n          <h2 className=\"text-2xl font-bold text-center mb-4 text-gray-800\">Core Knowledge Categories</h2>\n          <p className=\"text-sm text-gray-600 mb-6 text-center max-w-2xl mx-auto\">\n            Your study will focus on these five areas. A balanced understanding across all of them will make you a versatile and adaptable worker.\n          </p>\n          <div className=\"relative w-full max-w-lg mx-auto h-96\">\n            {loading ? (\n              <div className=\"flex items-center justify-center h-full\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800\"></div>\n              </div>\n            ) : knowledgeData && knowledgeOptions ? (\n              <Radar data={knowledgeData} options={knowledgeOptions as any} />\n            ) : (\n              <div className=\"flex items-center justify-center h-full text-gray-500\">\n                Chart loading...\n              </div>\n            )}\n          </div>\n        </section>\n\n        {/* Learning Loop Section */}\n        <section id=\"learning-loop\" className=\"mb-12 bg-white p-6 rounded-lg shadow-lg\">\n          <h2 className=\"text-2xl font-bold text-center mb-6 text-gray-800\">The Learning Loop</h2>\n          <div className=\"flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-8\">\n            <div className=\"text-center\">\n              <div className=\"w-24 h-24 bg-red-500 text-white rounded-full flex items-center justify-center text-4xl mx-auto\">①</div>\n              <h3 className=\"font-bold mt-3\">Watch & Learn</h3>\n              <p className=\"text-sm\">Use curated YouTube channels.</p>\n            </div>\n            <div className=\"text-4xl font-light text-gray-400\">→</div>\n            <div className=\"text-center\">\n              <div className=\"w-24 h-24 bg-yellow-500 text-white rounded-full flex items-center justify-center text-4xl mx-auto\">②</div>\n              <h3 className=\"font-bold mt-3\">Note & Synthesize</h3>\n              <p className=\"text-sm\">Keep a dedicated notebook.</p>\n            </div>\n            <div className=\"text-4xl font-light text-gray-400\">→</div>\n            <div className=\"text-center\">\n              <div className=\"w-24 h-24 bg-blue-400 text-white rounded-full flex items-center justify-center text-4xl mx-auto\">③</div>\n              <h3 className=\"font-bold mt-3\">Apply & Practice</h3>\n              <p className=\"text-sm\">Do weekend hands-on projects.</p>\n            </div>\n          </div>\n        </section>\n\n        {/* Safety Section */}\n        <section id=\"safety\" className=\"mb-12 bg-yellow-400 text-gray-800 p-6 rounded-lg shadow-lg\">\n          <h2 className=\"text-2xl font-bold text-center mb-4\">Safety is Non-Negotiable</h2>\n          <p className=\"text-center text-sm mb-6\">\n            Understanding and respecting safety protocols is the mark of a professional. Learn the purpose of each piece of Personal Protective Equipment (PPE).\n          </p>\n          <div className=\"flex justify-around text-center\">\n            <div><div className=\"text-5xl\">👷</div><p className=\"font-semibold mt-2\">Hard Hat</p></div>\n            <div><div className=\"text-5xl\">👓</div><p className=\"font-semibold mt-2\">Safety Glasses</p></div>\n            <div><div className=\"text-5xl\">🧤</div><p className=\"font-semibold mt-2\">Gloves</p></div>\n            <div><div className=\"text-5xl\">🥾</div><p className=\"font-semibold mt-2\">Steel-Toe Boots</p></div>\n          </div>\n        </section>\n\n        {/* Navigation */}\n        <div className=\"flex justify-between items-center\">\n          <Link\n            to=\"/japan/pillar-2\"\n            className=\"bg-gray-200 text-gray-800 px-6 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors\"\n          >\n            ← Previous: Japanese Language\n          </Link>\n          <Link\n            to=\"/japan/pillar-4\"\n            className=\"bg-reedsoft-primary text-white px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors\"\n          >\n            Next: Cognitive Fitness →\n          </Link>\n        </div>\n\n        <footer className=\"text-center mt-10 pt-6 border-t border-gray-300\">\n          <p className=\"text-gray-700 font-semibold\">A smart hand is a safe and productive hand.</p>\n        </footer>\n      </div>\n    </div>\n  );\n};\n\nexport default Pillar3;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,KAAK,KAAQ,iBAAiB,CACvC,OACEC,KAAK,GAAI,CAAAC,OAAO,CAChBC,iBAAiB,CACjBC,YAAY,CACZC,WAAW,CACXC,MAAM,CACNC,OAAO,CACPC,MAAM,KACD,UAAU,CACjB,OAASC,YAAY,KAAQ,uBAAuB,CAEpD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACAX,OAAO,CAACY,QAAQ,CAACX,iBAAiB,CAAEC,YAAY,CAAEC,WAAW,CAAEC,MAAM,CAAEC,OAAO,CAAEC,MAAM,CAAC,CAEvF,KAAM,CAAAO,OAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAEC,IAAI,CAAEC,aAAa,CAAEC,OAAO,CAAEC,gBAAgB,CAAEC,OAAQ,CAAC,CAAGX,YAAY,CAAC,WAAW,CAAC,CAE7F,mBACEE,IAAA,QAAKU,SAAS,CAAC,wCAAwC,CAAAC,QAAA,cACrDT,KAAA,QAAKQ,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3CT,KAAA,WAAQQ,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eACnCX,IAAA,QAAKU,SAAS,CAAC,mFAAmF,CAAAC,QAAA,CAAC,qBAEnG,CAAK,CAAC,cACNX,IAAA,OAAIU,SAAS,CAAC,8DAA8D,CAAAC,QAAA,CAAC,+BAE7E,CAAI,CAAC,cACLX,IAAA,MAAGU,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,mEAE1C,CAAG,CAAC,EACE,CAAC,cAGTT,KAAA,YAASU,EAAE,CAAC,iBAAiB,CAACF,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eAC/EX,IAAA,OAAIU,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAAC,2BAAyB,CAAI,CAAC,cAChGX,IAAA,MAAGU,SAAS,CAAC,0DAA0D,CAAAC,QAAA,CAAC,wIAExE,CAAG,CAAC,cACJX,IAAA,QAAKU,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CACnDF,OAAO,cACNT,IAAA,QAAKU,SAAS,CAAC,yCAAyC,CAAAC,QAAA,cACtDX,IAAA,QAAKU,SAAS,CAAC,gEAAgE,CAAM,CAAC,CACnF,CAAC,CACJJ,aAAa,EAAIE,gBAAgB,cACnCR,IAAA,CAACX,KAAK,EAACgB,IAAI,CAAEC,aAAc,CAACC,OAAO,CAAEC,gBAAwB,CAAE,CAAC,cAEhER,IAAA,QAAKU,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,kBAEvE,CAAK,CACN,CACE,CAAC,EACC,CAAC,cAGVT,KAAA,YAASU,EAAE,CAAC,eAAe,CAACF,SAAS,CAAC,yCAAyC,CAAAC,QAAA,eAC7EX,IAAA,OAAIU,SAAS,CAAC,mDAAmD,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cACxFT,KAAA,QAAKQ,SAAS,CAAC,2FAA2F,CAAAC,QAAA,eACxGT,KAAA,QAAKQ,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BX,IAAA,QAAKU,SAAS,CAAC,gGAAgG,CAAAC,QAAA,CAAC,QAAC,CAAK,CAAC,cACvHX,IAAA,OAAIU,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,eAAa,CAAI,CAAC,cACjDX,IAAA,MAAGU,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,+BAA6B,CAAG,CAAC,EACrD,CAAC,cACNX,IAAA,QAAKU,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,QAAC,CAAK,CAAC,cAC1DT,KAAA,QAAKQ,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BX,IAAA,QAAKU,SAAS,CAAC,mGAAmG,CAAAC,QAAA,CAAC,QAAC,CAAK,CAAC,cAC1HX,IAAA,OAAIU,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cACrDX,IAAA,MAAGU,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,4BAA0B,CAAG,CAAC,EAClD,CAAC,cACNX,IAAA,QAAKU,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,QAAC,CAAK,CAAC,cAC1DT,KAAA,QAAKQ,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BX,IAAA,QAAKU,SAAS,CAAC,iGAAiG,CAAAC,QAAA,CAAC,QAAC,CAAK,CAAC,cACxHX,IAAA,OAAIU,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,kBAAgB,CAAI,CAAC,cACpDX,IAAA,MAAGU,SAAS,CAAC,SAAS,CAAAC,QAAA,CAAC,+BAA6B,CAAG,CAAC,EACrD,CAAC,EACH,CAAC,EACC,CAAC,cAGVT,KAAA,YAASU,EAAE,CAAC,QAAQ,CAACF,SAAS,CAAC,4DAA4D,CAAAC,QAAA,eACzFX,IAAA,OAAIU,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,0BAAwB,CAAI,CAAC,cACjFX,IAAA,MAAGU,SAAS,CAAC,0BAA0B,CAAAC,QAAA,CAAC,sJAExC,CAAG,CAAC,cACJT,KAAA,QAAKQ,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9CT,KAAA,QAAAS,QAAA,eAAKX,IAAA,QAAKU,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cAAAX,IAAA,MAAGU,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,UAAQ,CAAG,CAAC,EAAK,CAAC,cAC3FT,KAAA,QAAAS,QAAA,eAAKX,IAAA,QAAKU,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cAAAX,IAAA,MAAGU,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,gBAAc,CAAG,CAAC,EAAK,CAAC,cACjGT,KAAA,QAAAS,QAAA,eAAKX,IAAA,QAAKU,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cAAAX,IAAA,MAAGU,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,QAAM,CAAG,CAAC,EAAK,CAAC,cACzFT,KAAA,QAAAS,QAAA,eAAKX,IAAA,QAAKU,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cAAAX,IAAA,MAAGU,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,iBAAe,CAAG,CAAC,EAAK,CAAC,EAC/F,CAAC,EACC,CAAC,cAGVT,KAAA,QAAKQ,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDX,IAAA,CAACZ,IAAI,EACHyB,EAAE,CAAC,iBAAiB,CACpBH,SAAS,CAAC,kGAAkG,CAAAC,QAAA,CAC7G,oCAED,CAAM,CAAC,cACPX,IAAA,CAACZ,IAAI,EACHyB,EAAE,CAAC,iBAAiB,CACpBH,SAAS,CAAC,iHAAiH,CAAAC,QAAA,CAC5H,gCAED,CAAM,CAAC,EACJ,CAAC,cAENX,IAAA,WAAQU,SAAS,CAAC,iDAAiD,CAAAC,QAAA,cACjEX,IAAA,MAAGU,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,6CAA2C,CAAG,CAAC,CACpF,CAAC,EACN,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAP,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}