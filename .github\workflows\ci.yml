name: CI/CD Pipeline

on:
  push:
    branches: [ master, main, develop ]
  pull_request:
    branches: [ master, main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        
    - name: Install dependencies
      run: |
        npm ci
        npm run install:all
        
    - name: Run TypeScript type checking
      run: |
        cd frontend
        npx tsc --noEmit
        
    - name: Run ESLint
      run: |
        cd frontend
        npx eslint src --ext .ts,.tsx --max-warnings 0
        
    - name: Run tests
      run: |
        cd frontend
        npm test -- --coverage --watchAll=false
        
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        directory: ./frontend/coverage
        
  build:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: |
        npm ci
        npm run install:all
        
    - name: Build frontend
      run: |
        cd frontend
        npm run build
        
    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-files
        path: frontend/build/
        retention-days: 7
