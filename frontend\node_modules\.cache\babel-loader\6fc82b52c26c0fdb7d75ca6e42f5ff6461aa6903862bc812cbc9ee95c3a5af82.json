{"ast": null, "code": "import React from'react';import{Link}from'react-router-dom';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ReedsoftLanding=()=>{return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gradient-to-br from-reedsoft-light to-white\",children:[/*#__PURE__*/_jsx(\"section\",{className:\"pt-20 pb-16 px-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-6xl mx-auto text-center\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-5xl md:text-7xl font-black text-reedsoft-primary mb-6 tracking-tight\",children:\"REEDSOFT\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xl md:text-2xl text-reedsoft-secondary mb-8 max-w-3xl mx-auto\",children:\"Building innovative solutions for personal and professional development\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg text-gray-600 mb-12 max-w-2xl mx-auto\",children:\"We create comprehensive digital experiences that transform ambitious goals into structured, achievable plans.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row gap-4 justify-center\",children:[/*#__PURE__*/_jsx(Link,{to:\"/japan\",className:\"bg-reedsoft-primary text-white px-8 py-4 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors shadow-lg\",children:\"Explore Japan Project\"}),/*#__PURE__*/_jsx(\"button\",{className:\"bg-white text-reedsoft-primary px-8 py-4 rounded-lg font-semibold border-2 border-reedsoft-primary hover:bg-reedsoft-primary hover:text-white transition-colors\",children:\"Learn More\"})]})]})}),/*#__PURE__*/_jsx(\"section\",{className:\"py-16 px-4 bg-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-6xl mx-auto\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl md:text-4xl font-bold text-center text-reedsoft-primary mb-12\",children:\"Featured Project\"}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-reedsoft-primary to-reedsoft-secondary rounded-2xl p-8 md:p-12 text-white\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"grid md:grid-cols-2 gap-8 items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-3xl md:text-4xl font-bold mb-4\",children:\"Japan Work Preparation Plan\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-lg mb-6 opacity-90\",children:\"A comprehensive 4-month training program featuring four core pillars: Physical Fitness, Japanese Language, Practical Skills, and Cognitive Fitness.\"}),/*#__PURE__*/_jsxs(\"ul\",{className:\"space-y-2 mb-8\",children:[/*#__PURE__*/_jsxs(\"li\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"w-2 h-2 bg-reedsoft-light rounded-full mr-3\"}),\"Interactive progress tracking\"]}),/*#__PURE__*/_jsxs(\"li\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"w-2 h-2 bg-reedsoft-light rounded-full mr-3\"}),\"Data visualizations with Chart.js\"]}),/*#__PURE__*/_jsxs(\"li\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"w-2 h-2 bg-reedsoft-light rounded-full mr-3\"}),\"Structured daily schedules\"]}),/*#__PURE__*/_jsxs(\"li\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"w-2 h-2 bg-reedsoft-light rounded-full mr-3\"}),\"Responsive design for all devices\"]})]}),/*#__PURE__*/_jsx(Link,{to:\"/japan\",className:\"inline-block bg-white text-reedsoft-primary px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-light transition-colors\",children:\"View Project \\u2192\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white bg-opacity-10 rounded-lg p-4 text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl font-bold mb-1\",children:\"4\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm opacity-80\",children:\"Core Pillars\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white bg-opacity-10 rounded-lg p-4 text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl font-bold mb-1\",children:\"16\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm opacity-80\",children:\"Weeks Program\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white bg-opacity-10 rounded-lg p-4 text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl font-bold mb-1\",children:\"45+\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm opacity-80\",children:\"Hours/Week\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white bg-opacity-10 rounded-lg p-4 text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl font-bold mb-1\",children:\"100%\"}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm opacity-80\",children:\"Commitment\"})]})]})]})})]})}),/*#__PURE__*/_jsx(\"section\",{className:\"py-16 px-4 bg-gray-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-6xl mx-auto\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl md:text-4xl font-bold text-center text-reedsoft-primary mb-12\",children:\"Built with Modern Technology\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid md:grid-cols-3 gap-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-16 h-16 bg-reedsoft-primary rounded-full flex items-center justify-center mx-auto mb-4\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-white font-bold text-xl\",children:\"R\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold mb-2\",children:\"React & TypeScript\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Modern frontend with type safety and component-based architecture\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-16 h-16 bg-reedsoft-secondary rounded-full flex items-center justify-center mx-auto mb-4\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-white font-bold text-xl\",children:\"T\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold mb-2\",children:\"Tailwind CSS\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Utility-first CSS framework for rapid UI development\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-16 h-16 bg-reedsoft-accent rounded-full flex items-center justify-center mx-auto mb-4\",children:/*#__PURE__*/_jsx(\"span\",{className:\"text-white font-bold text-xl\",children:\"C\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl font-semibold mb-2\",children:\"Chart.js\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Interactive data visualizations and progress tracking\"})]})]})]})}),/*#__PURE__*/_jsx(\"footer\",{className:\"bg-reedsoft-primary text-white py-12 px-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-6xl mx-auto text-center\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl font-bold mb-4\",children:\"Ready to Transform Your Goals?\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-reedsoft-light mb-8 max-w-2xl mx-auto\",children:\"Join the journey of structured personal development with data-driven insights and comprehensive planning.\"}),/*#__PURE__*/_jsx(Link,{to:\"/japan\",className:\"inline-block bg-white text-reedsoft-primary px-8 py-4 rounded-lg font-semibold hover:bg-reedsoft-light transition-colors\",children:\"Start Your Journey\"}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-12 pt-8 border-t border-reedsoft-secondary\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-reedsoft-light\",children:\"\\xA9 2025 Reedsoft. Building innovative solutions for personal and professional development.\"})})]})})]});};export default ReedsoftLanding;", "map": {"version": 3, "names": ["React", "Link", "jsx", "_jsx", "jsxs", "_jsxs", "ReedsoftLanding", "className", "children", "to"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/pages/ReedsoftLanding.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\n\nconst ReedsoftLanding: React.FC = () => {\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-reedsoft-light to-white\">\n      {/* Hero Section */}\n      <section className=\"pt-20 pb-16 px-4\">\n        <div className=\"max-w-6xl mx-auto text-center\">\n          <h1 className=\"text-5xl md:text-7xl font-black text-reedsoft-primary mb-6 tracking-tight\">\n            REEDSOFT\n          </h1>\n          <p className=\"text-xl md:text-2xl text-reedsoft-secondary mb-8 max-w-3xl mx-auto\">\n            Building innovative solutions for personal and professional development\n          </p>\n          <p className=\"text-lg text-gray-600 mb-12 max-w-2xl mx-auto\">\n            We create comprehensive digital experiences that transform ambitious goals into structured, achievable plans.\n          </p>\n          \n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Link\n              to=\"/japan\"\n              className=\"bg-reedsoft-primary text-white px-8 py-4 rounded-lg font-semibold hover:bg-reedsoft-secondary transition-colors shadow-lg\"\n            >\n              Explore Japan Project\n            </Link>\n            <button className=\"bg-white text-reedsoft-primary px-8 py-4 rounded-lg font-semibold border-2 border-reedsoft-primary hover:bg-reedsoft-primary hover:text-white transition-colors\">\n              Learn More\n            </button>\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Project Section */}\n      <section className=\"py-16 px-4 bg-white\">\n        <div className=\"max-w-6xl mx-auto\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-center text-reedsoft-primary mb-12\">\n            Featured Project\n          </h2>\n          \n          <div className=\"bg-gradient-to-r from-reedsoft-primary to-reedsoft-secondary rounded-2xl p-8 md:p-12 text-white\">\n            <div className=\"grid md:grid-cols-2 gap-8 items-center\">\n              <div>\n                <h3 className=\"text-3xl md:text-4xl font-bold mb-4\">\n                  Japan Work Preparation Plan\n                </h3>\n                <p className=\"text-lg mb-6 opacity-90\">\n                  A comprehensive 4-month training program featuring four core pillars: Physical Fitness, Japanese Language, Practical Skills, and Cognitive Fitness.\n                </p>\n                <ul className=\"space-y-2 mb-8\">\n                  <li className=\"flex items-center\">\n                    <span className=\"w-2 h-2 bg-reedsoft-light rounded-full mr-3\"></span>\n                    Interactive progress tracking\n                  </li>\n                  <li className=\"flex items-center\">\n                    <span className=\"w-2 h-2 bg-reedsoft-light rounded-full mr-3\"></span>\n                    Data visualizations with Chart.js\n                  </li>\n                  <li className=\"flex items-center\">\n                    <span className=\"w-2 h-2 bg-reedsoft-light rounded-full mr-3\"></span>\n                    Structured daily schedules\n                  </li>\n                  <li className=\"flex items-center\">\n                    <span className=\"w-2 h-2 bg-reedsoft-light rounded-full mr-3\"></span>\n                    Responsive design for all devices\n                  </li>\n                </ul>\n                <Link\n                  to=\"/japan\"\n                  className=\"inline-block bg-white text-reedsoft-primary px-6 py-3 rounded-lg font-semibold hover:bg-reedsoft-light transition-colors\"\n                >\n                  View Project →\n                </Link>\n              </div>\n              \n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"bg-white bg-opacity-10 rounded-lg p-4 text-center\">\n                  <div className=\"text-2xl font-bold mb-1\">4</div>\n                  <div className=\"text-sm opacity-80\">Core Pillars</div>\n                </div>\n                <div className=\"bg-white bg-opacity-10 rounded-lg p-4 text-center\">\n                  <div className=\"text-2xl font-bold mb-1\">16</div>\n                  <div className=\"text-sm opacity-80\">Weeks Program</div>\n                </div>\n                <div className=\"bg-white bg-opacity-10 rounded-lg p-4 text-center\">\n                  <div className=\"text-2xl font-bold mb-1\">45+</div>\n                  <div className=\"text-sm opacity-80\">Hours/Week</div>\n                </div>\n                <div className=\"bg-white bg-opacity-10 rounded-lg p-4 text-center\">\n                  <div className=\"text-2xl font-bold mb-1\">100%</div>\n                  <div className=\"text-sm opacity-80\">Commitment</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Technology Stack Section */}\n      <section className=\"py-16 px-4 bg-gray-50\">\n        <div className=\"max-w-6xl mx-auto\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-center text-reedsoft-primary mb-12\">\n            Built with Modern Technology\n          </h2>\n          \n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-reedsoft-primary rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-white font-bold text-xl\">R</span>\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">React & TypeScript</h3>\n              <p className=\"text-gray-600\">Modern frontend with type safety and component-based architecture</p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-reedsoft-secondary rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-white font-bold text-xl\">T</span>\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">Tailwind CSS</h3>\n              <p className=\"text-gray-600\">Utility-first CSS framework for rapid UI development</p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"w-16 h-16 bg-reedsoft-accent rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-white font-bold text-xl\">C</span>\n              </div>\n              <h3 className=\"text-xl font-semibold mb-2\">Chart.js</h3>\n              <p className=\"text-gray-600\">Interactive data visualizations and progress tracking</p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"bg-reedsoft-primary text-white py-12 px-4\">\n        <div className=\"max-w-6xl mx-auto text-center\">\n          <h3 className=\"text-2xl font-bold mb-4\">Ready to Transform Your Goals?</h3>\n          <p className=\"text-reedsoft-light mb-8 max-w-2xl mx-auto\">\n            Join the journey of structured personal development with data-driven insights and comprehensive planning.\n          </p>\n          <Link\n            to=\"/japan\"\n            className=\"inline-block bg-white text-reedsoft-primary px-8 py-4 rounded-lg font-semibold hover:bg-reedsoft-light transition-colors\"\n          >\n            Start Your Journey\n          </Link>\n          \n          <div className=\"mt-12 pt-8 border-t border-reedsoft-secondary\">\n            <p className=\"text-reedsoft-light\">\n              © 2025 Reedsoft. Building innovative solutions for personal and professional development.\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n};\n\nexport default ReedsoftLanding;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAExC,KAAM,CAAAC,eAAyB,CAAGA,CAAA,GAAM,CACtC,mBACED,KAAA,QAAKE,SAAS,CAAC,6DAA6D,CAAAC,QAAA,eAE1EL,IAAA,YAASI,SAAS,CAAC,kBAAkB,CAAAC,QAAA,cACnCH,KAAA,QAAKE,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CL,IAAA,OAAII,SAAS,CAAC,2EAA2E,CAAAC,QAAA,CAAC,UAE1F,CAAI,CAAC,cACLL,IAAA,MAAGI,SAAS,CAAC,oEAAoE,CAAAC,QAAA,CAAC,yEAElF,CAAG,CAAC,cACJL,IAAA,MAAGI,SAAS,CAAC,+CAA+C,CAAAC,QAAA,CAAC,+GAE7D,CAAG,CAAC,cAEJH,KAAA,QAAKE,SAAS,CAAC,gDAAgD,CAAAC,QAAA,eAC7DL,IAAA,CAACF,IAAI,EACHQ,EAAE,CAAC,QAAQ,CACXF,SAAS,CAAC,2HAA2H,CAAAC,QAAA,CACtI,uBAED,CAAM,CAAC,cACPL,IAAA,WAAQI,SAAS,CAAC,iKAAiK,CAAAC,QAAA,CAAC,YAEpL,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,CACC,CAAC,cAGVL,IAAA,YAASI,SAAS,CAAC,qBAAqB,CAAAC,QAAA,cACtCH,KAAA,QAAKE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCL,IAAA,OAAII,SAAS,CAAC,wEAAwE,CAAAC,QAAA,CAAC,kBAEvF,CAAI,CAAC,cAELL,IAAA,QAAKI,SAAS,CAAC,iGAAiG,CAAAC,QAAA,cAC9GH,KAAA,QAAKE,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDH,KAAA,QAAAG,QAAA,eACEL,IAAA,OAAII,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,6BAEpD,CAAI,CAAC,cACLL,IAAA,MAAGI,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,qJAEvC,CAAG,CAAC,cACJH,KAAA,OAAIE,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC5BH,KAAA,OAAIE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC/BL,IAAA,SAAMI,SAAS,CAAC,6CAA6C,CAAO,CAAC,gCAEvE,EAAI,CAAC,cACLF,KAAA,OAAIE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC/BL,IAAA,SAAMI,SAAS,CAAC,6CAA6C,CAAO,CAAC,oCAEvE,EAAI,CAAC,cACLF,KAAA,OAAIE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC/BL,IAAA,SAAMI,SAAS,CAAC,6CAA6C,CAAO,CAAC,6BAEvE,EAAI,CAAC,cACLF,KAAA,OAAIE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAC/BL,IAAA,SAAMI,SAAS,CAAC,6CAA6C,CAAO,CAAC,oCAEvE,EAAI,CAAC,EACH,CAAC,cACLJ,IAAA,CAACF,IAAI,EACHQ,EAAE,CAAC,QAAQ,CACXF,SAAS,CAAC,0HAA0H,CAAAC,QAAA,CACrI,qBAED,CAAM,CAAC,EACJ,CAAC,cAENH,KAAA,QAAKE,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCH,KAAA,QAAKE,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChEL,IAAA,QAAKI,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,GAAC,CAAK,CAAC,cAChDL,IAAA,QAAKI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,cAAY,CAAK,CAAC,EACnD,CAAC,cACNH,KAAA,QAAKE,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChEL,IAAA,QAAKI,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,IAAE,CAAK,CAAC,cACjDL,IAAA,QAAKI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,eAAa,CAAK,CAAC,EACpD,CAAC,cACNH,KAAA,QAAKE,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChEL,IAAA,QAAKI,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,KAAG,CAAK,CAAC,cAClDL,IAAA,QAAKI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,YAAU,CAAK,CAAC,EACjD,CAAC,cACNH,KAAA,QAAKE,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChEL,IAAA,QAAKI,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,MAAI,CAAK,CAAC,cACnDL,IAAA,QAAKI,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,YAAU,CAAK,CAAC,EACjD,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACC,CAAC,cAGVL,IAAA,YAASI,SAAS,CAAC,uBAAuB,CAAAC,QAAA,cACxCH,KAAA,QAAKE,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChCL,IAAA,OAAII,SAAS,CAAC,wEAAwE,CAAAC,QAAA,CAAC,8BAEvF,CAAI,CAAC,cAELH,KAAA,QAAKE,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxCH,KAAA,QAAKE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BL,IAAA,QAAKI,SAAS,CAAC,0FAA0F,CAAAC,QAAA,cACvGL,IAAA,SAAMI,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,CACpD,CAAC,cACNL,IAAA,OAAII,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,oBAAkB,CAAI,CAAC,cAClEL,IAAA,MAAGI,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,mEAAiE,CAAG,CAAC,EAC/F,CAAC,cAENH,KAAA,QAAKE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BL,IAAA,QAAKI,SAAS,CAAC,4FAA4F,CAAAC,QAAA,cACzGL,IAAA,SAAMI,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,CACpD,CAAC,cACNL,IAAA,OAAII,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,cAAY,CAAI,CAAC,cAC5DL,IAAA,MAAGI,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,sDAAoD,CAAG,CAAC,EAClF,CAAC,cAENH,KAAA,QAAKE,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BL,IAAA,QAAKI,SAAS,CAAC,yFAAyF,CAAAC,QAAA,cACtGL,IAAA,SAAMI,SAAS,CAAC,8BAA8B,CAAAC,QAAA,CAAC,GAAC,CAAM,CAAC,CACpD,CAAC,cACNL,IAAA,OAAII,SAAS,CAAC,4BAA4B,CAAAC,QAAA,CAAC,UAAQ,CAAI,CAAC,cACxDL,IAAA,MAAGI,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,uDAAqD,CAAG,CAAC,EACnF,CAAC,EACH,CAAC,EACH,CAAC,CACC,CAAC,cAGVL,IAAA,WAAQI,SAAS,CAAC,2CAA2C,CAAAC,QAAA,cAC3DH,KAAA,QAAKE,SAAS,CAAC,+BAA+B,CAAAC,QAAA,eAC5CL,IAAA,OAAII,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,gCAA8B,CAAI,CAAC,cAC3EL,IAAA,MAAGI,SAAS,CAAC,4CAA4C,CAAAC,QAAA,CAAC,2GAE1D,CAAG,CAAC,cACJL,IAAA,CAACF,IAAI,EACHQ,EAAE,CAAC,QAAQ,CACXF,SAAS,CAAC,0HAA0H,CAAAC,QAAA,CACrI,oBAED,CAAM,CAAC,cAEPL,IAAA,QAAKI,SAAS,CAAC,+CAA+C,CAAAC,QAAA,cAC5DL,IAAA,MAAGI,SAAS,CAAC,qBAAqB,CAAAC,QAAA,CAAC,8FAEnC,CAAG,CAAC,CACD,CAAC,EACH,CAAC,CACA,CAAC,EACN,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}