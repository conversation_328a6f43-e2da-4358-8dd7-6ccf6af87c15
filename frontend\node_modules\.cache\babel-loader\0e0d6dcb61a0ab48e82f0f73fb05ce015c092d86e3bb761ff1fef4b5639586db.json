{"ast": null, "code": "/**\n * react-router v7.9.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\n\"use client\";\n\nimport _objectSpread from \"C:/Developer/Web Development/reedsoft/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { deserializeErrors, getHydrationData } from \"./chunk-AKKEMMKB.mjs\";\nimport { CRITICAL_CSS_DATA_ATTRIBUTE, FrameworkContext, RemixErrorBoundary, RouterProvider, createBrowserHistory, createClientRoutes, createClientRoutesWithHMRRevalidationOptOut, createRouter, decodeViaTurboStream, getPatchRoutesOnNavigationFunction, getTurboStreamSingleFetchDataStrategy, hydrationRouteProperties, invariant, mapRouteProperties, useFogOFWarDiscovery } from \"./chunk-S5YDGZLY.mjs\";\n\n// lib/dom-export/dom-router-provider.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nfunction RouterProvider2(props) {\n  return /* @__PURE__ */React.createElement(RouterProvider, _objectSpread({\n    flushSync: ReactDOM.flushSync\n  }, props));\n}\n\n// lib/dom-export/hydrated-router.tsx\nimport * as React2 from \"react\";\nvar ssrInfo = null;\nvar router = null;\nfunction initSsrInfo() {\n  if (!ssrInfo && window.__reactRouterContext && window.__reactRouterManifest && window.__reactRouterRouteModules) {\n    if (window.__reactRouterManifest.sri === true) {\n      const importMap = document.querySelector(\"script[rr-importmap]\");\n      if (importMap !== null && importMap !== void 0 && importMap.textContent) {\n        try {\n          window.__reactRouterManifest.sri = JSON.parse(importMap.textContent).integrity;\n        } catch (err) {\n          console.error(\"Failed to parse import map\", err);\n        }\n      }\n    }\n    ssrInfo = {\n      context: window.__reactRouterContext,\n      manifest: window.__reactRouterManifest,\n      routeModules: window.__reactRouterRouteModules,\n      stateDecodingPromise: void 0,\n      router: void 0,\n      routerInitialized: false\n    };\n  }\n}\nfunction createHydratedRouter(_ref) {\n  let {\n    getContext\n  } = _ref;\n  initSsrInfo();\n  if (!ssrInfo) {\n    throw new Error(\"You must be using the SSR features of React Router in order to skip passing a `router` prop to `<RouterProvider>`\");\n  }\n  let localSsrInfo = ssrInfo;\n  if (!ssrInfo.stateDecodingPromise) {\n    let stream = ssrInfo.context.stream;\n    invariant(stream, \"No stream found for single fetch decoding\");\n    ssrInfo.context.stream = void 0;\n    ssrInfo.stateDecodingPromise = decodeViaTurboStream(stream, window).then(value => {\n      ssrInfo.context.state = value.value;\n      localSsrInfo.stateDecodingPromise.value = true;\n    }).catch(e => {\n      localSsrInfo.stateDecodingPromise.error = e;\n    });\n  }\n  if (ssrInfo.stateDecodingPromise.error) {\n    throw ssrInfo.stateDecodingPromise.error;\n  }\n  if (!ssrInfo.stateDecodingPromise.value) {\n    throw ssrInfo.stateDecodingPromise;\n  }\n  let routes = createClientRoutes(ssrInfo.manifest.routes, ssrInfo.routeModules, ssrInfo.context.state, ssrInfo.context.ssr, ssrInfo.context.isSpaMode);\n  let hydrationData = void 0;\n  if (ssrInfo.context.isSpaMode) {\n    var _ssrInfo$manifest$rou;\n    let {\n      loaderData\n    } = ssrInfo.context.state;\n    if ((_ssrInfo$manifest$rou = ssrInfo.manifest.routes.root) !== null && _ssrInfo$manifest$rou !== void 0 && _ssrInfo$manifest$rou.hasLoader && loaderData && \"root\" in loaderData) {\n      hydrationData = {\n        loaderData: {\n          root: loaderData.root\n        }\n      };\n    }\n  } else {\n    var _window$__reactRouter;\n    hydrationData = getHydrationData({\n      state: ssrInfo.context.state,\n      routes,\n      getRouteInfo: routeId => {\n        var _ssrInfo$routeModules, _ssrInfo$manifest$rou2, _ssrInfo$routeModules2;\n        return {\n          clientLoader: (_ssrInfo$routeModules = ssrInfo.routeModules[routeId]) === null || _ssrInfo$routeModules === void 0 ? void 0 : _ssrInfo$routeModules.clientLoader,\n          hasLoader: ((_ssrInfo$manifest$rou2 = ssrInfo.manifest.routes[routeId]) === null || _ssrInfo$manifest$rou2 === void 0 ? void 0 : _ssrInfo$manifest$rou2.hasLoader) === true,\n          hasHydrateFallback: ((_ssrInfo$routeModules2 = ssrInfo.routeModules[routeId]) === null || _ssrInfo$routeModules2 === void 0 ? void 0 : _ssrInfo$routeModules2.HydrateFallback) != null\n        };\n      },\n      location: window.location,\n      basename: (_window$__reactRouter = window.__reactRouterContext) === null || _window$__reactRouter === void 0 ? void 0 : _window$__reactRouter.basename,\n      isSpaMode: ssrInfo.context.isSpaMode\n    });\n    if (hydrationData && hydrationData.errors) {\n      hydrationData.errors = deserializeErrors(hydrationData.errors);\n    }\n  }\n  let router2 = createRouter({\n    routes,\n    history: createBrowserHistory(),\n    basename: ssrInfo.context.basename,\n    getContext,\n    hydrationData,\n    hydrationRouteProperties,\n    mapRouteProperties,\n    future: {\n      middleware: ssrInfo.context.future.v8_middleware\n    },\n    dataStrategy: getTurboStreamSingleFetchDataStrategy(() => router2, ssrInfo.manifest, ssrInfo.routeModules, ssrInfo.context.ssr, ssrInfo.context.basename),\n    patchRoutesOnNavigation: getPatchRoutesOnNavigationFunction(ssrInfo.manifest, ssrInfo.routeModules, ssrInfo.context.ssr, ssrInfo.context.routeDiscovery, ssrInfo.context.isSpaMode, ssrInfo.context.basename)\n  });\n  ssrInfo.router = router2;\n  if (router2.state.initialized) {\n    ssrInfo.routerInitialized = true;\n    router2.initialize();\n  }\n  router2.createRoutesForHMR = /* spacer so ts-ignore does not affect the right hand of the assignment */\n  createClientRoutesWithHMRRevalidationOptOut;\n  window.__reactRouterDataRouter = router2;\n  return router2;\n}\nfunction HydratedRouter(props) {\n  var _ssrInfo;\n  if (!router) {\n    router = createHydratedRouter({\n      getContext: props.getContext\n    });\n  }\n  let [criticalCss, setCriticalCss] = React2.useState(process.env.NODE_ENV === \"development\" ? (_ssrInfo = ssrInfo) === null || _ssrInfo === void 0 ? void 0 : _ssrInfo.context.criticalCss : void 0);\n  React2.useEffect(() => {\n    if (process.env.NODE_ENV === \"development\") {\n      setCriticalCss(void 0);\n    }\n  }, []);\n  React2.useEffect(() => {\n    if (process.env.NODE_ENV === \"development\" && criticalCss === void 0) {\n      document.querySelectorAll(\"[\".concat(CRITICAL_CSS_DATA_ATTRIBUTE, \"]\")).forEach(element => element.remove());\n    }\n  }, [criticalCss]);\n  let [location, setLocation] = React2.useState(router.state.location);\n  React2.useLayoutEffect(() => {\n    if (ssrInfo && ssrInfo.router && !ssrInfo.routerInitialized) {\n      ssrInfo.routerInitialized = true;\n      ssrInfo.router.initialize();\n    }\n  }, []);\n  React2.useLayoutEffect(() => {\n    if (ssrInfo && ssrInfo.router) {\n      return ssrInfo.router.subscribe(newState => {\n        if (newState.location !== location) {\n          setLocation(newState.location);\n        }\n      });\n    }\n  }, [location]);\n  invariant(ssrInfo, \"ssrInfo unavailable for HydratedRouter\");\n  useFogOFWarDiscovery(router, ssrInfo.manifest, ssrInfo.routeModules, ssrInfo.context.ssr, ssrInfo.context.routeDiscovery, ssrInfo.context.isSpaMode);\n  return (\n    // This fragment is important to ensure we match the <ServerRouter> JSX\n    // structure so that useId values hydrate correctly\n    /* @__PURE__ */\n    React2.createElement(React2.Fragment, null, /* @__PURE__ */React2.createElement(FrameworkContext.Provider, {\n      value: {\n        manifest: ssrInfo.manifest,\n        routeModules: ssrInfo.routeModules,\n        future: ssrInfo.context.future,\n        criticalCss,\n        ssr: ssrInfo.context.ssr,\n        isSpaMode: ssrInfo.context.isSpaMode,\n        routeDiscovery: ssrInfo.context.routeDiscovery\n      }\n    }, /* @__PURE__ */React2.createElement(RemixErrorBoundary, {\n      location\n    }, /* @__PURE__ */React2.createElement(RouterProvider2, {\n      router,\n      unstable_onError: props.unstable_onError\n    }))), /* @__PURE__ */React2.createElement(React2.Fragment, null))\n  );\n}\nexport { HydratedRouter, RouterProvider2 as RouterProvider };", "map": {"version": 3, "names": ["_objectSpread", "deserializeErrors", "getHydrationData", "CRITICAL_CSS_DATA_ATTRIBUTE", "FrameworkContext", "RemixErrorBoundary", "RouterProvider", "createBrowserHistory", "createClientRoutes", "createClientRoutesWithHMRRevalidationOptOut", "createRouter", "decodeViaTurboStream", "getPatchRoutesOnNavigationFunction", "getTurboStreamSingleFetchDataStrategy", "hydrationRouteProperties", "invariant", "mapRouteProperties", "useFogOFWarDiscovery", "React", "ReactDOM", "RouterProvider2", "props", "createElement", "flushSync", "React2", "ssrInfo", "router", "initSsrInfo", "window", "__reactRouterContext", "__reactRouterManifest", "__reactRouterRouteModules", "sri", "importMap", "document", "querySelector", "textContent", "JSON", "parse", "integrity", "err", "console", "error", "context", "manifest", "routeModules", "stateDecodingPromise", "routerInitialized", "createHydratedRouter", "_ref", "getContext", "Error", "localSsrInfo", "stream", "then", "value", "state", "catch", "e", "routes", "ssr", "isSpaMode", "hydrationData", "_ssrInfo$manifest$rou", "loaderData", "root", "<PERSON><PERSON><PERSON><PERSON>", "_window$__reactRouter", "getRouteInfo", "routeId", "_ssrInfo$routeModules", "_ssrInfo$manifest$rou2", "_ssrInfo$routeModules2", "clientLoader", "hasHydrateFallback", "HydrateFallback", "location", "basename", "errors", "router2", "history", "future", "middleware", "v8_middleware", "dataStrategy", "patchRoutesOnNavigation", "routeDiscovery", "initialized", "initialize", "createRoutesForHMR", "__reactRouterDataRouter", "HydratedRouter", "_ssrInfo", "criticalCss", "setCriticalCss", "useState", "process", "env", "NODE_ENV", "useEffect", "querySelectorAll", "concat", "for<PERSON>ach", "element", "remove", "setLocation", "useLayoutEffect", "subscribe", "newState", "Fragment", "Provider", "unstable_onError"], "sources": ["C:/Developer/Web Development/reedsoft/node_modules/react-router/dist/development/dom-export.mjs"], "sourcesContent": ["/**\n * react-router v7.9.0\n *\n * Copyright (c) Remix Software Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE.md file in the root directory of this source tree.\n *\n * @license MIT\n */\n\"use client\";\nimport {\n  deserializeErrors,\n  getHydrationData\n} from \"./chunk-AKKEMMKB.mjs\";\nimport {\n  CRITICAL_CSS_DATA_ATTRIBUTE,\n  FrameworkContext,\n  RemixErrorBoundary,\n  RouterProvider,\n  createBrowserHistory,\n  createClientRoutes,\n  createClientRoutesWithHMRRevalidationOptOut,\n  createRouter,\n  decodeViaTurboStream,\n  getPatchRoutesOnNavigationFunction,\n  getTurboStreamSingleFetchDataStrategy,\n  hydrationRouteProperties,\n  invariant,\n  mapRouteProperties,\n  useFogOFWarDiscovery\n} from \"./chunk-S5YDGZLY.mjs\";\n\n// lib/dom-export/dom-router-provider.tsx\nimport * as React from \"react\";\nimport * as ReactDOM from \"react-dom\";\nfunction RouterProvider2(props) {\n  return /* @__PURE__ */ React.createElement(RouterProvider, { flushSync: ReactDOM.flushSync, ...props });\n}\n\n// lib/dom-export/hydrated-router.tsx\nimport * as React2 from \"react\";\nvar ssrInfo = null;\nvar router = null;\nfunction initSsrInfo() {\n  if (!ssrInfo && window.__reactRouterContext && window.__reactRouterManifest && window.__reactRouterRouteModules) {\n    if (window.__reactRouterManifest.sri === true) {\n      const importMap = document.querySelector(\"script[rr-importmap]\");\n      if (importMap?.textContent) {\n        try {\n          window.__reactRouterManifest.sri = JSON.parse(\n            importMap.textContent\n          ).integrity;\n        } catch (err) {\n          console.error(\"Failed to parse import map\", err);\n        }\n      }\n    }\n    ssrInfo = {\n      context: window.__reactRouterContext,\n      manifest: window.__reactRouterManifest,\n      routeModules: window.__reactRouterRouteModules,\n      stateDecodingPromise: void 0,\n      router: void 0,\n      routerInitialized: false\n    };\n  }\n}\nfunction createHydratedRouter({\n  getContext\n}) {\n  initSsrInfo();\n  if (!ssrInfo) {\n    throw new Error(\n      \"You must be using the SSR features of React Router in order to skip passing a `router` prop to `<RouterProvider>`\"\n    );\n  }\n  let localSsrInfo = ssrInfo;\n  if (!ssrInfo.stateDecodingPromise) {\n    let stream = ssrInfo.context.stream;\n    invariant(stream, \"No stream found for single fetch decoding\");\n    ssrInfo.context.stream = void 0;\n    ssrInfo.stateDecodingPromise = decodeViaTurboStream(stream, window).then((value) => {\n      ssrInfo.context.state = value.value;\n      localSsrInfo.stateDecodingPromise.value = true;\n    }).catch((e) => {\n      localSsrInfo.stateDecodingPromise.error = e;\n    });\n  }\n  if (ssrInfo.stateDecodingPromise.error) {\n    throw ssrInfo.stateDecodingPromise.error;\n  }\n  if (!ssrInfo.stateDecodingPromise.value) {\n    throw ssrInfo.stateDecodingPromise;\n  }\n  let routes = createClientRoutes(\n    ssrInfo.manifest.routes,\n    ssrInfo.routeModules,\n    ssrInfo.context.state,\n    ssrInfo.context.ssr,\n    ssrInfo.context.isSpaMode\n  );\n  let hydrationData = void 0;\n  if (ssrInfo.context.isSpaMode) {\n    let { loaderData } = ssrInfo.context.state;\n    if (ssrInfo.manifest.routes.root?.hasLoader && loaderData && \"root\" in loaderData) {\n      hydrationData = {\n        loaderData: {\n          root: loaderData.root\n        }\n      };\n    }\n  } else {\n    hydrationData = getHydrationData({\n      state: ssrInfo.context.state,\n      routes,\n      getRouteInfo: (routeId) => ({\n        clientLoader: ssrInfo.routeModules[routeId]?.clientLoader,\n        hasLoader: ssrInfo.manifest.routes[routeId]?.hasLoader === true,\n        hasHydrateFallback: ssrInfo.routeModules[routeId]?.HydrateFallback != null\n      }),\n      location: window.location,\n      basename: window.__reactRouterContext?.basename,\n      isSpaMode: ssrInfo.context.isSpaMode\n    });\n    if (hydrationData && hydrationData.errors) {\n      hydrationData.errors = deserializeErrors(hydrationData.errors);\n    }\n  }\n  let router2 = createRouter({\n    routes,\n    history: createBrowserHistory(),\n    basename: ssrInfo.context.basename,\n    getContext,\n    hydrationData,\n    hydrationRouteProperties,\n    mapRouteProperties,\n    future: {\n      middleware: ssrInfo.context.future.v8_middleware\n    },\n    dataStrategy: getTurboStreamSingleFetchDataStrategy(\n      () => router2,\n      ssrInfo.manifest,\n      ssrInfo.routeModules,\n      ssrInfo.context.ssr,\n      ssrInfo.context.basename\n    ),\n    patchRoutesOnNavigation: getPatchRoutesOnNavigationFunction(\n      ssrInfo.manifest,\n      ssrInfo.routeModules,\n      ssrInfo.context.ssr,\n      ssrInfo.context.routeDiscovery,\n      ssrInfo.context.isSpaMode,\n      ssrInfo.context.basename\n    )\n  });\n  ssrInfo.router = router2;\n  if (router2.state.initialized) {\n    ssrInfo.routerInitialized = true;\n    router2.initialize();\n  }\n  router2.createRoutesForHMR = /* spacer so ts-ignore does not affect the right hand of the assignment */\n  createClientRoutesWithHMRRevalidationOptOut;\n  window.__reactRouterDataRouter = router2;\n  return router2;\n}\nfunction HydratedRouter(props) {\n  if (!router) {\n    router = createHydratedRouter({\n      getContext: props.getContext\n    });\n  }\n  let [criticalCss, setCriticalCss] = React2.useState(\n    process.env.NODE_ENV === \"development\" ? ssrInfo?.context.criticalCss : void 0\n  );\n  React2.useEffect(() => {\n    if (process.env.NODE_ENV === \"development\") {\n      setCriticalCss(void 0);\n    }\n  }, []);\n  React2.useEffect(() => {\n    if (process.env.NODE_ENV === \"development\" && criticalCss === void 0) {\n      document.querySelectorAll(`[${CRITICAL_CSS_DATA_ATTRIBUTE}]`).forEach((element) => element.remove());\n    }\n  }, [criticalCss]);\n  let [location, setLocation] = React2.useState(router.state.location);\n  React2.useLayoutEffect(() => {\n    if (ssrInfo && ssrInfo.router && !ssrInfo.routerInitialized) {\n      ssrInfo.routerInitialized = true;\n      ssrInfo.router.initialize();\n    }\n  }, []);\n  React2.useLayoutEffect(() => {\n    if (ssrInfo && ssrInfo.router) {\n      return ssrInfo.router.subscribe((newState) => {\n        if (newState.location !== location) {\n          setLocation(newState.location);\n        }\n      });\n    }\n  }, [location]);\n  invariant(ssrInfo, \"ssrInfo unavailable for HydratedRouter\");\n  useFogOFWarDiscovery(\n    router,\n    ssrInfo.manifest,\n    ssrInfo.routeModules,\n    ssrInfo.context.ssr,\n    ssrInfo.context.routeDiscovery,\n    ssrInfo.context.isSpaMode\n  );\n  return (\n    // This fragment is important to ensure we match the <ServerRouter> JSX\n    // structure so that useId values hydrate correctly\n    /* @__PURE__ */ React2.createElement(React2.Fragment, null, /* @__PURE__ */ React2.createElement(\n      FrameworkContext.Provider,\n      {\n        value: {\n          manifest: ssrInfo.manifest,\n          routeModules: ssrInfo.routeModules,\n          future: ssrInfo.context.future,\n          criticalCss,\n          ssr: ssrInfo.context.ssr,\n          isSpaMode: ssrInfo.context.isSpaMode,\n          routeDiscovery: ssrInfo.context.routeDiscovery\n        }\n      },\n      /* @__PURE__ */ React2.createElement(RemixErrorBoundary, { location }, /* @__PURE__ */ React2.createElement(\n        RouterProvider2,\n        {\n          router,\n          unstable_onError: props.unstable_onError\n        }\n      ))\n    ), /* @__PURE__ */ React2.createElement(React2.Fragment, null))\n  );\n}\nexport {\n  HydratedRouter,\n  RouterProvider2 as RouterProvider\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;;AAAC,OAAAA,aAAA;AACb,SACEC,iBAAiB,EACjBC,gBAAgB,QACX,sBAAsB;AAC7B,SACEC,2BAA2B,EAC3BC,gBAAgB,EAChBC,kBAAkB,EAClBC,cAAc,EACdC,oBAAoB,EACpBC,kBAAkB,EAClBC,2CAA2C,EAC3CC,YAAY,EACZC,oBAAoB,EACpBC,kCAAkC,EAClCC,qCAAqC,EACrCC,wBAAwB,EACxBC,SAAS,EACTC,kBAAkB,EAClBC,oBAAoB,QACf,sBAAsB;;AAE7B;AACA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAO,KAAKC,QAAQ,MAAM,WAAW;AACrC,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAO,eAAgBH,KAAK,CAACI,aAAa,CAAChB,cAAc,EAAAN,aAAA;IAAIuB,SAAS,EAAEJ,QAAQ,CAACI;EAAS,GAAKF,KAAK,CAAE,CAAC;AACzG;;AAEA;AACA,OAAO,KAAKG,MAAM,MAAM,OAAO;AAC/B,IAAIC,OAAO,GAAG,IAAI;AAClB,IAAIC,MAAM,GAAG,IAAI;AACjB,SAASC,WAAWA,CAAA,EAAG;EACrB,IAAI,CAACF,OAAO,IAAIG,MAAM,CAACC,oBAAoB,IAAID,MAAM,CAACE,qBAAqB,IAAIF,MAAM,CAACG,yBAAyB,EAAE;IAC/G,IAAIH,MAAM,CAACE,qBAAqB,CAACE,GAAG,KAAK,IAAI,EAAE;MAC7C,MAAMC,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,sBAAsB,CAAC;MAChE,IAAIF,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEG,WAAW,EAAE;QAC1B,IAAI;UACFR,MAAM,CAACE,qBAAqB,CAACE,GAAG,GAAGK,IAAI,CAACC,KAAK,CAC3CL,SAAS,CAACG,WACZ,CAAC,CAACG,SAAS;QACb,CAAC,CAAC,OAAOC,GAAG,EAAE;UACZC,OAAO,CAACC,KAAK,CAAC,4BAA4B,EAAEF,GAAG,CAAC;QAClD;MACF;IACF;IACAf,OAAO,GAAG;MACRkB,OAAO,EAAEf,MAAM,CAACC,oBAAoB;MACpCe,QAAQ,EAAEhB,MAAM,CAACE,qBAAqB;MACtCe,YAAY,EAAEjB,MAAM,CAACG,yBAAyB;MAC9Ce,oBAAoB,EAAE,KAAK,CAAC;MAC5BpB,MAAM,EAAE,KAAK,CAAC;MACdqB,iBAAiB,EAAE;IACrB,CAAC;EACH;AACF;AACA,SAASC,oBAAoBA,CAAAC,IAAA,EAE1B;EAAA,IAF2B;IAC5BC;EACF,CAAC,GAAAD,IAAA;EACCtB,WAAW,CAAC,CAAC;EACb,IAAI,CAACF,OAAO,EAAE;IACZ,MAAM,IAAI0B,KAAK,CACb,mHACF,CAAC;EACH;EACA,IAAIC,YAAY,GAAG3B,OAAO;EAC1B,IAAI,CAACA,OAAO,CAACqB,oBAAoB,EAAE;IACjC,IAAIO,MAAM,GAAG5B,OAAO,CAACkB,OAAO,CAACU,MAAM;IACnCtC,SAAS,CAACsC,MAAM,EAAE,2CAA2C,CAAC;IAC9D5B,OAAO,CAACkB,OAAO,CAACU,MAAM,GAAG,KAAK,CAAC;IAC/B5B,OAAO,CAACqB,oBAAoB,GAAGnC,oBAAoB,CAAC0C,MAAM,EAAEzB,MAAM,CAAC,CAAC0B,IAAI,CAAEC,KAAK,IAAK;MAClF9B,OAAO,CAACkB,OAAO,CAACa,KAAK,GAAGD,KAAK,CAACA,KAAK;MACnCH,YAAY,CAACN,oBAAoB,CAACS,KAAK,GAAG,IAAI;IAChD,CAAC,CAAC,CAACE,KAAK,CAAEC,CAAC,IAAK;MACdN,YAAY,CAACN,oBAAoB,CAACJ,KAAK,GAAGgB,CAAC;IAC7C,CAAC,CAAC;EACJ;EACA,IAAIjC,OAAO,CAACqB,oBAAoB,CAACJ,KAAK,EAAE;IACtC,MAAMjB,OAAO,CAACqB,oBAAoB,CAACJ,KAAK;EAC1C;EACA,IAAI,CAACjB,OAAO,CAACqB,oBAAoB,CAACS,KAAK,EAAE;IACvC,MAAM9B,OAAO,CAACqB,oBAAoB;EACpC;EACA,IAAIa,MAAM,GAAGnD,kBAAkB,CAC7BiB,OAAO,CAACmB,QAAQ,CAACe,MAAM,EACvBlC,OAAO,CAACoB,YAAY,EACpBpB,OAAO,CAACkB,OAAO,CAACa,KAAK,EACrB/B,OAAO,CAACkB,OAAO,CAACiB,GAAG,EACnBnC,OAAO,CAACkB,OAAO,CAACkB,SAClB,CAAC;EACD,IAAIC,aAAa,GAAG,KAAK,CAAC;EAC1B,IAAIrC,OAAO,CAACkB,OAAO,CAACkB,SAAS,EAAE;IAAA,IAAAE,qBAAA;IAC7B,IAAI;MAAEC;IAAW,CAAC,GAAGvC,OAAO,CAACkB,OAAO,CAACa,KAAK;IAC1C,IAAI,CAAAO,qBAAA,GAAAtC,OAAO,CAACmB,QAAQ,CAACe,MAAM,CAACM,IAAI,cAAAF,qBAAA,eAA5BA,qBAAA,CAA8BG,SAAS,IAAIF,UAAU,IAAI,MAAM,IAAIA,UAAU,EAAE;MACjFF,aAAa,GAAG;QACdE,UAAU,EAAE;UACVC,IAAI,EAAED,UAAU,CAACC;QACnB;MACF,CAAC;IACH;EACF,CAAC,MAAM;IAAA,IAAAE,qBAAA;IACLL,aAAa,GAAG5D,gBAAgB,CAAC;MAC/BsD,KAAK,EAAE/B,OAAO,CAACkB,OAAO,CAACa,KAAK;MAC5BG,MAAM;MACNS,YAAY,EAAGC,OAAO;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QAAA,OAAM;UAC1BC,YAAY,GAAAH,qBAAA,GAAE7C,OAAO,CAACoB,YAAY,CAACwB,OAAO,CAAC,cAAAC,qBAAA,uBAA7BA,qBAAA,CAA+BG,YAAY;UACzDP,SAAS,EAAE,EAAAK,sBAAA,GAAA9C,OAAO,CAACmB,QAAQ,CAACe,MAAM,CAACU,OAAO,CAAC,cAAAE,sBAAA,uBAAhCA,sBAAA,CAAkCL,SAAS,MAAK,IAAI;UAC/DQ,kBAAkB,EAAE,EAAAF,sBAAA,GAAA/C,OAAO,CAACoB,YAAY,CAACwB,OAAO,CAAC,cAAAG,sBAAA,uBAA7BA,sBAAA,CAA+BG,eAAe,KAAI;QACxE,CAAC;MAAA,CAAC;MACFC,QAAQ,EAAEhD,MAAM,CAACgD,QAAQ;MACzBC,QAAQ,GAAAV,qBAAA,GAAEvC,MAAM,CAACC,oBAAoB,cAAAsC,qBAAA,uBAA3BA,qBAAA,CAA6BU,QAAQ;MAC/ChB,SAAS,EAAEpC,OAAO,CAACkB,OAAO,CAACkB;IAC7B,CAAC,CAAC;IACF,IAAIC,aAAa,IAAIA,aAAa,CAACgB,MAAM,EAAE;MACzChB,aAAa,CAACgB,MAAM,GAAG7E,iBAAiB,CAAC6D,aAAa,CAACgB,MAAM,CAAC;IAChE;EACF;EACA,IAAIC,OAAO,GAAGrE,YAAY,CAAC;IACzBiD,MAAM;IACNqB,OAAO,EAAEzE,oBAAoB,CAAC,CAAC;IAC/BsE,QAAQ,EAAEpD,OAAO,CAACkB,OAAO,CAACkC,QAAQ;IAClC3B,UAAU;IACVY,aAAa;IACbhD,wBAAwB;IACxBE,kBAAkB;IAClBiE,MAAM,EAAE;MACNC,UAAU,EAAEzD,OAAO,CAACkB,OAAO,CAACsC,MAAM,CAACE;IACrC,CAAC;IACDC,YAAY,EAAEvE,qCAAqC,CACjD,MAAMkE,OAAO,EACbtD,OAAO,CAACmB,QAAQ,EAChBnB,OAAO,CAACoB,YAAY,EACpBpB,OAAO,CAACkB,OAAO,CAACiB,GAAG,EACnBnC,OAAO,CAACkB,OAAO,CAACkC,QAClB,CAAC;IACDQ,uBAAuB,EAAEzE,kCAAkC,CACzDa,OAAO,CAACmB,QAAQ,EAChBnB,OAAO,CAACoB,YAAY,EACpBpB,OAAO,CAACkB,OAAO,CAACiB,GAAG,EACnBnC,OAAO,CAACkB,OAAO,CAAC2C,cAAc,EAC9B7D,OAAO,CAACkB,OAAO,CAACkB,SAAS,EACzBpC,OAAO,CAACkB,OAAO,CAACkC,QAClB;EACF,CAAC,CAAC;EACFpD,OAAO,CAACC,MAAM,GAAGqD,OAAO;EACxB,IAAIA,OAAO,CAACvB,KAAK,CAAC+B,WAAW,EAAE;IAC7B9D,OAAO,CAACsB,iBAAiB,GAAG,IAAI;IAChCgC,OAAO,CAACS,UAAU,CAAC,CAAC;EACtB;EACAT,OAAO,CAACU,kBAAkB,GAAG;EAC7BhF,2CAA2C;EAC3CmB,MAAM,CAAC8D,uBAAuB,GAAGX,OAAO;EACxC,OAAOA,OAAO;AAChB;AACA,SAASY,cAAcA,CAACtE,KAAK,EAAE;EAAA,IAAAuE,QAAA;EAC7B,IAAI,CAAClE,MAAM,EAAE;IACXA,MAAM,GAAGsB,oBAAoB,CAAC;MAC5BE,UAAU,EAAE7B,KAAK,CAAC6B;IACpB,CAAC,CAAC;EACJ;EACA,IAAI,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAGtE,MAAM,CAACuE,QAAQ,CACjDC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAAN,QAAA,GAAGnE,OAAO,cAAAmE,QAAA,uBAAPA,QAAA,CAASjD,OAAO,CAACkD,WAAW,GAAG,KAAK,CAC/E,CAAC;EACDrE,MAAM,CAAC2E,SAAS,CAAC,MAAM;IACrB,IAAIH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;MAC1CJ,cAAc,CAAC,KAAK,CAAC,CAAC;IACxB;EACF,CAAC,EAAE,EAAE,CAAC;EACNtE,MAAM,CAAC2E,SAAS,CAAC,MAAM;IACrB,IAAIH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAIL,WAAW,KAAK,KAAK,CAAC,EAAE;MACpE3D,QAAQ,CAACkE,gBAAgB,KAAAC,MAAA,CAAKlG,2BAA2B,MAAG,CAAC,CAACmG,OAAO,CAAEC,OAAO,IAAKA,OAAO,CAACC,MAAM,CAAC,CAAC,CAAC;IACtG;EACF,CAAC,EAAE,CAACX,WAAW,CAAC,CAAC;EACjB,IAAI,CAACjB,QAAQ,EAAE6B,WAAW,CAAC,GAAGjF,MAAM,CAACuE,QAAQ,CAACrE,MAAM,CAAC8B,KAAK,CAACoB,QAAQ,CAAC;EACpEpD,MAAM,CAACkF,eAAe,CAAC,MAAM;IAC3B,IAAIjF,OAAO,IAAIA,OAAO,CAACC,MAAM,IAAI,CAACD,OAAO,CAACsB,iBAAiB,EAAE;MAC3DtB,OAAO,CAACsB,iBAAiB,GAAG,IAAI;MAChCtB,OAAO,CAACC,MAAM,CAAC8D,UAAU,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EACNhE,MAAM,CAACkF,eAAe,CAAC,MAAM;IAC3B,IAAIjF,OAAO,IAAIA,OAAO,CAACC,MAAM,EAAE;MAC7B,OAAOD,OAAO,CAACC,MAAM,CAACiF,SAAS,CAAEC,QAAQ,IAAK;QAC5C,IAAIA,QAAQ,CAAChC,QAAQ,KAAKA,QAAQ,EAAE;UAClC6B,WAAW,CAACG,QAAQ,CAAChC,QAAQ,CAAC;QAChC;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACd7D,SAAS,CAACU,OAAO,EAAE,wCAAwC,CAAC;EAC5DR,oBAAoB,CAClBS,MAAM,EACND,OAAO,CAACmB,QAAQ,EAChBnB,OAAO,CAACoB,YAAY,EACpBpB,OAAO,CAACkB,OAAO,CAACiB,GAAG,EACnBnC,OAAO,CAACkB,OAAO,CAAC2C,cAAc,EAC9B7D,OAAO,CAACkB,OAAO,CAACkB,SAClB,CAAC;EACD;IACE;IACA;IACA;IAAgBrC,MAAM,CAACF,aAAa,CAACE,MAAM,CAACqF,QAAQ,EAAE,IAAI,EAAE,eAAgBrF,MAAM,CAACF,aAAa,CAC9FlB,gBAAgB,CAAC0G,QAAQ,EACzB;MACEvD,KAAK,EAAE;QACLX,QAAQ,EAAEnB,OAAO,CAACmB,QAAQ;QAC1BC,YAAY,EAAEpB,OAAO,CAACoB,YAAY;QAClCoC,MAAM,EAAExD,OAAO,CAACkB,OAAO,CAACsC,MAAM;QAC9BY,WAAW;QACXjC,GAAG,EAAEnC,OAAO,CAACkB,OAAO,CAACiB,GAAG;QACxBC,SAAS,EAAEpC,OAAO,CAACkB,OAAO,CAACkB,SAAS;QACpCyB,cAAc,EAAE7D,OAAO,CAACkB,OAAO,CAAC2C;MAClC;IACF,CAAC,EACD,eAAgB9D,MAAM,CAACF,aAAa,CAACjB,kBAAkB,EAAE;MAAEuE;IAAS,CAAC,EAAE,eAAgBpD,MAAM,CAACF,aAAa,CACzGF,eAAe,EACf;MACEM,MAAM;MACNqF,gBAAgB,EAAE1F,KAAK,CAAC0F;IAC1B,CACF,CAAC,CACH,CAAC,EAAE,eAAgBvF,MAAM,CAACF,aAAa,CAACE,MAAM,CAACqF,QAAQ,EAAE,IAAI,CAAC;EAAC;AAEnE;AACA,SACElB,cAAc,EACdvE,eAAe,IAAId,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}