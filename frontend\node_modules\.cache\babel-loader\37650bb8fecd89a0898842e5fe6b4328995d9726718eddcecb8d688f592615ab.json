{"ast": null, "code": "var _jsxFileName = \"C:\\\\Developer\\\\Web Development\\\\reedsoft\\\\frontend\\\\src\\\\components\\\\ErrorBoundary.tsx\";\nimport React, { Component } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass ErrorBoundary extends Component {\n  constructor(props) {\n    super(props);\n    this.state = {\n      hasError: false\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      hasError: true,\n      error\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    this.setState({\n      error,\n      errorInfo\n    });\n  }\n  render() {\n    if (this.state.hasError) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-h-screen bg-gray-100 flex items-center justify-center px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-6 h-6 text-red-600\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 40,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-xl font-bold text-gray-900 text-center mb-2\",\n            children: \"Something went wrong\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 text-center mb-6\",\n            children: \"We're sorry, but something unexpected happened. Please try refreshing the page.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.location.reload(),\n              className: \"w-full bg-reedsoft-primary text-white py-2 px-4 rounded-md hover:bg-reedsoft-secondary transition-colors font-medium\",\n              children: \"Refresh Page\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 58,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => window.location.href = '/',\n              className: \"w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors font-medium\",\n              children: \"Go to Homepage\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this), process.env.NODE_ENV === 'development' && this.state.error && /*#__PURE__*/_jsxDEV(\"details\", {\n            className: \"mt-6 p-4 bg-gray-50 rounded-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n              className: \"cursor-pointer text-sm font-medium text-gray-700 mb-2\",\n              children: \"Error Details (Development Only)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-xs text-gray-600 font-mono\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Error:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 80,\n                  columnNumber: 21\n                }, this), \" \", this.state.error.message]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 79,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Stack:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n                  className: \"whitespace-pre-wrap mt-1\",\n                  children: this.state.error.stack\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 84,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 19\n              }, this), this.state.errorInfo && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Component Stack:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 90,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n                  className: \"whitespace-pre-wrap mt-1\",\n                  children: this.state.errorInfo.componentStack\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 91,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\nexport default ErrorBoundary;", "map": {"version": 3, "names": ["React", "Component", "jsxDEV", "_jsxDEV", "Error<PERSON>ou<PERSON><PERSON>", "constructor", "props", "state", "<PERSON><PERSON><PERSON><PERSON>", "getDerivedStateFromError", "error", "componentDidCatch", "errorInfo", "console", "setState", "render", "className", "children", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "location", "reload", "href", "process", "env", "NODE_ENV", "message", "stack", "componentStack"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/components/ErrorBoundary.tsx"], "sourcesContent": ["import React, { Component, ErrorInfo, ReactNode } from 'react';\n\ninterface Props {\n  children: ReactNode;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: Error;\n  errorInfo?: ErrorInfo;\n}\n\nclass ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(error: Error): State {\n    return { hasError: true, error };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n    this.setState({ error, errorInfo });\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return (\n        <div className=\"min-h-screen bg-gray-100 flex items-center justify-center px-4\">\n          <div className=\"max-w-md w-full bg-white rounded-lg shadow-lg p-6\">\n            <div className=\"flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full mb-4\">\n              <svg\n                className=\"w-6 h-6 text-red-600\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                />\n              </svg>\n            </div>\n            \n            <h1 className=\"text-xl font-bold text-gray-900 text-center mb-2\">\n              Something went wrong\n            </h1>\n            \n            <p className=\"text-gray-600 text-center mb-6\">\n              We're sorry, but something unexpected happened. Please try refreshing the page.\n            </p>\n\n            <div className=\"flex flex-col space-y-3\">\n              <button\n                onClick={() => window.location.reload()}\n                className=\"w-full bg-reedsoft-primary text-white py-2 px-4 rounded-md hover:bg-reedsoft-secondary transition-colors font-medium\"\n              >\n                Refresh Page\n              </button>\n              \n              <button\n                onClick={() => window.location.href = '/'}\n                className=\"w-full bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors font-medium\"\n              >\n                Go to Homepage\n              </button>\n            </div>\n\n            {process.env.NODE_ENV === 'development' && this.state.error && (\n              <details className=\"mt-6 p-4 bg-gray-50 rounded-md\">\n                <summary className=\"cursor-pointer text-sm font-medium text-gray-700 mb-2\">\n                  Error Details (Development Only)\n                </summary>\n                <div className=\"text-xs text-gray-600 font-mono\">\n                  <div className=\"mb-2\">\n                    <strong>Error:</strong> {this.state.error.message}\n                  </div>\n                  <div className=\"mb-2\">\n                    <strong>Stack:</strong>\n                    <pre className=\"whitespace-pre-wrap mt-1\">\n                      {this.state.error.stack}\n                    </pre>\n                  </div>\n                  {this.state.errorInfo && (\n                    <div>\n                      <strong>Component Stack:</strong>\n                      <pre className=\"whitespace-pre-wrap mt-1\">\n                        {this.state.errorInfo.componentStack}\n                      </pre>\n                    </div>\n                  )}\n                </div>\n              </details>\n            )}\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAA8B,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAY/D,MAAMC,aAAa,SAASH,SAAS,CAAe;EAClDI,WAAWA,CAACC,KAAY,EAAE;IACxB,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MAAEC,QAAQ,EAAE;IAAM,CAAC;EAClC;EAEA,OAAOC,wBAAwBA,CAACC,KAAY,EAAS;IACnD,OAAO;MAAEF,QAAQ,EAAE,IAAI;MAAEE;IAAM,CAAC;EAClC;EAEAC,iBAAiBA,CAACD,KAAY,EAAEE,SAAoB,EAAE;IACpDC,OAAO,CAACH,KAAK,CAAC,gCAAgC,EAAEA,KAAK,EAAEE,SAAS,CAAC;IACjE,IAAI,CAACE,QAAQ,CAAC;MAAEJ,KAAK;MAA<PERSON>;IAAU,CAAC,CAAC;EACrC;EAEAG,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACR,KAAK,CAACC,QAAQ,EAAE;MACvB,oBACEL,OAAA;QAAKa,SAAS,EAAC,gEAAgE;QAAAC,QAAA,eAC7Ed,OAAA;UAAKa,SAAS,EAAC,mDAAmD;UAAAC,QAAA,gBAChEd,OAAA;YAAKa,SAAS,EAAC,iFAAiF;YAAAC,QAAA,eAC9Fd,OAAA;cACEa,SAAS,EAAC,sBAAsB;cAChCE,IAAI,EAAC,MAAM;cACXC,MAAM,EAAC,cAAc;cACrBC,OAAO,EAAC,WAAW;cAAAH,QAAA,eAEnBd,OAAA;gBACEkB,aAAa,EAAC,OAAO;gBACrBC,cAAc,EAAC,OAAO;gBACtBC,WAAW,EAAE,CAAE;gBACfC,CAAC,EAAC;cAA2I;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9I;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzB,OAAA;YAAIa,SAAS,EAAC,kDAAkD;YAAAC,QAAA,EAAC;UAEjE;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAELzB,OAAA;YAAGa,SAAS,EAAC,gCAAgC;YAAAC,QAAA,EAAC;UAE9C;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJzB,OAAA;YAAKa,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCd,OAAA;cACE0B,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;cACxChB,SAAS,EAAC,sHAAsH;cAAAC,QAAA,EACjI;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETzB,OAAA;cACE0B,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACE,IAAI,GAAG,GAAI;cAC1CjB,SAAS,EAAC,uGAAuG;cAAAC,QAAA,EAClH;YAED;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,EAELM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAI,IAAI,CAAC7B,KAAK,CAACG,KAAK,iBACzDP,OAAA;YAASa,SAAS,EAAC,gCAAgC;YAAAC,QAAA,gBACjDd,OAAA;cAASa,SAAS,EAAC,uDAAuD;cAAAC,QAAA,EAAC;YAE3E;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAS,CAAC,eACVzB,OAAA;cAAKa,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9Cd,OAAA;gBAAKa,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBd,OAAA;kBAAAc,QAAA,EAAQ;gBAAM;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAAC,IAAI,CAACrB,KAAK,CAACG,KAAK,CAAC2B,OAAO;cAAA;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACNzB,OAAA;gBAAKa,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBd,OAAA;kBAAAc,QAAA,EAAQ;gBAAM;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACvBzB,OAAA;kBAAKa,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EACtC,IAAI,CAACV,KAAK,CAACG,KAAK,CAAC4B;gBAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EACL,IAAI,CAACrB,KAAK,CAACK,SAAS,iBACnBT,OAAA;gBAAAc,QAAA,gBACEd,OAAA;kBAAAc,QAAA,EAAQ;gBAAgB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjCzB,OAAA;kBAAKa,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,EACtC,IAAI,CAACV,KAAK,CAACK,SAAS,CAAC2B;gBAAc;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACV;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;IAEA,OAAO,IAAI,CAACtB,KAAK,CAACW,QAAQ;EAC5B;AACF;AAEA,eAAeb,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}