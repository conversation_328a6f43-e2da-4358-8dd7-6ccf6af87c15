{"ast": null, "code": "var _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$(),\n  _s5 = $RefreshSig$(),\n  _s6 = $RefreshSig$(),\n  _s7 = $RefreshSig$();\nimport { useState, useEffect, useMemo } from 'react';\nimport { convertTo12Hour, formatTimeRange, formatTimeInText, getCurrentTime12Hour } from '../utils/timeFormat';\n\n/**\n * Custom hook for formatting individual time values\n * @param time24 - Time in 24-hour format\n * @returns Formatted time object\n */\nexport const useTimeFormat = time24 => {\n  _s();\n  return useMemo(() => convertTo12Hour(time24), [time24]);\n};\n\n/**\n * Custom hook for formatting time ranges\n * @param timeRange - Time range string (e.g., \"06:00 - 07:30\")\n * @returns Formatted time range string\n */\n_s(useTimeFormat, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\nexport const useTimeRangeFormat = timeRange => {\n  _s2();\n  return useMemo(() => formatTimeRange(timeRange), [timeRange]);\n};\n\n/**\n * Custom hook for formatting text containing time information\n * @param text - Text containing time patterns\n * @returns Formatted text with converted times\n */\n_s2(useTimeRangeFormat, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\nexport const useTextTimeFormat = text => {\n  _s3();\n  return useMemo(() => formatTimeInText(text), [text]);\n};\n\n/**\n * Custom hook for formatting multiple time blocks\n * @param timeBlocks - Array of time blocks to format\n * @returns Array of formatted time blocks\n */\n_s3(useTextTimeFormat, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\nexport const useTimeBlocksFormat = timeBlocks => {\n  _s4();\n  return useMemo(() => {\n    return timeBlocks.map(block => ({\n      ...block,\n      time: formatTimeInText(block.time),\n      title: formatTimeInText(block.title),\n      description: formatTimeInText(block.description)\n    }));\n  }, [timeBlocks]);\n};\n\n/**\n * Custom hook for live current time display\n * @param updateInterval - Update interval in milliseconds (default: 60000 = 1 minute)\n * @returns Current time in 12-hour format, updates automatically\n */\n_s4(useTimeBlocksFormat, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\nexport const useCurrentTime = (updateInterval = 60000) => {\n  _s5();\n  const [currentTime, setCurrentTime] = useState(getCurrentTime12Hour());\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentTime(getCurrentTime12Hour());\n    }, updateInterval);\n    return () => clearInterval(interval);\n  }, [updateInterval]);\n  return currentTime;\n};\n\n/**\n * Custom hook for batch time formatting operations\n * @param times - Array of time strings in 24-hour format\n * @returns Object with various formatted versions\n */\n_s5(useCurrentTime, \"SXyt0OmSIjr17e8ZJc3D+5kKsC4=\");\nexport const useBatchTimeFormat = times => {\n  _s6();\n  return useMemo(() => {\n    const formatted = times.map(time => convertTo12Hour(time));\n    const formattedStrings = formatted.map(f => f.formatted);\n    const amTimes = formatted.filter(f => f.period === 'AM').map(f => f.formatted);\n    const pmTimes = formatted.filter(f => f.period === 'PM').map(f => f.formatted);\n    return {\n      all: formatted,\n      strings: formattedStrings,\n      am: amTimes,\n      pm: pmTimes,\n      count: {\n        total: times.length,\n        am: amTimes.length,\n        pm: pmTimes.length\n      }\n    };\n  }, [times]);\n};\n\n/**\n * Custom hook for time-based conditional rendering\n * @param targetTime - Target time in 24-hour format\n * @param tolerance - Tolerance in minutes (default: 30)\n * @returns Object with time comparison utilities\n */\n_s6(useBatchTimeFormat, \"nwk+m61qLgjDVUp4IGV/072DDN4=\");\nexport const useTimeComparison = (targetTime, tolerance = 30) => {\n  _s7();\n  const [currentTime, setCurrentTime] = useState(new Date());\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 60000); // Update every minute\n\n    return () => clearInterval(interval);\n  }, []);\n  return useMemo(() => {\n    const [targetHours, targetMinutes] = targetTime.split(':').map(Number);\n    const targetDate = new Date();\n    targetDate.setHours(targetHours, targetMinutes, 0, 0);\n    const diffMinutes = Math.abs(currentTime.getTime() - targetDate.getTime()) / (1000 * 60);\n    const isNear = diffMinutes <= tolerance;\n    const isPast = currentTime > targetDate;\n    const isUpcoming = !isPast && diffMinutes <= tolerance;\n    return {\n      isNear,\n      isPast,\n      isUpcoming,\n      diffMinutes: Math.round(diffMinutes),\n      targetFormatted: convertTo12Hour(targetTime).formatted,\n      currentFormatted: getCurrentTime12Hour()\n    };\n  }, [targetTime, tolerance, currentTime]);\n};\n_s7(useTimeComparison, \"ZUA9QX8qLOufe6UVf7zXMTW+B5g=\");", "map": {"version": 3, "names": ["useState", "useEffect", "useMemo", "convertTo12Hour", "formatTimeRange", "formatTimeInText", "getCurrentTime12Hour", "useTimeFormat", "time24", "_s", "useTimeRangeFormat", "timeRange", "_s2", "useTextTimeFormat", "text", "_s3", "useTimeBlocksFormat", "timeBlocks", "_s4", "map", "block", "time", "title", "description", "useCurrentTime", "updateInterval", "_s5", "currentTime", "setCurrentTime", "interval", "setInterval", "clearInterval", "useBatchTimeFormat", "times", "_s6", "formatted", "formattedStrings", "f", "amTimes", "filter", "period", "pmTimes", "all", "strings", "am", "pm", "count", "total", "length", "useTimeComparison", "targetTime", "tolerance", "_s7", "Date", "targetHours", "targetMinutes", "split", "Number", "targetDate", "setHours", "diffMinutes", "Math", "abs", "getTime", "isNear", "isPast", "isUpcoming", "round", "targetFormatted", "currentFormatted"], "sources": ["C:/Developer/Web Development/reedsoft/frontend/src/hooks/useTimeFormat.ts"], "sourcesContent": ["import { useState, useEffect, useMemo } from 'react';\nimport { FormattedTime, TimeBlock } from '../types';\nimport { \n  convertTo12Hour, \n  formatTimeRange, \n  formatTimeInText, \n  getCurrentTime12Hour \n} from '../utils/timeFormat';\n\n/**\n * Custom hook for formatting individual time values\n * @param time24 - Time in 24-hour format\n * @returns Formatted time object\n */\nexport const useTimeFormat = (time24: string): FormattedTime => {\n  return useMemo(() => convertTo12Hour(time24), [time24]);\n};\n\n/**\n * Custom hook for formatting time ranges\n * @param timeRange - Time range string (e.g., \"06:00 - 07:30\")\n * @returns Formatted time range string\n */\nexport const useTimeRangeFormat = (timeRange: string): string => {\n  return useMemo(() => formatTimeRange(timeRange), [timeRange]);\n};\n\n/**\n * Custom hook for formatting text containing time information\n * @param text - Text containing time patterns\n * @returns Formatted text with converted times\n */\nexport const useTextTimeFormat = (text: string): string => {\n  return useMemo(() => formatTimeInText(text), [text]);\n};\n\n/**\n * Custom hook for formatting multiple time blocks\n * @param timeBlocks - Array of time blocks to format\n * @returns Array of formatted time blocks\n */\nexport const useTimeBlocksFormat = (timeBlocks: TimeBlock[]) => {\n  return useMemo(() => {\n    return timeBlocks.map(block => ({\n      ...block,\n      time: formatTimeInText(block.time),\n      title: formatTimeInText(block.title),\n      description: formatTimeInText(block.description)\n    }));\n  }, [timeBlocks]);\n};\n\n/**\n * Custom hook for live current time display\n * @param updateInterval - Update interval in milliseconds (default: 60000 = 1 minute)\n * @returns Current time in 12-hour format, updates automatically\n */\nexport const useCurrentTime = (updateInterval: number = 60000): string => {\n  const [currentTime, setCurrentTime] = useState<string>(getCurrentTime12Hour());\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentTime(getCurrentTime12Hour());\n    }, updateInterval);\n\n    return () => clearInterval(interval);\n  }, [updateInterval]);\n\n  return currentTime;\n};\n\n/**\n * Custom hook for batch time formatting operations\n * @param times - Array of time strings in 24-hour format\n * @returns Object with various formatted versions\n */\nexport const useBatchTimeFormat = (times: string[]) => {\n  return useMemo(() => {\n    const formatted = times.map(time => convertTo12Hour(time));\n    const formattedStrings = formatted.map(f => f.formatted);\n    const amTimes = formatted.filter(f => f.period === 'AM').map(f => f.formatted);\n    const pmTimes = formatted.filter(f => f.period === 'PM').map(f => f.formatted);\n\n    return {\n      all: formatted,\n      strings: formattedStrings,\n      am: amTimes,\n      pm: pmTimes,\n      count: {\n        total: times.length,\n        am: amTimes.length,\n        pm: pmTimes.length\n      }\n    };\n  }, [times]);\n};\n\n/**\n * Custom hook for time-based conditional rendering\n * @param targetTime - Target time in 24-hour format\n * @param tolerance - Tolerance in minutes (default: 30)\n * @returns Object with time comparison utilities\n */\nexport const useTimeComparison = (targetTime: string, tolerance: number = 30) => {\n  const [currentTime, setCurrentTime] = useState<Date>(new Date());\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 60000); // Update every minute\n\n    return () => clearInterval(interval);\n  }, []);\n\n  return useMemo(() => {\n    const [targetHours, targetMinutes] = targetTime.split(':').map(Number);\n    const targetDate = new Date();\n    targetDate.setHours(targetHours, targetMinutes, 0, 0);\n\n    const diffMinutes = Math.abs(currentTime.getTime() - targetDate.getTime()) / (1000 * 60);\n    const isNear = diffMinutes <= tolerance;\n    const isPast = currentTime > targetDate;\n    const isUpcoming = !isPast && diffMinutes <= tolerance;\n\n    return {\n      isNear,\n      isPast,\n      isUpcoming,\n      diffMinutes: Math.round(diffMinutes),\n      targetFormatted: convertTo12Hour(targetTime).formatted,\n      currentFormatted: getCurrentTime12Hour()\n    };\n  }, [targetTime, tolerance, currentTime]);\n};\n"], "mappings": ";;;;;;;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,OAAO,QAAQ,OAAO;AAEpD,SACEC,eAAe,EACfC,eAAe,EACfC,gBAAgB,EAChBC,oBAAoB,QACf,qBAAqB;;AAE5B;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,aAAa,GAAIC,MAAc,IAAoB;EAAAC,EAAA;EAC9D,OAAOP,OAAO,CAAC,MAAMC,eAAe,CAACK,MAAM,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;AACzD,CAAC;;AAED;AACA;AACA;AACA;AACA;AAJAC,EAAA,CAJaF,aAAa;AAS1B,OAAO,MAAMG,kBAAkB,GAAIC,SAAiB,IAAa;EAAAC,GAAA;EAC/D,OAAOV,OAAO,CAAC,MAAME,eAAe,CAACO,SAAS,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;AAC/D,CAAC;;AAED;AACA;AACA;AACA;AACA;AAJAC,GAAA,CAJaF,kBAAkB;AAS/B,OAAO,MAAMG,iBAAiB,GAAIC,IAAY,IAAa;EAAAC,GAAA;EACzD,OAAOb,OAAO,CAAC,MAAMG,gBAAgB,CAACS,IAAI,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;AACtD,CAAC;;AAED;AACA;AACA;AACA;AACA;AAJAC,GAAA,CAJaF,iBAAiB;AAS9B,OAAO,MAAMG,mBAAmB,GAAIC,UAAuB,IAAK;EAAAC,GAAA;EAC9D,OAAOhB,OAAO,CAAC,MAAM;IACnB,OAAOe,UAAU,CAACE,GAAG,CAACC,KAAK,KAAK;MAC9B,GAAGA,KAAK;MACRC,IAAI,EAAEhB,gBAAgB,CAACe,KAAK,CAACC,IAAI,CAAC;MAClCC,KAAK,EAAEjB,gBAAgB,CAACe,KAAK,CAACE,KAAK,CAAC;MACpCC,WAAW,EAAElB,gBAAgB,CAACe,KAAK,CAACG,WAAW;IACjD,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACN,UAAU,CAAC,CAAC;AAClB,CAAC;;AAED;AACA;AACA;AACA;AACA;AAJAC,GAAA,CAXaF,mBAAmB;AAgBhC,OAAO,MAAMQ,cAAc,GAAGA,CAACC,cAAsB,GAAG,KAAK,KAAa;EAAAC,GAAA;EACxE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAASM,oBAAoB,CAAC,CAAC,CAAC;EAE9EL,SAAS,CAAC,MAAM;IACd,MAAM4B,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCF,cAAc,CAACtB,oBAAoB,CAAC,CAAC,CAAC;IACxC,CAAC,EAAEmB,cAAc,CAAC;IAElB,OAAO,MAAMM,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACJ,cAAc,CAAC,CAAC;EAEpB,OAAOE,WAAW;AACpB,CAAC;;AAED;AACA;AACA;AACA;AACA;AAJAD,GAAA,CAdaF,cAAc;AAmB3B,OAAO,MAAMQ,kBAAkB,GAAIC,KAAe,IAAK;EAAAC,GAAA;EACrD,OAAOhC,OAAO,CAAC,MAAM;IACnB,MAAMiC,SAAS,GAAGF,KAAK,CAACd,GAAG,CAACE,IAAI,IAAIlB,eAAe,CAACkB,IAAI,CAAC,CAAC;IAC1D,MAAMe,gBAAgB,GAAGD,SAAS,CAAChB,GAAG,CAACkB,CAAC,IAAIA,CAAC,CAACF,SAAS,CAAC;IACxD,MAAMG,OAAO,GAAGH,SAAS,CAACI,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACG,MAAM,KAAK,IAAI,CAAC,CAACrB,GAAG,CAACkB,CAAC,IAAIA,CAAC,CAACF,SAAS,CAAC;IAC9E,MAAMM,OAAO,GAAGN,SAAS,CAACI,MAAM,CAACF,CAAC,IAAIA,CAAC,CAACG,MAAM,KAAK,IAAI,CAAC,CAACrB,GAAG,CAACkB,CAAC,IAAIA,CAAC,CAACF,SAAS,CAAC;IAE9E,OAAO;MACLO,GAAG,EAAEP,SAAS;MACdQ,OAAO,EAAEP,gBAAgB;MACzBQ,EAAE,EAAEN,OAAO;MACXO,EAAE,EAAEJ,OAAO;MACXK,KAAK,EAAE;QACLC,KAAK,EAAEd,KAAK,CAACe,MAAM;QACnBJ,EAAE,EAAEN,OAAO,CAACU,MAAM;QAClBH,EAAE,EAAEJ,OAAO,CAACO;MACd;IACF,CAAC;EACH,CAAC,EAAE,CAACf,KAAK,CAAC,CAAC;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AALAC,GAAA,CArBaF,kBAAkB;AA2B/B,OAAO,MAAMiB,iBAAiB,GAAGA,CAACC,UAAkB,EAAEC,SAAiB,GAAG,EAAE,KAAK;EAAAC,GAAA;EAC/E,MAAM,CAACzB,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAO,IAAIqD,IAAI,CAAC,CAAC,CAAC;EAEhEpD,SAAS,CAAC,MAAM;IACd,MAAM4B,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCF,cAAc,CAAC,IAAIyB,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAMtB,aAAa,CAACF,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO3B,OAAO,CAAC,MAAM;IACnB,MAAM,CAACoD,WAAW,EAAEC,aAAa,CAAC,GAAGL,UAAU,CAACM,KAAK,CAAC,GAAG,CAAC,CAACrC,GAAG,CAACsC,MAAM,CAAC;IACtE,MAAMC,UAAU,GAAG,IAAIL,IAAI,CAAC,CAAC;IAC7BK,UAAU,CAACC,QAAQ,CAACL,WAAW,EAAEC,aAAa,EAAE,CAAC,EAAE,CAAC,CAAC;IAErD,MAAMK,WAAW,GAAGC,IAAI,CAACC,GAAG,CAACnC,WAAW,CAACoC,OAAO,CAAC,CAAC,GAAGL,UAAU,CAACK,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC;IACxF,MAAMC,MAAM,GAAGJ,WAAW,IAAIT,SAAS;IACvC,MAAMc,MAAM,GAAGtC,WAAW,GAAG+B,UAAU;IACvC,MAAMQ,UAAU,GAAG,CAACD,MAAM,IAAIL,WAAW,IAAIT,SAAS;IAEtD,OAAO;MACLa,MAAM;MACNC,MAAM;MACNC,UAAU;MACVN,WAAW,EAAEC,IAAI,CAACM,KAAK,CAACP,WAAW,CAAC;MACpCQ,eAAe,EAAEjE,eAAe,CAAC+C,UAAU,CAAC,CAACf,SAAS;MACtDkC,gBAAgB,EAAE/D,oBAAoB,CAAC;IACzC,CAAC;EACH,CAAC,EAAE,CAAC4C,UAAU,EAAEC,SAAS,EAAExB,WAAW,CAAC,CAAC;AAC1C,CAAC;AAACyB,GAAA,CA9BWH,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}