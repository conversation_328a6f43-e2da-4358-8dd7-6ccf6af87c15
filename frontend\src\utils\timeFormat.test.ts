import { formatTime, formatTimeRange } from './timeFormat';

describe('timeFormat utilities', () => {
  describe('formatTime', () => {
    test('converts 24-hour format to 12-hour AM format', () => {
      expect(formatTime('09:00')).toBe('9:00 AM');
      expect(formatTime('00:00')).toBe('12:00 AM');
      expect(formatTime('01:30')).toBe('1:30 AM');
      expect(formatTime('11:45')).toBe('11:45 AM');
    });

    test('converts 24-hour format to 12-hour PM format', () => {
      expect(formatTime('12:00')).toBe('12:00 PM');
      expect(formatTime('13:00')).toBe('1:00 PM');
      expect(formatTime('15:30')).toBe('3:30 PM');
      expect(formatTime('23:59')).toBe('11:59 PM');
    });

    test('handles edge cases', () => {
      expect(formatTime('12:30')).toBe('12:30 PM'); // Noon
      expect(formatTime('00:30')).toBe('12:30 AM'); // Midnight
    });

    test('handles invalid input gracefully', () => {
      expect(formatTime('')).toBe('');
      expect(formatTime('invalid')).toBe('invalid');
      expect(formatTime('25:00')).toBe('25:00'); // Invalid hour
    });

    test('preserves minutes correctly', () => {
      expect(formatTime('14:05')).toBe('2:05 PM');
      expect(formatTime('08:00')).toBe('8:00 AM');
      expect(formatTime('16:15')).toBe('4:15 PM');
    });
  });

  describe('formatTimeRange', () => {
    test('formats time ranges correctly', () => {
      expect(formatTimeRange('09:00', '10:30')).toBe('9:00 AM - 10:30 AM');
      expect(formatTimeRange('14:00', '15:30')).toBe('2:00 PM - 3:30 PM');
      expect(formatTimeRange('23:00', '23:59')).toBe('11:00 PM - 11:59 PM');
    });

    test('handles AM to PM transitions', () => {
      expect(formatTimeRange('11:30', '13:00')).toBe('11:30 AM - 1:00 PM');
      expect(formatTimeRange('09:00', '14:00')).toBe('9:00 AM - 2:00 PM');
    });

    test('handles midnight and noon correctly', () => {
      expect(formatTimeRange('00:00', '01:00')).toBe('12:00 AM - 1:00 AM');
      expect(formatTimeRange('12:00', '13:00')).toBe('12:00 PM - 1:00 PM');
    });

    test('handles invalid inputs', () => {
      expect(formatTimeRange('', '10:00')).toBe(' - 10:00 AM');
      expect(formatTimeRange('09:00', '')).toBe('9:00 AM - ');
      expect(formatTimeRange('', '')).toBe(' - ');
    });
  });
});
